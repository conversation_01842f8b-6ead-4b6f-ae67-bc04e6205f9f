package org.jeecg.modules.hours.service.impl;

import org.jeecg.modules.hours.service.ApprovalService;
import org.jeecg.modules.hours.dto.HourRecordDTO;
import org.jeecg.modules.hours.vo.ApiResponse;
import org.jeecg.modules.hours.vo.ApprovalRequest;
import org.jeecg.modules.hours.vo.TransferRequest;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class ApprovalServiceImpl implements ApprovalService {
    @Override
    public List<HourRecordDTO> listApprovals() {
        return null; // TODO: 实现审批列表查询
    }
    @Override
    public ApiResponse<Boolean> approve(ApprovalRequest req) {
        return null; // TODO: 实现审批操作
    }
    @Override
    public ApiResponse<Boolean> transferApprover(TransferRequest req) {
        return null; // TODO: 实现移交操作
    }
} 