package org.jeecg.modules.hours.service;

import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 人员项目工时统计报表导出服务
 * @Author: jeecg-boot
 * @Date: 2025-07-08
 * @Version: V1.0
 */
public interface IUserProjectHourExportService {
    
    /**
     * 导出人员项目工时统计报表到Excel
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statPeriod 统计周期
     * @param userIds 用户ID列表
     * @param projectIds 项目ID列表
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @param response HTTP响应
     */
    void exportUserProjectHourReportToExcel(Date startDate, Date endDate, String statPeriod,
                                           List<String> userIds, List<String> projectIds, Integer isHoliday,
                                           HttpServletResponse response);
}