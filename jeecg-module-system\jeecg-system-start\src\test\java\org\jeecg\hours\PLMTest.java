package org.jeecg.hours;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.attendance.v1.model.ChildItem;
import com.lark.oapi.service.attendance.v1.model.Item;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UserStatsView;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 消息推送测试
 * @Author: lsq
 */
@Slf4j
//@ActiveProfiles("dev")
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class PLMTest {

	@org.junit.jupiter.api.Test
	public void testname() throws Exception {

		// 1. 登录接口
		// POST
		// http://**************:9200/k3cloud/Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc
		// {
		// 	"acctid": "62a9a9e7aa9aae",
		// 	"username": "kingdee",
		// 	"password": "Abcd.1234",
		// 	"lcid": 2052
		// }

		// 2.获取项目全量信息
		// POST
		// http://**************:9200/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc
		// {   
		// 	"data": {
		// 		"FormId": "PLM_PDM_1080000000000000000",
		// 		"FieldKeys": "FCODE,FNAME,FCATEGORYID,FPRJSTATUS,FFLOWCREATETIME,FLASTCHECKINDATE,FMODIFYDATE,FCREATEDATE",
		// 		"FilterString": "FCATEGORYID='1080000000000000000' and FLIFECIRCLESTAGE!='AN' ",
		// 		"OrderString": "",
		// 		"TopRowCount": 0,
		// 		"StartRow": 0,
		// 		"Limit": 1000,
		// 		"SubSystemId": ""
		// 	}
		// }

		// 3. 获取项目增量信息，按照更新时间,查询最近1月变动的
		// POST
		// http://**************:9200/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc
		// {   
		// 	"data": {
		// 		"FormId": "PLM_PDM_1080000000000000000",
		// 		"FieldKeys": "FCODE,FNAME,FCATEGORYID,FPRJSTATUS,FFLOWCREATETIME,FLASTCHECKINDATE,FMODIFYDATE,FCREATEDATE",
		// 		"FilterString": "FCATEGORYID='1080000000000000000' and FLIFECIRCLESTAGE!='AN' and FMODIFYDATE>='2025-06-01'",
		// 		"OrderString": "",
		// 		"TopRowCount": 0,
		// 		"StartRow": 0,
		// 		"Limit": 1000,
		// 		"SubSystemId": ""
		// 	}
		// }

		
	}
	
	/**
	 * 登录接口实现
	 * @return 登录成功后的Cookie信息
	 * @throws Exception 请求异常
	 */
	@org.junit.jupiter.api.Test
	public String login() throws Exception {
		String url = "http://**************:9200/k3cloud/Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc";
		
		// 构建请求参数
		JSONObject requestBody = new JSONObject();
		requestBody.put("acctid", "62a9a9e7aa9aae");
		requestBody.put("username", "kingdee");
		requestBody.put("password", "Abcd.1234");
		requestBody.put("lcid", 2052);
		
		// 发送请求
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("Content-Type", "application/json");
		
		StringEntity entity = new StringEntity(requestBody.toString(), StandardCharsets.UTF_8);
		httpPost.setEntity(entity);
		
		CloseableHttpResponse response = httpClient.execute(httpPost);
		
		try {
			HttpEntity responseEntity = response.getEntity();
			String responseBody = EntityUtils.toString(responseEntity);
			log.info("登录响应: {}", responseBody);
			
			// 获取Cookie
			String cookie = response.getFirstHeader("Set-Cookie").getValue();
			log.info("登录成功，获取Cookie: {}", cookie);
			
			return cookie;
		} finally {
			response.close();
			httpClient.close();
		}
	}
	
	/**
	 * 获取项目全量信息
	 * @param cookie 登录后的Cookie
	 * @return 项目全量信息
	 * @throws Exception 请求异常
	 */
	@org.junit.jupiter.api.Test
	public String getProjectFullInfo(String cookie) throws Exception {
		String url = "http://**************:9200/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc";
		
		// 构建请求参数
		JSONObject dataObj = new JSONObject();
		dataObj.put("FormId", "PLM_PDM_1080000000000000000");
		dataObj.put("FieldKeys", "FCODE,FNAME,FCATEGORYID,FPRJSTATUS,FFLOWCREATETIME,FLASTCHECKINDATE,FMODIFYDATE,FCREATEDATE");
		dataObj.put("FilterString", "FCATEGORYID='1080000000000000000' and FLIFECIRCLESTAGE!='AN' ");
		dataObj.put("OrderString", "");
		dataObj.put("TopRowCount", 0);
		dataObj.put("StartRow", 0);
		dataObj.put("Limit", 1000);
		dataObj.put("SubSystemId", "");
		
		JSONObject requestBody = new JSONObject();
		requestBody.put("data", dataObj);
		
		// 发送请求
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("Content-Type", "application/json");
		
		// 如果有Cookie，添加到请求头
		if (cookie != null && !cookie.isEmpty()) {
			httpPost.setHeader("Cookie", cookie);
		}
		
		StringEntity entity = new StringEntity(requestBody.toString(), StandardCharsets.UTF_8);
		httpPost.setEntity(entity);
		
		CloseableHttpResponse response = httpClient.execute(httpPost);
		
		try {
			HttpEntity responseEntity = response.getEntity();
			String responseBody = EntityUtils.toString(responseEntity);
			log.info("获取项目全量信息响应: {}", responseBody);
			
			JSONArray jsonArray =  JSONArray.parseArray(responseBody);
			
			log.info("获取项目全量信息响应: {}", jsonArray);
			
			return responseBody;
		} finally {
			response.close();
			httpClient.close();
		}
	}
	
	/**
	 * 完整测试流程：登录并获取项目信息
	 * @throws Exception 请求异常
	 */
	@org.junit.jupiter.api.Test
	public void testFullProcess() throws Exception {
		// 1. 登录获取Cookie
		String cookie = login();
		
		// 2. 获取项目全量信息
		String projectInfo = getProjectFullInfo(cookie);
		
		// 输出结果
		log.info("项目信息获取完成");
	}
}
