import React, { useState, useEffect } from 'react';
import { Modal, Toast, Spin } from '@douyinfe/semi-ui';
import { RichTextEditor } from '@lark-project/ui-kit-plugin';
import { getFeishuSpaceId } from '../../../utils/auth';
import { get } from '../../../utils/request';

interface RichTextContent {
  doc: string;
  doc_html: string;
  doc_text: string;
  is_empty: boolean;
}

interface ApiResponse<T = any> {
  success: boolean;
  result?: T;
  message?: string;
}

interface RichTextModalProps {
  visible: boolean;
  title?: string;
  onClose: () => void;
  onSave?: (content: RichTextContent) => Promise<void>;
}

const RichTextModal: React.FC<RichTextModalProps> = ({
  visible,
  title = '富文本编辑',
  onClose,
  onSave
}) => {
  const [loading, setLoading] = useState(false);
  const [spaceId, setSpaceId] = useState<string | null>(null);
  const [richTextContent, setRichTextContent] = useState<RichTextContent>({
    doc: '',
    doc_html: '',
    doc_text: '',
    is_empty: false
  });

  // 获取飞书空间ID
  useEffect(() => {
    const feishuSpaceId = getFeishuSpaceId();
    setSpaceId(feishuSpaceId);
  }, []);

  // 加载富文本内容
  useEffect(() => {
    if (visible) {
      loadRichTextContent();
    }
  }, [visible]);

  // 加载富文本内容
  const loadRichTextContent = async () => {
    try {
      setLoading(true);
      const response = await get('/hours/sys/config/rules/richtext') as ApiResponse;
      
      if (response && response.success) {
        if (response.result) {
          try {
            const parsedContent = JSON.parse(response.result);
            const content = JSON.parse(parsedContent.content);
            setRichTextContent(content);
            console.log('成功加载富文本内容:', content);
          } catch (e) {
            console.error('解析富文本内容失败:', e);
            setRichTextContent({
              doc: response.result,
              doc_html: '',
              doc_text: '',
              is_empty: false
            });
          }
        }
      } else {
        console.error('获取富文本内容失败:', response?.message);
        Toast.error('获取内容失败');
      }
    } catch (error) {
      console.error('加载富文本内容出错:', error);
      Toast.error('获取内容失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开飞书富文本编辑器
  const openFeishuRichTextEditor = () => {
    if (typeof window.JSSDK !== 'undefined' && window.JSSDK.richTextEditor) {
      try {
        // 根据飞书文档，配置富文本编辑器
        const options = {
          title: title,
          value: richTextContent.doc || '', // 传递doc字符串内容
          context: {
            spaceId: spaceId || 'DEFAULT_SPACE_ID'
          }
        };
        
        console.log('打开富文本编辑器，传递的内容:', options);
        
      } catch (error) {
        console.error('调用富文本编辑器出错:', error);
        Toast.error('无法打开编辑器');
      }
    } else {
      console.error('JSSDK.richTextEditor 不可用');
      Toast.error('富文本编辑器不可用，请确保在飞书环境中运行');
    }
  };

  return (
    <Modal
      title={title}
      visible={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      bodyStyle={{ padding: '24px', minHeight: '400px' }}
    >
      <div style={{ position: 'relative' }}>
        {loading && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            zIndex: 1
          }}>
            <Spin />
          </div>
        )}
        <div style={{ 
          border: '1px dashed var(--semi-color-border)',
          borderRadius: '6px',
          padding: '16px',
          minHeight: '300px',
          cursor: 'pointer'
        }}
        >
          <RichTextEditor 
            spaceId={spaceId || 'DEFAULT_SPACE_ID'} 
            editable={false} 
            defaultValue={{ 
              doc: richTextContent?.doc
            }}
          />
        </div>
      </div>
    </Modal>
  );
};

export default RichTextModal; 