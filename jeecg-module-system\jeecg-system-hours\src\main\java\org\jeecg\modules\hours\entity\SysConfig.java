package org.jeecg.modules.hours.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统配置表
 */
@Data
@TableName("sys_config")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="系统配置对象", description="系统配置表")
public class SysConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @Excel(name = "项目标识", width = 15)
    @ApiModelProperty(value = "项目标识")
    private String projectKey;

    @Excel(name = "配置键", width = 15)
    @ApiModelProperty(value = "配置键")
    private String configKey;

    @Excel(name = "配置值", width = 15)
    @ApiModelProperty(value = "配置值")
    private String configValue;

    @Excel(name = "配置名称", width = 15)
    @ApiModelProperty(value = "配置名称")
    private String configName;

    @Excel(name = "配置分组", width = 15)
    @ApiModelProperty(value = "配置分组")
    private String configGroup;

    @Excel(name = "值类型", width = 15)
    @ApiModelProperty(value = "值类型")
    private String valueType;

    @Excel(name = "默认值", width = 15)
    @ApiModelProperty(value = "默认值")
    private String defaultValue;

    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private Boolean status;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
} 