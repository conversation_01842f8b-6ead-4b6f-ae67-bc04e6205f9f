package org.jeecg.modules.hours.controller;

import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.hours.entity.FsDepartment;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.entity.FsRole;
import org.jeecg.modules.hours.service.IFsUserService;
import org.jeecg.modules.hours.service.IFsRoleService;
import org.jeecg.modules.hours.service.IFsUserRoleService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.hours.util.RequestHeaderUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
@Slf4j
@Api(tags="用户表")
@RestController
@RequestMapping("/hapi/system/user")
public class FsUserController extends JeecgController<FsUser, IFsUserService> {
	@Autowired
	private IFsUserService fsUserService;
	
	@Autowired
	private IFsRoleService fsRoleService;
	
	@Autowired
	private IFsUserRoleService fsUserRoleService;
	
	/**
	 * 飞书空间ID的请求头名称
	 */
	private static final String SPACE_ID_HEADER = RequestHeaderUtil.SPACE_ID_HEADER;
	
	/**
	 * 管理员角色编码
	 */
	private static final String ADMIN_ROLE_CODE = "admin";
	
	/**
	 * 分页列表查询
	 *
	 * @param fsUser
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "用户表-分页列表查询")
	@ApiOperation(value="用户表-分页列表查询", notes="用户表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(FsUser fsUser,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<FsUser> queryWrapper = QueryGenerator.initQueryWrapper(fsUser, req.getParameterMap());
		Page<FsUser> page = new Page<FsUser>(pageNo, pageSize);
		IPage<FsUser> pageList = fsUserService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	/**
	 * 获取所有部门（不分页）
	 *
	 * @return
	 */
	@AutoLog(value = "部门表-获取所有部门")
	@ApiOperation(value="部门表-获取所有部门", notes="部门表-获取所有部门")
	@GetMapping(value = "/all")
	public Result<?> getAllFsUsers(HttpServletRequest request) {
		String spaceId = request.getHeader("X-Feishu-Space-Id");
		List<FsUser> fsUsers = fsUserService.lambdaQuery().eq(FsUser::getProjectKey, spaceId).list();
		return Result.OK(fsUsers);
	}
	/**
	 * 添加
	 *
	 * @param fsUser
	 * @return
	 */
	@AutoLog(value = "用户表-添加")
	@ApiOperation(value="用户表-添加", notes="用户表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody FsUser fsUser) {
		fsUserService.save(fsUser);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param fsUser
	 * @return
	 */
	@AutoLog(value = "用户表-编辑")
	@ApiOperation(value="用户表-编辑", notes="用户表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody FsUser fsUser) {
		fsUserService.updateById(fsUser);
		return Result.OK("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户表-通过id删除")
	@ApiOperation(value="用户表-通过id删除", notes="用户表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		fsUserService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户表-批量删除")
	@ApiOperation(value="用户表-批量删除", notes="用户表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.fsUserService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户表-通过id查询")
	@ApiOperation(value="用户表-通过id查询", notes="用户表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		FsUser fsUser = fsUserService.getById(id);
		if(fsUser==null) {
			return Result.error("未找到对应数据");
		}
		// 处理密码，不返回给前端
//		fsUser.setPassword(null);
		return Result.OK(fsUser);
	}
	
    /**
     * 导出excel
     *
     * @param request
     * @param fsUser
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, FsUser fsUser) {
        return super.exportXls(request, fsUser, FsUser.class, "用户表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, FsUser.class);
    }
	
	/**
	 * 根据部门ID获取用户列表
	 *
	 * @param departmentId
	 * @return
	 */
	@AutoLog(value = "用户表-根据部门ID获取用户列表")
	@ApiOperation(value="用户表-根据部门ID获取用户列表", notes="用户表-根据部门ID获取用户列表")
	@GetMapping(value = "/listByDepartment")
	public Result<?> getUsersByDepartment(@RequestParam(name="departmentId",required=true) String departmentId) {
		List<FsUser> users = fsUserService.getUsersByDepartmentId(departmentId);
		return Result.OK(users);
	}
	
	/**
	 * 根据项目ID获取用户列表
	 *
	 * @param projectId
	 * @return
	 */
	@AutoLog(value = "用户表-根据项目ID获取用户列表")
	@ApiOperation(value="用户表-根据项目ID获取用户列表", notes="用户表-根据项目ID获取用户列表")
	@GetMapping(value = "/listByProject")
	public Result<?> getUsersByProject(@RequestParam(name="projectId",required=true) String projectId) {
		List<FsUser> users = fsUserService.getUsersByProjectId(projectId);
		return Result.OK(users);
	}
	
	/**
	 * 重置密码
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户表-重置密码")
	@ApiOperation(value="用户表-重置密码", notes="用户表-重置密码")
	@PostMapping(value = "/resetPassword")
	public Result<?> resetPassword(@RequestParam(name="id",required=true) String id) {
		boolean result = fsUserService.resetPassword(id);
		if (result) {
			return Result.OK("密码重置成功！");
		} else {
			return Result.error("密码重置失败！");
		}
	}
	
	/**
	 * 获取管理员列表
	 * 管理员是指拥有"admin"角色的用户
	 *
	 * @return 管理员用户列表
	 */
	@AutoLog(value = "用户表-获取管理员列表")
	@ApiOperation(value="用户表-获取管理员列表", notes="用户表-获取管理员列表")
	@GetMapping(value = "/listAdmins")
	public Result<?> listAdmins(HttpServletRequest request) {
		// 直接调用service方法获取管理员列表
		List<FsUser> adminList = fsUserService.getAdminList();
		return Result.OK(adminList);
	}
	
	/**
	 * 移除管理员
	 * 将移除用户的"admin"角色
	 *
	 * @param id 用户ID
	 * @return 操作结果
	 */
	@AutoLog(value = "用户表-移除管理员")
	@ApiOperation(value="用户表-移除管理员", notes="用户表-移除管理员")
	@GetMapping(value = "/removeAdmin")
	public Result<?> removeAdmin(@RequestParam(name="id",required=true) String id) {
		boolean result = fsUserService.removeAdmin(id);
		if (result) {
			return Result.OK("管理员角色移除成功！");
		} else {
			return Result.error("管理员角色移除失败！");
		}
	}
	
	/**
	 * 添加管理员
	 * 为用户添加"admin"角色
	 *
	 * @param id 用户ID
	 * @return 操作结果
	 */
	@AutoLog(value = "用户表-添加管理员")
	@ApiOperation(value="用户表-添加管理员", notes="用户表-添加管理员")
	@GetMapping(value = "/addAdmin")
	public Result<?> addAdmin(@RequestParam(name="id",required=true) String id) {
		boolean result = fsUserService.addAdmin(id);
		if (result) {
			return Result.OK("管理员添加成功！");
		} else {
			return Result.error("管理员添加失败！");
		}
	}
	
	/**
	 * 批量添加管理员
	 * 
	 * @param ids 用户ID列表，使用JSON数组传递
	 * @return 操作结果
	 */
	@AutoLog(value = "用户表-批量添加管理员")
	@ApiOperation(value="用户表-批量添加管理员", notes="用户表-批量添加管理员")
	@PostMapping(value = "/batchAddAdmins")
	public Result<?> batchAddAdmins(@RequestBody Map<String, List<String>> requestBody) {
		List<String> ids = requestBody.get("ids");
		if (ids == null || ids.isEmpty()) {
			return Result.error("请选择要添加的管理员");
		}
		
		int successCount = fsUserService.batchAddAdmins(ids);
		if (successCount > 0) {
			return Result.OK("成功添加 " + successCount + " 名管理员");
		} else {
			return Result.error("管理员添加失败，请重试");
		}
	}
	
	/**
	 * 修改用户状态
	 *
	 * @param id
	 * @param status
	 * @return
	 */
	@AutoLog(value = "用户表-修改用户状态")
	@ApiOperation(value="用户表-修改用户状态", notes="用户表-修改用户状态")
	@PutMapping(value = "/updateStatus")
	public Result<?> updateStatus(@RequestParam(name="id",required=true) String id,
								  @RequestParam(name="status",required=true) String status) {
		FsUser fsUser = fsUserService.getById(id);
		if(fsUser==null) {
			return Result.error("未找到对应用户");
		}
		fsUser.setStatus(status);
		fsUserService.updateById(fsUser);
		return Result.OK("状态更新成功");
	}
	
	/**
	 * 导入用户
	 *
	 * @param request
	 * @return
	 */
	@AutoLog(value = "用户表-导入用户")
	@ApiOperation(value="用户表-导入用户", notes="用户表-导入用户")
	@PostMapping(value = "/import")
	public Result<?> importUsers(HttpServletRequest request) {
		try {
			// 获取上传的文件
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
			MultipartFile file = multipartRequest.getFile("file");
			
			if (file == null) {
				return Result.error("未获取到上传文件");
			}
			
			boolean result = fsUserService.importUsers(file);
			if (result) {
				return Result.OK("导入成功");
			} else {
				return Result.error("导入失败");
			}
		} catch (Exception e) {
			log.error("导入用户失败", e);
			return Result.error("导入失败：" + e.getMessage());
		}
	}

    /**
     * 获取所有可选为管理员的用户列表
     * 不包括已经拥有"admin"角色的用户
     *
     * @param request HttpServletRequest
     * @return 用户列表
     */
    @AutoLog(value = "用户表-获取所有可选为管理员的用户列表")
    @ApiOperation(value="用户表-获取所有可选为管理员的用户列表", notes="用户表-获取所有可选为管理员的用户列表")
    @GetMapping(value = "/listAllUsersForAdmin")
    public Result<?> listAllUsersForAdmin(HttpServletRequest request) {
        String spaceId = request.getHeader(SPACE_ID_HEADER);
        log.info("获取可选管理员用户列表，当前空间ID: {}", spaceId);
        
        List<FsUser> userList = fsUserService.getAllUsersForAdminSelection();
        log.info("查询到 {} 个可选管理员用户", userList.size());
        
        return Result.OK(userList);
    }
	
	/**
	 * 检查用户是否是管理员
	 *
	 * @param userKey 用户ID
	 * @return 是否是管理员
	 */
	@AutoLog(value = "用户表-检查用户是否是管理员")
	@ApiOperation(value="用户表-检查用户是否是管理员", notes="用户表-检查用户是否是管理员")
	@GetMapping(value = "/isAdmin")
	public Result<?> isAdmin(@RequestParam(name="id",required=true) String userKey) {
		// 获取工作空间标识
        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
        if (StringUtils.isBlank(projectKey)) {
            return Result.error("未提供有效的工作空间标识");
        }
		FsUser userInfo = this.fsUserService.getUserByProjectAndUserKey(projectKey, userKey);
		if(null == userInfo) {
			return Result.error("未匹配到有效用户!");
		}
		
		boolean isAdmin = fsUserService.isAdmin(userInfo.getId());
		return Result.OK(isAdmin);
	}

	/**
	 * 获取项目下的所有有效用户列表
	 * 用于用户选择器组件
	 * projectKey通过RequestHeaderUtil从请求header中获取
	 *
	 * @return 用户列表
	 */
	@AutoLog(value = "用户表-获取项目下的所有有效用户列表")
	@ApiOperation(value="用户表-获取项目下的所有有效用户列表", notes="用户表-获取项目下的所有有效用户列表")
	@GetMapping(value = "/listActiveUsers")
	public Result<?> listActiveUsers() {
		List<FsUser> userList = fsUserService.getActiveUsersByProject();
		log.info("查询到 {} 个有效用户", userList.size());

		return Result.OK(userList);
	}
}