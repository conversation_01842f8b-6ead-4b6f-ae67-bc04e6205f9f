package org.jeecg.modules.plm.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PLM项目VO对象
 */
@Data
@ApiModel(value = "PlmProjectVO对象", description = "PLM项目VO")
public class PlmProjectVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private String status;


    /**
     * 修改日期
     */
    @ApiModelProperty(value = "修改日期")
    private String modifyDate;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    private String createDate;

} 