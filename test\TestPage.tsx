/**
 * 瀑布流级联选择器测试页面
 * 用于测试和验证瀑布流分页功能
 */
import React from 'react';
import { Card, Typography, Tabs, TabPane } from '@douyinfe/semi-ui';
import CascaderTest from './components/CascaderTest';
import ApiTest from './components/ApiTest';
import DebugCascader from './components/DebugCascader';
import SimpleTest from './components/SimpleTest';

const { Title } = Typography;

const TestPage: React.FC = () => {
  return (
    <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title heading={2} style={{ marginBottom: '24px', textAlign: 'center' }}>
          瀑布流级联选择器测试
        </Title>
        
        <Card>
          <Tabs type="line" defaultActiveKey="simple">
            <TabPane tab="简单测试" itemKey="simple">
              <SimpleTest />
            </TabPane>
            <TabPane tab="级联选择器测试" itemKey="cascader">
              <CascaderTest />
            </TabPane>
            <TabPane tab="API接口测试" itemKey="api">
              <ApiTest />
            </TabPane>
            <TabPane tab="API调试工具" itemKey="debug">
              <DebugCascader />
            </TabPane>
          </Tabs>
        </Card>
      </div>
    </div>
  );
};

export default TestPage;
