package org.jeecg.modules.hours.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.DeptProjectDailyHourStatistics;
import org.jeecg.modules.hours.vo.DeptProjectDailyHourReportResult;
import org.jeecg.modules.hours.vo.DeptProjectDailyHourReportVO;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 部门项目每日工时分布统计表
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
public interface DeptProjectDailyHourStatisticsMapper extends BaseMapper<DeptProjectDailyHourStatistics> {

    /**
     * 获取指定月份的日期列表（用于动态列）
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @return 日期列信息
     */
    List<DeptProjectDailyHourReportResult.DayColumnVO> getDayColumns(@Param("startDate") Date startDate,
                                                                     @Param("endDate") Date endDate,
                                                                     @Param("isHoliday") Integer isHoliday);

    /**
     * 获取部门项目每日工时分布报表数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param departmentIds 部门ID列表（可选）
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @param projectKeyword 项目关键字（可选）
     * @return 部门项目每日工时统计数据
     */
    List<DeptProjectDailyHourReportVO> getDeptProjectDailyHourReport(@Param("startDate") Date startDate,
                                                                     @Param("endDate") Date endDate,
                                                                     @Param("departmentIds") List<String> departmentIds,
                                                                     @Param("isHoliday") Integer isHoliday,
                                                                     @Param("projectKeyword") String projectKeyword);

    /**
     * 获取总计行数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param departmentIds 部门ID列表（可选）
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @param projectKeyword 项目关键字（可选）
     * @return 总计数据列表
     */
    List<DeptProjectDailyHourReportVO> getTotalRow(@Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate,
                                                   @Param("departmentIds") List<String> departmentIds,
                                                   @Param("isHoliday") Integer isHoliday,
                                                   @Param("projectKeyword") String projectKeyword);

    /**
     * 按条件统计汇总数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param departmentIds 部门ID列表（可选）
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @param projectKeyword 项目关键字（可选）
     * @return 汇总统计结果
     */
    DeptProjectDailyHourReportResult.ReportSummaryVO getSummaryData(@Param("startDate") Date startDate,
                                                                    @Param("endDate") Date endDate,
                                                                    @Param("departmentIds") List<String> departmentIds,
                                                                    @Param("isHoliday") Integer isHoliday,
                                                                    @Param("projectKeyword") String projectKeyword);

    /**
     * 从基础统计表生成月度统计数据并插入/更新到当前表
     *
     * @param statMonth 统计月份 (格式: 2025-07)
     * @param isHoliday 是否统计假日：0-否；1-是
     * @return 处理的记录数
     */
    int generateMonthlyStatistics(@Param("statMonth") String statMonth,
                                  @Param("isHoliday") Integer isHoliday);

    /**
     * 删除指定月份的统计数据
     *
     * @param statMonth 统计月份
     * @param isHoliday 是否统计假日
     */
    void deleteByStatMonth(@Param("statMonth") String statMonth, 
                          @Param("isHoliday") Integer isHoliday);
}