/**
 * 调试级联选择器 - 用于测试API响应和分页逻辑
 */
import React, { useState } from 'react';
import { Card, Typography, Space, Button, Notification, Descriptions, Tag } from '@douyinfe/semi-ui';
import { get } from '../../../utils/request';
import { ApiResponse } from '../types';

const { Title, Text } = Typography;

interface DebugInfo {
  url: string;
  response: any;
  timestamp: string;
  level: number;
}

const DebugCascader: React.FC = () => {
  const [debugHistory, setDebugHistory] = useState<DebugInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 添加调试信息
  const addDebugInfo = (info: Omit<DebugInfo, 'timestamp'>) => {
    setDebugHistory(prev => [...prev, {
      ...info,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  // 测试工作项接口
  const testWorkItems = async () => {
    setLoading(true);
    try {
      const url = '/hours/workItemCascade/workItems';
      console.log('测试工作项接口:', url);
      
      const response = await get<ApiResponse<any>>(url);
      console.log('工作项响应:', response);
      
      addDebugInfo({
        url,
        response,
        level: 1
      });
      
      if (response?.success) {
        Notification.success({
          title: '工作项接口测试成功',
          content: `返回 ${response.result?.length || 0} 条数据`
        });
      }
    } catch (error) {
      console.error('工作项接口测试失败:', error);
      Notification.error({
        title: '工作项接口测试失败',
        content: String(error)
      });
    } finally {
      setLoading(false);
    }
  };

  // 测试实例接口（使用第一个工作项）
  const testInstances = async () => {
    setLoading(true);
    try {
      // 先获取工作项
      const workItemsResponse = await get<ApiResponse<any>>('/hours/workItemCascade/workItems');
      if (!workItemsResponse?.success || !workItemsResponse.result?.length) {
        Notification.error({
          title: '测试失败',
          content: '无法获取工作项数据'
        });
        return;
      }

      const firstWorkItem = workItemsResponse.result[0];
      const workItemKey = firstWorkItem.value;

      const params = new URLSearchParams();
      params.append('workItemKey', String(workItemKey));
      params.append('pageNo', '1');
      params.append('pageSize', '5');

      const url = `/hours/workItemCascade/instances?${params.toString()}`;
      console.log('测试实例接口:', url);
      
      const response = await get<ApiResponse<any>>(url);
      console.log('实例响应:', response);
      
      addDebugInfo({
        url,
        response,
        level: 2
      });
      
      if (response?.success) {
        const total = response.result?.total || 0;
        const records = response.result?.records?.length || 0;
        
        Notification.success({
          title: '实例接口测试成功',
          content: `返回 ${records} 条数据，总计 ${total} 条`
        });
      }
    } catch (error) {
      console.error('实例接口测试失败:', error);
      Notification.error({
        title: '实例接口测试失败',
        content: String(error)
      });
    } finally {
      setLoading(false);
    }
  };

  // 清空调试历史
  const clearHistory = () => {
    setDebugHistory([]);
  };

  // 渲染调试信息
  const renderDebugInfo = (info: DebugInfo) => {
    const { response } = info;
    
    let dataInfo = '';
    let paginationInfo = '';
    
    if (response?.success) {
      if (response.result?.records) {
        // 分页数据
        dataInfo = `records: ${response.result.records.length} 条`;
        paginationInfo = `total: ${response.result.total}, pageNo: ${response.result.pageNo}, pageSize: ${response.result.pageSize}`;
      } else if (Array.isArray(response.result)) {
        // 非分页数据
        dataInfo = `result: ${response.result.length} 条`;
      }
    }

    return (
      <Card key={info.timestamp} style={{ marginBottom: '16px' }}>
        <Descriptions row>
          <Descriptions.Item itemKey="时间">{info.timestamp}</Descriptions.Item>
          <Descriptions.Item itemKey="级别">第{info.level}级</Descriptions.Item>
          <Descriptions.Item itemKey="URL">{info.url}</Descriptions.Item>
          <Descriptions.Item itemKey="状态">
            <Tag color={response?.success ? 'green' : 'red'}>
              {response?.success ? '成功' : '失败'}
            </Tag>
          </Descriptions.Item>
          {dataInfo && <Descriptions.Item itemKey="数据">{dataInfo}</Descriptions.Item>}
          {paginationInfo && <Descriptions.Item itemKey="分页">{paginationInfo}</Descriptions.Item>}
        </Descriptions>
        
        <details style={{ marginTop: '12px' }}>
          <summary style={{ cursor: 'pointer', color: 'var(--semi-color-primary)' }}>
            查看完整响应
          </summary>
          <pre style={{ 
            marginTop: '8px', 
            padding: '12px', 
            backgroundColor: '#f8f9fa', 
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto',
            maxHeight: '300px'
          }}>
            {JSON.stringify(response, null, 2)}
          </pre>
        </details>
      </Card>
    );
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1000px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>级联选择器API调试工具</Title>
        
        <Space vertical style={{ width: '100%' }} spacing={24}>
          {/* 操作按钮 */}
          <div>
            <Space>
              <Button 
                onClick={testWorkItems} 
                type="primary" 
                loading={loading}
                disabled={loading}
              >
                测试工作项接口
              </Button>
              <Button 
                onClick={testInstances} 
                loading={loading}
                disabled={loading}
              >
                测试实例接口（分页）
              </Button>
              <Button onClick={clearHistory} type="tertiary">
                清空历史
              </Button>
            </Space>
          </div>

          {/* 说明 */}
          <div>
            <Text strong>使用说明：</Text>
            <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
              <li>点击"测试工作项接口"查看第1级数据格式</li>
              <li>点击"测试实例接口"查看第2级分页数据格式</li>
              <li>重点关注响应中的 total 字段，这是分页判断的关键</li>
              <li>如果 total > 5，应该显示"加载更多"按钮</li>
            </ul>
          </div>

          {/* 调试历史 */}
          <div>
            <Text strong>调试历史：</Text>
            <div style={{ marginTop: '16px' }}>
              {debugHistory.length === 0 ? (
                <Text type="tertiary">暂无调试记录</Text>
              ) : (
                debugHistory.map(renderDebugInfo)
              )}
            </div>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default DebugCascader;
