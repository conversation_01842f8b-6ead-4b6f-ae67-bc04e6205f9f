package org.jeecg.modules.hours.service;

import java.util.List;
import java.util.Map;

import org.jeecg.modules.hours.entity.FsUser;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lark.oapi.service.contact.v3.model.User;
import com.lark.project.service.user.model.UserBasicInfo;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
public interface IFsUserService extends IService<FsUser> {

    /**
     * 根据部门ID获取用户列表
     * @param departmentId 部门ID
     * @return 用户列表
     */
    List<FsUser> getUsersByDepartmentId(String departmentId);
    
    /**
     * 根据项目ID获取用户列表
     * @param projectId 项目ID
     * @return 用户列表
     */
    List<FsUser> getUsersByProjectId(String projectId);
    
    /**
     * 重置用户密码
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean resetPassword(String userId);
    
    /**
     * 导入用户
     * @param file Excel文件
     * @return 操作结果
     */
    boolean importUsers(MultipartFile file);
    
    /**
     * 获取管理员列表
     * @return 管理员用户列表
     */
    List<FsUser> getAdminList();
    
    /**
     * 移除管理员
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean removeAdmin(String userId);
    
    /**
     * 添加管理员
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean addAdmin(String userId);
    
    boolean saveAdminList(List<UserBasicInfo> userList,String projectKey);
    
    boolean saveNormalList(List<UserBasicInfo> userList,String projectKey);
    /**
     * 批量添加管理员
     * @param userIds 用户ID列表
     * @return 操作结果，成功添加的用户数量
     */
    int batchAddAdmins(List<String> userIds);

    /**
     * 获取可添加为管理员的用户列表
     * @return 用户列表
     */
    List<FsUser> getAllUsersForAdminSelection();

    /**
     * 判断用户是否是管理员
     * @param userId 用户ID
     * @return 是否是管理员
     */
    boolean isAdmin(String userId);
    
    FsUser getAdminUserLimit1(String projectKey);
    
    List<String> getUnionIds(String projectKey);
    /**
     * 根据项目Key和用户Key获取用户信息
     * @param projectKey 项目Key
     * @param userKey 用户Key
     * @return 用户信息
     */
    FsUser getUserByProjectAndUserKey(String projectKey, String userKey);

    FsUser getUserByProjectAndUnionId(String projectKey, String unionId);

    /**
     * 根据项目Key和用户Key列表获取用户信息
     * @param projectKey 项目Key
     * @param userKeys 用户Key列表
     * @return 用户信息列表
     */
    List<FsUser> getUserByProjectAndUserKeys(String projectKey, List<String> userKeys);

    /**
     * 获取项目下的所有有效用户
     * projectKey通过RequestHeaderUtil从请求header中获取
     * @return 有效用户列表
     */
    List<FsUser> getActiveUsersByProject();
    
    String getUnionIdByUserKey(String userId);
    
    List<String> getUnionIdsByUserKey(String projectKey, List<String> userKeys);
    
    Map<String, User> getFsOpenUser(String projectKey, List<String> unionIds);
    
}