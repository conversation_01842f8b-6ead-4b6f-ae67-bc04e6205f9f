package org.jeecg.modules.hours.service;

import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 部门项目工时统计报表导出服务
 * @Author: jeecg-boot
 * @Date: 2025-07-07
 * @Version: V1.0
 */
public interface IDeptProjectHourExportService {

    /**
     * 导出部门项目工时统计报表为Excel
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statPeriod 统计周期
     * @param departmentIds 部门ID列表（可选）
     * @param projectIds 项目ID列表（可选）
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @param response  HTTP响应对象
     * @throws Exception 导出异常
     */
    void exportDeptProjectHourReportToExcel(Date startDate, Date endDate, String statPeriod,
                                          List<String> departmentIds, List<String> projectIds, Integer isHoliday,
                                          HttpServletResponse response) throws Exception;
}