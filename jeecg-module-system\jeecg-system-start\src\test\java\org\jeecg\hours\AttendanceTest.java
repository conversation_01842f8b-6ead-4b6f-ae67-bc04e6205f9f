package org.jeecg.hours;

import java.util.ArrayList;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.hours.context.AuthContext;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.service.impl.FeishuAuthServiceImpl;
import org.jeecg.modules.hours.task.AttendanceStatJob;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.plugin.builder.GetPluginTokenReq;
import com.lark.project.service.plugin.builder.GetPluginTokenResp;
import com.lark.project.service.plugin.builder.GetUserPluginTokenReq;
import com.lark.project.service.plugin.builder.GetUserPluginTokenResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 消息推送测试
 * @Author: lsq
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class AttendanceTest {

	@Autowired
	private AttendanceStatJob attendanceStatJob;
	
	// 测试环境
	private static final String appId = "cli_a7b2f4bbcb38500d";
	private static final String appSecret = "kzen0nGwHB15eEUVsDXkp1wn0YOF4HUK";
	
	@org.junit.jupiter.api.Test
	public void testname() throws Exception {
		
		String unionId = "on_03144b4670493fe27269925cf7e9cdbf";
//		String unionId = "on_cbad40331d989c6728967eecc3e5c544";
//		 考勤周期
		Integer start = 20250711;
		Integer end = 20250713;
		
		attendanceStatJob.getUserData(unionId,start,end,new ArrayList<String>() {
			{
//				this.add("on_cbad40331d989c6728967eecc3e5c544");
//				this.add("on_03144b4670493fe27269925cf7e9cdbf");
//				this.add("on_4a9c8d448b2465c07a022f7cfbaad4b0");
				this.add("on_a103bf00d2dd4e74022ceaa784a4654b");
//				this.add("on_0079e66017e81ca9b4b15540c6cb64a7");
//				this.add("on_cbad40331d989c6728967eecc3e5c544");
			}
		},appId,appSecret);
		log.info("数据同步完成!");
//		Thread.sleep(1*3600*1000);
	}

}
