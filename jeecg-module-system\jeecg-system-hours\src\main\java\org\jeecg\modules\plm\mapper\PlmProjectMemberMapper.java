package org.jeecg.modules.plm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.plm.entity.PlmProjectMember;

import java.util.List;

/**
 * PLM项目成员Mapper接口
 */
public interface PlmProjectMemberMapper extends BaseMapper<PlmProjectMember> {

    /**
     * 根据项目ID查询项目成员
     *
     * @param projectId 项目ID
     * @return 项目成员列表
     */
    List<PlmProjectMember> queryByProjectId(@Param("projectId") String projectId);

    /**
     * 根据项目ID列表批量查询项目成员
     *
     * @param projectIds 项目ID列表
     * @return 项目成员列表
     */
    List<PlmProjectMember> queryByProjectIds(@Param("projectIds") List<String> projectIds);

    /**
     * 根据用户ID查询参与的项目
     *
     * @param userId 用户ID
     * @return 项目成员列表
     */
    List<PlmProjectMember> queryByUserId(@Param("userId") String userId);

    /**
     * 批量新增项目成员
     *
     * @param memberList 项目成员列表
     * @return 影响行数
     */
    int batchInsert(@Param("memberList") List<PlmProjectMember> memberList);

    /**
     * 根据项目ID删除项目成员
     *
     * @param projectId 项目ID
     * @return 影响行数
     */
    int deleteByProjectId(@Param("projectId") String projectId);
} 