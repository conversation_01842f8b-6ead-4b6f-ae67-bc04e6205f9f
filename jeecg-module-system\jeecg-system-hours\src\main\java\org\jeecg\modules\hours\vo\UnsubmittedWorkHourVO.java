package org.jeecg.modules.hours.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 未提交工时信息VO
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
public class UnsubmittedWorkHourVO {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 飞书UnionID
     */
    private String unionId;

    /**
     * 未提交的日期列表
     */
    private List<LocalDate> unsubmittedDates;

    /**
     * 项目标识
     */
    private String projectKey;

    /**
     * 未提交日期字符串（用于数据库查询结果映射）
     */
    private String unsubmittedDatesStr;

    /**
     * 设置未提交日期字符串并解析为日期列表
     */
    public void setUnsubmittedDatesStr(String unsubmittedDatesStr) {
        this.unsubmittedDatesStr = unsubmittedDatesStr;
        if (StringUtils.hasText(unsubmittedDatesStr)) {
            this.unsubmittedDates = Arrays.stream(unsubmittedDatesStr.split(","))
                    .map(dateStr -> LocalDate.parse(dateStr.trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                    .collect(Collectors.toList());
        } else {
            this.unsubmittedDates = new ArrayList<>();
        }
    }
}
