import { createSlice, configureStore } from '@reduxjs/toolkit'
import {UserInfo} from "../types/api";



const counterSlice = createSlice({
  name: 'counter',
  initialState: {

    activeTab:null as string | null,
        /*{
      name: '',
      mobile: ''
    }*/
  },

  reducers: {

    setActiveTab:(state,pageName)=>{

        state.activeTab=pageName.payload;
    }
  }
})

export const { setUserInfo,setActiveTab } = counterSlice.actions

const store = configureStore({
  reducer: counterSlice.reducer
})

// 7. 导出必要的内容
export default store;
export type RootState = ReturnType<typeof store.getState>;
