package org.jeecg.modules.hours.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.hours.entity.FsSysReminderConfig;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 业务日期计算工具类
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Slf4j
public class BusinessDateCalculator {

    /**
     * 根据提醒配置计算需要检查的业务日期范围
     * 
     * @param reminderConfig 提醒配置
     * @return 业务日期列表
     */
    public static List<LocalDate> calculateBusinessDates(FsSysReminderConfig reminderConfig) {
        String reminderType = reminderConfig.getReminderType();
        String repeatMode = reminderConfig.getRepeatMode();
        
        log.info("计算业务日期范围 - 提醒类型: {}, 重复方式: {}", reminderType, repeatMode);
        
        List<LocalDate> businessDates = new ArrayList<>();
        LocalDate today = LocalDate.now();
        
        switch (reminderType) {
            case "daily":
                businessDates.addAll(calculateDailyBusinessDates(repeatMode, today));
                break;
            case "weekly":
                businessDates.addAll(calculateWeeklyBusinessDates(repeatMode, today));
                break;
            case "monthly":
                businessDates.addAll(calculateMonthlyBusinessDates(repeatMode, today));
                break;
            default:
                throw new IllegalArgumentException("不支持的提醒类型: " + reminderType);
        }
        
        log.info("计算得到的业务日期范围: {}", businessDates);
        return businessDates;
    }
    
    /**
     * 计算每天提醒的业务日期
     * 
     * @param repeatMode 重复方式
     * @param today 当前日期
     * @return 业务日期列表
     */
    private static List<LocalDate> calculateDailyBusinessDates(String repeatMode, LocalDate today) {
        List<LocalDate> dates = new ArrayList<>();
        
        switch (repeatMode) {
            case "current_day":
                // 当天：检查当前日期
                dates.add(today);
                log.info("每天提醒-当天：检查日期 {}", today);
                break;
            case "next_day":
                // 次日：检查昨天的日期
                LocalDate yesterday = today.minusDays(1);
                dates.add(yesterday);
                log.info("每天提醒-次日：检查日期 {}", yesterday);
                break;
            default:
                throw new IllegalArgumentException("不支持的每天提醒重复方式: " + repeatMode);
        }
        
        return dates;
    }
    
    /**
     * 计算每周提醒的业务日期
     * 
     * @param repeatMode 重复方式
     * @param today 当前日期
     * @return 业务日期列表
     */
    private static List<LocalDate> calculateWeeklyBusinessDates(String repeatMode, LocalDate today) {
        List<LocalDate> dates = new ArrayList<>();
        
        switch (repeatMode) {
            case "current_week":
                // 本周：当周的周一到周日
                LocalDate currentWeekStart = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                LocalDate currentWeekEnd = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
                dates.addAll(getDateRange(currentWeekStart, currentWeekEnd));
                log.info("每周提醒-本周：检查日期范围 {} 到 {}", currentWeekStart, currentWeekEnd);
                break;
            case "next_week":
                // 次周：上周的周一到周日
                LocalDate lastWeekStart = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).minusWeeks(1);
                LocalDate lastWeekEnd = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).minusWeeks(1);
                dates.addAll(getDateRange(lastWeekStart, lastWeekEnd));
                log.info("每周提醒-次周：检查日期范围 {} 到 {}", lastWeekStart, lastWeekEnd);
                break;
            default:
                throw new IllegalArgumentException("不支持的每周提醒重复方式: " + repeatMode);
        }
        
        return dates;
    }
    
    /**
     * 计算每月提醒的业务日期
     * 
     * @param repeatMode 重复方式
     * @param today 当前日期
     * @return 业务日期列表
     */
    private static List<LocalDate> calculateMonthlyBusinessDates(String repeatMode, LocalDate today) {
        List<LocalDate> dates = new ArrayList<>();
        
        switch (repeatMode) {
            case "current_month":
                // 本月：当月1号到当前日期
                LocalDate currentMonthStart = today.withDayOfMonth(1);
                LocalDate currentMonthEnd = today;
                dates.addAll(getDateRange(currentMonthStart, currentMonthEnd));
                log.info("每月提醒-本月：检查日期范围 {} 到 {}", currentMonthStart, currentMonthEnd);
                break;
            case "next_month":
                // 次月：上月1号到上月最后一天
                LocalDate lastMonth = today.minusMonths(1);
                LocalDate lastMonthStart = lastMonth.withDayOfMonth(1);
                LocalDate lastMonthEnd = lastMonth.with(TemporalAdjusters.lastDayOfMonth());
                dates.addAll(getDateRange(lastMonthStart, lastMonthEnd));
                log.info("每月提醒-次月：检查日期范围 {} 到 {}", lastMonthStart, lastMonthEnd);
                break;
            default:
                throw new IllegalArgumentException("不支持的每月提醒重复方式: " + repeatMode);
        }
        
        return dates;
    }
    
    /**
     * 获取日期范围内的所有日期
     * 
     * @param startDate 开始日期（包含）
     * @param endDate 结束日期（包含）
     * @return 日期列表
     */
    private static List<LocalDate> getDateRange(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate current = startDate;
        
        while (!current.isAfter(endDate)) {
            dates.add(current);
            current = current.plusDays(1);
        }
        
        return dates;
    }
    
    /**
     * 格式化日期列表为字符串（用于消息显示）
     * 
     * @param dates 日期列表
     * @return 格式化的日期字符串
     */
    public static String formatDatesForMessage(List<LocalDate> dates) {
        if (dates == null || dates.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < dates.size(); i++) {
            LocalDate date = dates.get(i);
            sb.append(date.getMonthValue()).append(".").append(date.getDayOfMonth());
            if (i < dates.size() - 1) {
                sb.append(", ");
            }
        }
        
        return sb.toString();
    }
}
