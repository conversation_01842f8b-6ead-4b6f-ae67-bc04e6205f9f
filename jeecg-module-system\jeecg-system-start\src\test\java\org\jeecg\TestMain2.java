package org.jeecg;

import java.util.Collection;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Arrays;

import lombok.extern.slf4j.Slf4j;

//
//import com.alibaba.fastjson.JSONObject;
//import org.jeecg.common.util.RestUtil;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//
/**
 * @Description: TODO
 * @author: scott
 * @date: 2022年05月10日 14:02
 */
@Slf4j
public class TestMain2 {

	public static void main(String[] args) {

		List<String> admins = java.util.Arrays.asList("A,B".split(","));

		List<String> memberAll = java.util.Arrays.asList("A,B,C,D".split(","));
		Collection<String> members = CollectionUtils.subtract(memberAll, admins);

		log.info("members={}", members);

	}

}
