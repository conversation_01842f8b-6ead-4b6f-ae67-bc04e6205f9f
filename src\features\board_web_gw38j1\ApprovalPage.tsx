import React, { useState, useCallback, useEffect } from 'react';
import { Tooltip,Table, Button, Typography, Tabs, Tag, Badge, Select, Input, Notification, Pagination, DatePicker, Modal, Form, Space, Spin } from '@douyinfe/semi-ui';
import { get, post } from '../../utils/request';
import { getUserInfo } from '../../utils/auth';
import './workPage.css';
import DateRangePicker from './components/DateRangePicker';
import UserSelector from './components/UserSelector';
import HourRecordAuditDetail from './HourRecordAuditDetail';

const { TabPane } = Tabs;
const { Text } = Typography;

// 添加可截断文本的展示组件
const TruncateText: React.FC<{ content: string, width?: number }> = ({ content, width }) => {
  if (!content) return <span>-</span>;
  
  return (
    <Tooltip content={content}>
      <span className="truncate-cell" style={{ width: width ? `${width}px` : '100%' }}>
        {content}
      </span>
    </Tooltip>
  );
};

// 接口返回的工时记录类型
interface HourRecord {
  id: string | number;
  recordId: string | number;
  userId: string;
  userName: string;
  workDate: string;
  hours: number;
  description: string;
  workItemId: string;       // 工作项ID
  workItemName: string;     // 工作项名称
  projectId: string;
  projectName: string;
  instanceId?: string;
  instanceName?: string;
  nodeId?: string;
  nodeName?: string;
  taskId?: string;
  taskName?: string;
  status: number | string;
  statusName: string;
  applyStatus?: number | string;
  applyStatusName?: string;
  dealStatus?: number | string;
  dealStatusName?: string;
  createdAt: string;
  submitTime?: string | null;
  approverName?: string | null;
  rejectReason?: string | null;
  workPath?: string;
  workPeriodStart?: string;
  workPeriodEnd?: string;
  applyId?: number; // 申请批次ID
  approvers?: {
    userId: string;
    userName: string;
    status: number;
    statusName: string;
    updatedAt: string;
  }[];
}

function toFormInitialData(record: HourRecord | null): any {
  if (!record) return undefined;
  return {
    workDate: record.workDate ? new Date(record.workDate) : undefined,
    worker: record.userId,
    workContent: [record.workItemId, record.instanceId, record.nodeId, record.taskId].filter(Boolean),
    projectKey: record.projectId,
    recordId: record.recordId, // 用recordId字段
    projectName: record.projectName,
    instanceName: record.instanceName,
    nodeName: record.nodeName,
    taskName: record.taskName,
    applyId: record.applyId // 添加申请批次ID
  };
}

// API响应接口
interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  code?: number;
  result: T;
  timestamp?: number;
}

// 分页结果接口
interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages?: number;
}

// 工时记录接口
interface HourRecord {
  id: string | number;
  userId: string;
  userName: string;
  workDate: string;
  hours: number;
  description: string;
  workItemId: string;
  workItemName: string;
  projectId: string;
  projectName: string;
  instanceId?: string;
  instanceName?: string;
  nodeId?: string;
  nodeName?: string;
  taskId?: string;
  taskName?: string;
  status: number | string;
  statusName: string;
  applyStatus?: number | string;
  applyStatusName?: string;
  dealStatus?: number | string;
  dealStatusName?: string;
  createdAt: string;
  submitTime?: string | null;
  approverName?: string | null;
  rejectReason?: string | null;
  workPath?: string;
  applyId?: number; // 申请批次ID
  approvers?: {
    userId: string;
    userName: string;
    status: number;
    statusName: string;
    updatedAt: string;
  }[];
}

// 分页查询参数接口
interface QueryParams {
  pageNo: number;
  pageSize: number;
  status?: string;
  tab?: string;
  startDate?: string;
  endDate?: string;
  employeeId?: string;
}

// 获取本周日期范围的工具函数
const getThisWeekRange = (): [string, string] => {
  const now = new Date();
  // 本周开始：当前日期减去（星期几-1）天，即周一
  const startDate = new Date(now.getTime() - (now.getDay() - 1) * 24 * 60 * 60 * 1000);
  // 本周结束：当前日期加上（7-星期几）天，即周日
  const endDate = new Date(now.getTime() + (7 - now.getDay()) * 24 * 60 * 60 * 1000);

  // 格式化为YYYY-MM-DD
  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  return [formatDate(startDate), formatDate(endDate)];
};

// 新增：获取今年日期范围
const getThisYearRange = (): [string, string] => {
  const now = new Date();
  const year = now.getFullYear();
  return [
    `${year}-01-01`,
    `${year}-12-31`
  ];
};

const ApprovalPage: React.FC = () => {
  // 获取当前登录用户
  const currentUser = getUserInfo();
  
  const [activeTab, setActiveTab] = useState('pending');
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<HourRecord[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  // selectedRowKeys类型改为string[]
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<[string, string]>(getThisWeekRange());
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [currentRecord, setCurrentRecord] = useState<HourRecord | null>(null);
  const [approveModalVisible, setApproveModalVisible] = useState(false);
  const [approveComment, setApproveComment] = useState('');
  const [batchApproveModalVisible, setBatchApproveModalVisible] = useState(false);
  const [batchRejectModalVisible, setBatchRejectModalVisible] = useState(false);
  const [visible, setVisible] = useState(false);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    pageNo: 1,
    pageSize: 10,
    tab:activeTab,
    // status: activeTab === 'pending' ? '1' : undefined,
    status: undefined,
    startDate: dateRange[0],
    endDate: dateRange[1]
  });
  const [transferModalVisible, setTransferModalVisible] = useState(false);
  const [transferRecord, setTransferRecord] = useState<HourRecord | null>(null);
  const [oldApproverId, setOldApproverId] = useState<string>('');
  const [newApproverId, setNewApproverId] = useState<string>('');

  // 新增批量移交相关状态
  const [batchTransferModalVisible, setBatchTransferModalVisible] = useState(false);
  const [batchOldApproverId, setBatchOldApproverId] = useState('');
  const [batchNewApproverId, setBatchNewApproverId] = useState('');
  const [batchTransferLoading, setBatchTransferLoading] = useState(false);
  const [batchNewApproverName, setBatchNewApproverName] = useState('');
  const [batchTransferConfirmVisible, setBatchTransferConfirmVisible] = useState(false);

  const [statusFilter, setStatusFilter] = useState<string | undefined>('');

  // 新增交接管理相关状态
  const [handoverModalVisible, setHandoverModalVisible] = useState(false);
  const [handoverSelectedRowKeys, setHandoverSelectedRowKeys] = useState<string[]>([]);
  const [handoverRecords, setHandoverRecords] = useState<HourRecord[]>([]);
  const [handoverTotal, setHandoverTotal] = useState(0);
  const [handoverLoading, setHandoverLoading] = useState(false);
  const [handoverUserIds, setHandoverUserIds] = useState<string[]>([]);
  const [handoverPage, setHandoverPage] = useState(1);
  const [handoverPageSize, setHandoverPageSize] = useState(10);
  const [handoverProjectId, setHandoverProjectId] = useState<string | undefined>(undefined);
  const [handoverProjectOptions, setHandoverProjectOptions] = useState<{ label: string; value: string }[]>([]);
  const [isAdmin, setIsAdmin] = useState(false);

  // 拉取项目列表
  useEffect(() => {
    if (handoverModalVisible) {
      get('/plm/project/list', { pageNo: 1, pageSize: 100 }).then(res => {
        if (res && res.success && Array.isArray(res.result?.records)) {
          setHandoverProjectOptions(res.result.records.map((item: any) => ({ label: item.projectName, value: String(item.id) })));
        } else {
          setHandoverProjectOptions([]);
        }
      });
    }
  }, [handoverModalVisible]);

  // 检查管理员权限
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        const currentUser = getUserInfo();
        if (!currentUser?.userId) {
          setIsAdmin(false);
          return;
        }
        
        const response = await get('/system/user/isAdmin', { id: currentUser.userId });
        if (response && response.success && response.result !== undefined) {
          setIsAdmin(response.result);
        } else {
          setIsAdmin(false);
        }
      } catch (error) {
        console.error('检查管理员权限失败:', error);
        setIsAdmin(false);
      }
    };

    checkAdminStatus();
  }, []);

  // 拉取交接管理Modal内待办数据
  const fetchHandoverRecords = useCallback(async (params?: { userIds?: string[], page?: number, pageSize?: number, projectId?: string }) => {
    setHandoverLoading(true);
    try {
      const res = await get<ApiResponse<PageResult<HourRecord>>>('/hours/hourRecordApply/handovers', {
        tab: 'pending',
        employeeId: params?.userIds && params.userIds.length > 0 ? params.userIds.join(',') : undefined,
        pageNo: params?.page || handoverPage,
        pageSize: params?.pageSize || handoverPageSize,
        projectId: params?.projectId || handoverProjectId,
      });
      if (res && res.success && res.result) {
        setHandoverRecords(res.result.records || []);
        setHandoverTotal(res.result.total || 0);
      } else {
        setHandoverRecords([]);
        setHandoverTotal(0);
      }
    } catch {
      setHandoverRecords([]);
      setHandoverTotal(0);
    } finally {
      setHandoverLoading(false);
    }
  }, [handoverPage, handoverPageSize, handoverProjectId]);

  // Modal打开时拉取
  useEffect(() => {
    if (handoverModalVisible) {
      fetchHandoverRecords({ userIds: handoverUserIds, page: handoverPage, pageSize: handoverPageSize });
    }
    // eslint-disable-next-line
  }, [handoverModalVisible]);
  // 筛选变化时拉取
  useEffect(() => {
    if (handoverModalVisible) {
      fetchHandoverRecords({ userIds: handoverUserIds, page: 1, pageSize: handoverPageSize });
      setHandoverPage(1);
    }
    // eslint-disable-next-line
  }, [handoverUserIds]);
  // 分页变化时拉取
  useEffect(() => {
    if (handoverModalVisible) {
      fetchHandoverRecords({ userIds: handoverUserIds, page: handoverPage, pageSize: handoverPageSize });
    }
    // eslint-disable-next-line
  }, [handoverPage, handoverPageSize]);
  // handoverProjectId变化时拉取
  useEffect(() => {
    if (handoverModalVisible) {
      fetchHandoverRecords({ userIds: handoverUserIds, page: 1, pageSize: handoverPageSize, projectId: handoverProjectId });
      setHandoverPage(1);
    }
    // eslint-disable-next-line
  }, [handoverProjectId]);

  // 获取工时审批列表
  const fetchRecords = useCallback(async (params?: Partial<QueryParams>) => {
    setRecords([]);
    setLoading(true);
    let mergedParams = { ...queryParams, ...params, tab: activeTab };

    // 仅“全部”Tab允许传status，其他Tab不传status
    if (activeTab !== 'all') {
      delete mergedParams.status;
    }

    try {
      // 使用GET请求
      const response = await get<ApiResponse<PageResult<HourRecord>>>('/hours/hourRecordApply/approvals', mergedParams);
      
      // 统一接口响应判断风格
      if (response && response.success && response.result) {
        setRecords(response.result.records || []);
        setTotal(response.result.total || 0);
        // 新增：同步维护pendingRecords
        if ((params?.tab || activeTab) === 'pending') {
          // setPendingRecords(response.result.records || []); // This line is removed as per new_code
        }
      } else {
        console.error('获取工时审批记录失败:', response);
        setRecords([]);
        setTotal(0);
        if ((params?.tab || activeTab) === 'pending') {
          // setPendingRecords([]); // This line is removed as per new_code
        }
      }

    } catch (error) {
      console.error('获取工时审批记录失败:', error);
      Notification.error({
        title: '获取工时审批记录失败',
        content: error.message
      });
      setRecords([]);
      setTotal(0);
      if ((params?.tab || activeTab) === 'pending') {
        // setPendingRecords([]); // This line is removed as per new_code
      }
    } finally {
      setLoading(false);
    }
  }, [queryParams,activeTab]);

  // 首次加载数据
  useEffect(() => {
    fetchRecords({
      pageNo:1
    });
  }, [activeTab]);

  useEffect(() => {
    if (activeTab === 'pending') {
      setDateRange(['', '']);
      setQueryParams(prev => ({
        ...prev,
        startDate: undefined,
        endDate: undefined,
        pageNo: 1,
        tab: activeTab,
      }));
      fetchRecords({
        startDate: undefined,
        endDate: undefined,
        pageNo: 1,
        tab: activeTab,
      });
    } else if (activeTab === 'approved' || activeTab === 'all') {
      const thisYearRange = getThisYearRange();
      setDateRange(thisYearRange);
      setQueryParams(prev => ({
        ...prev,
        startDate: thisYearRange[0],
        endDate: thisYearRange[1],
        pageNo: 1,
        tab: activeTab,
      }));
      fetchRecords({
        startDate: thisYearRange[0],
        endDate: thisYearRange[1],
        pageNo: 1,
        tab: activeTab,
      });
    } else {
      const thisWeekRange = getThisWeekRange();
      setDateRange(thisWeekRange);
      setQueryParams(prev => ({
        ...prev,
        startDate: thisWeekRange[0],
        endDate: thisWeekRange[1],
        pageNo: 1,
        tab: activeTab,
      }));
      fetchRecords({
        startDate: thisWeekRange[0],
        endDate: thisWeekRange[1],
        pageNo: 1,
        tab: activeTab,
      });
    }
  }, [activeTab]);

  useEffect(() => {
    if (activeTab !== 'all') {
      setStatusFilter('');
    }
  }, [activeTab]);

  useEffect(() => {
    if (batchTransferModalVisible) {
      fetchApproverOptions('');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [batchTransferModalVisible]);

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchRecords({ pageNo: page });
  };

  // 处理每页条数变化
  const handlePageSizeChange = (size: number) => {
    setCurrentPage(1); // 改变页面大小时重置到第一页
    setPageSize(size);
    fetchRecords({ pageSize: size, pageNo: 1 });
  };

  // 统一的分页处理函数
  const handlePaginationChange = (page: number, size: number) => {
    if (size !== pageSize) {
      // 页面大小改变
      handlePageSizeChange(size);
    } else {
      // 只是页码改变
      handlePageChange(page);
    }
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates: [string, string]) => {
    setDateRange(dates);
    fetchRecords({
      startDate: dates[0],
      endDate: dates[1],
      pageNo: 1
    });
  };

  // 处理用户选择变化
  const handleUserChange = (userIds: string[]) => {
    setSelectedUsers(userIds);
    fetchRecords({
      employeeId: userIds.length > 0 ? userIds.join(',') : undefined,
      pageNo: 1
    });
  };

  // 重置筛选条件
  const handleResetFilters = () => {
    const thisWeekRange = getThisWeekRange();
    setDateRange(thisWeekRange);
    setSelectedUsers([]);
    fetchRecords({
      startDate: thisWeekRange[0],
      endDate: thisWeekRange[1],
      employeeId: undefined,
      pageNo: 1
    });
  };

  // 处理审批通过
  const handleApprove = async () => {
    if (!currentRecord) return;
    
    if (!currentRecord.applyId) {
      Notification.error({
        title: '审批失败',
        content: '当前记录没有有效的申请批次ID'
      });
      return;
    }
    
    try {
      setLoading(true);
      const response = await post<ApiResponse<boolean>>('/hours/hourRecordApprover/approve', {
        recordId: currentRecord.recordId,
        applyId: currentRecord.applyId,
        action: 'approve',
        remarks: approveComment,
        approverId: currentUser?.userId,
        approverName: currentUser?.name
      });
      
      if (response && response.success) {
        Notification.success({
          title: '审批成功',
          content: '已通过该工时申请'
        });
        setApproveModalVisible(false);
        fetchRecords();
      } else {
        Notification.error({
          title: '审批失败',
          content: response?.message || '操作未完成，请重试'
        });
      }
    } catch (error: any) {
      console.error('审批失败:', error);
      Notification.error({
        title: '审批失败',
        content: error.message || '操作未完成，请重试'
      });
    } finally {
      setLoading(false);
    }
  };


  // 处理审批驳回
  const handleReject = async () => {
    if (!currentRecord) return;
    
    if (!rejectReason.trim()) {
      Notification.warning({
        title: '请填写驳回原因',
        content: '驳回时必须填写驳回原因'
      });
      return;
    }
    
    if (!currentRecord.applyId) {
      Notification.error({
        title: '驳回失败',
        content: '当前记录没有有效的申请批次ID'
      });
      return;
    }
    
    try {
      setLoading(true);
      const response = await post<ApiResponse<boolean>>('/hours/hourRecordApprover/approve', {
        recordId: currentRecord.id,
        applyId: currentRecord.applyId,
        action: 'reject',
        remarks: rejectReason,
        approverId: currentUser?.userId,
        approverName: currentUser?.name
      });
      
      if (response && response.success) {
        Notification.success({
          title: '驳回成功',
          content: '已驳回该工时申请'
        });
        setRejectModalVisible(false);
        fetchRecords();
      } else {
        Notification.error({
          title: '驳回失败',
          content: response?.message || '操作未完成，请重试'
        });
      }
    } catch (error: any) {
      console.error('驳回失败:', error);
      Notification.error({
        title: '驳回失败',
        content: error.message || '操作未完成，请重试'
      });
    } finally {
      setLoading(false);
    }
  };


  // 打开批量审批通过确认弹窗
  const openBatchApproveModal = () => {
    if (!selectedRowKeys || selectedRowKeys.length === 0) {
      Notification.warning({
        title: '请选择记录',
        content: '请先选择需要审批的工时记录'
      });
      return;
    }
    setBatchApproveModalVisible(true);
  };

  // 打开批量驳回确认弹窗
  const openBatchRejectModal = () => {
    if (!selectedRowKeys || selectedRowKeys.length === 0) {
      Notification.warning({
        title: '请选择记录',
        content: '请先选择需要驳回的工时记录'
      });
      return;
    }
    setRejectReason('');
    setBatchRejectModalVisible(true);
  };

  // 处理批量审批通过
  const handleBatchApprove = async () => {
    if (!selectedRowKeys || selectedRowKeys.length === 0) {
      Notification.warning({ title: '请选择记录', content: '请先选择需要审批的工时记录' });
      return;
    }
    try {
      setLoading(true);
      // 获取选中记录的applyId
      const selectedRecords = records.filter(record => selectedRowKeys.includes(String(record.id)));
      const applyIds = selectedRecords.map(record => record.applyId).filter(Boolean);
      
      if (applyIds.length === 0) {
        Notification.error({ title: '批量审批失败', content: '选中的记录中没有有效的申请批次ID' });
        return;
      }
      
      // 构建URL参数
      const applyIdsString = applyIds.join(',');
      const url = `/hours/hourRecordApply/batch-approve?applyIds=${applyIdsString}&action=approve`;
      const response = await post<ApiResponse<boolean>>(url, null, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      
      if (response && response.success) {
        Notification.success({
          title: '批量审批成功',
          content: `已通过${selectedRowKeys.length}条工时申请`
        });
        setSelectedRowKeys([]);
        setBatchApproveModalVisible(false);
        fetchRecords();
      } else {
        Notification.error({
          title: '批量审批失败',
          content: response?.message || '操作未完成，请重试'
        });
      }
    } catch (error: any) {
      console.error('批量审批失败:', error);
      Notification.error({
        title: '批量审批失败',
        content: error.message || '操作未完成，请重试'
      });
    } finally {
      setLoading(false);
    }
  };

  // 处理批量驳回
  const handleBatchReject = async () => {
    if (!selectedRowKeys || selectedRowKeys.length === 0) {
      Notification.warning({ title: '请选择记录', content: '请先选择需要驳回的工时记录' });
      return;
    }
    if (!rejectReason.trim()) {
      Notification.warning({
        title: '请填写驳回原因',
        content: '批量驳回时必须填写驳回原因'
      });
      return;
    }
    
    try {
      setLoading(true);
      // 获取选中记录的applyId
      const selectedRecords = records.filter(record => selectedRowKeys.includes(String(record.id)));
      const applyIds = selectedRecords.map(record => record.applyId).filter(Boolean);
      
      if (applyIds.length === 0) {
        Notification.error({ title: '批量驳回失败', content: '选中的记录中没有有效的申请批次ID' });
        return;
      }
      
      // 构建URL参数
      const applyIdsString = applyIds.join(',');
      const url = `/hours/hourRecordApply/batch-approve?applyIds=${applyIdsString}&action=reject&remarks=${encodeURIComponent(rejectReason)}`;
      const response = await post<ApiResponse<boolean>>(url, null, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      
      if (response && response.success) {
        Notification.success({
          title: '批量驳回成功',
          content: `已驳回${selectedRowKeys.length}条工时申请`
        });
        setSelectedRowKeys([]);
        setBatchRejectModalVisible(false);
        fetchRecords();
      } else {
        Notification.error({
          title: '批量驳回失败',
          content: response?.message || '操作未完成，请重试'
        });
      }
    } catch (error: any) {
      console.error('批量驳回失败:', error);
      Notification.error({
        title: '批量驳回失败',
        content: error.message || '操作未完成，请重试'
      });
    } finally {
      setLoading(false);
    }
  };

  // 批量移交弹窗
  const openBatchTransferModal = () => {
    if (!selectedRowKeys || selectedRowKeys.length === 0) {
      Notification.warning({
        title: '请选择记录',
        content: '请先选择需要移交的工时记录'
      });
      return;
    }
    setBatchOldApproverId('');
    setBatchNewApproverId('');
    setBatchTransferModalVisible(true);
  };

  const handleBatchTransfer = async () => {
    if (!batchOldApproverId || !batchNewApproverId) return;
    setBatchTransferLoading(true);
    try {
      await post('/api/work-hours/batch-transfer-approver', {
        recordIds: selectedRowKeys,
        oldApproverId: batchOldApproverId,
        newApproverId: batchNewApproverId
      });
      setBatchTransferModalVisible(false);
      setSelectedRowKeys([]);
      fetchRecords();
      Notification.success({ title: '批量移交成功' });
    } catch (e: any) {
      Notification.error({ title: '批量移交失败', content: e.message });
    } finally {
      setBatchTransferLoading(false);
    }
  };

  // 1. 获取新审批人下拉数据（排除当前用户，支持搜索）
  const getAvailableApprovers = useCallback(
    async (keyword = ''): Promise<{ value: string; label: string }[]> => {
      try {
        const res = await get('/hours/hourRecordApprover/availableApprovers', { keyword });
        // 统一接口响应判断风格
        if (res && res.success && Array.isArray(res.result)) {
          return res.result.map((u: any) => ({
            value: u.userId,
            label: u.userName,
          }));
        }
      } catch (e) {
        Notification.error({ title: '获取审批人失败', content: e.message });
      }
      return [];
    },
    []
  );

  // 2. 批量移交审批人确认方法
  const handleBatchTransferConfirm = useCallback(async () => {
    setBatchTransferLoading(true);
    try {
      const applyIds = records
        .filter(r => selectedRowKeys.includes(String(r.id)))
        .map(r => r.applyId)
        .filter(Boolean)
        .join(',');
      const res = await post('/hours/hourRecordApprover/batchTransfer', { applyIds, newApproverId: batchNewApproverId });
      // 统一接口响应判断风格
      if (res && res.success) {
        Notification.success({ title: '批量移交成功' });
        setBatchTransferModalVisible(false);
        setBatchTransferConfirmVisible(false);
        setSelectedRowKeys([]);
        fetchRecords();
      } else {
        Notification.warning({ title: '批量移交失败', content: res?.message || '请求失败' });
      }
    } catch (e: any) {
      Notification.warning({ title: '批量移交失败', content: e.message });
    } finally {
      setBatchTransferLoading(false);
    }
  }, [records, selectedRowKeys, batchNewApproverId, fetchRecords]);

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: (string | number)[]) => {
      console.log('Table勾选原始selectedKeys:', selectedKeys);
      const keys = selectedKeys.map(String);
      console.log('Table勾选转为string后的selectedRowKeys:', keys);
      setSelectedRowKeys(keys);
    },
  };

  // 处理打开详情模态窗
  const handleOpenDetailRecord = (record: HourRecord) => {
   // console.log('test.record',record);
    // 设置当前编辑的记录
    setCurrentRecord(record);
    
    // setEditMode('view');
    setVisible(true);
  };


  // HourRecord 增加 approvers 字段
  interface Approver {
    userId: string;
    userName: string;
    status: number;
    statusName: string;
    updatedAt: string;
  }
  interface HourRecord {
    id: string | number;
    userId: string;
    userName: string;
    workDate: string;
    hours: number;
    description: string;
    workItemId: string;
    workItemName: string;
    projectId: string;
    projectName: string;
    instanceId?: string;
    instanceName?: string;
    nodeId?: string;
    nodeName?: string;
    taskId?: string;
    taskName?: string;
    status: number | string;
    statusName: string;
    applyStatus?: number | string;
    applyStatusName?: string;
    dealStatus?: number | string;
    dealStatusName?: string;
    createdAt: string;
    submitTime?: string | null;
    approverName?: string | null;
    rejectReason?: string | null;
    workPath?: string;
    applyId?: number; // 申请批次ID
    approvers?: Approver[];
    hourRecordApproverId?: string; // 新增：工时审批记录的ID
  }

  // 审批进度列
  const columns = [
    {
      title: '批次号',
      dataIndex: 'batchNo',
      width: 90,
      render: (batchNo: string) => <TruncateText content={batchNo || '-'} />
    },
    {
      title: '登记周期',
      dataIndex: 'workDate',
      width: 150,
      render: (_: any, record: any) => {
        const start = record.workPeriodStart ? record.workPeriodStart.replace(/-/g, '.').slice(0, 10) : '';
        const end = record.workPeriodEnd ? record.workPeriodEnd.replace(/-/g, '.').slice(0, 10) : '';
        return <TruncateText content={start && end ? `${start}-${end}` : '-'} />;
      }
    },
    {
      title: '工作人员',
      dataIndex: 'userName',
      width: 90,
      render: (userName: string) => <TruncateText content={userName || '-'} />
    },
    {
      title: '状态',
      dataIndex: 'applyStatusName',
      width: 90,
      render: (applyStatusName: string, record: HourRecord) => {
        let type: 'primary' | 'success' | 'warning' | 'danger' | 'default' = 'default';
        let displayName = applyStatusName;
        // 部分审批判断
        if (
          record.status === 1 &&
          Array.isArray(record.approvers) &&
          record.approvers.length > 1 &&
          record.approvers.some(a => a.status === 1) &&
          record.approvers.some(a => a.status === 0)
        ) {
          type = 'warning';
          displayName = '部分审批';
        } else {
          switch (record.status) {
            case 0: // 待提交
              type = 'default';
              break;
            case 1: // 审批中
              type = 'primary';
              break;
            case 2: // 审批通过
              type = 'success';
              break;
            case 3: // 审批退回
              type = 'danger';
              break;
            default:
              type = 'default';
          }
        }
        return <Tag type={type as any}>{displayName}</Tag>;
      },
    },
    {
      title: '处理状态',
      dataIndex: 'dealStatusName',
      width: 90,
      render: (dealStatusName: string) => <TruncateText content={dealStatusName || '-'} />
    },
    {
      title: '提交时间',
      dataIndex: 'applyTime',
      width: 120,
      render: (submitTime: string) => <TruncateText content={submitTime || '-'} />
    },
    {
      title: '申请人',
      dataIndex: 'applicantName',
      width: 90,
      render: (approverName: string) => <TruncateText content={approverName || '-'} />
    },

    {
      title: '操作',
      dataIndex: 'operation',
      width: 120,
      fixed: 'right' as const,
      render: (_: any, record: HourRecord) => (
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button 
              theme="borderless" 
              type="primary" 
              size="small"
              onClick={() => handleOpenDetailRecord(record)}
            >
              详情
            </Button>
          
        </div>
      ),
    },
  ];

  const handleTransfer = async () => {
    if (!transferRecord || !oldApproverId || !newApproverId) return;
    await post('/api/work-hours/transfer-approver', {
      recordId: transferRecord.id,
      oldApproverId,
      newApproverId
    });
    setTransferModalVisible(false);
    fetchRecords();
  };

  // 新审批人下拉相关状态
  const [approverOptions, setApproverOptions] = useState<{ value: string; label: string }[]>([]);
  const [approverLoading, setApproverLoading] = useState(false);

  const fetchApproverOptions = async (keyword = '') => {
    setApproverLoading(true);
    try {
      const res = await get('/hours/hourRecordApprover/availableApprovers', { keyword });
      if (res && res.success && Array.isArray(res.result)) {
        setApproverOptions(res.result.map((u: any) => ({
          value: u.userId,
          label: u.userName,
        })));
      }
    } finally {
      setApproverLoading(false);
    }
  };

  // 生成交接管理专用columns（去掉操作列，添加审批人，批次号锁定）
  const handoverColumns = columns
    .filter(col => col.dataIndex !== 'operation')
    .map(col => {
      if (col.dataIndex === 'batchNo') {
        return { ...col, fixed: 'left',width: 150 };
      }
      return col;
    });
  // 插入审批人列到“申请人”前
  const applicantIdx = handoverColumns.findIndex(col => col.dataIndex === 'applicantName');
  handoverColumns.splice(applicantIdx, 0, {
    title: '审批人',
    dataIndex: 'approverName',
    width: 100,
    render: (approverName: string) => <TruncateText content={approverName || '-'} />
  });

  // 交接管理Modal批量移交确认逻辑
  const handleHandoverBatchTransferConfirm = async () => {
    // 根据勾选的rowKey（hourRecordApproverId）找到ids
    const selectedRecords = handoverRecords.filter(record => handoverSelectedRowKeys.includes(String(record.hourRecordApproverId)));
    const ids = selectedRecords.map(record => record.hourRecordApproverId).filter(Boolean).join(',');
    if (!ids) {
      Notification.warning({ title: '选中的记录没有有效的ID' });
      return;
    }
    if (!batchNewApproverId) {
      Notification.warning({ title: '请选择新审批人' });
      return;
    }
    setBatchTransferLoading(true);
    try {
      const res = await post('/hours/hourRecordApprover/batchTransfer', { ids, newApproverId: batchNewApproverId });
      if (res && res.success) {
        Notification.success({ title: '批量移交成功' });
        setBatchTransferModalVisible(false);
        setBatchTransferConfirmVisible(false);
        setHandoverSelectedRowKeys([]);
        fetchHandoverRecords();
      } else {
        Notification.warning({ title: '批量移交失败', content: res?.message || '请求失败' });
      }
    } catch (e: any) {
      Notification.warning({ title: '批量移交失败', content: e.message });
    } finally {
      setBatchTransferLoading(false);
    }
  };

  return (
    <div className="work-list-root">
      <div style={{ alignItems: 'center', justifyContent: 'space-between' }}>
        <Tabs 
          type="line"
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            isAdmin ? (
              <Button
                type="primary"
                onClick={() => {
                  setHandoverSelectedRowKeys([]);
                  setHandoverUserIds([]);
                  setHandoverProjectId(undefined);
                  setHandoverPage(1);
                  setHandoverModalVisible(true);
                }}
              >
                交接管理
              </Button>
            ) : undefined
          }
        >
          <TabPane 
            tab={<span>待办</span>} 
            itemKey="pending"
          >
            <div className="work-list-toolbar">
              <div className="toolbar-filters">
                <DateRangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  dimension="day"
                  placeholder="请选择日期范围"
                />
                
                <div style={{ width: 200 }}>
                  <UserSelector
                    value={selectedUsers}
                    onChange={handleUserChange}
                    placeholder="全部人员"
                  />
                </div>
                
                <Button
                  type="tertiary"
                  onClick={handleResetFilters}
                >
                  重置
                </Button>
              </div>
              
              <div className="toolbar-buttons">
                <Button
                  theme="solid"
                  type="primary"
                  onClick={openBatchApproveModal}
                  disabled={selectedRowKeys.length === 0}
                >
                  批量通过
                </Button>
                <Button
                  theme="solid"
                  type="danger"
                  onClick={openBatchRejectModal}
                  disabled={selectedRowKeys.length === 0}
                >
                  批量驳回
                </Button>
                {/* <Button
                  theme="solid"
                  type="tertiary"
                  onClick={openBatchTransferModal}
                  disabled={selectedRowKeys.length === 0}
                >
                  批量移交
                </Button> */}
              </div>
            </div>
            
            <Table
              columns={columns}
              dataSource={records}
              loading={loading}
              pagination={false}
              rowSelection={rowSelection}
              rowKey={record => String(record.id)} // 强制rowKey为string类型
              scroll={{ x: 'max-content' }}
              size="middle"
              style={{ marginTop: 16 }}
            />
          </TabPane>
          <TabPane 
            tab="已办" 
            itemKey="approved"
          >
            <div className="work-list-toolbar">
              <div className="toolbar-filters">
                <DateRangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  dimension="day"
                  placeholder="请选择日期范围"
                />
                
                <div style={{ width: 200 }}>
                  <UserSelector
                    value={selectedUsers}
                    onChange={handleUserChange}
                    placeholder="全部人员"
                  />
                </div>
                
                <Button
                  type="tertiary"
                  onClick={handleResetFilters}
                >
                  重置
                </Button>
              </div>
            </div>
            
            <Table
              columns={columns}
              dataSource={records}
              loading={loading}
              pagination={false}
              rowKey="id"
              scroll={{ x: 'max-content' }}
              size="middle"
              style={{ marginTop: 16 }}
            />
          </TabPane>
          <TabPane 
            tab="全部" 
            itemKey="all"
          >
            <div className="work-list-toolbar">
              <div className="toolbar-filters">
                <DateRangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  dimension="day"
                  placeholder="请选择日期范围"
                />
                
                <div style={{ width: 200 }}>
                  <UserSelector
                    value={selectedUsers}
                    onChange={handleUserChange}
                    placeholder="全部人员"
                  />
                </div>
                
                <Select 
                  placeholder="选择状态" 
                  style={{ width: 120 }}
                  value={statusFilter}
                  onChange={(value: string | undefined) => {
                    setStatusFilter(value);
                    fetchRecords({
                      status: value,
                      pageNo: 1
                    });
                  }}
                >
                  <Select.Option value="">全部</Select.Option>
                  <Select.Option value="0">审批中</Select.Option>
                  <Select.Option value="1">已通过</Select.Option>
                  <Select.Option value="2">已驳回</Select.Option>
                </Select>
                
                <Button
                  type="tertiary"
                  onClick={handleResetFilters}
                >
                  重置
                </Button>
              </div>
            </div>
            
            <Table
              columns={columns}
              dataSource={records}
              loading={loading}
              pagination={false}
              rowKey="id"
              scroll={{ x: 'max-content' }}
              size="middle"
              style={{ marginTop: 16 }}
            />
          </TabPane>
        </Tabs>
      </div>
      
      {/* 分页 */}
      {total > 0 && (
        <div style={{
          marginTop: 16,
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-end'
        }}>
          <Pagination
            currentPage={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={handlePaginationChange}
            showSizeChanger
            showQuickJumper
            showTotal
            pageSizeOpts={[10, 20, 50]}
          />
        </div>
      )}
      
      {/* 单条驳回原因模态窗 */}
      <Modal
        title="驳回原因"
        visible={rejectModalVisible}
        onOk={handleReject}
        onCancel={() => setRejectModalVisible(false)}
        maskClosable={false}
        okText="确认驳回"
        cancelText="取消"
      >
        <Form>
          <Form.TextArea
            field="rejectReason"
            label="驳回原因"
            initValue={rejectReason}
            onChange={(value: string) => setRejectReason(value)}
            placeholder="请输入驳回原因"
            rules={[
              { required: true, message: '请输入驳回原因' }
            ]}
            rows={4}
          />
        </Form>
      </Modal>

      {/* 单条审批通过确认弹窗 */}
      <Modal
        title="工时审批通过确认"
        visible={approveModalVisible}
        onOk={handleApprove}
        onCancel={() => setApproveModalVisible(false)}
        maskClosable={false}
        okText="确定"
        cancelText="取消"
      >
        <div style={{ padding: '20px 0' }}>
          <Text>是否审批通过该数据？确认后当前工时立即生效。</Text>
        </div>
        <Form>
          <Form.TextArea
            field="approveComment"
            label="审批意见（非必填）"
            initValue={approveComment}
            onChange={(value: string) => setApproveComment(value)}
            placeholder="请输入审批意见"
            rows={4}
          />
        </Form>
      </Modal>

      {/* 批量审批通过确认弹窗 */}
      <Modal
        title="工时审批通过确认"
        visible={batchApproveModalVisible}
        onOk={handleBatchApprove}
        onCancel={() => setBatchApproveModalVisible(false)}
        maskClosable={false}
        okText="确定"
        cancelText="取消"
      >
        <div style={{ padding: '20px 0' }}>
          <Text>是否审批通过该数据？确认后当前工时立即生效。</Text>
        </div>
        <Form>
          <Form.TextArea
            field="approveComment"
            label="审批意见（非必填）"
            initValue={approveComment}
            onChange={(value: string) => setApproveComment(value)}
            placeholder="请输入审批意见"
            rows={4}
          />
        </Form>
      </Modal>

      {/* 批量驳回确认弹窗 */}
      <Modal
        title="工时审批驳回确认"
        visible={batchRejectModalVisible}
        onOk={handleBatchReject}
        onCancel={() => setBatchRejectModalVisible(false)}
        maskClosable={false}
        okText="确定"
        cancelText="取消"
      >
        <div style={{ padding: '20px 0' }}>
          <Text>是否驳回该数据？驳回后当前工时将被取回给对应工作人员</Text>
        </div>
        <Form>
          <Form.TextArea
            field="rejectReason"
            label="审批意见（必填）"
            initValue={rejectReason}
            onChange={(value: string) => setRejectReason(value)}
            placeholder="请输入驳回原因"
            rules={[
              { required: true, message: '请输入驳回原因' }
            ]}
            rows={4}
          />
        </Form>
      </Modal>

      {/* 移交审批人弹窗 */}
      <Modal
        title="移交审批人"
        visible={transferModalVisible}
        onOk={handleTransfer}
        onCancel={() => setTransferModalVisible(false)}
        okText="确认移交"
        cancelText="取消"
      >
        <div style={{ marginBottom: 12 }}>
          <div>原审批人：</div>
          <Select
            style={{ width: 200 }}
            value={oldApproverId}
            onChange={setOldApproverId}
            placeholder="请选择原审批人"
          >
            {transferRecord?.approvers?.filter(a => a.status === 0).map(a => (
              <Select.Option value={a.userId} key={a.userId}>{a.userName}</Select.Option>
            ))}
          </Select>
        </div>
        <div>
          <div>新审批人：</div>
          <Input
            style={{ width: 200 }}
            value={newApproverId}
            onChange={setNewApproverId}
            placeholder="请输入新审批人ID"
          />
          {/* 实际项目可用用户选择器，这里用输入框占位 */}
        </div>
      </Modal>

      {/* 批量移交弹窗 */}
      <Modal
        title="批量移交审批人"
        visible={batchTransferModalVisible}
        onOk={handleHandoverBatchTransferConfirm}
        onCancel={() => setBatchTransferModalVisible(false)}
        okText="确认移交"
        cancelText="取消"
        width={500}
      >
        <div>
          <div>新审批人：</div>
          <Select
            value={batchNewApproverId}
            onChange={v => {
              setBatchNewApproverId((v ?? '') as string);
              const option = approverOptions.find(opt => opt.value === v);
              setBatchNewApproverName(option?.label || '');
            }}
            placeholder="请选择交接人"
            optionList={approverOptions}
            loading={approverLoading}
            showClear
            filter
            onSearch={fetchApproverOptions}
            style={{ width: 200 }}
            emptyContent={approverLoading ? <Spin /> : '暂无数据'}
          />
        </div>
      </Modal>

      {/* 二次确认弹窗 */}
      <Modal
        title="确认批量移交"
        visible={batchTransferConfirmVisible}
        onOk={handleBatchTransferConfirm}
        onCancel={() => setBatchTransferConfirmVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <div>是否将所选 {selectedRowKeys.length} 条记录移交给新审批人：{batchNewApproverName}？</div>
      </Modal>

      {/* 交接管理模态窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span>交接管理</span>
            <Typography.Text type="tertiary" style={{ fontSize: 12, marginLeft: 12 }}>
              先选取交接人员，并勾选列表数据，然后再点击【批量交接】
            </Typography.Text>
          </div>
        }
        visible={handoverModalVisible}
        onOk={() => setHandoverModalVisible(false)}
        onCancel={() => setHandoverModalVisible(false)}
        width={900}
        maskClosable={false}
        footer={null}
      >
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <div style={{ width: 200 }}>
            <UserSelector
              value={handoverUserIds}
              onChange={setHandoverUserIds}
              placeholder="全部人员"
            />
          </div>
          <div style={{ width: 200, marginLeft: 12 }}>
            <Select
              style={{ width: 200 }}
              value={handoverProjectId}
              placeholder="全部项目"
              optionList={handoverProjectOptions}
              onChange={(value: string | undefined) => {
                setHandoverProjectId(value);
                // fetchRecords({
                //   status: value,
                //   pageNo: 1
                // });
              }}
              showClear
            />
          </div>
          <div style={{ flex: 1 }} />
          <Button type="primary" onClick={() => {
            // 交接管理Modal批量移交逻辑
            if (!handoverSelectedRowKeys || handoverSelectedRowKeys.length === 0) {
              Notification.warning({ title: '请选择要移交的记录' });
              return;
            }
            // 复用原有openBatchTransferModal逻辑，但用handoverSelectedRowKeys
            setBatchOldApproverId('');
            setBatchNewApproverId('');
            setBatchTransferModalVisible(true);
            // 你可以在openBatchTransferModal逻辑中用handoverSelectedRowKeys
          }}>
            批量移交
          </Button>
        </div>
        <Table
          columns={handoverColumns}
          dataSource={handoverRecords}
          loading={handoverLoading}
          rowKey={record => String(record.hourRecordApproverId)}
          rowSelection={{
            selectedRowKeys: handoverSelectedRowKeys,
            onChange: (selectedRowKeys = [], selectedRows) => setHandoverSelectedRowKeys(selectedRowKeys.map(String)),
            fixed: true,
          }}
          pagination={false}
          scroll={{ x: 'max-content' }}
          size="middle"
        />
        <div style={{ marginTop: 5, marginBottom: 10, display: 'flex', justifyContent: 'flex-end' }}>
          <Pagination
            currentPage={handoverPage}
            pageSize={handoverPageSize}
            total={handoverTotal}
            onChange={(page, size) => {
              setHandoverPage(page);
              setHandoverPageSize(size);
            }}
            showSizeChanger
            showQuickJumper
            showTotal
            pageSizeOpts={[10, 20, 50]}
          />
        </div>
      </Modal>

      <HourRecordAuditDetail
          visible={visible}
          isAudit={true}
          readOnly={true}
          initialData={currentRecord ? toFormInitialData(currentRecord as any) : undefined}
          onCancel={() => { setVisible(false); setCurrentRecord(null); }}
          onSave={fetchRecords}
          onSubmit={fetchRecords}
        />

    </div>
  );
};

export default ApprovalPage; 