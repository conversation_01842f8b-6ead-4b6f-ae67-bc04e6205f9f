package org.jeecg.modules.hours.service.impl;

import org.jeecg.modules.hours.entity.HourRecordDetail;
import org.jeecg.modules.hours.mapper.HourRecordDetailMapper;
import org.jeecg.modules.hours.service.IHourRecordDetailService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

@Service
public class HourRecordDetailServiceImpl extends ServiceImpl<HourRecordDetailMapper, HourRecordDetail>
        implements IHourRecordDetailService {
    // 可扩展自定义方法
} 