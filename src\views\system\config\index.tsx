import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card,
  Table, 
  Button, 
  Input, 
  Select, 
  Space, 
  Typography, 
  Popconfirm, 
  Modal, 
  Form, 
  Notification,
  Tag
} from '@douyinfe/semi-ui';
import { IconPlus, IconSearch, IconRefresh, IconDelete } from '@douyinfe/semi-icons';
import { configApi } from '../../../api/system/config';
import { SysConfig, SysConfigQuery, PageResult, SysConfigGroupStat } from '../../../types/systemConfig';

const { Title } = Typography;
const { Option } = Select;

// 模拟数据 - 配置列表
const mockConfigs: SysConfig[] = [
  {
    id: '1',
    configKey: 'sys.name',
    configValue: '工时管理系统',
    configName: '系统名称',
    configGroup: '系统设置',
    remark: '显示在浏览器标签页和系统首页',
    status: '0',
    createTime: '2024-02-20 10:00:00',
    createBy: 'admin'
  },
  {
    id: '2',
    configKey: 'sys.logo',
    configValue: '/logo.png',
    configName: '系统Logo',
    configGroup: '系统设置',
    remark: '系统Logo路径',
    status: '0',
    createTime: '2024-02-20 10:00:00',
    createBy: 'admin'
  },
  {
    id: '3',
    configKey: 'sys.index.skinName',
    configValue: 'skin-blue',
    configName: '主题皮肤',
    configGroup: '系统设置',
    remark: '主题颜色配置',
    status: '0',
    createTime: '2024-02-20 10:00:00',
    createBy: 'admin'
  },
  {
    id: '4',
    configKey: 'sys.user.initPassword',
    configValue: '123456',
    configName: '用户初始密码',
    configGroup: '用户设置',
    remark: '创建用户时的初始密码',
    status: '0',
    createTime: '2024-02-20 10:00:00',
    createBy: 'admin'
  },
  {
    id: '5',
    configKey: 'sys.account.captchaEnabled',
    configValue: 'true',
    configName: '是否开启验证码',
    configGroup: '安全设置',
    remark: '登录时是否需要验证码',
    status: '0',
    createTime: '2024-02-20 10:00:00',
    createBy: 'admin'
  },
  {
    id: '6',
    configKey: 'sys.account.registerUser',
    configValue: 'false',
    configName: '是否允许注册',
    configGroup: '安全设置',
    remark: '是否开启用户注册功能',
    status: '1',
    createTime: '2024-02-20 10:00:00',
    createBy: 'admin'
  },
  {
    id: '7',
    configKey: 'job.whitelist',
    configValue: '127.0.0.1',
    configName: '定时任务白名单',
    configGroup: '作业设置',
    remark: '定时任务可访问的IP白名单，多个IP用逗号分隔',
    status: '0',
    createTime: '2024-02-20 10:00:00',
    createBy: 'admin'
  }
];

// 模拟数据 - 配置分组
const mockConfigGroups: SysConfigGroupStat[] = [
  { groupName: '系统设置', count: 3 },
  { groupName: '用户设置', count: 1 },
  { groupName: '安全设置', count: 2 },
  { groupName: '作业设置', count: 1 }
];

// 状态选项
const statusOptions = [
  { label: '正常', value: '0' },
  { label: '停用', value: '1' }
];

const ConfigManagement: React.FC = () => {
  // 表单实例
  const formRef = React.useRef<Form>(null);
  const editFormRef = React.useRef<Form>(null);

  // 状态定义
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<SysConfig[]>([]);
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentId, setCurrentId] = useState<string>('');
  const [searchParams, setSearchParams] = useState<Partial<SysConfigQuery>>({});
  const [configGroups, setConfigGroups] = useState<string[]>([]);
  const [useApi, setUseApi] = useState(true); // 默认使用API

  // 获取配置分组列表
  const fetchConfigGroups = useCallback(async () => {
    try {
      setLoading(true);
      if (useApi) {
        const response = await configApi.getConfigGroupStats();
        const groups = response.map(item => item.groupName);
        setConfigGroups(groups);
      } else {
        // 使用模拟数据
        setConfigGroups(mockConfigGroups.map(group => group.groupName));
      }
    } catch (error) {
      console.error('获取配置分组失败:', error);
      // 发生错误时回退到模拟数据
      setConfigGroups(mockConfigGroups.map(group => group.groupName));
      Notification.error({
        title: '获取配置分组失败',
        content: '已切换到本地模拟数据',
        duration: 3
      });
      setUseApi(false);
    } finally {
      setLoading(false);
    }
  }, [useApi]);

  // 获取数据列表
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      if (useApi) {
        // 使用API获取数据
        const params: SysConfigQuery = {
          ...searchParams,
          pageNo,
          pageSize
        };
        const response = await configApi.getConfigList(params);
        setDataSource(response.records);
        setTotal(response.total);
      } else {
        // 使用模拟数据并进行过滤
        let filteredData = [...mockConfigs];
        
        // 应用搜索条件
        if (searchParams.configKey) {
          filteredData = filteredData.filter(item => 
            item.configKey.toLowerCase().includes(searchParams.configKey!.toLowerCase())
          );
        }
        
        if (searchParams.configName) {
          filteredData = filteredData.filter(item => 
            item.configName.toLowerCase().includes(searchParams.configName!.toLowerCase())
          );
        }
        
        if (searchParams.configGroup) {
          filteredData = filteredData.filter(item => 
            item.configGroup === searchParams.configGroup
          );
        }
        
        if (searchParams.status) {
          filteredData = filteredData.filter(item => 
            item.status === searchParams.status
          );
        }
        
        // 分页
        const startIndex = (pageNo - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const pagedData = filteredData.slice(startIndex, endIndex);
        
        setDataSource(pagedData);
        setTotal(filteredData.length);
      }
    } catch (error) {
      console.error('获取配置列表失败:', error);
      Notification.error({
        title: '获取数据失败',
        content: '已切换到本地模拟数据',
        duration: 3
      });
      
      // 发生错误时回退到模拟数据
      setUseApi(false);
      const pagedData = mockConfigs.slice(0, pageSize);
      setDataSource(pagedData);
      setTotal(mockConfigs.length);
    } finally {
      setLoading(false);
    }
  }, [pageNo, pageSize, searchParams, useApi]);

  // 初始化
  useEffect(() => {
    fetchConfigGroups();
    fetchData();
  }, [fetchConfigGroups, fetchData]);

  // 搜索方法
  const handleSearch = () => {
    if (formRef.current) {
      const values = formRef.current.getValues();
      setSearchParams(values);
      setPageNo(1); // 重置页码
    }
  };

  // 重置搜索
  const handleReset = () => {
    if (formRef.current) {
      formRef.current.reset();
      setSearchParams({});
      setPageNo(1);
    }
  };

  // 分页变化
  const handlePageChange = (currentPage: number) => {
    setPageNo(currentPage);
  };

  // 页大小变化
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setPageNo(1);
  };

  // 选择行变化
  const handleSelectChange = (selectedRowKeys: string[]) => {
    setSelectedRowKeys(selectedRowKeys);
  };

  // 新增配置
  const handleAdd = () => {
    if (editFormRef.current) {
      editFormRef.current.reset();
    }
    setEditMode(false);
    setCurrentId('');
    setModalVisible(true);
  };

  // 编辑配置
  const handleEdit = async (id: string) => {
    try {
      setLoading(true);
      
      if (useApi) {
        // 使用API获取数据
        const data = await configApi.getConfigById(id);
        if (editFormRef.current) {
          editFormRef.current.setValues(data);
        }
      } else {
        // 使用模拟数据
        const data = mockConfigs.find(item => item.id === id);
        if (data && editFormRef.current) {
          editFormRef.current.setValues(data);
        }
      }
      
      setEditMode(true);
      setCurrentId(id);
      setModalVisible(true);
    } catch (error) {
      Notification.error({
        title: '获取数据失败',
        content: '请稍后重试',
        duration: 3
      });
    } finally {
      setLoading(false);
    }
  };

  // 删除配置
  const handleDelete = async (id: string) => {
    try {
      setLoading(true);
      
      if (useApi) {
        // 使用API删除
        await configApi.deleteConfig(id);
      } else {
        // 模拟删除操作
        // 实际上不改变 mockConfigs，而只是过滤当前页面显示的数据
        setDataSource(prev => prev.filter(item => item.id !== id));
      }
      
      Notification.success({
        title: '删除成功',
        duration: 3
      });
      
      // 如果当前页只有一条数据，且不是第一页，则跳转到上一页
      if (dataSource.length === 1 && pageNo > 1) {
        setPageNo(pageNo - 1);
      } else {
        fetchData();
      }
    } catch (error) {
      Notification.error({
        title: '删除失败',
        content: '请稍后重试',
        duration: 3
      });
    } finally {
      setLoading(false);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      Notification.warning({
        title: '请选择要删除的记录',
        duration: 3
      });
      return;
    }

    try {
      setLoading(true);
      
      if (useApi) {
        // 使用API批量删除
        await configApi.deleteBatch(selectedRowKeys.join(','));
      } else {
        // 模拟批量删除操作
        setDataSource(prev => prev.filter(item => !selectedRowKeys.includes(item.id)));
      }
      
      Notification.success({
        title: '删除成功',
        duration: 3
      });
      
      setSelectedRowKeys([]);
      // 如果删除后当前页没有数据，则跳转到上一页
      if (selectedRowKeys.length === dataSource.length && pageNo > 1) {
        setPageNo(pageNo - 1);
      } else {
        fetchData();
      }
    } catch (error) {
      Notification.error({
        title: '删除失败',
        content: '请稍后重试',
        duration: 3
      });
    } finally {
      setLoading(false);
    }
  };

  // 保存配置
  const handleSave = async () => {
    if (!editFormRef.current) return;
    
    try {
      const values = await editFormRef.current.validate();
      setLoading(true);

      if (useApi) {
        // 使用API保存数据
        if (editMode) {
          // 更新
          await configApi.updateConfig({ ...values, id: currentId });
        } else {
          // 新增
          await configApi.addConfig(values);
        }
      } else {
        // 模拟保存操作
        if (editMode) {
          // 更新 - 查找并替换当前页面的数据
          setDataSource(prev => prev.map(item => 
            item.id === currentId ? { ...values, id: currentId } : item
          ));
        } else {
          // 新增 - 生成新ID并添加到当前页面
          const newId = String(Date.now());
          const newItem: SysConfig = { 
            ...values, 
            id: newId,
            createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
            createBy: 'admin'
          };
          setDataSource(prev => [...prev, newItem]);
        }
      }

      Notification.success({
        title: editMode ? '更新成功' : '添加成功',
        duration: 3
      });
      
      setModalVisible(false);
      fetchData();
      
      // 如果新增了分组，刷新分组列表
      if (!configGroups.includes(values.configGroup)) {
        fetchConfigGroups();
      }
    } catch (error) {
      console.error('保存失败:', error);
      Notification.error({
        title: '保存失败',
        content: '请检查表单填写是否正确',
        duration: 3
      });
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '配置键',
      dataIndex: 'configKey',
      key: 'configKey',
      width: 180,
    },
    {
      title: '配置名称',
      dataIndex: 'configName',
      key: 'configName',
      width: 180,
    },
    {
      title: '配置值',
      dataIndex: 'configValue',
      key: 'configValue',
      width: 200,
      render: (text: string) => (
        <div style={{ 
          maxWidth: '200px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {text}
        </div>
      )
    },
    {
      title: '配置分组',
      dataIndex: 'configGroup',
      key: 'configGroup',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 150,
      render: (text: string) => (
        <div style={{ 
          maxWidth: '150px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {text || '-'}
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (text: string) => (
        <Tag color={text === '0' ? 'green' : 'red'}>
          {text === '0' ? '正常' : '停用'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: SysConfig) => (
        <Space>
          <Button 
            type="primary" 
            theme="light" 
            onClick={() => handleEdit(record.id)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条配置吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="danger" theme="light">删除</Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '20px' }}>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <Title heading={4} style={{ margin: 0 }}>系统配置管理</Title>
          <Button 
            type={useApi ? 'primary' : 'tertiary'} 
            onClick={() => setUseApi(!useApi)}
          >
            {useApi ? '使用API' : '使用模拟数据'}
          </Button>
        </div>
        
        {/* 搜索区域 */}
        <Form ref={formRef} layout="horizontal" labelPosition="left" onSubmit={handleSearch}>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px', marginBottom: '20px' }}>
            <Form.Input
              field="configKey"
              label="配置键"
              placeholder="请输入配置键"
              style={{ width: 240 }}
            />
            <Form.Input
              field="configName"
              label="配置名称"
              placeholder="请输入配置名称"
              style={{ width: 240 }}
            />
            <Form.Select
              field="configGroup"
              label="配置分组"
              placeholder="请选择配置分组"
              style={{ width: 240 }}
              showClear
            >
              {configGroups.map(group => (
                <Option key={group} value={group}>{group}</Option>
              ))}
            </Form.Select>
            <Form.Select
              field="status"
              label="状态"
              placeholder="请选择状态"
              style={{ width: 240 }}
              showClear
            >
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>{option.label}</Option>
              ))}
            </Form.Select>
            
            <div style={{ display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
              <Button type="primary" htmlType="submit" icon={<IconSearch />}>搜索</Button>
              <Button icon={<IconRefresh />} onClick={handleReset}>重置</Button>
            </div>
          </div>
        </Form>
        
        {/* 操作按钮区域 */}
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button type="primary" icon={<IconPlus />} onClick={handleAdd}>新增</Button>
            <Popconfirm
              title="确定要删除选中的配置吗？"
              onConfirm={handleBatchDelete}
              okText="确定"
              cancelText="取消"
              disabled={selectedRowKeys.length === 0}
            >
              <Button 
                type="danger" 
                icon={<IconDelete />} 
                disabled={selectedRowKeys.length === 0}
              >
                批量删除
              </Button>
            </Popconfirm>
          </Space>
          
          <div>已选择 {selectedRowKeys.length} 项</div>
        </div>
        
        {/* 表格区域 */}
        <Table
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          rowKey="id"
          pagination={{
            currentPage: pageNo,
            pageSize: pageSize,
            total: total,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            showSizeChanger: true,
            showTotal: true
          }}
          rowSelection={{
            selectedRowKeys,
            onChange: handleSelectChange as any
          }}
        />
      </Card>
      
      {/* 编辑/新增模态框 */}
      <Modal
        title={editMode ? '编辑配置' : '新增配置'}
        visible={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        maskClosable={false}
        width={600}
        confirmLoading={loading}
      >
        <Form 
          ref={editFormRef}
          labelPosition="left"
          labelWidth={80}
          labelAlign="right"
        >
          <Form.Input
            field="configKey"
            label="配置键"
            placeholder="请输入配置键"
            rules={[
              { required: true, message: '请输入配置键' },
              { max: 100, message: '长度不能超过100个字符' }
            ]}
            disabled={editMode}
          />
          <Form.Input
            field="configName"
            label="配置名称"
            placeholder="请输入配置名称"
            rules={[
              { required: true, message: '请输入配置名称' },
              { max: 100, message: '长度不能超过100个字符' }
            ]}
          />
          <Form.Input
            field="configValue"
            label="配置值"
            placeholder="请输入配置值"
            rules={[
              { required: true, message: '请输入配置值' },
              { max: 500, message: '长度不能超过500个字符' }
            ]}
          />
          <Form.Input
            field="configGroup"
            label="配置分组"
            placeholder="请输入配置分组"
            rules={[
              { required: true, message: '请输入配置分组' },
              { max: 50, message: '长度不能超过50个字符' }
            ]}
            list="configGroupList"
          />
          <datalist id="configGroupList">
            {configGroups.map(group => (
              <option key={group} value={group} />
            ))}
          </datalist>
          <Form.TextArea
            field="remark"
            label="备注"
            placeholder="请输入备注"
            rules={[
              { max: 500, message: '长度不能超过500个字符' }
            ]}
          />
          <Form.Select
            field="status"
            label="状态"
            placeholder="请选择状态"
            rules={[
              { required: true, message: '请选择状态' }
            ]}
            initValue="0"
          >
            {statusOptions.map(option => (
              <Option key={option.value} value={option.value}>{option.label}</Option>
            ))}
          </Form.Select>
        </Form>
      </Modal>
    </div>
  );
};

export default ConfigManagement; 