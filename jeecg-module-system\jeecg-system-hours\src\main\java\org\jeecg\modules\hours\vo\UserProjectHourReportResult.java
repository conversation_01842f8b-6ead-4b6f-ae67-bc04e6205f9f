package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 人员项目工时统计报表结果
 * @Author: jeecg-boot
 * @Date: 2025-07-08
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "UserProjectHourReportResult", description = "人员项目工时统计报表结果")
public class UserProjectHourReportResult {
    
    @ApiModelProperty(value = "项目列信息")
    private List<ProjectColumnVO> projectColumns;
    
    @ApiModelProperty(value = "人员工时数据")
    private List<UserProjectHourReportVO> userHourData;
    
    @ApiModelProperty(value = "总计行数据")
    private UserProjectHourReportVO totalRow;
    
    @ApiModelProperty(value = "汇总统计信息")
    private ReportSummaryVO summary;
    
    @Data
    @Accessors(chain = true)
    @ApiModel(value = "ReportSummaryVO", description = "报表汇总信息")
    public static class ReportSummaryVO {
        
        @ApiModelProperty(value = "日期范围")
        private String dateRange;
        
        @ApiModelProperty(value = "总工时")
        private BigDecimal totalHours;
        
        @ApiModelProperty(value = "人员数量")
        private Integer userCount;
        
        @ApiModelProperty(value = "项目数量")
        private Integer projectCount;
        
        @ApiModelProperty(value = "部门数量")
        private Integer deptCount;
    }
}