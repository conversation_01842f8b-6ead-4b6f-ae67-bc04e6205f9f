package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.dto.HourRecordDTO;
import org.jeecg.modules.hours.vo.ApiResponse;
import org.jeecg.modules.hours.vo.ApprovalRequest;
import org.jeecg.modules.hours.vo.TransferRequest;
import java.util.List;

public interface ApprovalService {
    List<HourRecordDTO> listApprovals();
    ApiResponse<Boolean> approve(ApprovalRequest req);
    ApiResponse<Boolean> transferApprover(TransferRequest req);
} 