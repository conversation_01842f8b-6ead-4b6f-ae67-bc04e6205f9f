package org.jeecg.modules.plm.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.plm.entity.PlmSyncLog;

/**
 * PLM同步日志Service接口
 */
public interface IPlmSyncLogService extends IService<PlmSyncLog> {

    /**
     * 分页查询同步日志
     *
     * @param page     分页参数
     * @param syncType 同步类型
     * @param status   同步状态
     * @return 同步日志列表
     */
    IPage<PlmSyncLog> querySyncLogList(Page<PlmSyncLog> page, String syncType, String status);

    /**
     * 查询最近一次成功的同步记录
     *
     * @param syncType 同步类型
     * @return 同步日志
     */
    PlmSyncLog queryLastSuccessLog(String syncType);
} 