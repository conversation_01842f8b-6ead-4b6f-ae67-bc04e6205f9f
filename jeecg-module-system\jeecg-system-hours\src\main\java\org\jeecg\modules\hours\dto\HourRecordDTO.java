package org.jeecg.modules.hours.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.List;

/**
 * 工时记录DTO
 */
@Data
@ApiModel(value = "工时记录DTO", description = "工时记录DTO")
public class HourRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（用于区分新增/修改）
     */
    @ApiModelProperty(value = "主键ID", required = false)
    private String id;

    /**
     * 工作月份
     */
    @NotBlank(message = "工作月份不能为空")
    @ApiModelProperty(value = "工作月份", required = true)
    private String workMonth;

    /**
     * 开始日期
     */
    @NotBlank(message = "开始日期不能为空")
    @ApiModelProperty(value = "开始日期", required = true)
    private String startDate;

    /**
     * 结束日期
     */
    @NotBlank(message = "结束日期不能为空")
    @ApiModelProperty(value = "结束日期", required = true)
    private String endDate;

    /**
     * 项目工时列表
     */
    @NotEmpty(message = "项目工时列表不能为空")
    @ApiModelProperty(value = "项目工时列表", required = true)
    private List<ProjectDTO> projects;
    
    @NotEmpty(message = "审核状态")
    @ApiModelProperty(value = "审核状态", required = false)
    private Integer status;

    @NotEmpty(message = "描述")
    @ApiModelProperty(value = "描述", required = false)
    private String description;
    
    /**
     * 项目工时DTO
     */
    @Data
    @ApiModel(value = "项目工时DTO", description = "项目工时DTO")
    public static class ProjectDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 项目ID
         */
        @NotBlank(message = "项目ID不能为空")
        @ApiModelProperty(value = "项目ID", required = true)
        private String projectId;

        /**
         * 项目名称
         */
        @NotBlank(message = "项目名称不能为空")
        @ApiModelProperty(value = "项目名称", required = true)
        private String projectName;

        /**
         * 工作描述
         */
        @ApiModelProperty(value = "工作描述")
        private String description;

        /**
         * 日期百分比列表
         */
        @NotEmpty(message = "日期百分比列表不能为空")
        @ApiModelProperty(value = "日期百分比列表", required = true)
        private List<DatePercentageDTO> details;
    }

    /**
     * 日期百分比DTO
     */
    @Data
    @ApiModel(value = "日期百分比DTO", description = "日期百分比DTO")
    public static class DatePercentageDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 日期
         */
        @NotBlank(message = "日期不能为空")
        @ApiModelProperty(value = "日期", required = true)
        private String date;

        /**
         * 百分比
         */
        @NotNull(message = "百分比不能为空")
        @ApiModelProperty(value = "百分比", required = true)
        private Double percentage;
        
        /**
         * 是否休息：0-否；1-是
         */
        @Excel(name = "是否休息：0-否；1-是", width = 15)
        @ApiModelProperty(value = "是否休息：0-否；1-是")
        private Integer isRest;
        
    }
} 