package org.jeecg.modules.hours.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.plugin.builder.RefreshTokenReq;
import com.lark.project.service.plugin.builder.RefreshTokenResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 飞书认证服务实现
 * @Author: jeecg-boot
 * @Date: 2024-06-17
 * @Version: V1.0
 */
@Service
@Slf4j
public class FeishuAuthServiceImpl implements IFeishuAuthService {
    
    // 默认用户Key
//    private static final String DEFAULT_USER_KEY = "7347539727127461916";
    
    // Redis缓存key
    private static final String REDIS_USER_KEY = "feishu:auth:userKey";
    private static final String REDIS_TOKEN_KEY = "feishu:auth:token";
    private static final String REDIS_REFRESH_TOKEN_KEY = "feishu:auth:refreshToken";
    
    // MeegoAuthController中使用的key - 兼容原有实现
    private static final String MEEGO_USER_KEY = "MEEGO_USER";
    // JSON格式的token信息缓存key
    private static final String MEEGO_TOKEN_JSON_KEY = "feishu:auth:token:json";
    
    @Value("${feishu.project.plugin.id}")
    private String pluginId;
    
    @Value("${feishu.project.plugin.secret}")
    private String pluginSecret;
    
    @Value("${feishu.api.base-url:https://project.feishu.cn/}")
    private String feishuApiBaseUrl;
    
    @Autowired(required = false)
    private RedisUtil redisUtil;

    @Override
    public Map<String, String> getAuthInfo() {
        Map<String, String> authInfo = new HashMap<>();
        
        // 获取或刷新token
        String token = getAccessToken();
        String userKey = getCurrentUserKey();
        
        authInfo.put("token", token);
        authInfo.put("userKey", userKey);
        
        log.debug("获取飞书认证信息: token={}, userKey={}", maskToken(token), userKey);
        return authInfo;
    }

    @Override
    public String getAccessToken() {
        if (redisUtil == null) {
            log.warn("Redis工具类未注入，无法获取token");
            return "";
        }
        
        String token = "";
        
        // 1. 首先尝试从Redis缓存中获取token
        if (redisUtil.hasKey(REDIS_TOKEN_KEY)) {
            token = (String) redisUtil.get(REDIS_TOKEN_KEY);
            log.debug("从Redis缓存获取飞书token: {}", maskToken(token));
            
            // 检查token是否即将过期（小于10分钟），如果是则刷新
            Long expireTime = redisUtil.getExpire(REDIS_TOKEN_KEY);
            if (expireTime != null && expireTime < 600) {
                log.info("飞书token即将过期，剩余时间{}秒，尝试刷新", expireTime);
                token = refreshToken();
            }
            
            if (token != null && !token.isEmpty()) {
                return token;
            }
        }
        
        // 2. 检查JSON格式的token信息缓存
        if (redisUtil.hasKey(MEEGO_TOKEN_JSON_KEY)) {
            try {
                String tokenJson = (String) redisUtil.get(MEEGO_TOKEN_JSON_KEY);
                if (tokenJson != null && !tokenJson.isEmpty()) {
                    // 解析JSON字符串
                    Map<String, Object> tokenMap = JSON.parseObject(tokenJson, Map.class);
                    token = (String) tokenMap.get("token");
                    String refreshToken = (String) tokenMap.get("refreshToken");
                    Integer expireTime = (Integer) tokenMap.get("expireTime");
                    
                    // 保存到单独的缓存中
                    if (token != null && !token.isEmpty()) {
                        redisUtil.set(REDIS_TOKEN_KEY, token, expireTime != null ? expireTime : 7200);
                        if (refreshToken != null && !refreshToken.isEmpty()) {
                            redisUtil.set(REDIS_REFRESH_TOKEN_KEY, refreshToken, 30 * 24 * 60 * 60); // 30天
                        }
                        log.info("从JSON缓存获取并保存token信息成功");
                        return token;
                    }
                }
            } catch (Exception e) {
                log.error("解析token JSON信息失败", e);
            }
        }
        
        // 3. 尝试使用刷新令牌获取新的token
        if (redisUtil.hasKey(REDIS_REFRESH_TOKEN_KEY)) {
            log.info("Redis中不存在有效的飞书token，但存在refreshToken，尝试刷新");
            token = refreshToken();
            if (token != null && !token.isEmpty()) {
                return token;
            }
        }
        
        // 4. 检查是否有用户标识，并提示需要重新授权
        if (redisUtil.hasKey(MEEGO_USER_KEY)) {
            String userKey = (String) redisUtil.get(MEEGO_USER_KEY);
            if (userKey != null && !userKey.isEmpty()) {
                log.info("检测到MeegoAuth中存在用户标识({}), 但无有效token，需用户重新授权", userKey);
                // 保存用户标识到自己的缓存
                redisUtil.set(REDIS_USER_KEY, userKey);
            }
        }
        
        log.warn("无法获取有效的飞书token，请用户通过MeegoAuthController进行授权");
        return "";
    }

    @Override
    public String getCurrentUserKey() {
        // 从请求头获取token
        String token = RequestHeaderUtil.getTokenFromHeader();
        if (token == null || token.isEmpty()) {
            log.warn("未从请求头获取到token");
            return null;
        }
        
        // 从token中解析用户标识
        return parseUserKeyFromToken(token);
    }

    /**
     * 刷新飞书访问令牌
     * @return 新的访问令牌
     */
    private String refreshToken() {
        if (redisUtil == null || !redisUtil.hasKey(REDIS_REFRESH_TOKEN_KEY)) {
            log.warn("Redis中不存在刷新令牌，无法刷新token");
            return "";
        }
        
        String refreshToken = (String) redisUtil.get(REDIS_REFRESH_TOKEN_KEY);
        if (refreshToken == null || refreshToken.isEmpty()) {
            log.warn("刷新令牌为空，无法刷新token");
            return "";
        }
        
        try {
            log.info("开始刷新飞书token，refreshToken: {}", maskToken(refreshToken));
            
            // 初始化飞书项目SDK配置
            Client client = Client.newBuilder(pluginId, pluginSecret)
                    .openBaseUrl(feishuApiBaseUrl)
                    .build();
            
            RefreshTokenReq req = RefreshTokenReq.newBuilder()
                    .refreshToken(refreshToken)
                    .build();
            
            RefreshTokenResp response = client.getPluginService()
                    .refreshToken(req, RequestOptions.newBuilder().build());
            if(null!=response.getErr() && response.getErr().getCode()!=0) {
    			log.error("errCode={},errMsg={}",response.getErr().getCode(),response.getErr().getMsg());
    		}
            if (response != null && response.getData() != null && response.getData().getToken() != null) {
                // 提取响应数据
                String newToken = response.getData().getToken();
                String newRefreshToken = response.getData().getRefreshToken();
                Integer expireTime = response.getData().getExpireTime();
                Integer refreshExpireTime = response.getData().getRefreshTokenExpireTime();
                
                log.info("刷新token成功，过期时间: {}秒", expireTime);
                
                // 保存新的token到Redis
                if (expireTime != null) {
                    redisUtil.set(REDIS_TOKEN_KEY, newToken, expireTime - 60);  // 提前60秒过期
                } else {
                    redisUtil.set(REDIS_TOKEN_KEY, newToken);
                }
                
                // 保存新的刷新令牌到Redis
                if (refreshExpireTime != null) {
                    redisUtil.set(REDIS_REFRESH_TOKEN_KEY, newRefreshToken, refreshExpireTime - 60);
                } else {
                    redisUtil.set(REDIS_REFRESH_TOKEN_KEY, newRefreshToken);
                }
                
                // 保存完整token信息到JSON格式
                try {
                    Map<String, Object> tokenMap = new HashMap<>();
                    tokenMap.put("token", newToken);
                    tokenMap.put("refreshToken", newRefreshToken);
                    tokenMap.put("expireTime", expireTime);
                    tokenMap.put("refreshTokenExpireTime", refreshExpireTime);
                    tokenMap.put("userKey", getCurrentUserKey());
                    
                    // 将Map转换为JSON字符串存储
                    String tokenJson = JSON.toJSONString(tokenMap);
                    redisUtil.set(MEEGO_TOKEN_JSON_KEY, tokenJson, expireTime - 60);
                    log.info("更新token JSON缓存成功");
                } catch (Exception e) {
                    log.error("更新token JSON缓存失败", e);
                }
                
                return newToken;
            } else {
                log.error("刷新token失败，响应数据为空");
                return "";
            }
        } catch (Exception e) {
            log.error("刷新token异常", e);
            return "";
        }
    }
    
    /**
     * 手动设置token信息，通常由MeegoAuthController调用
     */
    @Override
    public void setAuthInfo(String token, String refreshToken, Integer expireTime,
                          Integer refreshExpireTime, String userKey) {
        if (redisUtil == null) {
            log.warn("Redis工具未初始化，无法设置token信息");
            return;
        }
        
        log.info("手动设置飞书认证信息: userKey={}, tokenLength={}", 
                userKey, token == null ? 0 : token.length());
        
        try {
            // 设置token
            if (token != null && !token.isEmpty()) {
                if (expireTime != null) {
                    redisUtil.set(REDIS_TOKEN_KEY, token, expireTime - 60);
                } else {
                    redisUtil.set(REDIS_TOKEN_KEY, token);
                }
            }
            
            // 设置refresh token
            if (refreshToken != null && !refreshToken.isEmpty()) {
                if (refreshExpireTime != null) {
                    redisUtil.set(REDIS_REFRESH_TOKEN_KEY, refreshToken, refreshExpireTime - 60);
                } else {
                    redisUtil.set(REDIS_REFRESH_TOKEN_KEY, refreshToken);
                }
            }
            
            // 设置用户标识
            if (userKey != null && !userKey.isEmpty()) {
                redisUtil.set(REDIS_USER_KEY, userKey);
            }
        } catch (Exception e) {
            log.error("设置飞书认证信息失败", e);
        }
    }
    
    /**
     * 将token部分字符替换为*，用于日志打印
     */
    private String maskToken(String token) {
        if (token == null || token.length() <= 10) {
            return "***";
        }
        return token.substring(0, 5) + "..." + token.substring(token.length() - 5);
    }

    @Override
    public String parseUserKeyFromToken(String token) {
        if (token == null || token.isEmpty()) {
            log.warn("token为空，无法解析用户标识");
            return null;
        }
        
        try {
            // 从JWT token中解析用户标识
            String userKey = JwtUtil.getUsername(token);
            if (userKey == null || userKey.isEmpty()) {
                log.warn("从token中解析用户标识失败");
                return null;
            }
            
            return userKey;
        } catch (Exception e) {
            log.error("解析token中的用户标识异常", e);
            return null;
        }
    }
    
    @Override
    public boolean refreshToken(String refreshToken) {
        if (refreshToken == null || refreshToken.isEmpty()) {
            log.warn("刷新令牌为空，无法刷新token");
            return false;
        }
        
        try {
            log.info("开始刷新飞书token，refreshToken: {}", maskToken(refreshToken));
            
            // 初始化飞书项目SDK配置
            Client client = Client.newBuilder(pluginId, pluginSecret)
                    .openBaseUrl(feishuApiBaseUrl)
                    .build();
            
            RefreshTokenReq req = RefreshTokenReq.newBuilder()
                    .refreshToken(refreshToken)
                    .build();
            
            RefreshTokenResp response = client.getPluginService()
                    .refreshToken(req, RequestOptions.newBuilder().build());
            if(null!=response.getErr() && response.getErr().getCode()!=0) {
				log.error("errCode={},errMsg={}",response.getErr().getCode(),response.getErr().getMsg());
			}
            
            if (response != null && response.getData() != null && response.getData().getToken() != null) {
                // 提取响应数据
                String newToken = response.getData().getToken();
                String newRefreshToken = response.getData().getRefreshToken();
                Integer expireTime = response.getData().getExpireTime();
                Integer refreshExpireTime = response.getData().getRefreshTokenExpireTime();
                
                // 保存新的认证信息
                setAuthInfo(newToken, newRefreshToken, expireTime, refreshExpireTime, getCurrentUserKey());
                
                log.info("刷新token成功");
                return true;
            } else {
                log.error("刷新token失败，响应数据为空");
                return false;
            }
        } catch (Exception e) {
            log.error("刷新token异常", e);
            return false;
        }
    }
} 