package org.jeecg.modules.hours.util;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.core.env.Environment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 请求头工具类
 * 用于从HTTP请求头中获取常用信息
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RequestHeaderUtil {
    
    /**
     * 默认开发环境空间ID
     */
    private static final String DEV_SPACE_ID = "dev_space";
    
    /**
     * 飞书空间ID的标准请求头名称
     */
    public static final String SPACE_ID_HEADER = "X-FEISHU-SPACE-ID";
    
    /**
     * token的标准请求头名称
     */
    public static final String TOKEN_HEADER = "x-hour-token";
    
    /**
     * Spring环境
     */
    private static Environment environment;
    
    @Autowired
    public void setEnvironment(Environment environment) {
        RequestHeaderUtil.environment = environment;
    }
    
    /**
     * 检查是否为开发或测试环境
     */
    private static boolean isDevOrTestEnv() {
        if (environment == null) {
            return false;
        }
        
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if (profile.contains("dev") || profile.contains("test")) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 从请求头中获取飞书空间ID
     * 
     * @return 飞书空间ID，如果未找到则返回null，在开发环境中返回默认值
     */
    public static String getSpaceIdFromHeader() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            
            // 使用标准头名称
            String spaceId = request.getHeader(SPACE_ID_HEADER);
            if (spaceId != null && !spaceId.trim().isEmpty()) {
                log.info("从请求头[{}]中获取到飞书空间ID: {}", SPACE_ID_HEADER, spaceId);
                return spaceId;
            }
            
            // 获取不到时记录所有请求头进行调试
            if (log.isDebugEnabled()) {
                String headers = getAllHeadersForDebug();
                log.debug("未从标准请求头找到飞书空间ID，当前所有请求头: {}", headers);
            }
            
            // 尝试从URL参数中获取
            String spaceIdParam = request.getParameter("spaceId");
            if (spaceIdParam != null && !spaceIdParam.trim().isEmpty()) {
                log.info("从URL参数spaceId中获取到飞书空间ID: {}", spaceIdParam);
                return spaceIdParam;
            }
        }
        
        // 在开发或测试环境中，可以使用默认值
        if (isDevOrTestEnv()) {
            log.warn("未从请求头或URL参数中找到飞书空间ID，开发环境使用默认值: {}", DEV_SPACE_ID);
            return DEV_SPACE_ID;
        }
        
        log.warn("未从请求头或URL参数中找到飞书空间ID");
        return null;
    }
    
    /**
     * 从请求头中获取token
     * 
     * @return token，如果未找到则返回null
     */
    public static String getTokenFromHeader() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return null;
        }
        
        // 从标准头中获取（不区分大小写）
        String token = request.getHeader(TOKEN_HEADER);
        if (token == null) {
            // 尝试大写形式
            token = request.getHeader(TOKEN_HEADER.toUpperCase());
        }
        if (token != null && !token.trim().isEmpty()) {
            return token.trim();
        }
        
        // 从Authorization头中获取
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7).trim();
        }
        
        log.debug("未从请求头中找到有效的token");
        return null;
    }
    
    /**
     * 获取请求中的所有头信息（用于调试）
     * 
     * @return 包含所有请求头的字符串
     */
    public static String getAllHeadersForDebug() {
        StringBuilder headers = new StringBuilder();
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            java.util.Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                headers.append(headerName).append(": ").append(request.getHeader(headerName)).append("\n");
            }
        }
        return headers.toString();
    }
    
    /**
     * 获取当前请求对象
     */
    private static HttpServletRequest getRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                return attributes.getRequest();
            }
        } catch (Exception e) {
            log.warn("获取请求对象失败", e);
        }
        return null;
    }
} 