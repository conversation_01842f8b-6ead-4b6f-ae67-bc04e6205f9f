package org.jeecg.modules.hours.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "部门工时差值统计", description = "部门工时差值统计表")
@Data
@Accessors(chain = true)
@TableName("dept_hour_variance_stat")
public class DeptHourVarianceStat {
    
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "部门ID")
    @Excel(name = "部门ID", width = 15)
    @TableField("dept_id")
    private String deptId;

    @ApiModelProperty(value = "部门名称")
    @Excel(name = "部门名称", width = 20)
    @TableField("dept_name")
    private String deptName;

    @ApiModelProperty(value = "统计年份")
    @Excel(name = "统计年份", width = 10)
    @TableField("stat_year")
    private Integer statYear;

    @ApiModelProperty(value = "统计月份")
    @Excel(name = "统计月份", width = 10)
    @TableField("stat_month")
    private Integer statMonth;

    @ApiModelProperty(value = "实际工时")
    @Excel(name = "实际工时", width = 12)
    @TableField("actual_hours")
    private BigDecimal actualHours;

    @ApiModelProperty(value = "可登记工时")
    @Excel(name = "可登记工时", width = 12)
    @TableField("required_hours")
    private BigDecimal requiredHours;

    @ApiModelProperty(value = "差值工时")
    @Excel(name = "差值工时", width = 12)
    @TableField("variance_hours")
    private BigDecimal varianceHours;

    @ApiModelProperty(value = "是否包含假日")
    @Excel(name = "包含假日", width = 10, replace = {"否_0", "是_1"})
    @TableField("include_holiday")
    private Boolean includeHoliday;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;
}