package org.jeecg.modules.hours.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 飞书项目空间基础信息表
 * @Author: jeecg-boot
 * @Date:   2025-05-20
 * @Version: V1.0
 */
@Data
@TableName("fs_project")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="fs_project对象", description="飞书项目空间基础信息表")
public class FsProject implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    /**
     * 空间标识
     */
    @Excel(name = "空间标识", width = 15)
    @ApiModelProperty(value = "空间标识")
    private String projectKey;
    
    /**
     * 租户ID
     */
    @Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
    
    /**
     * 空间名称
     */
    @Excel(name = "空间名称", width = 20)
    @ApiModelProperty(value = "空间名称")
    private String name;
    
    /**
     * 空间域名
     */
    @Excel(name = "空间域名", width = 20)
    @ApiModelProperty(value = "空间域名")
    private String simpleName;
    
    /**
     * 备注
     */
    @Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注")
    private String remark;
    
    /**
     * 空间状态
     */
    @Excel(name = "空间状态", width = 15)
    @ApiModelProperty(value = "空间状态")
    private String status;
    
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;
    
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

    /**
     * 是否初始化
     */
    @Excel(name = "是否初始化", width = 15)
    @ApiModelProperty(value = "是否初始化: 1-已初始化；0-未初始化")
    private Integer isInit;
} 