<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.larksuite.project</groupId>
    <artifactId>oapi-sdk</artifactId>
    <version>1.0.15</version>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
                <version>${maven-compiler-plugin.version}</version>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
                <version>${maven-resources-plugin.version}</version>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <id>attach-sources</id>
                    </execution>
                </executions>
                <version>${maven-source-plugin.version}</version>
            </plugin>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
                <groupId>org.apache.maven.plugins</groupId>
                <version>${maven-surefire-plugin.version}</version>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>
                    </archive>
                </configuration>
                <groupId>org.apache.maven.plugins</groupId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
            <plugin>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <charset>UTF-8</charset>
                    <docencoding>UTF-8</docencoding>
                    <doclint>none</doclint>
                    <encoding>UTF-8</encoding>
                    <links>
                        <link>http://docs.oracle.com/javase/8/docs/api</link>
                    </links>
                    <show>public</show>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <id>attach-javadoc</id>
                    </execution>
                </executions>
                <groupId>org.apache.maven.plugins</groupId>
                <version>${maven-javadoc-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <artifactId>gson</artifactId>
            <groupId>com.google.code.gson</groupId>
            <version>${gson.version}</version>
        </dependency>
        <dependency>
            <artifactId>slf4j-api</artifactId>
            <groupId>org.slf4j</groupId>
            <version>${slf4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpclient.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>${httpclient.version}</version>
        </dependency>
    </dependencies>
    <properties>
        <httpclient.version>4.5.14</httpclient.version>
        <gson.version>2.9.0</gson.version>
        <slf4j.version>1.7.30</slf4j.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-gpg-plugin.version>1.6</maven-gpg-plugin.version>
        <maven-jar-plugin.version>3.2.0</maven-jar-plugin.version>
        <maven-javadoc-plugin.version>3.2.0</maven-javadoc-plugin.version>
        <maven-release-plugin.version>2.5.3</maven-release-plugin.version>
        <maven-resources-plugin.version>2.6</maven-resources-plugin.version>
        <maven-source-plugin.version>3.0.1</maven-source-plugin.version>
        <maven-surefire-plugin.version>2.12.4</maven-surefire-plugin.version>
        <maven-versions-plugin.version>2.7</maven-versions-plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <distributionManagement>
        <repository>
            <id>meego</id>
            <url>https://artifacts-cn-beijing.volces.com/repository/meego/</url>
        </repository>
    </distributionManagement>
</project>