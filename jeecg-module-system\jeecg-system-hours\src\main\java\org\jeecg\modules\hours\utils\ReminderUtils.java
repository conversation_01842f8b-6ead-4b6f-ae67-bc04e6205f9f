package org.jeecg.modules.hours.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.hours.constant.ReminderConstant;
import org.jeecg.modules.hours.entity.FsSysReminderConfig;

/**
 * 登记提醒工具类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ReminderUtils {

    /**
     * 格式化提醒配置显示文本
     * 
     * @param config 提醒配置
     * @return 格式化后的文本
     */
    public static String formatReminderConfigText(FsSysReminderConfig config) {
        if (config == null) {
            return "";
        }

        StringBuilder text = new StringBuilder();
        
        // 提醒类型
        switch (config.getReminderType()) {
            case ReminderConstant.ReminderType.DAILY:
                text.append("每天");
                break;
            case ReminderConstant.ReminderType.WEEKLY:
                text.append("每周");
                break;
            case ReminderConstant.ReminderType.MONTHLY:
                text.append("每月");
                break;
            default:
                text.append(config.getReminderType());
        }
        
        text.append("提醒，");
        
        // 重复方式
        switch (config.getRepeatMode()) {
            case ReminderConstant.RepeatMode.CURRENT_DAY:
                text.append("当天");
                break;
            case ReminderConstant.RepeatMode.NEXT_DAY:
                text.append("次日");
                break;
            case ReminderConstant.RepeatMode.CURRENT_WEEK:
                text.append("本周");
                break;
            case ReminderConstant.RepeatMode.NEXT_WEEK:
                text.append("次周");
                break;
            case ReminderConstant.RepeatMode.CURRENT_MONTH:
                text.append("本月");
                break;
            case ReminderConstant.RepeatMode.NEXT_MONTH:
                text.append("次月");
                break;
            default:
                text.append(config.getRepeatMode());
        }
        
        // 提醒日期
        if (StringUtils.isNotBlank(config.getReminderDay())) {
            switch (config.getReminderDay()) {
                case ReminderConstant.WeekDay.MONDAY:
                    text.append("周一");
                    break;
                case ReminderConstant.WeekDay.TUESDAY:
                    text.append("周二");
                    break;
                case ReminderConstant.WeekDay.WEDNESDAY:
                    text.append("周三");
                    break;
                case ReminderConstant.WeekDay.THURSDAY:
                    text.append("周四");
                    break;
                case ReminderConstant.WeekDay.FRIDAY:
                    text.append("周五");
                    break;
                case ReminderConstant.WeekDay.SATURDAY:
                    text.append("周六");
                    break;
                case ReminderConstant.WeekDay.SUNDAY:
                    text.append("周日");
                    break;
                case ReminderConstant.MonthDay.LAST_DAY:
                    text.append("月末");
                    break;
                default:
                    if (config.getReminderType().equals(ReminderConstant.ReminderType.MONTHLY)) {
                        text.append(config.getReminderDay()).append("号");
                    } else {
                        text.append(config.getReminderDay());
                    }
            }
        }
        
        text.append(" ").append(config.getReminderTime());
        
        return text.toString();
    }

    /**
     * 生成提醒消息内容
     * 
     * @param config 提醒配置
     * @return 提醒消息
     */
    public static String generateReminderMessage(FsSysReminderConfig config) {
        if (config == null) {
            return "";
        }

        String message;
        
        // 如果有自定义备注，使用自定义消息
        if (StringUtils.isNotBlank(config.getRemark())) {
            message = ReminderConstant.MessageTemplate.CUSTOM_REMINDER_TEMPLATE
                    .replace("{remark}", config.getRemark());
        } else {
            // 根据提醒类型使用默认消息模板
            switch (config.getReminderType()) {
                case ReminderConstant.ReminderType.DAILY:
                    message = ReminderConstant.MessageTemplate.DAILY_REMINDER_TEMPLATE;
                    break;
                case ReminderConstant.ReminderType.WEEKLY:
                    message = ReminderConstant.MessageTemplate.WEEKLY_REMINDER_TEMPLATE;
                    break;
                case ReminderConstant.ReminderType.MONTHLY:
                    message = ReminderConstant.MessageTemplate.MONTHLY_REMINDER_TEMPLATE;
                    break;
                default:
                    message = "请及时完成工时登记。";
            }
        }
        
        return message;
    }

    /**
     * 检查提醒配置是否应该在当前时间执行
     * 
     * @param config 提醒配置
     * @return 是否应该执行
     */
    public static boolean shouldExecuteReminder(FsSysReminderConfig config) {
        if (config == null || !ReminderConstant.Status.ENABLED.equals(config.getStatus())) {
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        String currentTime = now.format(DateTimeFormatter.ofPattern(ReminderConstant.TimeFormat.TIME_PATTERN));
        
        // 检查时间是否匹配
        if (!currentTime.equals(config.getReminderTime())) {
            return false;
        }

        // 根据提醒类型检查日期条件
        switch (config.getReminderType()) {
            case ReminderConstant.ReminderType.DAILY:
                return true; // 每天都执行
                
            case ReminderConstant.ReminderType.WEEKLY:
                return checkWeeklyCondition(config, now);
                
            case ReminderConstant.ReminderType.MONTHLY:
                return checkMonthlyCondition(config, now);
                
            default:
                return false;
        }
    }

    /**
     * 检查每周提醒条件
     */
    private static boolean checkWeeklyCondition(FsSysReminderConfig config, LocalDateTime now) {
        if (StringUtils.isBlank(config.getReminderDay())) {
            return false;
        }

        int currentDayOfWeek = now.getDayOfWeek().getValue(); // 1=Monday, 7=Sunday
        
        switch (config.getReminderDay()) {
            case ReminderConstant.WeekDay.MONDAY:
                return currentDayOfWeek == 1;
            case ReminderConstant.WeekDay.TUESDAY:
                return currentDayOfWeek == 2;
            case ReminderConstant.WeekDay.WEDNESDAY:
                return currentDayOfWeek == 3;
            case ReminderConstant.WeekDay.THURSDAY:
                return currentDayOfWeek == 4;
            case ReminderConstant.WeekDay.FRIDAY:
                return currentDayOfWeek == 5;
            case ReminderConstant.WeekDay.SATURDAY:
                return currentDayOfWeek == 6;
            case ReminderConstant.WeekDay.SUNDAY:
                return currentDayOfWeek == 7;
            default:
                return false;
        }
    }

    /**
     * 检查每月提醒条件
     */
    private static boolean checkMonthlyCondition(FsSysReminderConfig config, LocalDateTime now) {
        if (StringUtils.isBlank(config.getReminderDay())) {
            return false;
        }

        if (ReminderConstant.MonthDay.LAST_DAY.equals(config.getReminderDay())) {
            // 检查是否为月末
            return now.toLocalDate().equals(
                now.toLocalDate().withDayOfMonth(now.toLocalDate().lengthOfMonth())
            );
        } else {
            try {
                int targetDay = Integer.parseInt(config.getReminderDay());
                return now.getDayOfMonth() == targetDay;
            } catch (NumberFormatException e) {
                return false;
            }
        }
    }

    /**
     * 获取提醒类型选项
     * 
     * @return 提醒类型选项映射
     */
    public static Map<String, String> getReminderTypeOptions() {
        Map<String, String> options = new HashMap<>();
        options.put(ReminderConstant.ReminderType.DAILY, "每天");
        options.put(ReminderConstant.ReminderType.WEEKLY, "每周");
        options.put(ReminderConstant.ReminderType.MONTHLY, "每月");
        return options;
    }

    /**
     * 获取重复方式选项
     * 
     * @param reminderType 提醒类型
     * @return 重复方式选项映射
     */
    public static Map<String, String> getRepeatModeOptions(String reminderType) {
        Map<String, String> options = new HashMap<>();
        
        if (StringUtils.isBlank(reminderType)) {
            return options;
        }
        
        switch (reminderType) {
            case ReminderConstant.ReminderType.DAILY:
                options.put(ReminderConstant.RepeatMode.CURRENT_DAY, "当天");
                options.put(ReminderConstant.RepeatMode.NEXT_DAY, "次日");
                break;
            case ReminderConstant.ReminderType.WEEKLY:
                options.put(ReminderConstant.RepeatMode.CURRENT_WEEK, "本周");
                options.put(ReminderConstant.RepeatMode.NEXT_WEEK, "次周");
                break;
            case ReminderConstant.ReminderType.MONTHLY:
                options.put(ReminderConstant.RepeatMode.CURRENT_MONTH, "本月");
                options.put(ReminderConstant.RepeatMode.NEXT_MONTH, "次月");
                break;
        }
        
        return options;
    }

    /**
     * 获取星期选项
     * 
     * @return 星期选项映射
     */
    public static Map<String, String> getWeekDayOptions() {
        Map<String, String> options = new HashMap<>();
        options.put(ReminderConstant.WeekDay.MONDAY, "周一");
        options.put(ReminderConstant.WeekDay.TUESDAY, "周二");
        options.put(ReminderConstant.WeekDay.WEDNESDAY, "周三");
        options.put(ReminderConstant.WeekDay.THURSDAY, "周四");
        options.put(ReminderConstant.WeekDay.FRIDAY, "周五");
        options.put(ReminderConstant.WeekDay.SATURDAY, "周六");
        options.put(ReminderConstant.WeekDay.SUNDAY, "周日");
        return options;
    }

    /**
     * 获取月份日期选项
     * 
     * @return 月份日期选项映射
     */
    public static Map<String, String> getMonthDayOptions() {
        Map<String, String> options = new HashMap<>();
        
        // 1-31号
        for (int i = 1; i <= 31; i++) {
            options.put(String.valueOf(i), i + "号");
        }
        
        // 月末
        options.put(ReminderConstant.MonthDay.LAST_DAY, "月末");
        
        return options;
    }

    private ReminderUtils() {
        // 私有构造函数，防止实例化
    }
}
