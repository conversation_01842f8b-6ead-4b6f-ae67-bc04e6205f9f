package org.jeecg.modules.hours.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 人员工时差值统计
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Data
@TableName("user_hour_variance_stat")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="user_hour_variance_stat对象", description="人员工时差值统计")
public class UserHourVarianceStat implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    @Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;

    @Excel(name = "用户姓名", width = 15)
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @Excel(name = "工号", width = 15)
    @ApiModelProperty(value = "工号")
    private String userCode;

    @Excel(name = "部门ID", width = 15)
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    @Excel(name = "部门名称", width = 15)
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @Excel(name = "统计年份", width = 15)
    @ApiModelProperty(value = "统计年份")
    private Integer statYear;

    @Excel(name = "统计月份", width = 15)
    @ApiModelProperty(value = "统计月份")
    private Integer statMonth;

    @Excel(name = "实际工时", width = 15)
    @ApiModelProperty(value = "实际工时")
    private BigDecimal actualHours;

    @Excel(name = "可登记工时", width = 15)
    @ApiModelProperty(value = "可登记工时")
    private BigDecimal requiredHours;

    @Excel(name = "差值工时", width = 15)
    @ApiModelProperty(value = "差值工时(可登记-实际)")
    private BigDecimal varianceHours;

    @Excel(name = "是否包含假日", width = 15, replace = {"不包含_0", "包含_1"})
    @ApiModelProperty(value = "是否包含假日(0:不包含, 1:包含)")
    private Boolean includeHoliday;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
}