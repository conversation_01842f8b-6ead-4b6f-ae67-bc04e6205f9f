package org.jeecg.modules.hours.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.hours.service.IUserHourVarianceStatService;
import org.jeecg.modules.hours.vo.UserHourVarianceReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "人员工时差值分析")
@RestController
@RequestMapping("/hapi/api/user-variance")
@Slf4j
public class UserHourVarianceController {

    @Autowired
    private IUserHourVarianceStatService userHourVarianceStatService;

    @ApiOperation(value = "查询人员工时差值报表", notes = "按年度查询人员工时差值分析数据")
    @GetMapping("/report")
    public Result<List<UserHourVarianceReportVO>> getVarianceReport(
            @ApiParam(value = "统计年份", required = true) @RequestParam int year,
            @ApiParam(value = "用户ID列表，多个用逗号分隔") @RequestParam(required = false) String userIds,
            @ApiParam(value = "部门ID列表，多个用逗号分隔") @RequestParam(required = false) String deptIds,
            @ApiParam(value = "是否包含假日", defaultValue = "false") @RequestParam(defaultValue = "false") boolean includeHoliday) {
        
        try {
            List<String> userIdList = null;
            if (userIds != null && !userIds.trim().isEmpty()) {
                userIdList = Arrays.stream(userIds.split(",")).collect(Collectors.toList());
            }
            
            List<String> deptIdList = null;
            if (deptIds != null && !deptIds.trim().isEmpty()) {
                deptIdList = Arrays.stream(deptIds.split(",")).collect(Collectors.toList());
            }
            
            List<UserHourVarianceReportVO> reportData = 
                userHourVarianceStatService.getVarianceReport(year, userIdList, deptIdList, includeHoliday);
            
            return Result.ok(reportData);
            
        } catch (Exception e) {
            log.error("查询人员工时差值报表失败", e);
            return Result.error("查询报表失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "导出人员工时差值报表", notes = "导出Excel格式的人员工时差值分析报表")
    @GetMapping("/export")
    public void exportVarianceReport(
            @ApiParam(value = "统计年份", required = true) @RequestParam int year,
            @ApiParam(value = "用户ID列表，多个用逗号分隔") @RequestParam(required = false) String userIds,
            @ApiParam(value = "部门ID列表，多个用逗号分隔") @RequestParam(required = false) String deptIds,
            @ApiParam(value = "是否包含假日", defaultValue = "false") @RequestParam(defaultValue = "false") boolean includeHoliday,
            HttpServletResponse response) {
        
        try {
            List<String> userIdList = null;
            if (userIds != null && !userIds.trim().isEmpty()) {
                userIdList = Arrays.stream(userIds.split(",")).collect(Collectors.toList());
            }
            
            List<String> deptIdList = null;
            if (deptIds != null && !deptIds.trim().isEmpty()) {
                deptIdList = Arrays.stream(deptIds.split(",")).collect(Collectors.toList());
            }
            
            userHourVarianceStatService.exportVarianceReport(year, userIdList, deptIdList, includeHoliday, response);
            
        } catch (Exception e) {
            log.error("导出人员工时差值报表失败", e);
            throw new RuntimeException("导出报表失败: " + e.getMessage(), e);
        }
    }

    @ApiOperation(value = "获取年份列表", notes = "获取可选择的统计年份列表")
    @GetMapping("/years")
    public Result<List<Integer>> getAvailableYears() {
        try {
            int currentYear = LocalDate.now().getYear();
            List<Integer> years = new ArrayList<>(Arrays.asList(
                    currentYear - 2,
                    currentYear - 1,
                    currentYear,
                    currentYear + 1
            ));

            return Result.ok(years);
            
        } catch (Exception e) {
            log.error("获取年份列表失败", e);
            return Result.error("获取年份列表失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "手动执行统计任务", notes = "手动触发指定年月的人员工时差值统计计算")
    @PostMapping("/manual-stat")
    public Result<String> executeManualStat(
            @ApiParam(value = "统计年份", required = true) @RequestParam int year,
            @ApiParam(value = "统计月份", required = true) @RequestParam int month) {
        
        try {
            int recordCount = userHourVarianceStatService.calculateMonthlyVarianceStat(year, month);
            return Result.ok("统计任务执行成功，生成" + recordCount + "条记录");
            
        } catch (Exception e) {
            log.error("手动执行统计任务失败", e);
            return Result.error("统计任务执行失败：" + e.getMessage());
        }
    }
}