package org.jeecg.modules.hapi.filter;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.service.IFsUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 飞书工时项目API授权过滤器
 * 
 * @date 2024/07/01
 */
@Slf4j
@Component
public class ApiMeegoAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private RedisUtil redisUtil;
    
    @Autowired
    private IFsUserService fsUserService;
    
    /**
     * 飞书token请求头名称
     */
    private static final String X_HOUR_TOKEN = "x-hour-token";
    
    // Redis缓存key前缀
    private static final String TOKEN_CACHE_PREFIX = "meego:login:token:";
    
    // 不需要鉴权的API路径
    private static final List<String> AUTH_WHITELIST = new ArrayList<String>() {
        private static final long serialVersionUID = 1L;
        {
            // 授权相关API
            this.add("/hapi/plugin/auth/login");
            this.add("/hapi/plugin/auth/token");
            this.add("/hapi/plugin/auth/refresh");
            this.add("/hapi/plugin/auth/verify");
            
            // 插件回调API
            this.add("/hapi/hours/plugin/webhook");
            this.add("/hapi/hours/plugin/monitor");
        }
    };
    
    /**
     * 判断请求URL是否在白名单中
     * @param path 请求路径
     * @return 是否在白名单中
     */
    private boolean isWhiteListUrl(String path) {
        // 精确匹配
        if (AUTH_WHITELIST.contains(path)) {
            return true;
        }
        
        // 模糊匹配
        for (String whitePath : AUTH_WHITELIST) {
            if (path.contains(whitePath)) {
                return true;
            }
        }
        
        return false;
    }
    
    public static void main(String[] args) {
		
//    	String str = "/jeecg-boot//hapi/plugin/auth/login";
    	
    	String str = "/jeecg-boot/hapi/plugin/auth/login";
    	
    	log.info("res={}",new ApiMeegoAuthInterceptor().isWhiteListUrl(str));
    	
	}
    
    /**
     * 从token中提取用户ID
     * @param token JWT token
     * @return 用户ID
     */
    private String extractUserIdFromToken(String token) {
        try {
            // 使用JwtUtil工具类解析token
            return org.jeecg.common.system.util.JwtUtil.getUsername(token);
        } catch (Exception e) {
            log.error("解析token失败", e);
            return null;
        }
    }
    
    /**
     * 验证用户是否存在
     * @param userId 用户ID
     * @return 用户信息
     */
    private FsUser validateUser(String userId) {
        if (oConvertUtils.isEmpty(userId)) {
            return null;
        }
        
        try {
            LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUser::getUserKey, userId);
            queryWrapper.eq(FsUser::getStatus, "1"); // 正常状态
            
            return fsUserService.getOne(queryWrapper);
        } catch (Exception e) {
            log.error("查询用户信息失败", e);
            return null;
        }
    }
    
    /**
     * 处理未授权的响应
     * @param response HttpServletResponse
     * @param message 错误消息
     */
    private void handleUnauthorized(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        
        Result<?> result = Result.error(HttpStatus.UNAUTHORIZED.value(), message);
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(result));
        writer.flush();
    }

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		
		log.info("==================================================");
        log.info("请求URI: {}", request.getRequestURI());
        log.info("请求方法: {}", request.getMethod());
        log.info("请求URL: {}", request.getRequestURL());
        log.info("上下文路径: {}", request.getContextPath());
        
        // 记录所有请求头
        StringBuilder headers = new StringBuilder();
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            headers.append(name).append(": ").append(request.getHeader(name)).append(", ");
        }
        log.info("请求头: {}", headers.toString());
        
        // 获取请求路径
        String requestURI = request.getRequestURI();

        // 判断是否在白名单中
        if (isWhiteListUrl(requestURI)) {
            log.info("白名单路径: {}, 跳过验证", requestURI);
            return true;
        }
        
        // 获取token
        String token = request.getHeader(X_HOUR_TOKEN);
        log.info("接收到X-HOUR-TOKEN: {}", token != null ? token.substring(0, 10) + "..." : "null");
        
        // 检查token是否存在
        if (StringUtils.isBlank(token)) {
            log.warn("未提供token，拒绝访问");
            handleUnauthorized(response, "未授权，请重新授权");
            return false;
        }
        
        try {
            // 从token中提取用户ID
            String userId = extractUserIdFromToken(token);
            if (StringUtils.isBlank(userId)) {
                log.warn("无法从token中提取用户ID");
                handleUnauthorized(response, "无效的token，请重新授权");
                return false;
            }
            
            log.info("从token中提取的用户ID: {}", userId);
            
            // 检查token是否存在于Redis中
            String cacheKey = TOKEN_CACHE_PREFIX + userId;
            if (!redisUtil.hasKey(cacheKey)) {
                log.warn("Redis中不存在token: {}", cacheKey);
                handleUnauthorized(response, "token已失效，请重新授权");
                return false;
            }
            
            // 验证Redis中的token与请求中的token是否一致
            String cachedToken = (String) redisUtil.get(cacheKey);
            if (!token.equals(cachedToken)) {
                log.warn("token不匹配 - 请求: {}, 缓存: {}", token.substring(0, 10), cachedToken.substring(0, 10));
                handleUnauthorized(response, "token不匹配，请重新授权");
                return false;
            }
            
            // 验证用户是否存在
            FsUser user = validateUser(userId);
            if (user == null) {
                log.warn("找不到用户: {}", userId);
                handleUnauthorized(response, "用户不存在，请重新授权");
                return false;
            }
            
            log.info("用户 {} 验证通过", user.getName());
            
            // 刷新token过期时间
            redisUtil.expire(cacheKey, 24 * 60 * 60);
            
            // 验证通过，继续执行请求
//            filterChain.doFilter(request, response);
            
            return true;
            
        } catch (Exception e) {
            log.error("Token验证异常", e);
            handleUnauthorized(response, "授权验证失败，请重新授权");
        }
		
		return false;
		
//		return HandlerInterceptor.super.preHandle(request, response, handler);
	}
    
    
}
