import React, { useEffect, useState, ReactNode } from 'react';
import { Spin, Typography, Space } from '@douyinfe/semi-ui';
import { isAuthenticated, authorize, saveFeishuSpaceId, saveFeishuProjectKey } from '../../../utils/auth';
import FeishuAuth from './FeishuAuth';

interface CheckAuthProps {
  children: ReactNode;
}

// 飞书页面上下文类型扩展
interface ExtendedPageContext {
  spaceId?: string;
  projectKey?: string; // 添加projectKey字段
  [key: string]: any; // 允许额外的字段
}

/**
 * 认证检查组件
 * 用于检查用户是否已登录，如果未登录则显示授权登录按钮
 */
const CheckAuth: React.FC<CheckAuthProps> = ({ children }) => {
  const [loading, setLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(false);

  // 初始化飞书环境
  useEffect(() => {
    const initFeishuEnv = async () => {
      try {
        // 获取并保存飞书空间ID
        await saveFeishuSpaceId();
        
        // 尝试获取项目Key的方法
        const getAndSaveProjectKey = async () => {
          // 方法1: 首先尝试从飞书SDK获取
          if (window.JSSDK && window.JSSDK.page) {
            try {
              const context = await window.JSSDK.page.getContext() as ExtendedPageContext;
              if (context && context.projectKey) {
                console.log('从飞书SDK获取到项目Key:', context.projectKey);
                saveFeishuProjectKey(context.projectKey);
                return true;
              } else {
                console.log('飞书SDK未提供项目Key');
              }
            } catch (error) {
              console.warn('从飞书SDK获取项目Key失败:', error);
            }
          } else {
            console.log('飞书JSSDK不可用');
          }
          
          // 方法2: 从URL参数中获取
          try {
            const urlParams = new URLSearchParams(window.location.search);
            const projectKeyFromUrl = urlParams.get('project_key') || urlParams.get('projectKey');
            if (projectKeyFromUrl) {
              console.log('从URL参数获取到项目Key:', projectKeyFromUrl);
              saveFeishuProjectKey(projectKeyFromUrl);
              return true;
            }
          } catch (error) {
            console.warn('从URL参数获取项目Key失败:', error);
          }
          
          return false;
        };
        
        // 执行获取项目Key的逻辑
        const gotProjectKey = await getAndSaveProjectKey();
        if (!gotProjectKey) {
          console.warn('未能获取有效的项目Key，这可能会导致系统无法正确加载项目数据');
        }
      } catch (error) {
        console.error('初始化飞书环境失败:', error);
      }
    };
    
    initFeishuEnv();
  }, []);

  // 检查是否已认证
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isAuthorized = await authorize();
        setAuthenticated(isAuthorized);
      } catch (error) {
        console.error('授权检查失败:', error);
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };
    
    // 立即检查一次
    checkAuth();
    
    // 建立本地存储变更监听
    const handleStorageChange = () => {
      // 本地存储变化时重新检查授权状态
      checkAuth();
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // 处理授权成功
  const handleAuthSuccess = () => {
    setAuthenticated(true);
  };

  // 加载中状态
  if (loading) {
    return (
      <div className="auth-loading-container">
        <Spin size="large" />
        <Typography.Text>正在检查授权状态...</Typography.Text>
      </div>
    );
  }

  // 未授权状态
  if (!authenticated) {
    return (
      <div className="auth-login-container">
        <Space vertical align="center">
          <Typography.Title heading={4}>需要授权访问</Typography.Title>
          <Typography.Paragraph>请使用飞书项目账号授权登录后使用</Typography.Paragraph>
          <FeishuAuth 
            buttonText="飞书项目授权登录" 
            size="large"
            onSuccess={handleAuthSuccess}
          />
        </Space>
      </div>
    );
  }

  // 已授权，渲染子组件
  return <>{children}</>;
};

export default CheckAuth; 