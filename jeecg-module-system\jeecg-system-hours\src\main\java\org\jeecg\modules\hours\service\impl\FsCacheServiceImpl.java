package org.jeecg.modules.hours.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.modules.hours.context.AuthContext;
import org.jeecg.modules.hours.entity.FsDataTask;
import org.jeecg.modules.hours.entity.FsInstanceCache;
import org.jeecg.modules.hours.entity.FsNodeCache;
import org.jeecg.modules.hours.entity.FsTaskCache;
import org.jeecg.modules.hours.entity.FsWorkItemCache;
import org.jeecg.modules.hours.mapper.FsDataTaskMapper;
import org.jeecg.modules.hours.mapper.FsInstanceCacheMapper;
import org.jeecg.modules.hours.mapper.FsNodeCacheMapper;
import org.jeecg.modules.hours.mapper.FsTaskCacheMapper;
import org.jeecg.modules.hours.mapper.FsWorkItemCacheMapper;
import org.jeecg.modules.hours.service.IFsCacheService;
import org.jeecg.modules.hours.service.IFsDataTaskService;
import org.jeecg.modules.hours.service.IFsTaskCacheService;
import org.jeecg.modules.hours.service.feishu.FeishuApiService;
import org.jeecg.modules.hours.utils.DateFormatUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lark.project.service.field.model.FieldValuePair;
import com.lark.project.service.workitem.model.Schedule;
import com.lark.project.service.workitem.model.SubTask;
import com.lark.project.service.workitem.model.WorkItemInfo;
import com.lark.project.service.workitem.model.WorkItemTypeInfo;
import com.lark.project.service.workitem.model.WorkflowNode;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 飞书数据缓存服务实现
 * @Author: jeecg-boot
 * @Date: 2023-08-20
 * @Version: V1.0
 */
@Service
@Slf4j
public class FsCacheServiceImpl implements IFsCacheService {

	@Autowired
	private FsWorkItemCacheMapper workItemCacheMapper;
	@Autowired
	private FsInstanceCacheMapper instanceCacheMapper;
	@Autowired
	private FsNodeCacheMapper nodeCacheMapper;
	@Autowired
	private FsTaskCacheMapper taskCacheMapper;
	@Autowired
	private FsDataTaskMapper dataTaskMapper;
	@Autowired
	private IFsDataTaskService fsDataTaskService;
	@Autowired
	private IFsTaskCacheService fsTaskCacheService;
	@Autowired
	private FeishuApiService feishuApiService;

	@Override
	public List<FsWorkItemCache> getWorkItems(String projectKey) {
		LambdaQueryWrapper<FsWorkItemCache> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsWorkItemCache::getProjectKey, projectKey).orderByAsc(FsWorkItemCache::getName);
		return workItemCacheMapper.selectList(queryWrapper);
	}

	@Override
	public List<FsInstanceCache> getInstances(String projectKey, String workItemKey) {
		LambdaQueryWrapper<FsInstanceCache> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsInstanceCache::getProjectKey, projectKey).eq(FsInstanceCache::getWorkItemKey, workItemKey)
				.orderByAsc(FsInstanceCache::getName);
		return instanceCacheMapper.selectList(queryWrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean syncInstances(String projectKey, String workItemKey) {
		try {
			// 在主线程中获取认证信息
			AuthContext context = AuthContext.getContext();
			log.debug("获取飞书认证信息: token={}, userKey={}", context.getToken(), context.getUserKey());

			// 获取实例列表
			List<FsInstanceCache> instances = getMeegoInstances(projectKey, workItemKey, context.getToken(),
					context.getUserKey());
			if (instances == null || instances.isEmpty()) {
				log.info("\n项目[{}]工作项[{}]暂无实例数据\n", projectKey, workItemKey);
				// 避免批量跑批时报错,无数据需标记为正常
				return true;
			}

			// 批量保存或更新实例
			int totalCount = instances.size();
			int successCount = 0;

			for (FsInstanceCache instance : instances) {
				try {
					// 检查是否存在
					LambdaQueryWrapper<FsInstanceCache> queryWrapper = new LambdaQueryWrapper<>();
					queryWrapper.eq(FsInstanceCache::getProjectKey, projectKey)
							.eq(FsInstanceCache::getWorkItemKey, workItemKey)
							.eq(FsInstanceCache::getInstanceKey, instance.getInstanceKey());

					FsInstanceCache existInstance = instanceCacheMapper.selectOne(queryWrapper);

					if (existInstance == null) {
						// 新增
						instance.setUpdateTime(new Date());
						instanceCacheMapper.insert(instance);
					} else {
						// 更新
						existInstance.setName(instance.getName());
						existInstance.setStatus(instance.getStatus());
						existInstance.setUpdateTime(new Date());
						existInstance.setAssignee(instance.getAssignee());
						existInstance.setAssigneeName(instance.getAssigneeName());
						existInstance.setEstimateHours(instance.getEstimateHours());
						existInstance.setScheduleStartTime(instance.getScheduleStartTime()).setScheduleEndTime(instance.getScheduleEndTime());
						instanceCacheMapper.updateById(existInstance);
					}
					successCount++;
				} catch (Exception e) {
					log.error("保存实例缓存数据异常: {}", e.getMessage());
				}
			}

			log.info("同步工作空间[{}]工作项[{}]的实例数据完成, 总数: {}, 成功: {}", projectKey, workItemKey, totalCount, successCount);
			return successCount > 0;
		} catch (Exception e) {
			log.error("同步工作空间[{}]工作项[{}]实例数据异常", projectKey, workItemKey, e);
			return false;
		}
	}

	private List<FsInstanceCache> getMeegoInstances(String projectKey, String workItemKey, String token, String userKey)
			throws Exception {

		// 验证token和userKey是否有效
		if (token == null || token.isEmpty()) {
			throw new Exception("无法获取有效的访问令牌(token)，请检查飞书授权配置");
		}
		if (userKey == null || userKey.isEmpty()) {
			throw new Exception("无法获取有效的用户标识(userKey)，请检查飞书授权配置");
		}
		
		Long pageNum = 1L;
		Long pageSize = 100L;
		boolean isSubTask = "sub_task".equals(workItemKey);
		
		List<FsInstanceCache> resList = new ArrayList<>();

		do {
			// 构建分页请求
			List<com.lark.project.service.workitem.model.WorkItemInfo> instanceList = feishuApiService.getInstances(projectKey, workItemKey, token, userKey,pageNum,pageSize);
			
			if (null != instanceList && !CollectionUtils.isEmpty(instanceList)) {
				// 处理当前页的数据
				for (WorkItemInfo instance : instanceList) {
					FsInstanceCache cache = new FsInstanceCache();
					cache.setProjectKey(projectKey);
					cache.setWorkItemKey(workItemKey);
					cache.setInstanceKey(instance.getID() + "");
					cache.setName(instance.getName());
					cache.setStatus("1"); // 默认状态
					cache.setUpdateTime(new Date());
					// 评估工时
					if(this.hasKey(instance.getFields(), "points")) {
						BigDecimal estimateHours = getPoints(instance.getFields());
						cache.setEstimateHours(estimateHours);
					}
					// 排期
					if(this.hasKey(instance.getFields(), "sub_task_schedule")) {
						// 获取计划开始和结束时间
						Map<String, Long> schedule = getSchedule(instance.getFields());
						if (schedule.containsKey("start_time")) {
							cache.setScheduleStartTime(new Date(schedule.get("start_time")));
						}
						if (schedule.containsKey("end_time")) {
							cache.setScheduleEndTime(new Date(schedule.get("end_time")));
						}
					}
					// 负责人
					if(this.hasKey(instance.getFields(), "current_status_operator")) {
						// 负责人为多个
						List<String> personList = this.getOperatorList(instance.getFields());
						if(CollectionUtils.isNotEmpty(personList)) {
							cache.setAssignee(String.join(",", personList));
						}
					}
					
					resList.add(cache);
				}

				// 如果当前页数据量等于页大小，说明可能还有下一页
				if (CollectionUtils.size(instanceList) == pageSize) {
					pageNum++;
				} else {
					break;
				}
			} else {
				break;
			}
		} while (true);

		if (isSubTask) {
			// 保存任务的统计数据
			Set<String> instanceKeys = new HashSet<String>();
			List<FsDataTask> tasksToSave = new ArrayList<>();
			for (FsInstanceCache instance : resList) {
				if (instanceKeys.contains(instance.getInstanceKey())) {
					continue;
				}
				instanceKeys.add(instance.getInstanceKey());
				FsDataTask task = new FsDataTask();
				task.setId(instance.getInstanceKey()); // 使用组合键
				task.setProjectKey(projectKey);
				task.setTaskKey(instance.getInstanceKey());
				task.setTaskName(instance.getName());
				task.setStatus(instance.getStatus());
				task.setStartTime(instance.getScheduleStartTime());
				task.setEndTime(instance.getScheduleEndTime());
				task.setEstimateHours(instance.getEstimateHours());
				task.setUpdateTime(new Date());
				tasksToSave.add(task);
			}
			// 批量保存/更新任务数据
			if (!tasksToSave.isEmpty()) {
				fsDataTaskService.saveOrUpdateBatch(tasksToSave);
			}
		}
		return resList;
	}

	private BigDecimal getPoints(List<FieldValuePair> fields) {
		if (CollectionUtils.isEmpty(fields)) {
			return new BigDecimal(0);
		}
		for (FieldValuePair fieldValuePair : fields) {
			if ("points".equals(fieldValuePair.getFieldKey())) {
				if (null == fieldValuePair.getFieldValue()) {
					return new BigDecimal(0);
				}
				return new BigDecimal(fieldValuePair.getFieldValue().toString());
			}
		}
		return new BigDecimal(0);
	}
	
	private boolean hasKey(List<FieldValuePair> fields,String key) {
		if (CollectionUtils.isEmpty(fields)) {
			return false;
		}
		for (FieldValuePair fieldValuePair : fields) {
			if ("sub_task_schedule".equals(fieldValuePair.getFieldKey())) {
				return true;
			}
		}
		return false;
	}

	private Map<String, Long> getSchedule(List<FieldValuePair> fields) {
		if (CollectionUtils.isEmpty(fields)) {
			return new HashMap<String, Long>();
		}
		Map<String, Long> map = new HashMap<String, Long>();
		for (FieldValuePair fieldValuePair : fields) {
			if ("sub_task_schedule".equals(fieldValuePair.getFieldKey())) {
				Object schedule = fieldValuePair.getFieldValue();
				if (schedule != null) {
					try {
						// 将schedule转换为Map
						String startTime = BeanUtils.getProperty(schedule, "start_time");
						map.put("start_time", parseTimeStamp(startTime));
						String endTime = BeanUtils.getProperty(schedule, "end_time");
						map.put("end_time", parseTimeStamp(endTime));
					} catch (Exception e) {
						log.error("转换排期数据失败: " + e.getMessage(), e);
					}
				}
			}
		}
		return map;
	}
	
	private List<String> getOperatorList(List<FieldValuePair> fields) {
		if (CollectionUtils.isEmpty(fields)) {
			return new ArrayList<String>();
		}
		List<String> dataList = new ArrayList<String>();
		for (FieldValuePair fieldValuePair : fields) {
			if ("current_status_operator".equals(fieldValuePair.getFieldKey())) {
				Object obj = fieldValuePair.getFieldValue();
				try {
					dataList = (List<String>)obj;
				} catch (Exception e) {
					log.error("当前负责人转换失败: " + e.getMessage(), e);
				}
			}
		}
		return dataList;
	}

	private Long parseTimeStamp(String timeStamp) {
		if (timeStamp.contains(".")) {
			return Math.round(Double.parseDouble(timeStamp));
		}
		return Long.valueOf(timeStamp);
	}

	@Override
	public List<FsNodeCache> getNodes(String projectKey, String instanceKey) {
		LambdaQueryWrapper<FsNodeCache> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsNodeCache::getProjectKey, projectKey).eq(FsNodeCache::getInstanceKey, instanceKey)
				.orderByAsc(FsNodeCache::getNodeKey);
		return nodeCacheMapper.selectList(queryWrapper);
	}

	@Override
	public List<FsTaskCache> getTasks(String projectKey, String nodeKey) {
		LambdaQueryWrapper<FsTaskCache> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsTaskCache::getProjectKey, projectKey).eq(FsTaskCache::getNodeKey, nodeKey)
				.orderByAsc(FsTaskCache::getTaskName);
		return taskCacheMapper.selectList(queryWrapper);
	}

	@Override
	public List<FsInstanceCache> getAllInstances(String projectKey) {
		LambdaQueryWrapper<FsInstanceCache> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsInstanceCache::getProjectKey, projectKey).orderByAsc(FsInstanceCache::getName);
		return instanceCacheMapper.selectList(queryWrapper);
	}

	@Override
	public List<FsNodeCache> getAllNodes(String projectKey) {
		LambdaQueryWrapper<FsNodeCache> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsNodeCache::getProjectKey, projectKey).orderByAsc(FsNodeCache::getNodeKey);
		return nodeCacheMapper.selectList(queryWrapper);
	}

	@Override
	public List<FsTaskCache> getAllTasks(String projectKey) {
		LambdaQueryWrapper<FsTaskCache> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsTaskCache::getProjectKey, projectKey).orderByAsc(FsTaskCache::getTaskName);
		return taskCacheMapper.selectList(queryWrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean syncWorkItems(String projectKey) {
		try {
			// 在主线程中获取认证信息
			// 获取工作项列表
			List<FsWorkItemCache> workItems = getMeegoWorkItems(projectKey);
			if (workItems == null || workItems.isEmpty()) {
				log.error("projectKey={}暂无工作项,无需同步!", projectKey);
				return false;
			}

			// 批量保存或更新工作项
			int totalCount = workItems.size();
			int successCount = 0;

			for (FsWorkItemCache item : workItems) {
				try {
					// 检查是否存在
					LambdaQueryWrapper<FsWorkItemCache> queryWrapper = new LambdaQueryWrapper<>();
					queryWrapper.eq(FsWorkItemCache::getProjectKey, projectKey).eq(FsWorkItemCache::getItemKey,
							item.getItemKey());

					FsWorkItemCache existItem = workItemCacheMapper.selectOne(queryWrapper);

					if (existItem == null) {
						// 新增
						item.setUpdateTime(new Date());
						workItemCacheMapper.insert(item);
					} else {
						// 更新
						existItem.setName(item.getName());
						existItem.setItemType(item.getItemType());
						existItem.setStatus(item.getStatus());
						existItem.setFlowMode(item.getFlowMode());
						existItem.setUpdateTime(new Date());
						workItemCacheMapper.updateById(existItem);
					}
					successCount++;
				} catch (Exception e) {
					log.error("保存工作项缓存数据异常: {}", e.getMessage());
				}
			}

			log.info("同步工作空间[{}]的工作项数据完成, 总数: {}, 成功: {}", projectKey, totalCount, successCount);
			return successCount > 0;
		} catch (Exception e) {
			log.error("同步工作空间[{}]工作项数据异常", projectKey, e);
			return false;
		}
	}

	private List<FsWorkItemCache> getMeegoWorkItems(String projectKey) throws Exception {
//		log.info("使用飞书应用凭证: pluginId={}, projectKey={}", pluginId, projectKey);
		AuthContext authContext = AuthContext.getContext();
		if (authContext == null) {
			throw new Exception("无法获取认证上下文，请确保在Web请求上下文中调用");
		}
		String token = authContext.getToken();
		String userKey = authContext.getUserKey();
		if (token == null || token.isEmpty() || userKey == null || userKey.isEmpty()) {
			throw new Exception("无法获取有效的认证信息");
		}
		List<com.lark.project.service.workitem.model.WorkItemKeyType> workItemList = feishuApiService.getWorkItems(projectKey, token, userKey);
		List<FsWorkItemCache> resList = new ArrayList<>();
		if (workItemList == null || workItemList.isEmpty()) {
			return resList;
		}
		for (com.lark.project.service.workitem.model.WorkItemKeyType workItemKeyType : workItemList) {
			FsWorkItemCache cache = new FsWorkItemCache();
			cache.setProjectKey(projectKey);
			cache.setItemKey(workItemKeyType.getTypeKey());
			cache.setName(workItemKeyType.getName());
			cache.setItemType(workItemKeyType.getAPIName());
			cache.setStatus(workItemKeyType.getIsDisable() + "");
			cache.setUpdateTime(Calendar.getInstance().getTime());
			// 获取工作项的流程模式
			try {
				String flowMode = getMeegoWorkItemDetail(projectKey, workItemKeyType.getTypeKey());
				cache.setFlowMode(flowMode);
				log.info("工作项[{}]流程模式: {}", workItemKeyType.getTypeKey(), flowMode);
			} catch (Exception e) {
				log.error(e.getMessage(),e);
				log.warn("获取工作项[{}]流程模式失败: {}", workItemKeyType.getTypeKey(), e.getMessage());
			}
						
			resList.add(cache);
		}
		return resList;
	}

	private String getMeegoWorkItemDetail(String projectKey, String workItemTypeKey) throws Exception {
		// 从配置文件中获取pluginId和pluginSecret
//		log.info("使用飞书应用凭证: pluginId={}, projectKey={}", pluginId, projectKey);

//		Client client = Client.newBuilder(pluginId, pluginSecret).openBaseUrl(feishuApiBaseUrl).build();
//
//		GetWorkItemTypeInfoByKeyReq req = GetWorkItemTypeInfoByKeyReq.newBuilder().workItemTypeKey(workItemTypeKey)
//				.projectKey(projectKey).build();

		// 从认证上下文获取token和userKey
		AuthContext authContext = AuthContext.getContext();
		if (authContext == null) {
			throw new Exception("无法获取认证上下文，请确保在Web请求上下文中调用");
		}

		String token = authContext.getToken();
		String userKey = authContext.getUserKey();

		// 验证token和userKey是否有效
		if (token == null || token.isEmpty()) {
			throw new Exception("无法获取有效的访问令牌(token)，请检查飞书授权配置");
		}

		if (userKey == null || userKey.isEmpty()) {
			throw new Exception("无法获取有效的用户标识(userKey)，请检查飞书授权配置");
		}

		log.info("调用飞书API: userKey={}, tokenLength={}", userKey, token.length());

		// 构建请求选项
//		RequestOptions options = RequestOptions.newBuilder().userKey(userKey).accessToken(token).build();

		// 调用飞书API
//		GetWorkItemTypeInfoByKeyResp resp1 = client.getWorkItemService().getWorkItemTypeInfoByKey(req, options);
//		if(null!=resp1.getErr() && resp1.getErr().getCode()!=0) {
//			log.error("errCode={},errMsg={}",resp1.getErrCode(),resp1.getErrMsg());
//			log.error("\npluginId={},pluginSecret={},userKey={},projectKey={},workItemTypeKey={}",pluginId,pluginSecret,userKey,projectKey,workItemTypeKey);
//			log.error("\ntoken={}",token);
//		}
		
		WorkItemTypeInfo info = feishuApiService.getWorkItemTypeInfo(projectKey, workItemTypeKey, token, userKey);

		// 流程模式，节点流workflow ，状态流stateflow
		if(null==info) {
			return null;
		}
		return info.getFlowMode();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean syncNodes(String projectKey, String workItemKey, String instanceKey) {
		try {
			// 在主线程中获取认证信息

			// 获取节点列表
			List<FsNodeCache> nodes = getMeegoNodes(projectKey, workItemKey, instanceKey);
			if (nodes == null || nodes.isEmpty()) {
				log.info("项目[{}]工作项[{}]实例[{}]暂无节点数据", projectKey, workItemKey, instanceKey);
				return false;
			}

			// 批量保存或更新节点
			int totalCount = nodes.size();
			int successCount = 0;
			List<FsTaskCache> taskList = new ArrayList<FsTaskCache>();

			for (FsNodeCache node : nodes) {
				try {
					// 检查是否存在
					LambdaQueryWrapper<FsNodeCache> queryWrapper = new LambdaQueryWrapper<>();
					queryWrapper.eq(FsNodeCache::getProjectKey, projectKey).eq(FsNodeCache::getWorkItemKey, workItemKey)
							.eq(FsNodeCache::getInstanceKey, instanceKey)
							.eq(FsNodeCache::getNodeKey, node.getNodeKey());

					FsNodeCache existNode = nodeCacheMapper.selectOne(queryWrapper);

					if (existNode == null) {
						// 新增
						node.setUpdateTime(new Date());
						nodeCacheMapper.insert(node);
					} else {
						// 更新
						existNode.setNodeName(node.getNodeName());
						existNode.setStatus(node.getStatus());
						existNode.setUpdateTime(new Date());
						existNode.setEstimateHours(node.getEstimateHours());
						existNode.setScheduleStartTime(node.getScheduleStartTime());
						existNode.setScheduleEndTime(node.getScheduleEndTime());
						existNode.setWorkflowId(node.getWorkflowId());
						nodeCacheMapper.updateById(existNode);
					}

					if (!CollectionUtils.isEmpty(node.getTaskList())) {
						taskList.addAll(node.getTaskList());
					}
					successCount++;
				} catch (Exception e) {
					log.error("保存节点缓存数据异常: {}", e.getMessage());
				}
			}

			log.info("同步工作空间[{}]工作项[{}]实例[{}]的节点数据完成, 总数: {}, 成功: {}", projectKey, workItemKey, instanceKey, totalCount,
					successCount);

			if (!CollectionUtils.isEmpty(taskList)) {
				fsTaskCacheService.saveOrUpdateBatchByUniqueKey(taskList, 300);
			}

			// ====保存统计数据=====
			if (!CollectionUtils.isEmpty(nodes)) {
				this.saveDataNodeAndTask(nodes);
			}

			return successCount > 0;
		} catch (Exception e) {
			log.error("同步工作空间[{}]工作项[{}]实例[{}]节点数据异常", projectKey, workItemKey, instanceKey, e);
			return false;
		}
	}

	private void saveDataNodeAndTask(List<FsNodeCache> nodes) {

		Set<String> nodeKeys = new HashSet<>(); // 保存新数据
		List<FsDataTask> dataTasks = new ArrayList<FsDataTask>();
		for (FsNodeCache node : nodes) {
			if (nodeKeys.contains(node.getNodeKey())) {
				continue;
			}
			nodeKeys.add(node.getNodeKey());

			if (!CollectionUtils.isEmpty(node.getTaskList())) {
				Set<String> keys = new HashSet<String>();
				for (FsTaskCache fsTaskCache : node.getTaskList()) {
					// 同步数据到fs_data_task表
					if (keys.contains(fsTaskCache.getTaskKey())) {
						continue;
					}
					FsDataTask dataTask = new FsDataTask();
					dataTask.setId(fsTaskCache.getTaskKey());
					dataTask.setProjectKey(fsTaskCache.getProjectKey());
					dataTask.setTaskKey(fsTaskCache.getTaskKey());
					dataTask.setTaskName(fsTaskCache.getTaskName());
					dataTask.setStatus(fsTaskCache.getStatus());
					dataTask.setAssignee(fsTaskCache.getAssignee());
					dataTask.setAssigneeName(fsTaskCache.getAssigneeName());
					dataTask.setUpdateTime(fsTaskCache.getUpdateTime());
					dataTask.setCreateTime(new Date());
					if (null != fsTaskCache.getStartTime()) {
						dataTask.setStartTime(fsTaskCache.getStartTime());
					}
					if (null != fsTaskCache.getEndTime()) {
						dataTask.setEndTime(fsTaskCache.getEndTime());
					}
					if (null != fsTaskCache.getEstimateHours()) {
						dataTask.setEstimateHours(fsTaskCache.getEstimateHours());
					}
					dataTasks.add(dataTask);
				}
			}

		}
		if (!dataTasks.isEmpty()) {
			fsDataTaskService.saveOrUpdateBatch(dataTasks);
		}

	}

	private List<FsNodeCache> getMeegoNodes(String projectKey, String workItemKey, String instanceKey)
			throws Exception {
		// 从认证上下文获取token和userKey
		AuthContext authContext = AuthContext.getContext();
		if (authContext == null) {
			throw new Exception("无法获取认证上下文，请确保在Web请求上下文中调用");
		}

		String token = authContext.getToken();
		String userKey = authContext.getUserKey();

		// 验证token和userKey是否有效
		if (token == null || token.isEmpty()) {
			throw new Exception("无法获取有效的访问令牌(token)，请检查飞书授权配置");
		}

		if (userKey == null || userKey.isEmpty()) {
			throw new Exception("无法获取有效的用户标识(userKey)，请检查飞书授权配置");
		}

		log.info("调用飞书API: userKey={}, tokenLength={}", userKey, token.length());

		List<WorkflowNode> nodeList = feishuApiService.queryWorkflow(projectKey, workItemKey, instanceKey, token, userKey);

		if (nodeList != null && !CollectionUtils.isEmpty(nodeList)) {
			List<FsNodeCache> resList = new ArrayList<>();

			// 处理节点数据
			for (WorkflowNode flowNode : nodeList) {
				FsNodeCache cache = new FsNodeCache();
				cache.setProjectKey(projectKey);
				cache.setWorkItemKey(workItemKey);
				cache.setInstanceKey(instanceKey);
				String nodeKey = flowNode.getID() + "";
				cache.setNodeKey(nodeKey);
				cache.setNodeName(flowNode.getName());
				cache.setStatus("1"); // 默认状态
				cache.setUpdateTime(new Date());
				// 节点上的排期+估分有多个人的; 目前结构上不满足
				// flowNode.getSchedules();
				
				// 获取节点：排期开始和结束时间
				if(null != flowNode.getNodeSchedule()) {
					Schedule scheduleInfo = flowNode.getNodeSchedule();
					if(null!=scheduleInfo.getPoints()) {
						// 工时
						cache.setEstimateHours(new BigDecimal(scheduleInfo.getPoints().toString()));
					}
					// 排期
					if (null != scheduleInfo.getEstimateStartDate()) {
						cache.setScheduleStartTime(DateFormatUtil.timestampToDate(scheduleInfo.getEstimateStartDate()));
					}
					if (null != scheduleInfo.getEstimateEndDate()) {
						cache.setScheduleEndTime(DateFormatUtil.timestampToDate(scheduleInfo.getEstimateEndDate()));
					}
				}

				// 节点下级任务列表
				List<FsTaskCache> taskList = this.getMeegoTasks(flowNode.getSubTasks(), projectKey, workItemKey,
						instanceKey, nodeKey);
				cache.setTaskList(taskList);

				resList.add(cache);
			}
			return resList;
		}

		return null;
	}

	private List<FsTaskCache> getMeegoTasks(List<SubTask> subTasks, String projectKey, String workItemKey,
			String instanceKey, String nodeKey) {
		if (CollectionUtils.isEmpty(subTasks)) {
			return new ArrayList<FsTaskCache>();
		}
		List<FsTaskCache> dataList = new ArrayList<FsTaskCache>();
		for (SubTask subTask : subTasks) {
			FsTaskCache data = new FsTaskCache();
			data.setId(UUID.randomUUID().toString().replace("-", ""));
			data.setProjectKey(projectKey).setWorkItemKey(workItemKey).setInstanceKey(instanceKey).setNodeKey(nodeKey);
			data.setTaskKey(subTask.getID()).setTaskName(subTask.getName());
			data.setStatus("1");
//    		data.setStatus(subTask.getString("status"));

			// 获取负责人信息
			if (!CollectionUtils.isEmpty(subTask.getAssignee())) {
				String assignee = subTask.getAssignee().get(0);
				data.setAssignee(assignee);
				// TODO 查询接口
				data.setAssigneeName(assignee);
			}

			if (CollectionUtils.isEmpty(subTask.getSchedules())) {
				Schedule schInfo = subTask.getSchedules().get(0);
				// 获取计划开始和结束时间
				if (null != schInfo.getEstimateStartDate()) {
					data.setStartTime(DateFormatUtil.timestampToDate(schInfo.getEstimateStartDate()));
				}
				if (null != schInfo.getEstimateEndDate()) {
					data.setEndTime(DateFormatUtil.timestampToDate(schInfo.getEstimateEndDate()));
				}

				// 获取预估工时
				if (schInfo.getPoints() > 0) {
					data.setEstimateHours(new BigDecimal(schInfo.getPoints().toString()));
				}
			}
			data.setUpdateTime(new Date());
			dataList.add(data);
		}
		return dataList;
	}

	@Override
	public FsWorkItemCache getWorkItemDetail(String projectKey, String itemKey) {
		LambdaQueryWrapper<FsWorkItemCache> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsWorkItemCache::getProjectKey, projectKey).eq(FsWorkItemCache::getItemKey, itemKey);
		return workItemCacheMapper.selectOne(queryWrapper);
	}

	@Override
	public FsInstanceCache getInstanceDetail(String projectKey, String instanceKey) {
		LambdaQueryWrapper<FsInstanceCache> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsInstanceCache::getProjectKey, projectKey).eq(FsInstanceCache::getInstanceKey, instanceKey);
		return instanceCacheMapper.selectOne(queryWrapper);
	}

	@Override
	public FsNodeCache getNodeDetail(String projectKey, String workItemKey, String instanceKey, String nodeKey) {
		// 1. 验证工作项
		FsWorkItemCache workItem = getWorkItemDetail(projectKey, workItemKey);
		if (workItem == null) {
			log.warn("未找到工作项: projectKey={}, workItemKey={}", projectKey, workItemKey);
			return null;
		}

		// 2. 验证实例
		FsInstanceCache instance = getInstanceDetail(projectKey, instanceKey);
		if (instance == null) {
			log.warn("未找到实例: projectKey={}, instanceKey={}", projectKey, instanceKey);
			return null;
		}

		// 3. 获取并验证节点
		LambdaQueryWrapper<FsNodeCache> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsNodeCache::getProjectKey, projectKey).eq(FsNodeCache::getWorkItemKey, workItemKey)
				.eq(FsNodeCache::getInstanceKey, instanceKey).eq(FsNodeCache::getNodeKey, nodeKey);

		FsNodeCache node = nodeCacheMapper.selectOne(queryWrapper);
		if (node == null) {
			log.warn("未找到节点: projectKey={}, nodeKey={}, workItemKey={}, instanceKey={}", projectKey, nodeKey,
					workItemKey, instanceKey);
			return null;
		}

		return node;
	}

	@Override
	public FsTaskCache getTaskDetail(String projectKey, String taskKey) {
		LambdaQueryWrapper<FsTaskCache> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsTaskCache::getProjectKey, projectKey).eq(FsTaskCache::getTaskKey, taskKey);
		return taskCacheMapper.selectOne(queryWrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean syncFsDataTask(String projectKey, String workItemKey, String instanceKey, String nodeKey) {
		try {
			// 获取任务数据
			List<FsTaskCache> tasks = getTasks(projectKey, nodeKey);
			for (FsTaskCache task : tasks) {
				FsDataTask dataTask = new FsDataTask();
				dataTask.setId(task.getId());
				dataTask.setProjectKey(projectKey);
				dataTask.setTaskKey(task.getTaskKey());
				dataTask.setTaskName(task.getTaskName());
				dataTask.setDescription(task.getDescription());
				dataTask.setStatus(task.getStatus());
				dataTask.setAssignee(task.getAssignee());
				dataTask.setAssigneeName(task.getAssigneeName());
				dataTask.setEstimateHours(task.getEstimateHours());
				dataTask.setStartTime(task.getStartTime());
				dataTask.setEndTime(task.getEndTime());
				dataTask.setUpdateTime(new Date());
				dataTask.setCreateTime(new Date());

				// 插入或更新数据
				dataTaskMapper.insertOrUpdate(dataTask);
			}
			return true;
		} catch (Exception e) {
			log.error("同步任务数据到 fs_data_task 表失败: {}", e.getMessage(), e);
			return false;
		}
	}

	@Override
	public List<FsTaskCache> getTasksByKeys(String projectKey, String workItemKey, String instanceKey, String nodeKey) {
		return fsTaskCacheService.getTasksByKeys(projectKey, workItemKey, instanceKey, nodeKey);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean syncWorkItem(String projectKey, String workItemTypeKey, Long workItemId) {
		try {
			// 获取认证上下文
			AuthContext context = AuthContext.getContext();
			if (context == null) {
				log.error("无法获取有效的认证上下文");
				return false;
			}
			String token = context.getToken();
			String userKey = context.getUserKey();
			if (token == null || token.isEmpty() || userKey == null || userKey.isEmpty()) {
				log.error("无法获取有效的认证信息");
				return false;
			}

			// 调用 FeishuApiService 获取工作项类型详情
			com.lark.project.service.workitem.model.WorkItemTypeInfo info = feishuApiService.getWorkItemTypeInfo(projectKey, workItemTypeKey, token, userKey);
			if (info == null) {
				log.warn("未获取到工作项信息: projectKey={}, workItemTypeKey={}", projectKey, workItemTypeKey);
				return false;
			}

			// 更新工作项信息
			LambdaQueryWrapper<FsWorkItemCache> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(FsWorkItemCache::getProjectKey, projectKey)
					.eq(FsWorkItemCache::getItemKey, workItemTypeKey);
			FsWorkItemCache workItemCache = workItemCacheMapper.selectOne(queryWrapper);
			if (workItemCache == null) {
				// 新增工作项
				workItemCache = new FsWorkItemCache();
				workItemCache.setProjectKey(projectKey);
				workItemCache.setItemKey(workItemTypeKey);
				workItemCache.setName(info.getName());
				workItemCache.setItemType(info.getAPIName());
				workItemCache.setStatus("1"); // 默认状态为启用
				workItemCache.setFlowMode(info.getFlowMode());
				workItemCache.setUpdateTime(new Date());
				workItemCacheMapper.insert(workItemCache);
			} else {
				// 更新工作项
				workItemCache.setName(info.getName());
				workItemCache.setItemType(info.getAPIName());
				// 保持原有状态不变
				workItemCache.setFlowMode(info.getFlowMode());
				workItemCache.setUpdateTime(new Date());
				workItemCacheMapper.updateById(workItemCache);
			}

			log.info("同步工作项成功: projectKey={}, workItemTypeKey={}, workItemId={}", 
					projectKey, workItemTypeKey, workItemId);
			return true;
		} catch (Exception e) {
			log.error("同步工作项异常: projectKey={}, workItemTypeKey={}, workItemId={}", 
					projectKey, workItemTypeKey, workItemId, e);
			return false;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean syncTask(String projectKey, String workItemTypeKey, Long taskId) {
		try {
			// 获取认证上下文
			AuthContext context = AuthContext.getContext();
			if (context == null) {
				log.error("无法获取有效的认证上下文");
				return false;
			}
			String token = context.getToken();
			String userKey = context.getUserKey();
			if (token == null || token.isEmpty() || userKey == null || userKey.isEmpty()) {
				log.error("无法获取有效的认证信息");
				return false;
			}

			// 调用 FeishuApiService 获取任务详情
			com.lark.project.service.workitem.model.SubTask taskDetail = feishuApiService.getTaskDetail(projectKey, taskId, token, userKey);
			if (taskDetail == null) {
				log.warn("未获取到任务详情: projectKey={}, taskId={}", projectKey, taskId);
				return false;
			}

			// TODO: 需根据实际API补充节点、实例等信息
			String nodeKey = "";
			String instanceKey = "";

			// 更新任务信息
			LambdaQueryWrapper<FsTaskCache> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(FsTaskCache::getProjectKey, projectKey)
					.eq(FsTaskCache::getTaskKey, taskId.toString());
			FsTaskCache taskCache = taskCacheMapper.selectOne(queryWrapper);
			if (taskCache == null) {
				// 新增任务
				taskCache = new FsTaskCache();
				taskCache.setId(UUID.randomUUID().toString().replace("-", ""));
				taskCache.setProjectKey(projectKey);
				taskCache.setWorkItemKey(workItemTypeKey);
				taskCache.setInstanceKey(instanceKey);
				taskCache.setNodeKey(nodeKey);
				taskCache.setTaskKey(taskId.toString());
				taskCache.setTaskName(""); // 可从taskDetail获取
				taskCache.setStatus("1");
				taskCache.setUpdateTime(new Date());
				// TODO: 其他字段从taskDetail获取
				taskCacheMapper.insert(taskCache);
			} else {
				// 更新任务
				// TODO: 其他字段从taskDetail获取
				taskCache.setUpdateTime(new Date());
				taskCacheMapper.updateById(taskCache);
			}
			log.info("同步任务成功: projectKey={}, taskId={}", projectKey, taskId);
			return true;
		} catch (Exception e) {
			log.error("同步任务异常: projectKey={}, taskId={}", projectKey, taskId, e);
			return false;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteTask(String projectKey, Long taskId) {
		try {
			// 删除任务
			LambdaQueryWrapper<FsTaskCache> taskWrapper = new LambdaQueryWrapper<>();
			taskWrapper.eq(FsTaskCache::getProjectKey, projectKey)
					.eq(FsTaskCache::getTaskKey, taskId.toString());
			
			int count = taskCacheMapper.delete(taskWrapper);
			
			// 同时删除fs_data_task表中的数据
			LambdaQueryWrapper<FsDataTask> dataTaskWrapper = new LambdaQueryWrapper<>();
			dataTaskWrapper.eq(FsDataTask::getProjectKey, projectKey)
					.eq(FsDataTask::getTaskKey, taskId.toString());
			
			dataTaskMapper.delete(dataTaskWrapper);
			
			log.info("删除任务成功: projectKey={}, taskId={}, 删除记录数={}", projectKey, taskId, count);
			return true;
		} catch (Exception e) {
			log.error("删除任务异常: projectKey={}, taskId={}", projectKey, taskId, e);
			return false;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteWorkItem(String projectKey, String workItemTypeKey, Long workItemId) {
		try {
			// 1. 删除工作项
			LambdaQueryWrapper<FsWorkItemCache> workItemWrapper = new LambdaQueryWrapper<>();
			workItemWrapper.eq(FsWorkItemCache::getProjectKey, projectKey)
					.eq(FsWorkItemCache::getItemKey, workItemTypeKey);
			workItemCacheMapper.delete(workItemWrapper);
			
			// 2. 删除关联的实例
			LambdaQueryWrapper<FsInstanceCache> instanceWrapper = new LambdaQueryWrapper<>();
			instanceWrapper.eq(FsInstanceCache::getProjectKey, projectKey)
					.eq(FsInstanceCache::getWorkItemKey, workItemTypeKey);
			List<FsInstanceCache> instances = instanceCacheMapper.selectList(instanceWrapper);
			
			// 3. 删除实例关联的节点和任务
			for (FsInstanceCache instance : instances) {
				String instanceKey = instance.getInstanceKey();
				
				// 删除节点
				LambdaQueryWrapper<FsNodeCache> nodeWrapper = new LambdaQueryWrapper<>();
				nodeWrapper.eq(FsNodeCache::getProjectKey, projectKey)
						.eq(FsNodeCache::getWorkItemKey, workItemTypeKey)
						.eq(FsNodeCache::getInstanceKey, instanceKey);
				List<FsNodeCache> nodes = nodeCacheMapper.selectList(nodeWrapper);
				
				// 删除节点关联的任务
				for (FsNodeCache node : nodes) {
					String nodeKey = node.getNodeKey();
					
					// 删除任务
					LambdaQueryWrapper<FsTaskCache> taskWrapper = new LambdaQueryWrapper<>();
					taskWrapper.eq(FsTaskCache::getProjectKey, projectKey)
							.eq(FsTaskCache::getNodeKey, nodeKey);
					taskCacheMapper.delete(taskWrapper);
				}
				
				// 删除节点
				nodeCacheMapper.delete(nodeWrapper);
			}
			
			// 4. 删除实例
			instanceCacheMapper.delete(instanceWrapper);
			
			log.info("删除工作项相关数据成功: projectKey={}, workItemTypeKey={}, workItemId={}", 
					projectKey, workItemTypeKey, workItemId);
			return true;
		} catch (Exception e) {
			log.error("删除工作项相关数据异常: projectKey={}, workItemTypeKey={}, workItemId={}", 
					projectKey, workItemTypeKey, workItemId, e);
			return false;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean syncNode(String projectKey, String workItemTypeKey, String instanceKey, String nodeKey) {
		try {
			// 获取认证上下文
			AuthContext context = AuthContext.getContext();
			if (context == null) {
				log.error("无法获取有效的认证上下文");
				return false;
			}
			String token = context.getToken();
			String userKey = context.getUserKey();
			if (token == null || token.isEmpty() || userKey == null || userKey.isEmpty()) {
				log.error("无法获取有效的认证信息");
				return false;
			}

			// 调用 FeishuApiService 查询工作流节点
			List<com.lark.project.service.workitem.model.WorkflowNode> nodeList = feishuApiService.queryWorkflow(projectKey, workItemTypeKey, instanceKey, token, userKey);
			if (nodeList == null || nodeList.isEmpty()) {
				log.warn("未获取到节点信息: projectKey={}, workItemTypeKey={}, instanceKey={}", projectKey, workItemTypeKey, instanceKey);
				return false;
			}

			// 查找指定的节点
			com.lark.project.service.workitem.model.WorkflowNode targetNode = null;
			for (com.lark.project.service.workitem.model.WorkflowNode node : nodeList) {
				if (String.valueOf(node.getID()).equals(nodeKey)) {
					targetNode = node;
					break;
				}
			}
			if (targetNode == null) {
				log.warn("未找到指定节点: projectKey={}, workItemTypeKey={}, instanceKey={}, nodeKey={}", projectKey, workItemTypeKey, instanceKey, nodeKey);
				return false;
			}

			// 更新节点信息
			LambdaQueryWrapper<FsNodeCache> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(FsNodeCache::getProjectKey, projectKey)
					.eq(FsNodeCache::getWorkItemKey, workItemTypeKey)
					.eq(FsNodeCache::getInstanceKey, instanceKey)
					.eq(FsNodeCache::getNodeKey, nodeKey);
			FsNodeCache nodeCache = nodeCacheMapper.selectOne(queryWrapper);
			if (nodeCache == null) {
				// 新增节点
				nodeCache = new FsNodeCache();
				nodeCache.setId(UUID.randomUUID().toString().replace("-", ""));
				nodeCache.setProjectKey(projectKey);
				nodeCache.setWorkItemKey(workItemTypeKey);
				nodeCache.setInstanceKey(instanceKey);
				nodeCache.setNodeKey(nodeKey);
				nodeCache.setNodeName(targetNode.getName());
				nodeCache.setStatus("1"); // 默认状态
				// 设置排期和工时
				if (targetNode.getNodeSchedule() != null) {
					com.lark.project.service.workitem.model.Schedule schedule = targetNode.getNodeSchedule();
					if (schedule.getPoints() != null) {
						nodeCache.setEstimateHours(new BigDecimal(schedule.getPoints().toString()));
					}
					if (schedule.getEstimateStartDate() != null) {
						nodeCache.setScheduleStartTime(DateFormatUtil.timestampToDate(schedule.getEstimateStartDate()));
					}
					if (schedule.getEstimateEndDate() != null) {
						nodeCache.setScheduleEndTime(DateFormatUtil.timestampToDate(schedule.getEstimateEndDate()));
					}
				}
				nodeCache.setUpdateTime(new Date());
				nodeCacheMapper.insert(nodeCache);
			} else {
				// 更新节点
				nodeCache.setNodeName(targetNode.getName());
				if (targetNode.getNodeSchedule() != null) {
					com.lark.project.service.workitem.model.Schedule schedule = targetNode.getNodeSchedule();
					if (schedule.getPoints() != null) {
						nodeCache.setEstimateHours(new BigDecimal(schedule.getPoints().toString()));
					}
					if (schedule.getEstimateStartDate() != null) {
						nodeCache.setScheduleStartTime(DateFormatUtil.timestampToDate(schedule.getEstimateStartDate()));
					}
					if (schedule.getEstimateEndDate() != null) {
						nodeCache.setScheduleEndTime(DateFormatUtil.timestampToDate(schedule.getEstimateEndDate()));
					}
				}
				nodeCache.setUpdateTime(new Date());
				nodeCacheMapper.updateById(nodeCache);
			}

			// 同步节点下的任务
			List<com.lark.project.service.workitem.model.SubTask> subTasks = targetNode.getSubTasks();
			if (subTasks != null && !subTasks.isEmpty()) {
				List<FsTaskCache> taskList = getMeegoTasks(subTasks, projectKey, workItemTypeKey, instanceKey, nodeKey);
				for (FsTaskCache task : taskList) {
					LambdaQueryWrapper<FsTaskCache> taskQuery = new LambdaQueryWrapper<>();
					taskQuery.eq(FsTaskCache::getProjectKey, projectKey)
							.eq(FsTaskCache::getTaskKey, task.getTaskKey());
					FsTaskCache existTask = taskCacheMapper.selectOne(taskQuery);
					if (existTask == null) {
						taskCacheMapper.insert(task);
					} else {
						existTask.setTaskName(task.getTaskName());
						existTask.setAssignee(task.getAssignee());
						existTask.setAssigneeName(task.getAssigneeName());
						existTask.setEstimateHours(task.getEstimateHours());
						existTask.setStartTime(task.getStartTime());
						existTask.setEndTime(task.getEndTime());
						existTask.setUpdateTime(new Date());
						taskCacheMapper.updateById(existTask);
					}
				}
			}

			log.info("同步节点成功: projectKey={}, workItemTypeKey={}, instanceKey={}, nodeKey={}",
					projectKey, workItemTypeKey, instanceKey, nodeKey);
			return true;
		} catch (Exception e) {
			log.error("同步节点异常: projectKey={}, workItemTypeKey={}, instanceKey={}, nodeKey={}",
					projectKey, workItemTypeKey, instanceKey, nodeKey, e);
			return false;
		}
	}
}