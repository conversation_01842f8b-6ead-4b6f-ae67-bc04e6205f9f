package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.dto.DeptProjectDailyHourReportDTO;
import org.jeecg.modules.hours.vo.DeptProjectDailyHourReportResult;

/**
 * @Description: 部门项目每日工时分布统计服务接口
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
public interface IDeptProjectDailyHourStatisticsService {

    /**
     * 获取部门项目每日工时分布报表
     *
     * @param reportDTO 查询参数
     * @return 报表结果
     */
    DeptProjectDailyHourReportResult getDeptProjectDailyHourReport(DeptProjectDailyHourReportDTO reportDTO);

    /**
     * 生成月度统计数据
     *
     * @param statMonth 统计月份 (格式: 2025-07)
     * @param isHoliday 是否统计假日：0-否；1-是
     * @return 处理的记录数
     */
    int generateMonthlyStatistics(String statMonth, Integer isHoliday);
}