//package org.jeecg.modules.hours.controller;
//
//import org.jeecg.modules.hours.entity.HourRecord;
//import org.junit.jupiter.api.Test;
//import org.springframework.util.StringUtils;
//
//import java.util.Arrays;
//import java.util.List;
//
///**
// * 工作内容路径构建测试
// */
//public class WorkContentPathTest {
//
//    /**
//     * 模拟构建工作内容路径的方法（根据传入参数）
//     */
//    private String buildWorkContentPath(List<HourRecord> records, String workItemId, String instanceId, String nodeId, String taskId) {
//        if (records == null || records.isEmpty()) {
//            return "未知工作内容";
//        }
//
//        HourRecord firstRecord = records.get(0);
//        StringBuilder pathBuilder = new StringBuilder();
//
//        // 根据传入的参数决定显示哪些层级
//        // 只有传入了对应参数，才显示对应层级的名称
//        if (StringUtils.hasText(workItemId) && StringUtils.hasText(firstRecord.getWorkItemName())) {
//            pathBuilder.append(firstRecord.getWorkItemName());
//        }
//        if (StringUtils.hasText(instanceId) && StringUtils.hasText(firstRecord.getInstanceName())) {
//            if (pathBuilder.length() > 0) {
//                pathBuilder.append("/");
//            }
//            pathBuilder.append(firstRecord.getInstanceName());
//        }
//        if (StringUtils.hasText(nodeId) && StringUtils.hasText(firstRecord.getNodeName())) {
//            if (pathBuilder.length() > 0) {
//                pathBuilder.append("/");
//            }
//            pathBuilder.append(firstRecord.getNodeName());
//        }
//        if (StringUtils.hasText(taskId) && StringUtils.hasText(firstRecord.getTaskName())) {
//            if (pathBuilder.length() > 0) {
//                pathBuilder.append("/");
//            }
//            pathBuilder.append(firstRecord.getTaskName());
//        }
//
//        return pathBuilder.length() > 0 ? pathBuilder.toString() : "未知工作内容";
//    }
//
//    @Test
//    public void testWorkContentPathBuilding() {
//        // 创建测试数据
//        HourRecord record = new HourRecord();
//        record.setWorkItemName("项目");
//        record.setInstanceName("all in ai");
//        record.setNodeName("项目立项");
//        record.setTaskName("all in ai内部宣讲");
//
//        List<HourRecord> records = Arrays.asList(record);
//
//        // 测试场景1：只传递 workItemId 和 instanceId
//        String result1 = buildWorkContentPath(records, "672d784c9a71acdb4f8180ef", "6165783422", null, null);
//        System.out.println("场景1 - 只传递workItemId和instanceId:");
//        System.out.println("传入参数: workItemId=672d784c9a71acdb4f8180ef, instanceId=6165783422");
//        System.out.println("期望结果: 项目/all in ai");
//        System.out.println("实际结果: " + result1);
//        System.out.println("测试结果: " + ("项目/all in ai".equals(result1) ? "✅ 通过" : "❌ 失败"));
//        System.out.println();
//
//        // 测试场景2：传递 workItemId、instanceId 和 nodeId
//        String result2 = buildWorkContentPath(records, "672d784c9a71acdb4f8180ef", "6165783422", "state_0", null);
//        System.out.println("场景2 - 传递workItemId、instanceId和nodeId:");
//        System.out.println("传入参数: workItemId=672d784c9a71acdb4f8180ef, instanceId=6165783422, nodeId=state_0");
//        System.out.println("期望结果: 项目/all in ai/项目立项");
//        System.out.println("实际结果: " + result2);
//        System.out.println("测试结果: " + ("项目/all in ai/项目立项".equals(result2) ? "✅ 通过" : "❌ 失败"));
//        System.out.println();
//
//        // 测试场景3：传递所有参数
//        String result3 = buildWorkContentPath(records, "672d784c9a71acdb4f8180ef", "6165783422", "state_0", "616581875");
//        System.out.println("场景3 - 传递所有参数:");
//        System.out.println("传入参数: workItemId=672d784c9a71acdb4f8180ef, instanceId=6165783422, nodeId=state_0, taskId=616581875");
//        System.out.println("期望结果: 项目/all in ai/项目立项/all in ai内部宣讲");
//        System.out.println("实际结果: " + result3);
//        System.out.println("测试结果: " + ("项目/all in ai/项目立项/all in ai内部宣讲".equals(result3) ? "✅ 通过" : "❌ 失败"));
//        System.out.println();
//
//        // 测试场景4：只传递 workItemId
//        String result4 = buildWorkContentPath(records, "672d784c9a71acdb4f8180ef", null, null, null);
//        System.out.println("场景4 - 只传递workItemId:");
//        System.out.println("传入参数: workItemId=672d784c9a71acdb4f8180ef");
//        System.out.println("期望结果: 项目");
//        System.out.println("实际结果: " + result4);
//        System.out.println("测试结果: " + ("项目".equals(result4) ? "✅ 通过" : "❌ 失败"));
//        System.out.println();
//
//        // 测试场景5：不传递任何参数
//        String result5 = buildWorkContentPath(records, null, null, null, null);
//        System.out.println("场景5 - 不传递任何参数:");
//        System.out.println("传入参数: 无");
//        System.out.println("期望结果: 未知工作内容");
//        System.out.println("实际结果: " + result5);
//        System.out.println("测试结果: " + ("未知工作内容".equals(result5) ? "✅ 通过" : "❌ 失败"));
//    }
//}
