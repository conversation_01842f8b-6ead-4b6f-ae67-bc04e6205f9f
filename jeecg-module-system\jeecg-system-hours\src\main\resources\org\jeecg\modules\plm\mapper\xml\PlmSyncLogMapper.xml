<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.plm.mapper.PlmSyncLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.jeecg.modules.plm.entity.PlmSyncLog">
        <id column="id" property="id"/>
        <result column="sync_type" property="syncType"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="duration" property="duration"/>
        <result column="total_count" property="totalCount"/>
        <result column="success_count" property="successCount"/>
        <result column="error_msg" property="errorMsg"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sync_type, status, start_time, end_time, duration, total_count, success_count, error_msg, create_time, update_time
    </sql>

    <!-- 分页查询同步日志 -->
    <select id="querySyncLogList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plm_sync_log
        WHERE 1=1
        <if test="syncType != null and syncType != ''">
            AND sync_type = #{syncType}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY start_time DESC
    </select>

    <!-- 查询最近一次成功的同步记录 -->
    <select id="queryLastSuccessLog" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plm_sync_log
        WHERE status = 'SUCCESS'
        <if test="syncType != null and syncType != ''">
            AND sync_type = #{syncType}
        </if>
        ORDER BY end_time DESC
        LIMIT 1
    </select>
</mapper> 