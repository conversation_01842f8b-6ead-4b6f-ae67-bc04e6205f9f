/**
 * 瀑布流分页级联选择器测试组件
 *
 * 用于测试和演示WaterfallCascader组件的功能：
 * - 四级级联选择测试
 * - 瀑布流分页加载测试
 * - 统计接口调用测试
 * - 数据缓存和错误处理测试
 */
import React, { useState } from 'react';
import { Card, Typography, Space, Button, Notification } from '@douyinfe/semi-ui';
import WaterfallCascader from './WaterfallCascader';
import { CascaderItem } from '../types';

const { Title, Text } = Typography;

const CascaderTest: React.FC = () => {
  const [value, setValue] = useState<(string | number)[]>([]);
  const [selectedLabels, setSelectedLabels] = useState<string[]>([]);
  const [debugMode, setDebugMode] = useState<boolean>(true);
  const [loadHistory, setLoadHistory] = useState<string[]>([]);

  const handleChange = (newValue: (string | number)[], selectedOptions: CascaderItem[]) => {
    console.log('测试页面 - 值变化:', newValue, selectedOptions);
    setValue(newValue);
    setSelectedLabels(selectedOptions.map(opt => opt.label as string));

    if (debugMode) {
      const timestamp = new Date().toLocaleTimeString();
      setLoadHistory(prev => [...prev, `${timestamp}: 选择变化 - ${newValue.join(' → ')}`]);
    }
  };

  const handleStatisticsLoad = (workItemKey: string, instanceKey: string, nodeKey: string) => {
    console.log('统计信息加载:', { workItemKey, instanceKey, nodeKey });
    Notification.success({
      title: '统计信息加载成功',
      content: `工作项: ${workItemKey}, 实例: ${instanceKey}, 节点: ${nodeKey}`,
      duration: 3
    });
  };

  const formatWorkContent = (labels: string[]): React.ReactNode => {
    if (labels.length === 0) return '';
    
    return (
      <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap', overflow: 'hidden' }}>
        {labels.map((label, index) => (
          <React.Fragment key={index}>
            {index > 0 && <span style={{ margin: '0 4px', color: '#ccc' }}>/</span>}
            <span>{label}</span>
          </React.Fragment>
        ))}
      </div>
    );
  };

  const clearSelection = () => {
    setValue([]);
    setSelectedLabels([]);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>瀑布流分页级联选择器测试</Title>
        
        <Space vertical style={{ width: '100%' }} spacing={24}>
          {/* 级联选择器 */}
          <div>
            <Text strong>工作内容选择：</Text>
            <div style={{ marginTop: '8px' }}>
              <WaterfallCascader
                value={value}
                onChange={handleChange}
                onStatisticsLoad={handleStatisticsLoad}
                placeholder="请选择工作内容（支持瀑布流分页）"
                style={{ width: '100%' }}
                displayRender={formatWorkContent}
              />
            </div>
          </div>

          {/* 当前选择信息 */}
          <div>
            <Text strong>当前选择：</Text>
            <div style={{ marginTop: '8px', padding: '12px', backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
              <div><Text type="secondary">值: </Text>{JSON.stringify(value)}</div>
              <div style={{ marginTop: '4px' }}>
                <Text type="secondary">标签: </Text>
                {selectedLabels.length > 0 ? selectedLabels.join(' / ') : '未选择'}
              </div>
              <div style={{ marginTop: '4px' }}>
                <Text type="secondary">级别: </Text>
                {value.length === 0 && '未选择'}
                {value.length === 1 && '工作项'}
                {value.length === 2 && '实例'}
                {value.length === 3 && '节点'}
                {value.length === 4 && '任务'}
              </div>
            </div>
          </div>

          {/* 功能说明 */}
          <div>
            <Text strong>功能特性：</Text>
            <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
              <li>✅ 四级级联选择：工作项 → 实例 → 节点 → 任务</li>
              <li>✅ 瀑布流分页：实例列表和任务列表都是5条/页，支持"加载更多"</li>
              <li>✅ 叶子状态修复：节点接口传递工作项ID，正确判断叶子状态</li>
              <li>✅ 统计接口调用：选择完整路径后自动调用统计接口</li>
              <li>✅ 数据缓存：避免重复请求，提升性能</li>
              <li>✅ 错误处理：网络错误和数据异常的友好提示</li>
            </ul>
          </div>

          {/* 测试说明 */}
          <div>
            <Text strong>测试步骤：</Text>
            <ol style={{ marginTop: '8px', paddingLeft: '20px' }}>
              <li>选择一个工作项，观察实例列表加载</li>
              <li>如果实例超过5条，会显示"📄 加载更多"选项</li>
              <li>点击"加载更多"测试瀑布流分页效果</li>
              <li>选择实例后，观察节点列表加载（传递工作项ID）</li>
              <li>选择节点后，观察任务列表加载</li>
              <li>如果任务超过5条，会显示"📄 加载更多"选项</li>
              <li>点击"加载更多"测试任务列表分页效果</li>
              <li>选择任务后，会自动调用统计接口</li>
            </ol>
          </div>

          {/* 调试信息 */}
          {debugMode && (
            <div>
              <Text strong>调试信息：</Text>
              <div style={{
                marginTop: '8px',
                padding: '12px',
                backgroundColor: '#f8f9fa',
                borderRadius: '6px',
                maxHeight: '200px',
                overflowY: 'auto'
              }}>
                {loadHistory.length === 0 ? (
                  <Text type="tertiary">暂无操作记录</Text>
                ) : (
                  loadHistory.map((item, index) => (
                    <div key={index} style={{ marginBottom: '4px', fontSize: '12px' }}>
                      {item}
                    </div>
                  ))
                )}
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div>
            <Space>
              <Button onClick={clearSelection} type="secondary">
                清空选择
              </Button>
              <Button
                onClick={() => setDebugMode(!debugMode)}
                type={debugMode ? "primary" : "tertiary"}
              >
                {debugMode ? '关闭调试' : '开启调试'}
              </Button>
              <Button onClick={() => setLoadHistory([])} type="tertiary">
                清空日志
              </Button>
            </Space>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default CascaderTest;
