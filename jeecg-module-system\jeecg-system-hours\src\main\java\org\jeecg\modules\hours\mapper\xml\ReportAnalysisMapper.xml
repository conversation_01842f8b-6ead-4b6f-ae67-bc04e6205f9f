<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.ReportAnalysisMapper">

    <!-- 查询实例工时数据 -->
    <select id="queryInstanceHoursData" resultType="java.util.Map">
        SELECT 
            hr.instance_name,
            <choose>
                <when test="params.dimension == 'month'">
                    DATE_FORMAT(hr.work_date, '%Y-%m') as period,
                </when>
                <when test="params.dimension == 'week'">
                    CONCAT(
                        DATE_FORMAT(DATE_SUB(hr.work_date, INTERVAL WEEKDAY(hr.work_date) DAY), '%m/%d'),
                        '-',
                        DATE_FORMAT(DATE_ADD(DATE_SUB(hr.work_date, INTERVAL WEEKDAY(hr.work_date) DAY), INTERVAL 6 DAY), '%m/%d')
                    ) as period,
                </when>
                <otherwise>
                    DATE_FORMAT(hr.work_date, '%Y/%m/%d') as period,
                </otherwise>
            </choose>
            SUM(hr.hours) as total_hours
        FROM hour_record hr
        WHERE hr.project_id = #{params.projectKey}
          AND hr.status = 2
          AND hr.work_date BETWEEN #{params.startDate} AND #{params.endDate}
          AND hr.instance_name IS NOT NULL
          <if test="params.userIds != null and params.userIds.size() > 0">
              AND hr.user_id IN
              <foreach collection="params.userIds" item="userId" open="(" separator="," close=")">
                  #{userId}
              </foreach>
          </if>
          <if test="params.instanceIds != null and params.instanceIds.size() > 0">
              AND hr.instance_id IN
              <foreach collection="params.instanceIds" item="instanceId" open="(" separator="," close=")">
                  #{instanceId}
              </foreach>
          </if>
        GROUP BY hr.instance_name, period
        ORDER BY hr.instance_name, period
    </select>

    <!-- 查询实例工时数据（分页） -->
    <select id="queryInstanceHoursDataPage" resultType="java.util.Map">
        SELECT
            hr.instance_name,
            <choose>
                <when test="params.dimension == 'month'">
                    DATE_FORMAT(hr.work_date, '%Y-%m') as period,
                </when>
                <when test="params.dimension == 'week'">
                    CONCAT(
                        DATE_FORMAT(DATE_SUB(hr.work_date, INTERVAL WEEKDAY(hr.work_date) DAY), '%m/%d'),
                        '-',
                        DATE_FORMAT(DATE_ADD(DATE_SUB(hr.work_date, INTERVAL WEEKDAY(hr.work_date) DAY), INTERVAL 6 DAY), '%m/%d')
                    ) as period,
                </when>
                <otherwise>
                    DATE_FORMAT(hr.work_date, '%Y/%m/%d') as period,
                </otherwise>
            </choose>
            SUM(hr.hours) as total_hours
        FROM hour_record hr
        WHERE hr.project_id = #{params.projectKey}
          AND hr.status = 2
          AND hr.work_date BETWEEN #{params.startDate} AND #{params.endDate}
          AND hr.instance_name IS NOT NULL
          <if test="params.userIds != null and params.userIds.size() > 0">
              AND hr.user_id IN
              <foreach collection="params.userIds" item="userId" open="(" separator="," close=")">
                  #{userId}
              </foreach>
          </if>
          <if test="params.instanceIds != null and params.instanceIds.size() > 0">
              AND hr.instance_id IN
              <foreach collection="params.instanceIds" item="instanceId" open="(" separator="," close=")">
                  #{instanceId}
              </foreach>
          </if>
        GROUP BY hr.instance_name, period
        ORDER BY hr.instance_name, period
    </select>

    <!-- 查询实例列表（分页） -->
    <select id="queryInstanceListPage" resultType="java.lang.String">
        SELECT DISTINCT hr.instance_name
        FROM hour_record hr
        WHERE hr.project_id = #{params.projectKey}
          AND hr.status = 2
          AND hr.work_date BETWEEN #{params.startDate} AND #{params.endDate}
          AND hr.instance_name IS NOT NULL
          <if test="params.userIds != null and params.userIds.size() > 0">
              AND hr.user_id IN
              <foreach collection="params.userIds" item="userId" open="(" separator="," close=")">
                  #{userId}
              </foreach>
          </if>
          <if test="params.instanceIds != null and params.instanceIds.size() > 0">
              AND hr.instance_id IN
              <foreach collection="params.instanceIds" item="instanceId" open="(" separator="," close=")">
                  #{instanceId}
              </foreach>
          </if>
        ORDER BY hr.instance_name
    </select>

    <!-- 查询指定实例的工时数据 -->
    <select id="queryInstanceHoursDataByInstances" resultType="java.util.Map">
        SELECT
            hr.instance_name,
            <choose>
                <when test="params.dimension == 'month'">
                    DATE_FORMAT(hr.work_date, '%Y-%m') as period,
                </when>
                <when test="params.dimension == 'week'">
                    CONCAT(
                        DATE_FORMAT(DATE_SUB(hr.work_date, INTERVAL WEEKDAY(hr.work_date) DAY), '%m/%d'),
                        '-',
                        DATE_FORMAT(DATE_ADD(DATE_SUB(hr.work_date, INTERVAL WEEKDAY(hr.work_date) DAY), INTERVAL 6 DAY), '%m/%d')
                    ) as period,
                </when>
                <otherwise>
                    DATE_FORMAT(hr.work_date, '%Y/%m/%d') as period,
                </otherwise>
            </choose>
            SUM(hr.hours) as total_hours
        FROM hour_record hr
        WHERE hr.project_id = #{params.projectKey}
          AND hr.status = 2
          AND hr.work_date BETWEEN #{params.startDate} AND #{params.endDate}
          AND hr.instance_name IS NOT NULL
          AND hr.instance_name IN
          <foreach collection="instanceNames" item="instanceName" open="(" separator="," close=")">
              #{instanceName}
          </foreach>
          <if test="params.userIds != null and params.userIds.size() > 0">
              AND hr.user_id IN
              <foreach collection="params.userIds" item="userId" open="(" separator="," close=")">
                  #{userId}
              </foreach>
          </if>
          <if test="params.instanceIds != null and params.instanceIds.size() > 0">
              AND hr.instance_id IN
              <foreach collection="params.instanceIds" item="instanceId" open="(" separator="," close=")">
                  #{instanceId}
              </foreach>
          </if>
        GROUP BY hr.instance_name, period
        ORDER BY hr.instance_name, period
    </select>

    <!-- 查询员工工时数据（分页） -->
    <select id="queryEmployeeHoursData" resultType="java.util.Map">
        SELECT 
            hr.user_id as employee_id,
            hr.user_name as employee_name,
            <choose>
                <when test="params.dimension == 'month'">
                    DATE_FORMAT(hr.work_date, '%Y-%m') as period,
                </when>
                <when test="params.dimension == 'week'">
                    CONCAT(
                        DATE_FORMAT(DATE_SUB(hr.work_date, INTERVAL WEEKDAY(hr.work_date) DAY), '%m/%d'),
                        '-',
                        DATE_FORMAT(DATE_ADD(DATE_SUB(hr.work_date, INTERVAL WEEKDAY(hr.work_date) DAY), INTERVAL 6 DAY), '%m/%d')
                    ) as period,
                </when>
                <otherwise>
                    DATE_FORMAT(hr.work_date, '%Y/%m/%d') as period,
                </otherwise>
            </choose>
            SUM(hr.hours) as total_hours
        FROM hour_record hr
        WHERE hr.project_id = #{params.projectKey}
          AND hr.status = 2
          AND hr.work_date BETWEEN #{params.startDate} AND #{params.endDate}
          <if test="params.userIds != null and params.userIds.size() > 0">
              AND hr.user_id IN
              <foreach collection="params.userIds" item="userId" open="(" separator="," close=")">
                  #{userId}
              </foreach>
          </if>
        GROUP BY hr.user_id, hr.user_name, period
        ORDER BY hr.user_name, period
    </select>

    <!-- 查询员工工时实例分布数据（分页） -->
    <select id="queryEmployeeInstanceDistributionData" resultType="java.util.Map">
        SELECT 
            hr.user_id as employee_id,
            hr.user_name as employee_name,
            hr.instance_name,
            SUM(hr.hours) as total_hours
        FROM hour_record hr
        WHERE hr.project_id = #{params.projectKey}
          AND hr.status = 2
          AND hr.work_date BETWEEN #{params.startDate} AND #{params.endDate}
          AND hr.instance_name IS NOT NULL
          <if test="params.userIds != null and params.userIds.size() > 0">
              AND hr.user_id IN
              <foreach collection="params.userIds" item="userId" open="(" separator="," close=")">
                  #{userId}
              </foreach>
          </if>
          <if test="params.instanceIds != null and params.instanceIds.size() > 0">
              AND hr.instance_id IN
              <foreach collection="params.instanceIds" item="instanceId" open="(" separator="," close=")">
                  #{instanceId}
              </foreach>
          </if>
        GROUP BY hr.user_id, hr.user_name, hr.instance_id, hr.instance_name
        ORDER BY hr.user_name, hr.instance_name
    </select>

    <!-- 查询实例名称列表 -->
    <select id="queryInstanceNames" resultType="java.lang.String">
        SELECT DISTINCT hr.instance_name
        FROM hour_record hr
        WHERE hr.project_id = #{params.projectKey}
          AND hr.status = 2
          AND hr.work_date BETWEEN #{params.startDate} AND #{params.endDate}
          AND hr.instance_name IS NOT NULL
          <if test="params.userIds != null and params.userIds.size() > 0">
              AND hr.user_id IN
              <foreach collection="params.userIds" item="userId" open="(" separator="," close=")">
                  #{userId}
              </foreach>
          </if>
          <if test="params.instanceIds != null and params.instanceIds.size() > 0">
              AND hr.instance_id IN
              <foreach collection="params.instanceIds" item="instanceId" open="(" separator="," close=")">
                  #{instanceId}
              </foreach>
          </if>
        ORDER BY hr.instance_name
    </select>

    <!-- 查询项目下的用户列表 -->
    <select id="queryProjectUsers" resultType="org.jeecg.modules.hours.vo.UserVO">
        SELECT DISTINCT hr.user_id as id, hr.user_name as realname, hr.user_id as username
        FROM hour_record hr
        WHERE hr.project_id = #{projectKey}
          AND hr.status = 2
          AND hr.user_name IS NOT NULL
        ORDER BY hr.user_name
    </select>

    <!-- 查询项目下的实例列表 -->
    <select id="queryProjectInstances" resultType="org.jeecg.modules.hours.vo.InstanceVO">
        SELECT DISTINCT hr.instance_id as id, hr.instance_name as workItemName, hr.project_id as projectKey
        FROM hour_record hr
        WHERE hr.project_id = #{projectKey}
          AND hr.status = 2
          AND hr.instance_name IS NOT NULL
        ORDER BY hr.instance_name
    </select>

</mapper>
