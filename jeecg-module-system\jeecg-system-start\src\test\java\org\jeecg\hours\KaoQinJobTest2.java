package org.jeecg.hours;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.task.AttendanceStatJob;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.attendance.v1.model.ChildItem;
import com.lark.oapi.service.attendance.v1.model.Item;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UserStatsView;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 消息推送测试
 * @Author: lsq
 */
@Slf4j
@ActiveProfiles("dev")
//@ActiveProfiles("prod")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class KaoQinJobTest2 {

	@Autowired
	private AttendanceStatJob attendanceStatJob;

	@org.junit.jupiter.api.Test
	public void testname7() throws Exception {

//		Integer start = this.getBefore7Day();
//		Integer end = this.getYester();
		
		Integer start = 20250701;
		Integer end = 20250715;

		attendanceStatJob.startTest(start, end);

		log.info("start={},end={}", start, end);
	}

	private Integer getBefore7Day() {
		LocalDate yesterday = LocalDate.now().minusDays(7);

		// 格式化为 yyyyMMdd 的整数
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
		Integer date = Integer.valueOf(yesterday.format(formatter));
		return date;
	}

	private Integer getYester() {
		LocalDate yesterday = LocalDate.now().minusDays(1);

		// 格式化为 yyyyMMdd 的整数
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
		Integer date = Integer.valueOf(yesterday.format(formatter));
		return date;
	}

}
