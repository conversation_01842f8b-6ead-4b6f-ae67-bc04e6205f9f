//package org.jeecg.modules.hours.service;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
//import java.util.Arrays;
//import java.util.Date;
//import java.util.List;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//
//import org.jeecg.modules.hours.constant.ReminderConstant;
//import org.jeecg.modules.hours.entity.FsSysReminderConfig;
//import org.jeecg.modules.hours.mapper.FsSysReminderConfigMapper;
//import org.jeecg.modules.hours.service.impl.FsSysReminderConfigServiceImpl;
//
///**
// * 登记提醒配置服务测试类
// * 
// * <AUTHOR>
// * @date 2024-01-01
// */
//@ExtendWith(MockitoExtension.class)
//class FsSysReminderConfigServiceTest {
//
//    @Mock
//    private FsSysReminderConfigMapper reminderConfigMapper;
//
//    @InjectMocks
//    private FsSysReminderConfigServiceImpl reminderConfigService;
//
//    private FsSysReminderConfig testConfig;
//    private String testProjectKey = "test_project_001";
//    private String testCreateBy = "test_user";
//
//    @BeforeEach
//    void setUp() {
//        testConfig = new FsSysReminderConfig();
////        testConfig.setId("test_id_001");
//        testConfig.setProjectKey(testProjectKey);
//        testConfig.setReminderType(ReminderConstant.ReminderType.DAILY);
//        testConfig.setRepeatMode(ReminderConstant.RepeatMode.CURRENT_DAY);
//        testConfig.setReminderTime("09:00");
//        testConfig.setStatus(ReminderConstant.Status.ENABLED);
//        testConfig.setRemark("测试提醒");
//        testConfig.setCreateBy(testCreateBy);
//        testConfig.setCreateTime(new Date());
//        testConfig.setDelFlag(0);
//    }
//
//    @Test
//    void testValidateReminderConfig_ValidDaily() {
//        // 测试有效的每天提醒配置
//        String result = reminderConfigService.validateReminderConfig(testConfig);
//        assertNull(result, "有效的每天提醒配置应该通过验证");
//    }
//
//    @Test
//    void testValidateReminderConfig_ValidWeekly() {
//        // 测试有效的每周提醒配置
//        testConfig.setReminderType(ReminderConstant.ReminderType.WEEKLY);
//        testConfig.setRepeatMode(ReminderConstant.RepeatMode.CURRENT_WEEK);
//        testConfig.setReminderDay(ReminderConstant.WeekDay.FRIDAY);
//        
//        String result = reminderConfigService.validateReminderConfig(testConfig);
//        assertNull(result, "有效的每周提醒配置应该通过验证");
//    }
//
//    @Test
//    void testValidateReminderConfig_ValidMonthly() {
//        // 测试有效的每月提醒配置
//        testConfig.setReminderType(ReminderConstant.ReminderType.MONTHLY);
//        testConfig.setRepeatMode(ReminderConstant.RepeatMode.CURRENT_MONTH);
//        testConfig.setReminderDay("15");
//        
//        String result = reminderConfigService.validateReminderConfig(testConfig);
//        assertNull(result, "有效的每月提醒配置应该通过验证");
//    }
//
//    @Test
//    void testValidateReminderConfig_InvalidReminderType() {
//        // 测试无效的提醒类型
//        testConfig.setReminderType("");
//        
//        String result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.REMINDER_TYPE_REQUIRED, result);
//        
//        testConfig.setReminderType("invalid_type");
//        result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.INVALID_REMINDER_TYPE, result);
//    }
//
//    @Test
//    void testValidateReminderConfig_InvalidRepeatMode() {
//        // 测试无效的重复方式
//        testConfig.setRepeatMode("");
//        
//        String result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.REPEAT_MODE_REQUIRED, result);
//        
//        testConfig.setRepeatMode("invalid_mode");
//        result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.INVALID_REPEAT_MODE, result);
//    }
//
//    @Test
//    void testValidateReminderConfig_InvalidReminderTime() {
//        // 测试无效的提醒时间
//        testConfig.setReminderTime("");
//        
//        String result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.REMINDER_TIME_REQUIRED, result);
//        
//        testConfig.setReminderTime("25:00");
//        result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.INVALID_REMINDER_TIME_FORMAT, result);
//        
//        testConfig.setReminderTime("09:60");
//        result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.INVALID_REMINDER_TIME_FORMAT, result);
//    }
//
//    @Test
//    void testValidateReminderConfig_WeeklyWithoutDay() {
//        // 测试每周提醒但未设置提醒日期
//        testConfig.setReminderType(ReminderConstant.ReminderType.WEEKLY);
//        testConfig.setRepeatMode(ReminderConstant.RepeatMode.CURRENT_WEEK);
//        testConfig.setReminderDay("");
//        
//        String result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.REMINDER_DAY_REQUIRED, result);
//    }
//
//    @Test
//    void testValidateReminderConfig_MonthlyWithoutDay() {
//        // 测试每月提醒但未设置提醒日期
//        testConfig.setReminderType(ReminderConstant.ReminderType.MONTHLY);
//        testConfig.setRepeatMode(ReminderConstant.RepeatMode.CURRENT_MONTH);
//        testConfig.setReminderDay("");
//        
//        String result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.REMINDER_DAY_REQUIRED, result);
//    }
//
//    @Test
//    void testValidateReminderConfig_InvalidWeekDay() {
//        // 测试无效的星期设置
//        testConfig.setReminderType(ReminderConstant.ReminderType.WEEKLY);
//        testConfig.setRepeatMode(ReminderConstant.RepeatMode.CURRENT_WEEK);
//        testConfig.setReminderDay("invalid_day");
//        
//        String result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.INVALID_WEEK_DAY, result);
//    }
//
//    @Test
//    void testValidateReminderConfig_InvalidMonthDay() {
//        // 测试无效的月份日期设置
//        testConfig.setReminderType(ReminderConstant.ReminderType.MONTHLY);
//        testConfig.setRepeatMode(ReminderConstant.RepeatMode.CURRENT_MONTH);
//        testConfig.setReminderDay("32");
//        
//        String result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.INVALID_MONTH_DAY, result);
//        
//        testConfig.setReminderDay("0");
//        result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals(ReminderConstant.ErrorMessage.INVALID_MONTH_DAY, result);
//    }
//
//    @Test
//    void testValidateReminderConfig_TypeModeMismatch() {
//        // 测试提醒类型和重复方式不匹配
//        testConfig.setReminderType(ReminderConstant.ReminderType.DAILY);
//        testConfig.setRepeatMode(ReminderConstant.RepeatMode.CURRENT_WEEK);
//        
//        String result = reminderConfigService.validateReminderConfig(testConfig);
//        assertEquals("每天提醒只能选择当天或次日", result);
//    }
//
//    @Test
//    void testAddReminderConfig_Success() {
//        // 测试成功添加提醒配置
//        when(reminderConfigMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0L);
//        
//        boolean result = reminderConfigService.addReminderConfig(testConfig, testProjectKey, testCreateBy);
//        
//        assertTrue(result, "应该成功添加提醒配置");
//        assertEquals(testProjectKey, testConfig.getProjectKey());
//        assertEquals(testCreateBy, testConfig.getCreateBy());
//        assertEquals(0, testConfig.getDelFlag());
//    }
//
//    @Test
//    void testAddReminderConfig_ValidationFailed() {
//        // 测试添加提醒配置时验证失败
//        testConfig.setReminderType("");
//        
//        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
//            reminderConfigService.addReminderConfig(testConfig, testProjectKey, testCreateBy);
//        });
//        
//        assertEquals(ReminderConstant.ErrorMessage.REMINDER_TYPE_REQUIRED, exception.getMessage());
//    }
//
//    @Test
//    void testAddReminderConfig_ConfigExists() {
//        // 测试添加重复的提醒配置
//        when(reminderConfigMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(1L);
//        
//        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
//            reminderConfigService.addReminderConfig(testConfig, testProjectKey, testCreateBy);
//        });
//        
//        assertEquals(ReminderConstant.ErrorMessage.CONFIG_ALREADY_EXISTS, exception.getMessage());
//    }
//
//    @Test
//    void testUpdateReminderConfig_Success() {
//        // 测试成功更新提醒配置
//        String updateBy = "update_user";
//        
//        boolean result = reminderConfigService.updateReminderConfig(testConfig, updateBy);
//        
//        assertTrue(result, "应该成功更新提醒配置");
//        assertEquals(updateBy, testConfig.getUpdateBy());
//        assertNotNull(testConfig.getUpdateTime());
//    }
//
//    @Test
//    void testToggleStatus_Success() {
//        // 测试成功切换状态
//        String updateBy = "update_user";
//        String newStatus = ReminderConstant.Status.DISABLED;
//        
//        boolean result = reminderConfigService.toggleStatus(testConfig.getId(), newStatus, updateBy);
//        
//        assertTrue(result, "应该成功切换状态");
//    }
//
//    @Test
//    void testToggleStatus_InvalidStatus() {
//        // 测试无效状态值
//        String updateBy = "update_user";
//        String invalidStatus = "invalid_status";
//        
//        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
//            reminderConfigService.toggleStatus(testConfig.getId(), invalidStatus, updateBy);
//        });
//        
//        assertEquals(ReminderConstant.ErrorMessage.INVALID_STATUS, exception.getMessage());
//    }
//
//    @Test
//    void testBatchDeleteReminderConfig_Success() {
//        // 测试批量删除成功
//        List<String> ids = Arrays.asList("id1", "id2", "id3");
//        String deleteBy = "delete_user";
//        
//        boolean result = reminderConfigService.batchDeleteReminderConfig(ids, deleteBy);
//        
//        assertTrue(result, "应该成功批量删除");
//    }
//
//    @Test
//    void testBatchDeleteReminderConfig_EmptyIds() {
//        // 测试空ID列表
//        List<String> emptyIds = Arrays.asList();
//        String deleteBy = "delete_user";
//        
//        boolean result = reminderConfigService.batchDeleteReminderConfig(emptyIds, deleteBy);
//        
//        assertFalse(result, "空ID列表应该返回false");
//    }
//
//    @Test
//    void testQueryByProjectKey() {
//        // 测试根据项目键查询
//        List<FsSysReminderConfig> expectedConfigs = Arrays.asList(testConfig);
//        when(reminderConfigMapper.selectByProjectKey(testProjectKey)).thenReturn(expectedConfigs);
//        
//        List<FsSysReminderConfig> result = reminderConfigService.queryByProjectKey(testProjectKey);
//        
//        assertEquals(expectedConfigs, result);
//        verify(reminderConfigMapper).selectByProjectKey(testProjectKey);
//    }
//
//    @Test
//    void testCountByProjectKey() {
//        // 测试统计项目配置数量
//        int expectedCount = 5;
//        when(reminderConfigMapper.countByProjectKey(testProjectKey)).thenReturn(expectedCount);
//        
//        int result = reminderConfigService.countByProjectKey(testProjectKey);
//        
//        assertEquals(expectedCount, result);
//        verify(reminderConfigMapper).countByProjectKey(testProjectKey);
//    }
//}
