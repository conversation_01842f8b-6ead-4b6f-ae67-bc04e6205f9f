<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.jeecg.modules.hours.mapper.FsDataTaskMapper">

    <insert id="insertOrUpdate" parameterType="org.jeecg.modules.hours.entity.FsDataTask">
        INSERT INTO fs_data_task (id, project_key, task_key, task_name, description, status, assignee, assignee_name, estimate_hours, start_time, end_time, update_time, create_time)
        VALUES (#{id}, #{projectKey}, #{taskKey}, #{taskName}, #{description}, #{status}, #{assignee}, #{assigneeName}, #{estimateHours}, #{startTime}, #{endTime}, #{updateTime}, #{createTime})
        ON DUPLICATE KEY UPDATE
        project_key = VALUES(project_key),
        task_key = VALUES(task_key),
        task_name = VALUES(task_name),
        description = VALUES(description),
        status = VALUES(status),
        assignee = VALUES(assignee),
        assignee_name = VALUES(assignee_name),
        estimate_hours = VALUES(estimate_hours),
        start_time = VALUES(start_time),
        end_time = VALUES(end_time),
        update_time = VALUES(update_time),
        create_time = VALUES(create_time)
    </insert>

    <insert id="saveOrUpdateBatch" parameterType="java.util.List">
        INSERT INTO fs_data_task (id, project_key, task_key, task_name, description, status, assignee, assignee_name, estimate_hours, start_time, end_time, update_time, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.projectKey}, #{item.taskKey}, #{item.taskName}, #{item.description}, #{item.status}, #{item.assignee}, #{item.assigneeName}, #{item.estimateHours}, #{item.startTime}, #{item.endTime}, #{item.updateTime}, #{item.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        project_key = VALUES(project_key),
        task_key = VALUES(task_key),
        task_name = VALUES(task_name),
        description = VALUES(description),
        status = VALUES(status),
        assignee = VALUES(assignee),
        assignee_name = VALUES(assignee_name),
        estimate_hours = VALUES(estimate_hours),
        start_time = VALUES(start_time),
        end_time = VALUES(end_time),
        update_time = VALUES(update_time),
        create_time = VALUES(create_time)
    </insert>

</mapper> 