package org.jeecg.modules.hours.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * @Description: 部门项目每日工时分布报表查询参数DTO
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DeptProjectDailyHourReportDTO", description = "部门项目每日工时分布报表查询参数DTO")
public class DeptProjectDailyHourReportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 查询年月 (格式: 2025-07)
     */
    @ApiModelProperty(value = "查询年月", required = true, example = "2025-07")
    @NotBlank(message = "查询年月不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "查询年月格式不正确，应为yyyy-MM")
    private String yearMonth;

    /**
     * 部门ID列表，多个用逗号分隔
     */
    @ApiModelProperty(value = "部门ID列表", example = "1,2,3")
    private String departmentIds;

    /**
     * 是否统计假日数据 (0:不统计, 1:统计)
     */
    @ApiModelProperty(value = "是否统计假日数据", example = "1")
    private Integer isHoliday;

    /**
     * 项目关键字
     */
    @ApiModelProperty(value = "项目关键字")
    private String projectKeyword;
}