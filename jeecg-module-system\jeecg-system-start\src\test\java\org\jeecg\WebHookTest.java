package org.jeecg;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.apache.commons.codec.binary.Hex;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WebHookTest {

	private static final String pluginId = "MII_686DDB0508E50003";

	private static final String token = "ac1300de-230e-4a30-8f1b-757cdc81837c";

	public static void main(String[] args) throws NoSuchAlgorithmException {

		// {log_id=20250714175811093F11E56C402E59A29C, source_plugin_version=0.0.2,
		// request_time=0,
		// signature=5992f05e2fc75781a72ad89fab28e6d5ee4a8ab32736cde26586a52d164fbf37,
		// source=normal, source_plugin_id=MII_686DDB0508E50003,
		// source_plugin_name=工时系统插件,
		// data={idempotent_key=18aa46924737fe05396a7601b245bc824bc643afb3aa27882d15ec3c46fc79e9,
		// event_type=6001, tenant_id=saas_7420348097701609473,
		// project_key=6710d7cebc5e8f1216111b2d, project_name=LEEKR}}
		// 2025-07-14 17:55:55.068 [http-nio-8080-exec-9] WARN
		// o.j.modules.hours.controller.HourPluginController:213 - 飞书插件事件签名验证失败
		String requestTime = "0";
		System.out.println("start");

		String signature = "5992f05e2fc75781a72ad89fab28e6d5ee4a8ab32736cde26586a52d164fbf37";

		String calculatedSignature = calculateSignature(pluginId, requestTime, token);
		if (!calculatedSignature.equals(signature)) {
			log.warn("飞书插件事件签名验证失败");
			return;
		}

		log.info("验签通过");

	}

	public static String calculateSignature(String pluginID, String requestTime, String token)
			throws NoSuchAlgorithmException {
		StringBuilder content = new StringBuilder();
		content.append(pluginID).append(requestTime).append(token);
		MessageDigest alg = MessageDigest.getInstance("SHA-256");
		String sign = Hex.encodeHexString(alg.digest(content.toString().getBytes()));
		return sign;
	}

}
