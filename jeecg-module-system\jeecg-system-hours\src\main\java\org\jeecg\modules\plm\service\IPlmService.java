package org.jeecg.modules.plm.service;

import java.util.List;

import org.jeecg.modules.plm.vo.PlmProjectVO;

/**
 * PLM服务接口
 */
public interface IPlmService {

    /**
     * 获取PLM项目列表(全量)
     * 
     * @return PLM项目列表
     */
    List<PlmProjectVO> getProjectList();
    
    /**
     * 获取PLM项目列表(增量)
     * 
     * @param dateFrom 起始日期 (yyyy-MM-dd)
     * @return PLM项目列表
     */
    List<PlmProjectVO> getIncrementalProjectList(String dateFrom);
    
} 