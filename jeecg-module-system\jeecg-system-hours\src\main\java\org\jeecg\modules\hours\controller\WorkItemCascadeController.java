//package org.jeecg.modules.hours.controller;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.util.HashSet;
//import java.util.Set;
//import java.util.Optional;
//
//import org.apache.commons.lang3.StringUtils;
//import org.apache.shiro.util.CollectionUtils;
//import org.jeecg.common.api.vo.Result;
//import org.jeecg.modules.hours.constant.ConfigKeyConstant;
//import org.jeecg.modules.hours.entity.FsInstanceCache;
//import org.jeecg.modules.hours.entity.FsNodeCache;
//import org.jeecg.modules.hours.entity.FsTaskCache;
//import org.jeecg.modules.hours.entity.FsWorkItemCache;
//import org.jeecg.modules.hours.entity.FsDataTask;
//import org.jeecg.modules.hours.service.IFsCacheService;
//import org.jeecg.modules.hours.service.ISysConfigService;
//import org.jeecg.modules.hours.service.IFsDataTaskService;
//import org.jeecg.modules.hours.service.IHourRecordService;
//import org.jeecg.modules.hours.vo.WorkItemStatisticsVO;
//import org.jeecg.modules.hours.util.RequestHeaderUtil;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiParam;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * @Description: 工作项级联接口
// * @Author: jeecg-boot
// * @Date: 2023-08-21
// * @Version: V1.0
// */
//@Api(tags="工作项级联选择")
//@RestController
//@RequestMapping("/hapi/hours/workItemCascade")
//@Slf4j
//public class WorkItemCascadeController {
//    
//    @Autowired
//    private IFsCacheService fsCacheService;
//    
//    @Autowired
//    private ISysConfigService sysConfigService;
//    
//    @Autowired
//    private IFsDataTaskService fsDataTaskService;
//    
//    @Autowired
//    private IHourRecordService hourRecordService;
//    
//    /**
//     * 获取工作项列表
//     */
//    @ApiOperation(value="获取工作项列表", notes="获取工作项列表，第一级下拉选项")
//    @GetMapping(value = "/workItems")
//    public Result<List<Map<String, Object>>> getWorkItems() {
//        // 从请求头获取项目Key
//        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
//        
//        // 检查项目Key是否为空
//        if (projectKey == null) {
//            return Result.error("未提供有效的项目Key");
//        }
//        
//        log.info("获取项目[{}]的工作项列表", projectKey);
//        
//        // 1. 获取所有工作项
//        List<FsWorkItemCache> workItems = fsCacheService.getWorkItems(projectKey);
//        if(CollectionUtils.isEmpty(workItems)) {
//        	return Result.OK(new ArrayList<Map<String,Object>>());
//        }
//        
//        
//        // 2. 获取系统配置的工作项类型过滤
//        String configValue = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.WORK_CONTENT_RANGE);
//        
//        // 3. 如果配置了工作项类型，进行过滤
//        if (StringUtils.isNotBlank(configValue)) {
//            // 创建一个新的列表来存储过滤后的工作项
//            final List<String> allowedItemTypes = Arrays.asList(configValue.split(","))
//                .stream()
//                .map(String::trim)
//                .collect(Collectors.toList());
//                
//            log.info("项目[{}]配置的工作项类型过滤: {}", projectKey, allowedItemTypes);
//            
//            if (!allowedItemTypes.isEmpty()) {
//                workItems = workItems.stream()
//                    .filter(item -> allowedItemTypes.contains(item.getItemKey()))
//                    .collect(Collectors.toList());
//            }
//        }
//        
//        // 4. 提前获取所有有实例的工作项ID集合
//        List<String> allWorkItemsWithInstances = new ArrayList<>();
//        List<FsInstanceCache> allInstances = fsCacheService.getAllInstances(projectKey);
//        for (FsInstanceCache instance : allInstances) {
//            String workItemKey = instance.getWorkItemKey();
//            if (!allWorkItemsWithInstances.contains(workItemKey)) {
//                allWorkItemsWithInstances.add(workItemKey);
//            }
//        }
//        
//        // 5. 构建结果
//        List<Map<String, Object>> result = new ArrayList<>(workItems.size());
//        
//        for (FsWorkItemCache item : workItems) {
//            Map<String, Object> map = new HashMap<>(5);
//            map.put("value", item.getItemKey());
//            map.put("label", item.getName());
//            map.put("itemType", item.getItemType());
//            map.put("status", item.getStatus());
//            
//            // 检查是否有下级数据（实例），使用预先获取的集合
//            boolean hasInstances = allWorkItemsWithInstances.contains(item.getItemKey());
//            map.put("isLeaf", !hasInstances); // 如果没有实例，则为叶子节点
//            
//            result.add(map);
//        }
//        
//        return Result.OK(result);
//    }
//    
//    /**
//     * 获取实例列表（支持分页）
//     */
//    @ApiOperation(value="获取实例列表", notes="获取实例列表，第二级下拉选项，支持分页")
//    @GetMapping(value = "/instances")
//    public Result<Map<String, Object>> getInstances(
//            @ApiParam(value = "工作项Key", required = true) @RequestParam(name="workItemKey") String workItemKey,
//            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//            @ApiParam(value = "每页条数", defaultValue = "10") @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
//            @ApiParam(value = "搜索关键词") @RequestParam(name="keyword", required=false) String keyword) {
//        
//        // 从请求头获取项目Key
//        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
//        
//        // 检查项目Key是否为空
//        if (projectKey == null) {
//            return Result.error("未提供有效的项目Key");
//        }
//        
//        log.info("获取项目[{}]工作项[{}]的实例列表, 页码: {}, 每页条数: {}, 关键词: {}", 
//                projectKey, workItemKey, pageNo, pageSize, keyword);
//        
//        // 获取所有匹配的实例数据
//        List<FsInstanceCache> allInstances = fsCacheService.getInstances(projectKey, workItemKey);
//        
//        // 筛选符合关键词条件的记录
//        int total = allInstances.size();
//        if (keyword != null && !keyword.trim().isEmpty()) {
//            String finalKeyword = keyword.trim().toLowerCase();
//            allInstances.removeIf(instance -> !instance.getName().toLowerCase().contains(finalKeyword));
//        }
//        
//        // 处理分页逻辑
//        int fromIndex = (pageNo - 1) * pageSize;
//        int toIndex = Math.min(fromIndex + pageSize, allInstances.size());
//        
//        List<FsInstanceCache> instances;
//        if (fromIndex < allInstances.size()) {
//            instances = allInstances.subList(fromIndex, toIndex);
//        } else {
//            instances = new ArrayList<>();
//        }
//        
//        // 提前获取所有有节点的实例ID集合
//        List<String> allInstancesWithNodes = new ArrayList<>();
//        List<FsNodeCache> allNodes = fsCacheService.getAllNodes(projectKey);
//        for (FsNodeCache node : allNodes) {
//            String instanceKey = node.getInstanceKey();
//            if (!allInstancesWithNodes.contains(instanceKey)) {
//                allInstancesWithNodes.add(instanceKey);
//            }
//        }
//        
//        // 转换为前端需要的格式
//        List<Map<String, Object>> list = new ArrayList<>(instances.size());
//        for (FsInstanceCache instance : instances) {
//            Map<String, Object> map = new HashMap<>(5);
//            map.put("value", instance.getInstanceKey());
//            map.put("label", instance.getName());
//            map.put("status", instance.getStatus());
//            
//            // 检查是否有下级数据（节点），使用预先获取的集合
//            boolean hasNodes = allInstancesWithNodes.contains(instance.getInstanceKey());
//            map.put("isLeaf", !hasNodes); // 如果没有节点，则为叶子节点
//            
//            list.add(map);
//        }
//        
//        // 构建返回的分页结果
//        Map<String, Object> result = new HashMap<>(4);
//        result.put("records", list);
//        result.put("total", total);
//        result.put("pageNo", pageNo);
//        result.put("pageSize", pageSize);
//        
//        return Result.OK(result);
//    }
//    
//    /**
//     * 获取节点列表
//     */
//    @ApiOperation(value="获取节点列表", notes="获取节点列表，第三级下拉选项")
//    @GetMapping(value = "/nodes")
//    public Result<List<Map<String, Object>>> getNodes(
//            @ApiParam(value = "实例Key", required = true) @RequestParam(name="instanceKey") String instanceKey,
//            @ApiParam(value = "工作项Key", required = false) @RequestParam(name="workItemKey", required=false) String workItemKey) {
//        
//        // 从请求头获取项目Key
//        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
//        
//        // 检查项目Key是否为空
//        if (projectKey == null) {
//            return Result.error("未提供有效的项目Key");
//        }
//        
//        log.info("获取项目[{}]工作项[{}]实例[{}]的节点列表", projectKey, workItemKey, instanceKey);
//        
//        List<FsNodeCache> nodes = fsCacheService.getNodes(projectKey, instanceKey);
//        List<Map<String, Object>> result = new ArrayList<>(nodes.size());
//        
//        // 提前获取所有有任务的节点ID集合，如果提供了workItemKey则进行过滤
//        List<String> allNodesWithTasks = new ArrayList<>();
//        List<FsTaskCache> allTasks = fsCacheService.getAllTasks(projectKey);
//        for (FsTaskCache task : allTasks) {
//            // 如果提供了workItemKey，只考虑该工作项下的任务
//            if (workItemKey != null && !workItemKey.equals(task.getWorkItemKey())) {
//                continue;
//            }
//            // 还需要检查实例是否匹配
//            if (!instanceKey.equals(task.getInstanceKey())) {
//                continue;
//            }
//
//            String nodeKey = task.getNodeKey();
//            if (!allNodesWithTasks.contains(nodeKey)) {
//                allNodesWithTasks.add(nodeKey);
//            }
//        }
//        
//        for (FsNodeCache node : nodes) {
//            Map<String, Object> map = new HashMap<>(5);
//            map.put("value", node.getNodeKey());
//            map.put("label", node.getNodeName());
//            map.put("status", node.getStatus());
//            map.put("workflowId", node.getWorkflowId());
//            
//            // 检查是否有下级数据（任务），使用预先获取的集合
//            boolean hasTasks = allNodesWithTasks.contains(node.getNodeKey());
//            map.put("isLeaf", !hasTasks); // 如果没有任务，则为叶子节点
//            
//            result.add(map);
//        }
//        
//        return Result.OK(result);
//    }
//    
//    /**
//     * 获取任务列表（支持分页）
//     */
//    @ApiOperation(value="获取任务列表", notes="获取任务列表，第四级下拉选项，支持分页")
//    @GetMapping(value = "/tasks")
//    public Result<Map<String, Object>> getTasks(
//            @ApiParam(value = "工作项Key", required = true) @RequestParam(name="workItemKey") String workItemKey,
//            @ApiParam(value = "实例Key", required = true) @RequestParam(name="instanceKey") String instanceKey,
//            @ApiParam(value = "节点Key", required = true) @RequestParam(name="nodeKey") String nodeKey,
//            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//            @ApiParam(value = "每页条数", defaultValue = "10") @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
//            @ApiParam(value = "搜索关键词") @RequestParam(name="keyword", required=false) String keyword) {
//        
//        // 从请求头获取项目Key
//        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
//        
//        // 检查项目Key是否为空
//        if (projectKey == null) {
//            return Result.error("未提供有效的项目Key");
//        }
//        
//        log.info("获取项目[{}]节点[{}]的任务列表, 页码: {}, 每页条数: {}, 关键词: {}", 
//                projectKey, nodeKey, pageNo, pageSize, keyword);
//        
//        try {
//            // 验证层级关系的正确性
//            FsNodeCache node = fsCacheService.getNodeDetail(projectKey, workItemKey, instanceKey, nodeKey);
//            if (node == null) {
//                return Result.error("无效的节点");
//            }
//            
//            // 获取所有任务
//            List<FsTaskCache> allTasks = fsCacheService.getTasksByKeys(projectKey,workItemKey,instanceKey,nodeKey);
//            
//            // 使用完整的层级路径过滤任务
//            String hierarchyKey = String.format("%s_%s_%s", workItemKey, instanceKey, nodeKey);
//            List<FsTaskCache> filteredTasks = allTasks.stream()
//                .filter(task -> {
//                    // 获取任务的完整层级路径
//                    FsNodeCache taskNode = fsCacheService.getNodeDetail(projectKey, workItemKey, instanceKey, task.getNodeKey());
//                    if (taskNode == null) return false;
//                    
//                    String taskHierarchyKey = String.format("%s_%s_%s", 
//                        workItemKey, 
//                        instanceKey, 
//                        task.getNodeKey());
//                    
//                    return hierarchyKey.equals(taskHierarchyKey);
//                })
//                .collect(Collectors.toList());
//            
//            // 添加关键词搜索条件
//            if (keyword != null && !keyword.trim().isEmpty()) {
//                String finalKeyword = keyword.trim().toLowerCase();
//                filteredTasks = filteredTasks.stream()
//                    .filter(task -> task.getTaskName().toLowerCase().contains(finalKeyword))
//                    .collect(Collectors.toList());
//            }
//            
//            // 计算总记录数
//            int total = filteredTasks.size();
//            
//            // 处理分页逻辑
//            int fromIndex = (pageNo - 1) * pageSize;
//            int toIndex = Math.min(fromIndex + pageSize, total);
//            
//            List<FsTaskCache> pagedTasks;
//            if (fromIndex < total) {
//                pagedTasks = filteredTasks.subList(fromIndex, toIndex);
//            } else {
//                pagedTasks = new ArrayList<>();
//            }
//            
//            // 转换为前端需要的格式
//            List<Map<String, Object>> list = new ArrayList<>(pagedTasks.size());
//            for (FsTaskCache task : pagedTasks) {
//                Map<String, Object> map = new HashMap<>(8);
//                map.put("value", task.getTaskKey());
//                map.put("label", task.getTaskName());
//                map.put("status", task.getStatus());
//                map.put("assignee", task.getAssignee());
//                map.put("assigneeName", task.getAssigneeName());
//                map.put("estimateHours", task.getEstimateHours());
//                map.put("isLeaf", true);
//                
//                list.add(map);
//            }
//            
//            // 构建返回的分页结果
//            Map<String, Object> result = new HashMap<>(4);
//            result.put("records", list);
//            result.put("total", total);
//            result.put("pageNo", pageNo);
//            result.put("pageSize", pageSize);
//            
//            return Result.OK(result);
//            
//        } catch (Exception e) {
//            log.error("获取任务列表失败", e);
//            return Result.error("获取任务列表失败：" + e.getMessage());
//        }
//    }
//    
//    /**
//     * 获取完整四级联动数据的详情
//     */
//    @ApiOperation(value="获取四级联动数据详情", notes="通过各级KEY获取四级联动数据的详细信息")
//    @GetMapping(value = "/detail")
//    public Result<Map<String, Object>> getCascadeDetail(
//            @ApiParam(value = "工作项Key", required = true) @RequestParam(name="workItemKey") String workItemKey,
//            @ApiParam(value = "实例Key", required = false) @RequestParam(name="instanceKey", required=false) String instanceKey,
//            @ApiParam(value = "节点Key", required = false) @RequestParam(name="nodeKey", required=false) String nodeKey,
//            @ApiParam(value = "任务Key", required = false) @RequestParam(name="taskKey", required=false) String taskKey) {
//        
//        // 从请求头获取项目Key
//        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
//        
//        // 检查项目Key是否为空
//        if (projectKey == null) {
//            return Result.error("未提供有效的项目Key");
//        }
//        
//        log.info("获取四级联动数据详情: 项目[{}], 工作项[{}], 实例[{}], 节点[{}], 任务[{}]", 
//                projectKey, workItemKey, instanceKey, nodeKey, taskKey);
//        
//        Map<String, Object> result = new HashMap<>(5);
//        
//        // 获取工作项详情
//        FsWorkItemCache workItem = fsCacheService.getWorkItemDetail(projectKey, workItemKey);
//        if (workItem != null) {
//            Map<String, Object> workItemMap = new HashMap<>(3);
//            workItemMap.put("key", workItem.getItemKey());
//            workItemMap.put("name", workItem.getName());
//            workItemMap.put("type", workItem.getItemType());
//            result.put("workItem", workItemMap);
//        }
//        
//        // 获取实例详情
//        if (instanceKey != null && !instanceKey.isEmpty()) {
//            FsInstanceCache instance = fsCacheService.getInstanceDetail(projectKey, instanceKey);
//            if (instance != null) {
//                Map<String, Object> instanceMap = new HashMap<>(2);
//                instanceMap.put("key", instance.getInstanceKey());
//                instanceMap.put("name", instance.getName());
//                result.put("instance", instanceMap);
//            }
//        }
//        
//        // 获取节点详情
//        if (nodeKey != null && !nodeKey.isEmpty()) {
//            FsNodeCache node = fsCacheService.getNodeDetail(projectKey,workItemKey,instanceKey, nodeKey);
//            if (node != null) {
//                Map<String, Object> nodeMap = new HashMap<>(2);
//                nodeMap.put("key", node.getNodeKey());
//                nodeMap.put("name", node.getNodeName());
//                result.put("node", nodeMap);
//            }
//        }
//        
//        // 获取任务详情
//        if (taskKey != null && !taskKey.isEmpty()) {
//            FsTaskCache task = fsCacheService.getTaskDetail(projectKey, taskKey);
//            if (task != null) {
//                Map<String, Object> taskMap = new HashMap<>(3);
//                taskMap.put("key", task.getTaskKey());
//                taskMap.put("name", task.getTaskName());
//                taskMap.put("assigneeName", task.getAssigneeName());
//                result.put("task", taskMap);
//            }
//        }
//        
//        return Result.OK(result);
//    }
//    
//    /**
//     * 获取工作项统计信息
//     */
//    @ApiOperation(value="获取工作项统计信息", notes="根据工作项、实例、节点、任务查询统计信息，包含排期、预估工时、已登记工时")
//    @GetMapping(value = "/statistics")
//    public Result<WorkItemStatisticsVO> getStatistics(
//            @ApiParam(value = "工作项Key", required = true) @RequestParam(name="workItemKey") String workItemKey,
//            @ApiParam(value = "实例Key") @RequestParam(name="instanceKey", required=false) String instanceKey,
//            @ApiParam(value = "节点Key") @RequestParam(name="nodeKey", required=false) String nodeKey,
//            @ApiParam(value = "任务Key") @RequestParam(name="taskKey", required=false) String taskKey) {
//        
////        // 从请求头获取项目Key
////        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
////        
////        // 检查项目Key是否为空
////        if (projectKey == null) {
////            return Result.error("未提供有效的项目Key");
////        }
////        
////        log.info("获取统计信息: 项目[{}], 工作项[{}], 实例[{}], 节点[{}], 任务[{}]", 
////                projectKey, workItemKey, instanceKey, nodeKey, taskKey);
////        
////        // 创建返回对象
////        WorkItemStatisticsVO statistics = new WorkItemStatisticsVO();
////        
////        // 获取每天标准工时配置
////        String workHourPerDayStr = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.WORK_HOUR_PER_DAY);
////        BigDecimal workHourPerDay = StringUtils.isNotBlank(workHourPerDayStr) ? 
////            new BigDecimal(workHourPerDayStr) : new BigDecimal("8");
////        
////        // 根据参数获取排期和预估工时
////        if (StringUtils.isNotBlank(taskKey)) {
////            // 如果有任务Key，从任务表获取
////            FsDataTask task = fsDataTaskService.getById(taskKey);
////            if (task != null) {
////                statistics.setScheduleStartTime(task.getStartTime())
////                         .setScheduleEndTime(task.getEndTime())
////                         .setEstimateHours(task.getEstimateHours());
////                
////                // 计算预估工时(天)
////                if (task.getEstimateHours() != null) {
////                    statistics.setEstimateDays(
////                        task.getEstimateHours().divide(workHourPerDay, 2, RoundingMode.HALF_UP)
////                    );
////                }
////            }
////        } else if (StringUtils.isNotBlank(nodeKey)) {
////            // 如果有节点Key，从节点缓存表获取
////            FsNodeCache node = fsCacheService.getNodeDetail(projectKey, workItemKey, instanceKey, nodeKey);
////            if (node != null) {
////                statistics.setScheduleStartTime(node.getScheduleStartTime())
////                         .setScheduleEndTime(node.getScheduleEndTime())
////                         .setEstimateHours(node.getEstimateHours());
////                
////                // 计算预估工时(天)
////                if (node.getEstimateHours() != null) {
////                    statistics.setEstimateDays(
////                        node.getEstimateHours().divide(workHourPerDay, 2, RoundingMode.HALF_UP)
////                    );
////                }
////            }
////        } else if (StringUtils.isNotBlank(instanceKey)) {
////            // 【增强】实例层级统计逻辑
////            // 先查实例缓存
////            if ("sub_task".equals(workItemKey)) {
////                // 如果是sub_task类型，从FsDataTask获取排期和工时
////                FsDataTask task = fsDataTaskService.getById(instanceKey);
////                if (task != null) {
////                    statistics.setScheduleStartTime(task.getStartTime())
////                               .setScheduleEndTime(task.getEndTime())
////                               .setEstimateHours(task.getEstimateHours());
////                    if (task.getEstimateHours() != null) {
////                        statistics.setEstimateDays(
////                            task.getEstimateHours().divide(workHourPerDay, 2, RoundingMode.HALF_UP)
////                        );
////                    }
////                }
////            } else {
////            	FsInstanceCache instance = fsCacheService.getInstanceDetail(projectKey, instanceKey);
////                // 普通实例，从FsInstanceCache获取排期和工时
////                statistics.setScheduleStartTime(instance.getScheduleStartTime())
////                           .setScheduleEndTime(instance.getScheduleEndTime())
////                           .setEstimateHours(instance.getEstimateHours());
////                if (instance.getEstimateHours() != null) {
////                    statistics.setEstimateDays(
////                        instance.getEstimateHours().divide(workHourPerDay, 2, RoundingMode.HALF_UP)
////                    );
////                }
////            }
////        }
////        
////        // 获取已登记工时
////        BigDecimal recordedHours = hourRecordService.getRecordedHours(projectKey, workItemKey, instanceKey, nodeKey, taskKey);
////        statistics.setRecordedHours(recordedHours);
////        
////        // 计算已登记工时(天)
////        if (recordedHours != null) {
////            statistics.setRecordedDays(
////                recordedHours.divide(workHourPerDay, 2, RoundingMode.HALF_UP)
////            );
////        }
////        
////        return Result.OK(statistics);
//    	
//    	return Result.OK(null);
//    }
//    
//    /**
//     * 获取批量登记工时的表格数据
//     */
//    @ApiOperation(value="获取批量登记工时的表格数据", notes="根据工作日期获取可登记工时的工作项列表，包含树形结构")
//    @GetMapping(value = "/getBatchRecordItems")
//    public Result<IPage<Map<String, Object>>> getBatchRecordItems(
//            @ApiParam(value = "工作日期", required = true) @RequestParam(name="workDate") String workDate,
//            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//            @ApiParam(value = "每页条数", defaultValue = "10") @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
//        
//        // 从请求头获取项目Key
//        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
//        
//        // 检查项目Key是否为空
//        if (projectKey == null) {
//            return Result.error("未提供有效的项目Key");
//        }
//        
//        log.info("获取批量登记工时数据: 项目[{}], 工作日期[{}], 页码[{}], 每页条数[{}]", 
//                projectKey, workDate, pageNo, pageSize);
//        
//        try {
//            // 1. 获取所有数据（不分页）
//            List<Map<String, Object>> allRecords = new ArrayList<>();
//            
//            // 2. 获取工作项类型过滤配置
//            final String configValue = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.WORK_CONTENT_RANGE);
//            final List<String> allowedItemTypes = StringUtils.isNotBlank(configValue) ?
//                Arrays.asList(configValue.split(",")).stream()
//                    .map(String::trim)
//                    .collect(Collectors.toList()) :
//                new ArrayList<>();
//            
//            // 3. 获取工作项列表并根据类型过滤
//            List<FsWorkItemCache> workItems = fsCacheService.getWorkItems(projectKey);
//            if (!allowedItemTypes.isEmpty()) {
//                workItems = workItems.stream()
//                    .filter(item -> allowedItemTypes.contains(item.getItemKey()))
//                    .collect(Collectors.toList());
//            }
//            
//            // 4. 获取所有工作项的实例
//            List<String> workItemKeys = workItems.stream()
//                .map(FsWorkItemCache::getItemKey)
//                .collect(Collectors.toList());
//            
//            // 批量获取实例
//            List<FsInstanceCache> allInstances = fsCacheService.getAllInstances(projectKey);
//            Map<String, List<FsInstanceCache>> instancesMap = allInstances.stream()
//                .filter(instance -> workItemKeys.contains(instance.getWorkItemKey()))
//                .collect(Collectors.groupingBy(FsInstanceCache::getWorkItemKey));
//            
//            // 5. 获取所有实例的节点
//            List<String> instanceKeys = allInstances.stream()
//                .map(FsInstanceCache::getInstanceKey)
//                .collect(Collectors.toList());
//            
//            // 批量获取节点
//            List<FsNodeCache> allNodes = fsCacheService.getAllNodes(projectKey);
//            Map<String, List<FsNodeCache>> nodeMap = allNodes.stream()
//                .filter(node -> instanceKeys.contains(node.getInstanceKey()))
//                .collect(Collectors.groupingBy(FsNodeCache::getInstanceKey));
//            
//            // 6. 获取所有节点的任务
//            List<String> nodeKeys = allNodes.stream()
//                .map(FsNodeCache::getNodeKey)
//                .collect(Collectors.toList());
//            
//            // 批量获取任务
//            List<FsTaskCache> allTasks = fsCacheService.getAllTasks(projectKey);
//            
//            // 对任务进行预处理，使用完整层级路径作为key
//            Map<String, List<FsTaskCache>> taskMap = new HashMap<>();
//            
//            for (FsTaskCache task : allTasks) {
//                // 构建完整的层级路径key
//                String hierarchyKey = String.format("%s_%s_%s", task.getWorkItemKey(), task.getInstanceKey(), task.getNodeKey());
//                
//                // 将任务添加到对应层级的列表中
//                taskMap.computeIfAbsent(hierarchyKey, k -> new ArrayList<>())
//                      .add(task);
//            }
//            
//            // 获取任务key列表
//            List<String> taskKeys = allTasks.stream()
//                .map(FsTaskCache::getTaskKey)
//                .collect(Collectors.toList());
//
//            // 批量获取已登记工时数据
//            Map<String, BigDecimal> recordedHoursMap = hourRecordService.getRecordedHoursBatch(
//                projectKey, workItemKeys, instanceKeys, nodeKeys, taskKeys);
//            
//            // 8. 构建一级数据（实例级别）
//            for (FsWorkItemCache workItem : workItems) {
//                List<FsInstanceCache> instances = instancesMap.getOrDefault(workItem.getItemKey(), new ArrayList<>());
//                for (FsInstanceCache instance : instances) {
//                    Map<String, Object> instanceData = new HashMap<>();
//                    // 实例基本信息
//                    instanceData.put("key", instance.getInstanceKey());
//                    instanceData.put("title", instance.getName());
//                    instanceData.put("type", "instance");
//                    // 添加工作项信息
//                    instanceData.put("workItemId", workItem.getItemKey());
//                    instanceData.put("workItemName", workItem.getName());
//                    // 直接从节点缓存获取数据
//                    instanceData.put("scheduleStartTime", instance.getScheduleStartTime());
//                    instanceData.put("scheduleEndTime", instance.getScheduleEndTime());
//                    instanceData.put("estimateHours", instance.getEstimateHours());
//                    
//                    // 获取实例的已登记工时
//                    BigDecimal instanceHours = recordedHoursMap.getOrDefault(instance.getInstanceKey(), BigDecimal.ZERO);
//                    instanceData.put("recordedHours", instanceHours);
//                    
//                    List<Map<String, Object>> nodeChildren = new ArrayList<>();
//                    List<FsNodeCache> nodes = nodeMap.getOrDefault(instance.getInstanceKey(), new ArrayList<>());
//                    for (FsNodeCache node : nodes) {
//                        Map<String, Object> nodeMapItem = new HashMap<>();
//                        // 节点基本信息
//                        nodeMapItem.put("key", node.getNodeKey());
//                        nodeMapItem.put("title", node.getNodeName());
//                        nodeMapItem.put("type", "node");
//                        // 添加工作项信息
//                        nodeMapItem.put("workItemId", workItem.getItemKey());
//                        nodeMapItem.put("workItemName", workItem.getName());
//                        
//                        // 直接从节点缓存获取数据
//                        nodeMapItem.put("scheduleStartTime", node.getScheduleStartTime());
//                        nodeMapItem.put("scheduleEndTime", node.getScheduleEndTime());
//                        nodeMapItem.put("estimateHours", node.getEstimateHours());
//                        
//                        // 获取节点的已登记工时
//                        BigDecimal nodeHours = recordedHoursMap.getOrDefault(node.getNodeKey(), BigDecimal.ZERO);
//                        nodeMapItem.put("recordedHours", nodeHours);
//                        
//                        // 使用完整的层级路径获取任务列表
//                        String instanceKey = node.getInstanceKey();
//                        Optional<FsInstanceCache> instanceOpt = allInstances.stream()
//                            .filter(i -> i.getInstanceKey().equals(instanceKey))
//                            .findFirst();
//                        
//                        if (instanceOpt.isPresent()) {
//                            String workItemKey = instanceOpt.get().getWorkItemKey();
//                            String hierarchyKey = String.format("%s_%s_%s", workItemKey, instanceKey, node.getNodeKey());
//                            List<FsTaskCache> tasks = taskMap.getOrDefault(hierarchyKey, new ArrayList<>());
//                            
//                            List<Map<String, Object>> taskChildren = new ArrayList<>();
//                            for (FsTaskCache task : tasks) {
//                                Map<String, Object> taskMapItem = new HashMap<>();
//                                // 任务基本信息
//                                taskMapItem.put("key", task.getTaskKey());
//                                taskMapItem.put("title", task.getTaskName());
//                                taskMapItem.put("type", "task");
//                                taskMapItem.put("assignee", task.getAssignee());
//                                taskMapItem.put("assigneeName", task.getAssigneeName());
//                                // 添加工作项信息
//                                taskMapItem.put("workItemId", workItem.getItemKey());
//                                taskMapItem.put("workItemName", workItem.getName());
//                                
//                                // 获取任务数据
//                                FsDataTask taskData = fsDataTaskService.getById(task.getTaskKey());
//                                if (taskData != null) {
//                                    taskMapItem.put("scheduleStartTime", taskData.getStartTime());
//                                    taskMapItem.put("scheduleEndTime", taskData.getEndTime());
//                                    taskMapItem.put("estimateHours", taskData.getEstimateHours());
//                                }
//                                
//                                // 获取任务的已登记工时
//                                BigDecimal taskHours = recordedHoursMap.getOrDefault(task.getTaskKey(), BigDecimal.ZERO);
//                                taskMapItem.put("recordedHours", taskHours);
//                                
//                                taskChildren.add(taskMapItem);
//                            }
//                            
//                            if (!taskChildren.isEmpty()) {
//                                nodeMapItem.put("children", taskChildren);
//                            }
//                        }
//                        
//                        nodeChildren.add(nodeMapItem);
//                    }
//                    
//                    if (!nodeChildren.isEmpty()) {
//                        instanceData.put("children", nodeChildren);
//                    }
//                    allRecords.add(instanceData);
//                }
//            }
//            
//            // 10. 对一级数据（实例级别）进行分页
//            int total = allRecords.size();
//            int fromIndex = (pageNo - 1) * pageSize;
//            int toIndex = Math.min(fromIndex + pageSize, total);
//            
//            List<Map<String, Object>> pagedRecords;
//            if (fromIndex < total) {
//                pagedRecords = allRecords.subList(fromIndex, toIndex);
//            } else {
//                pagedRecords = new ArrayList<>();
//            }
//            
//            // 11. 创建分页对象
//            Page<Map<String, Object>> page = new Page<>(pageNo, pageSize);
//            page.setRecords(pagedRecords);
//            page.setTotal(total);
//            
//            return Result.OK(page);
//            
//        } catch (Exception e) {
//            log.error("获取批量登记工时数据失败", e);
//            return Result.error("获取数据失败：" + e.getMessage());
//        }
//    }
//
//    // 添加 NodePath 类来保存节点的完整路径
//    private static class NodePath {
//        private final String workItemKey;
//        private final String instanceKey;
//        private final String nodeKey;
//        
//        public NodePath(String workItemKey, String instanceKey, String nodeKey) {
//            this.workItemKey = workItemKey;
//            this.instanceKey = instanceKey;
//            this.nodeKey = nodeKey;
//        }
//        
//        public String getWorkItemKey() {
//            return workItemKey;
//        }
//        
//        public String getInstanceKey() {
//            return instanceKey;
//        }
//        
//        public String getNodeKey() {
//            return nodeKey;
//        }
//    }
//} 