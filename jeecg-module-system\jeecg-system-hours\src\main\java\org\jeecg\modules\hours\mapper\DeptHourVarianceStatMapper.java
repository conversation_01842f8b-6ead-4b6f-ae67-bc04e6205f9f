package org.jeecg.modules.hours.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.dto.DeptVarianceDataDTO;
import org.jeecg.modules.hours.entity.DeptHourVarianceStat;

import java.util.List;

@Mapper
public interface DeptHourVarianceStatMapper extends BaseMapper<DeptHourVarianceStat> {

    /**
     * 计算指定年月的部门工时差值数据
     * @param year 年份
     * @param month 月份
     * @param includeHoliday 是否包含假日
     * @return 部门工时差值统计数据
     */
    List<DeptVarianceDataDTO> calculateMonthlyVarianceData(@Param("year") int year, 
                                                          @Param("month") int month, 
                                                          @Param("includeHoliday") boolean includeHoliday);
}