<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.plm.mapper.PlmProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.jeecg.modules.plm.entity.PlmProject">
        <id column="id" property="id"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="project_status" property="projectStatus"/>
        <result column="project_level" property="projectLevel"/>
        <result column="project_desc" property="projectDesc"/>
        <result column="manager_id" property="managerId"/>
        <result column="manager_name" property="managerName"/>
        <result column="sync_time" property="syncTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_code, project_name, project_status, project_level, project_desc, manager_id, manager_name, sync_time, create_time, update_time, create_by, update_by, remark
    </sql>

    <!-- 分页查询项目列表 -->
    <select id="queryProjectList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plm_project
        WHERE 1=1
        ORDER BY create_time DESC
    </select>

    <!-- 根据项目编码查询项目 -->
    <select id="queryByProjectCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plm_project
        WHERE project_code = #{projectCode}
        LIMIT 1
    </select>

    <!-- 统计项目状态分布 -->
    <select id="statisticsByStatus" resultType="java.util.Map">
        SELECT
            project_status as status,
            COUNT(1) as count
        FROM plm_project
        GROUP BY project_status
    </select>
</mapper> 