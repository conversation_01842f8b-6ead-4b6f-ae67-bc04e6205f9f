package org.jeecg.modules.hours.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.hours.entity.FsNodeCache;
import org.jeecg.modules.hours.mapper.FsNodeCacheMapper;
import org.jeecg.modules.hours.service.IFsDataAggregateService;
import org.jeecg.modules.hours.service.IFsNodeCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 飞书项目节点缓存表实现类
 * @Author: jeecg-boot
 * @Date: 2023-08-20
 * @Version: V1.0
 */
@Slf4j
@Service
public class FsNodeCacheServiceImpl extends ServiceImpl<FsNodeCacheMapper, FsNodeCache> implements IFsNodeCacheService {

    @Autowired
    private IFsDataAggregateService fsDataAggregateService;

    @Override
    public List<FsNodeCache> getNodesByProjectAndInstance(String projectKey, String instanceKey) {
        LambdaQueryWrapper<FsNodeCache> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(projectKey), FsNodeCache::getProjectKey, projectKey);
        queryWrapper.eq(StringUtils.isNotBlank(instanceKey), FsNodeCache::getInstanceKey, instanceKey);
        queryWrapper.orderByDesc(FsNodeCache::getNodeKey);
        return this.list(queryWrapper);
    }

    @Override
    public FsNodeCache getNodeDetail(String projectKey, String instanceKey, String nodeKey) {
        LambdaQueryWrapper<FsNodeCache> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(projectKey), FsNodeCache::getProjectKey, projectKey);
        queryWrapper.eq(StringUtils.isNotBlank(instanceKey), FsNodeCache::getInstanceKey, instanceKey);
        queryWrapper.eq(StringUtils.isNotBlank(nodeKey), FsNodeCache::getNodeKey, nodeKey);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<FsNodeCache> getNodesByWorkflow(String projectKey, String workflowId) {
        LambdaQueryWrapper<FsNodeCache> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(projectKey), FsNodeCache::getProjectKey, projectKey);
        queryWrapper.eq(StringUtils.isNotBlank(workflowId), FsNodeCache::getWorkflowId, workflowId);
        queryWrapper.orderByDesc(FsNodeCache::getNodeKey);
        return this.list(queryWrapper);
    }

    @Override
    public List<FsNodeCache> getNodesByStatus(String status) {
        LambdaQueryWrapper<FsNodeCache> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(status), FsNodeCache::getStatus, status);
        queryWrapper.orderByDesc(FsNodeCache::getNodeKey);
        return this.list(queryWrapper);
    }

    @Override
    public Page<FsNodeCache> pageList(Page<FsNodeCache> page, QueryWrapper<FsNodeCache> queryWrapper) {
        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatch(List<FsNodeCache> nodeList) {
        if (nodeList != null && !nodeList.isEmpty()) {
            Date now = new Date();
            for (FsNodeCache node : nodeList) {
                node.setUpdateTime(now);
            }
            return super.saveOrUpdateBatch(nodeList);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateAndAggregate(FsNodeCache node) {
        if (node != null) {
            node.setUpdateTime(new Date());
            boolean result = super.saveOrUpdate(node);
            
            if (result) {
                // 保存成功后，汇总到实例
                try {
                    baseMapper.aggregateNodeDataToInstance(node.getProjectKey(), node.getWorkItemKey(), node.getInstanceKey());
                } catch (Exception e) {
                    log.error("汇总节点数据到实例异常: {}", e.getMessage());
                }
            }
            
            return result;
        }
        return false;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatchAndAggregate(List<FsNodeCache> nodeList) {
        if (nodeList != null && !nodeList.isEmpty()) {
            Date now = new Date();
            for (FsNodeCache node : nodeList) {
                node.setUpdateTime(now);
            }
            boolean result = super.saveOrUpdateBatch(nodeList);
            
            if (result) {
                // 保存成功后，按实例分组汇总
                try {
                    // 获取所有涉及的实例
                    Map<String, Set<String>> projectInstanceMap = new HashMap<>();
                    
                    for (FsNodeCache node : nodeList) {
                        String projectKey = node.getProjectKey();
                        String workItemKey = node.getWorkItemKey();
                        String instanceKey = node.getInstanceKey();
                        
                        if (StringUtils.isNotBlank(projectKey) && StringUtils.isNotBlank(workItemKey) && StringUtils.isNotBlank(instanceKey)) {
                            String key = projectKey + ":" + workItemKey;
                            
                            if (!projectInstanceMap.containsKey(key)) {
                                projectInstanceMap.put(key, new HashSet<>());
                            }
                            
                            projectInstanceMap.get(key).add(instanceKey);
                        }
                    }
                    
                    // 对每个实例执行汇总
                    for (Map.Entry<String, Set<String>> entry : projectInstanceMap.entrySet()) {
                        String[] parts = entry.getKey().split(":");
                        String projectKey = parts[0];
                        String workItemKey = parts[1];
                        
                        for (String instanceKey : entry.getValue()) {
                            baseMapper.aggregateNodeDataToInstance(projectKey, workItemKey, instanceKey);
                        }
                    }
                } catch (Exception e) {
                    log.error("批量汇总节点数据到实例异常: {}", e.getMessage());
                }
            }
            
            return result;
        }
        return false;
    }
}