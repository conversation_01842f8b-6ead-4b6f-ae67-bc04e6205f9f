package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.dto.UserProjectDailyHourReportDTO;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 人员项目每日工时分布导出服务接口
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
public interface IUserProjectDailyHourExportService {

    /**
     * 导出人员项目每日工时分布报表
     *
     * @param reportDTO 查询参数
     * @param response  响应对象
     */
    void exportUserProjectDailyHourReport(UserProjectDailyHourReportDTO reportDTO, HttpServletResponse response);
}