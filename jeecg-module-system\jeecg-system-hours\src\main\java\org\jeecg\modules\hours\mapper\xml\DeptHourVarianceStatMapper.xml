<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.DeptHourVarianceStatMapper">

    <!-- 计算指定年月的部门工时差值数据 -->
    <select id="calculateMonthlyVarianceData" resultType="org.jeecg.modules.hours.dto.DeptVarianceDataDTO">
        SELECT
        fd.id as deptId,
        fd.name as deptName,
        COALESCE(actual_hours.total_hours, 0) as actualHours,
        COALESCE(required_hours.total_hours, 0) as requiredHours
        FROM fs_department fd
        LEFT JOIN (
        -- 实际工时统计：从工时记录中按部门汇总
        SELECT
        fd_inner.id as dept_id,
        SUM(hrd.actual_hours) as total_hours
        FROM hour_record_detail hrd
        INNER JOIN hour_record hr ON hrd.record_id = hr.id
        LEFT JOIN fs_user fu ON hr.user_id = fu.user_key
        LEFT JOIN fs_department_user fdu ON fu.union_id = fdu.union_id
        LEFT JOIN fs_department fd_inner ON fdu.dept_id = fd_inner.dept_id
        WHERE YEAR(hrd.work_date) = #{year}
        AND MONTH(hrd.work_date) = #{month}
        AND hr.status = 2
        <if test="!includeHoliday">
            AND hrd.is_rest = 0
        </if>
        <if test="includeHoliday">
            AND hrd.is_rest = 1
        </if>
        AND fd_inner.id IS NOT NULL
        GROUP BY fd_inner.id
        ) actual_hours ON fd.id = actual_hours.dept_id
        LEFT JOIN (
        -- 可登记工时统计：从考勤统计表中按部门汇总
        SELECT
        fd_inner.id as dept_id,
        SUM(CASE
        WHEN sas.required_attend_hours = 0 AND #{includeHoliday} = false THEN 0
        WHEN sas.required_attend_hours > 0 OR #{includeHoliday} = true THEN sas.required_attend_hours
        ELSE 0
        END) as total_hours
        FROM sys_user_attendance_stat sas
        LEFT JOIN fs_user fu ON sas.union_id = fu.union_id
        LEFT JOIN fs_department_user fdu ON fu.union_id = fdu.union_id
        LEFT JOIN fs_department fd_inner ON fdu.dept_id = fd_inner.dept_id
        WHERE YEAR(sas.stat_date) = #{year}
        AND MONTH(sas.stat_date) = #{month}
        AND fd_inner.id IS NOT NULL
        GROUP BY fd_inner.id
        ) required_hours ON fd.id = required_hours.dept_id
        WHERE fd.id IS NOT NULL
        AND (actual_hours.total_hours > 0 OR required_hours.total_hours > 0)
        ORDER BY fd.name
    </select>

</mapper>