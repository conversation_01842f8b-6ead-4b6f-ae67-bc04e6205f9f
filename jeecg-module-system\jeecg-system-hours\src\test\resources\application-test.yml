# 开发环境配置
spring:
  profiles:
    active: dev
  
  # 数据库配置（使用内存数据库或测试数据库）
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    
  # 禁用自动配置的组件
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration
      - org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration

# 禁用定时任务和初始化器
reminder:
  job:
    enabled: false  # 禁用提醒任务

# 日志配置
logging:
  level:
    org.jeecg: DEBUG
    org.springframework: WARN
    com.baomidou: WARN
