package org.jeecg.modules.plm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * PLM同步日志实体类
 */
@Data
@TableName("plm_sync_log")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "PlmSyncLog对象", description = "PLM同步日志")
public class PlmSyncLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 同步类型
     */
    @Excel(name = "同步类型", width = 15, dicCode = "sync_type")
    @ApiModelProperty(value = "同步类型")
    @Dict(dicCode = "sync_type")
    private String syncType;

    /**
     * 同步状态
     */
    @Excel(name = "同步状态", width = 15, dicCode = "sync_status")
    @ApiModelProperty(value = "同步状态")
    @Dict(dicCode = "sync_status")
    private String status;

    /**
     * 开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 执行时长(秒)
     */
    @Excel(name = "执行时长(秒)", width = 15)
    @ApiModelProperty(value = "执行时长(秒)")
    private Integer duration;

    /**
     * 总记录数
     */
    @Excel(name = "总记录数", width = 15)
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    /**
     * 成功记录数
     */
    @Excel(name = "成功记录数", width = 15)
    @ApiModelProperty(value = "成功记录数")
    private Integer successCount;

    /**
     * 错误信息
     */
    @Excel(name = "错误信息", width = 50)
    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
} 