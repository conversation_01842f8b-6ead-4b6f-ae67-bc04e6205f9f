/* 授权相关样式 */

/* 登录容器 */
.auth-login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
  background-color: #f5f5f7;
  text-align: center;
}

/* 加载状态容器 */
.auth-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
  gap: 16px;
}

/* 授权按钮样式 */
.auth-button {
  min-width: 180px;
  border-radius: 4px;
  padding: 8px 16px;
}

/* 授权结果提示 */
.auth-result-message {
  margin-top: 16px;
  padding: 8px;
  border-radius: 4px;
}

.auth-result-message.success {
  background-color: #e3f5e1;
  color: #18a058;
}

.auth-result-message.error {
  background-color: #ffebe5;
  color: #f5222d;
}

.auth-login-container .semi-typography-paragraph {
  margin-bottom: 24px;
  color: var(--semi-color-text-2);
} 