package org.jeecg.modules.plm.job;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.jeecg.common.util.UUIDGenerator;
import org.jeecg.modules.plm.entity.PlmProject;
import org.jeecg.modules.plm.service.IPlmProjectService;
import org.jeecg.modules.plm.service.IPlmService;
import org.jeecg.modules.plm.vo.PlmProjectVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * PLM项目数据同步定时任务
 */
@Slf4j
@Component
public class PlmSyncJob {

    @Autowired
    private IPlmService plmService;
    
    @Autowired
    private IPlmProjectService plmProjectService;
    
    /**
     * 初始化同步PLM项目数据（全量同步）
     * 手动触发
     */
    public void initSync() {
        log.info("开始初始化同步PLM项目数据");
        try {
            // 执行全量同步
        	List<PlmProjectVO> resultList =  plmService.getProjectList();
        	this.saveOrUpdateBatch(resultList);
        	
        } catch (Exception e) {
            log.error("初始化同步PLM项目数据异常", e);
        }
    }
    
    /**
     * 增量同步PLM项目数据
     * 每天凌晨2点执行一次
     * 同步最近1个月的数据
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void incrementalSync() {
        log.info("开始增量同步PLM项目数据");
        try {
            // 获取1个月前的日期
            LocalDate oneMonthAgo = LocalDate.now().minusMonths(1);
            String dateFrom = oneMonthAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            log.info("增量同步起始日期: {}", dateFrom);
            
            // 执行增量同步
            List<PlmProjectVO> resultList = plmService.getIncrementalProjectList(dateFrom);
            
            this.saveOrUpdateBatch(resultList);
            
        } catch (Exception e) {
            log.error("增量同步PLM项目数据异常", e);
        }
    }
    
    /**
     * 批量保存或更新项目数据
     * 
     * @param resultList 项目数据列表
     */
    @Transactional(rollbackFor = Exception.class)
    private void saveOrUpdateBatch(List<PlmProjectVO> resultList) {
        if (resultList == null || resultList.isEmpty()) {
            log.info("没有需要同步的PLM项目数据");
            return;
        }
        
        log.info("开始批量保存或更新{}个PLM项目", resultList.size());
        
        try {
            // 1. 提取所有项目编码
            List<String> projectCodes = resultList.stream()
                    .map(PlmProjectVO::getProjectCode)
                    .collect(Collectors.toList());
            
            // 2. 一次性查询所有已存在的项目
            LambdaQueryWrapper<PlmProject> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(PlmProject::getProjectCode, projectCodes);
            List<PlmProject> existingProjects = plmProjectService.list(queryWrapper);
            
            // 3. 构建项目编码到项目的映射，方便快速查找
            Map<String, PlmProject> existingProjectMap = existingProjects.stream()
                    .collect(Collectors.toMap(PlmProject::getProjectCode, project -> project));
            
            // 4. 准备需要更新和新增的列表
            List<PlmProject> projectsToUpdate = new ArrayList<>();
            List<PlmProject> projectsToInsert = new ArrayList<>();
            Date now = new Date();
            
            // 5. 分类处理
            for (PlmProjectVO projectVO : resultList) {
                PlmProject existingProject = existingProjectMap.get(projectVO.getProjectCode());
                
                if (existingProject != null) {
                    // 需要更新
                    BeanUtils.copyProperties(projectVO, existingProject);
                    existingProject.setUpdateTime(now);
                    existingProject.setSyncTime(now);
                    existingProject.setProjectStatus(projectVO.getStatus());
                    projectsToUpdate.add(existingProject);
                } else {
                    // 需要新增
                    PlmProject newProject = new PlmProject();
                    BeanUtils.copyProperties(projectVO, newProject);
                    newProject.setId(UUIDGenerator.generate());
                    newProject.setCreateTime(now);
                    newProject.setSyncTime(now);
                    newProject.setProjectStatus(projectVO.getStatus());
                    projectsToInsert.add(newProject);
                }
            }
            
            // 6. 批量更新和新增
            if (!projectsToUpdate.isEmpty()) {
                plmProjectService.updateBatchById(projectsToUpdate);
                log.info("批量更新{}个项目", projectsToUpdate.size());
            }
            
            if (!projectsToInsert.isEmpty()) {
                plmProjectService.saveBatch(projectsToInsert);
                log.info("批量新增{}个项目", projectsToInsert.size());
            }
            
            log.info("PLM项目同步完成, 新增: {}, 更新: {}", 
                    projectsToInsert.size(), projectsToUpdate.size());
        } catch (Exception e) {
            log.error("批量保存或更新项目数据异常", e);
            throw e;
        }
    }
}