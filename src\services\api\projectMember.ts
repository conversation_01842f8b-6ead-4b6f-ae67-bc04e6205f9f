import { get, post } from '../../utils/request';

/**
 * 获取项目列表
 * @param params 查询参数
 */
export async function getProjectList(params: {
  pageNo: number;
  pageSize: number;
}) {
  return get('/plm/project/list', params);
}

/**
 * 获取项目成员
 * @param projectId 项目ID
 */
export async function getProjectMembers(projectId: string) {
  return get('/plm/project/detail', { id: projectId });
}

/**
 * 获取用户列表
 * @param keyword 搜索关键词
 */
export async function getUserList(keyword: string) {
  return get('/hapi/sys/user/list', { realname: keyword });
}

/**
 * 保存项目信息（项目经理、成员和备注）
 * @param params 项目信息
 */
export async function saveProject(params: {
  projectId: string;
  managerId?: string;
  memberIds?: string;
  remark?: string;
}) {
  return post('/plm/project/save', params);
} 