import React, { useState, useEffect } from 'react';
import { Select, Spin } from '@douyinfe/semi-ui';
import { get } from '../../../utils/request';

interface Instance {
  id: string;
  workItemName: string;
  projectKey: string;
}

interface InstanceSelectorProps {
  value?: string[];                           // 当前选中的实例ID列表
  onChange: (instanceIds: string[]) => void;  // 实例选择变化回调
  placeholder?: string;                       // 占位符
  showSelectAll?: boolean;                    // 是否显示全选选项
  disabled?: boolean;                         // 是否禁用
}

/**
 * 实例选择组件
 * 支持多选、搜索、全选功能
 * projectKey已通过全局请求header传递，无需在组件中单独传递
 */
const InstanceSelector: React.FC<InstanceSelectorProps> = ({
  value,
  onChange,
  placeholder = "请选择实例",
  showSelectAll = true,
  disabled = false
}) => {
  const [instances, setInstances] = useState<Instance[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取项目下的实例列表
  const fetchInstances = async () => {
    setLoading(true);
    try {
      const response = await get('/api/reports/instances');
      
      if (response && (response as any).result) {
        setInstances((response as any).result);
      }
    } catch (error) {
      console.error('获取实例列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInstances();
  }, []);

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onChange(instances.map(instance => instance.id));
    } else {
      onChange([]);
    }
  };

  // 构建选项列表
  const optionList = instances.map(instance => ({
    value: instance.id,
    label: instance.workItemName,
    key: instance.id
  }));

  // 添加全选选项
  if (showSelectAll && instances.length > 0) {
    const isAllSelected = value && value.length === instances.length;
    optionList.unshift({
      value: 'SELECT_ALL',
      label: isAllSelected ? '取消全选' : '全选',
      key: 'SELECT_ALL'
    });
  }

  // 处理选择变化
  const handleChange = (selectedValues: any[]) => {
    // 如果选择了全选选项
    if (selectedValues.includes('SELECT_ALL')) {
      const isAllSelected = value && value.length === instances.length;
      handleSelectAll(!isAllSelected);
      return;
    }

    // 过滤掉全选选项
    const instanceIds = selectedValues.filter(id => id !== 'SELECT_ALL');
    onChange(instanceIds);
  };

  return (
    <Select
      multiple
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      loading={loading}
      disabled={disabled}
      style={{ width: '100%', minWidth: 200 }}
      optionList={optionList}
      filter
      searchPlaceholder="搜索实例"
      emptyContent={loading ? <Spin /> : "暂无数据"}
    />
  );
};

export default InstanceSelector;
