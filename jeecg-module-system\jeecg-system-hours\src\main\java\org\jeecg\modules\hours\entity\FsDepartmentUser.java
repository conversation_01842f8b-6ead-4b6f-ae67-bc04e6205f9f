package org.jeecg.modules.hours.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * @Description: 部门和用户id关联表
 * @Author: jeecg-boot
 * @Date:   2025-06-10
 * @Version: V1.0
 */
@Data
@TableName("fs_department_user")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="fs_department_user对象", description="部门和用户id关联表")
public class FsDepartmentUser {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private String id;
	/**部门ID*/
	@Excel(name = "部门ID", width = 15)
    @ApiModelProperty(value = "部门ID")
	private String deptId;
	/**unionId*/
	@Excel(name = "unionId", width = 15)
    @ApiModelProperty(value = "unionId")
	private String unionId;
	/**同步时间*/
    @ApiModelProperty(value = "同步时间")
	private Date syncTime;
}
