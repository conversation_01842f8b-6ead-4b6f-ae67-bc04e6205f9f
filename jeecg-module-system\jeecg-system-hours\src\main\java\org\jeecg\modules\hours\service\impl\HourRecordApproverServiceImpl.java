package org.jeecg.modules.hours.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.feishu.service.FeiShuMessageService;
import org.jeecg.modules.hours.constant.ConfigKeyConstant;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.entity.HourRecord;
import org.jeecg.modules.hours.entity.HourRecordApply;
import org.jeecg.modules.hours.entity.HourRecordApprover;
import org.jeecg.modules.hours.mapper.FsUserMapper;
import org.jeecg.modules.hours.mapper.HourRecordApproverMapper;
import org.jeecg.modules.hours.service.IFsUserService;
import org.jeecg.modules.hours.service.IHourRecordApplyService;
import org.jeecg.modules.hours.service.IHourRecordApproverService;
import org.jeecg.modules.hours.service.IHourRecordService;
import org.jeecg.modules.hours.service.ISysConfigService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.jeecg.modules.hours.utils.UserInfoUtils;
import org.jeecg.modules.plm.entity.PlmProject;
import org.jeecg.modules.plm.service.IPlmProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 工时单审批人清单表 Service实现
 * @Author: jeecg-boot
 * @Date: 2024-05-01
 * @Version: V1.0
 */
@Slf4j
@Service
public class HourRecordApproverServiceImpl extends ServiceImpl<HourRecordApproverMapper, HourRecordApprover> implements IHourRecordApproverService {
    @Autowired
    @Lazy
    private IPlmProjectService plmProjectService;
    @Autowired
    @Lazy
    private IFsUserService fsUserService;
    @Autowired
    @Lazy
    private IHourRecordService hourRecordService;
    @Autowired
    @Lazy
    private ISysConfigService sysConfigService;
    @Autowired
    private FsUserMapper fsUserMapper;
    @Autowired
    private IHourRecordApplyService hourRecordApplyService;

    @Autowired
    private UserInfoUtils userInfoUtils;
    
    @Value("${feishu.project.plugin.button.url}")
    private String buttonUrl;
    
    // 可扩展自定义方法，如批量移交等

    @Override
    public List<HourRecordApprover> buildApproverList(String hourRecordId, List<String> projectIds, String projectKey,Long applyId) {
        // 审批人：项目经理 + 直属领导(部门负责人)
        // 1.项目经理
        List<PlmProject> projects = projectIds.isEmpty() ? new ArrayList<>() : plmProjectService.listByIds(projectIds);
        List<String> managerIds = projects.stream()
            .map(PlmProject::getManagerId)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());
        List<FsUser> managers = managerIds.isEmpty() ? new ArrayList<>() : fsUserService.getUserByProjectAndUserKeys(projectKey, managerIds);
        Map<String, FsUser> managerMap = managers.stream().collect(Collectors.toMap(FsUser::getUserKey, u -> u));
        Map<String, HourRecordApprover> approverMap = new java.util.LinkedHashMap<>(); // 用于去重
        for (PlmProject project : projects) {
            String managerId = project.getManagerId();
            if (StringUtils.isNotBlank(managerId)) {
                FsUser manager = managerMap.get(managerId);
                if (manager != null) {
                    String approverId = manager.getUserKey();
                    if (!approverMap.containsKey(approverId)) {
                        HourRecordApprover approver = new HourRecordApprover();
                        approver.setRecordId(hourRecordId);
                        approver.setApproverId(manager.getUserKey());
                        approver.setApproverName(manager.getName());
                        approver.setRole("pm");
                        approver.setStatus(0); // 待审批
                        approver.setApplyId(applyId);
                        approverMap.put(approverId, approver);
                    }
                }
            }
        }
        // 2.直属领导(fsUser::leader), 调用 FsUserMapper.getUserLeaderList获取
        // 先获取当前填报人unionId（假设hourRecordId能查到HourRecord，HourRecord有userId字段=unionId）
        HourRecord hourRecord = hourRecordService.getById(hourRecordId);
        if (hourRecord != null && StringUtils.isNotBlank(hourRecord.getUserId())) {
        	FsUser user = fsUserService.getUserByProjectAndUserKey(projectKey, hourRecord.getUserId());
        	if(null!=user && StringUtils.isNotEmpty(user.getLeaderUserId())) {
        		FsUser leaderUser = fsUserService.getUserByProjectAndUnionId(projectKey, user.getLeaderUserId());
        		if(null!=leaderUser) {
        			String approverId = leaderUser.getUserKey();
        			// 若已存在（如既是pm又是leader），优先保留pm
                    if (!approverMap.containsKey(approverId)) {
                        HourRecordApprover approver = new HourRecordApprover();
                        approver.setRecordId(hourRecordId);
                        approver.setApproverId(leaderUser.getUserKey());
                        approver.setApproverName(leaderUser.getName());
                        approver.setRole("leader");
                        approver.setStatus(0); // 待审批
                        approver.setApplyId(applyId);
                        approverMap.put(approverId, approver);
                    }	
        		}
        	}
        }
        return new ArrayList<>(approverMap.values());
    }

    @Override
    public boolean processApproval(String recordId, String approverId, String approverName, boolean approve, String remarks,Long applyId) {
        // 1. 查找当前审批人待审批记录
        HourRecordApprover approver = this.lambdaQuery()
            .eq(HourRecordApprover::getRecordId, recordId)
            .eq(HourRecordApprover::getApproverId, approverId)
            .eq(HourRecordApprover::getStatus, 0)
            .one();
        if (approver == null) return false;
        approver.setStatus(approve ? 1 : 2);
        approver.setActionTime(new Date());
        approver.setRemarks(remarks);
        this.updateById(approver);
        
        HourRecord hourRecord = hourRecordService.getById(recordId);
        // 2. 拒绝：删除其他待审批，主表设为3
        if (!approve) {
            this.lambdaUpdate()
                .eq(HourRecordApprover::getRecordId, recordId)
                .eq(HourRecordApprover::getStatus, 0)
                .ne(HourRecordApprover::getApproverId, approverId)
                .remove();
            // 任意一个用户拒绝--则当前批次直接拒绝
            HourRecordApply apply = this.hourRecordApplyService.getById(applyId);
            apply.setStatus(approve ? 1 : 2);
            this.hourRecordApplyService.updateById(apply);
            
            // 主表状态设为3
            hourRecordService.lambdaUpdate()
                .eq(org.jeecg.modules.hours.entity.HourRecord::getId, recordId)
                .set(org.jeecg.modules.hours.entity.HourRecord::getStatus, 3)
                .update();
            // 推送消息
            this.sendApprovalNotification(hourRecord, false, remarks);
            return true;
        } else {
            // 3. 同意：如无待审批，主表设为2
            long pending = this.lambdaQuery()
                .eq(HourRecordApprover::getRecordId, recordId)
                .eq(HourRecordApprover::getStatus, 0)
                .count();
            if (pending == 0) {
                hourRecordService.lambdaUpdate()
                    .eq(org.jeecg.modules.hours.entity.HourRecord::getId, recordId)
                    .set(org.jeecg.modules.hours.entity.HourRecord::getStatus, 2)
                    .update();
                
                HourRecordApply apply = this.hourRecordApplyService.getById(applyId);
                apply.setStatus(approve ? 1 : 2);
                this.hourRecordApplyService.updateById(apply);
            }
            // 推送消息
            this.sendApprovalNotification(hourRecord, true, remarks);
            return true;
        }
    }
    
    @Override
    public boolean batchTransferApprover(String hourRecordApproverIds, String currentUserId1, String newApproverId, String newApproverName) {
        
     // 获取当前登录用户
        FsUser loginUser = userInfoUtils.getLoginUser();
        if (loginUser == null) {
            throw new JeecgBootException("用户未登录");
        }
        String currentUserKey = loginUser.getUserKey();
    	
        
        List<Long> ids = java.util.Arrays.stream(hourRecordApproverIds.split(","))
                                   .map(String::trim)
                                   .filter(s -> !s.isEmpty())
                                   .map(Long::parseLong)
                                   .collect(java.util.stream.Collectors.toList());
        for (Long id : ids) {
        	
        	// 1.获取【待移交】的数据
        	List<HourRecordApprover> approvers = this.lambdaQuery().eq(HourRecordApprover::getId, id)
//            		.eq(HourRecordApprover::getApproverId, currentUserKey)
            		.list();
        	if(CollectionUtils.isEmpty(approvers)) {
        		throw new JeecgBootException("未排查到需要移交的数据！");
        	}
        	// 2.验证相同applyId下是否已存在【新移交】用户的审批
        	List<HourRecordApprover> newApprovers = this.lambdaQuery().eq(HourRecordApprover::getApplyId, approvers.get(0).getApplyId())
            		.eq(HourRecordApprover::getApproverId, newApproverId)
            		.list();
        	
        	// 3.移交
        	if(CollectionUtils.isEmpty(newApprovers)) {
        		// 不存在【新移交】用户,则直接进行移交
        		List<HourRecordApprover> updateList = new ArrayList<HourRecordApprover>();
        		for (HourRecordApprover record : approvers) {
                    record.setTransferredFrom(record.getApproverId());
                    record.setTransferredTime(new java.util.Date());
                    record.setApproverId(newApproverId);
                    record.setApproverName(newApproverName);
                    updateList.add(record);
				}
        		super.updateBatchById(updateList);
        		
        		continue;
        	}
        	
        	// 已存在【新移交】用户,则直接进行移交; 
        	List<HourRecordApprover> updateList = new ArrayList<HourRecordApprover>();
    		for (HourRecordApprover record : newApprovers) {
                record.setTransferredFrom(approvers.get(0).getApproverId());
                record.setTransferredTime(new java.util.Date());
                updateList.add(record);
			}
    		super.updateBatchById(updateList);
    		// 同时删除【待移交】
    		List<Long> idList = approvers.stream().map(item->item.getId()).collect(Collectors.toList());
    		super.removeBatchByIds(idList);
        }
        return true;
    }
    
    /**
     * 发送审批结果通知
     * 
     * @param record 工时记录
     * @param isApprove 是否通过
     */
    private void sendApprovalNotification(HourRecord record, boolean isApprove,String rejectReason) {
        try {
        	// 从请求头获取项目Key
            String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
            if (projectKey == null) {
                throw new JeecgBootException("未提供有效的项目Key");
            }
            
            // 获取用户ID
            String userId = record.getUserId();
            if (StringUtils.isBlank(userId)) {
                log.warn("工时记录用户ID为空，无法发送通知");
                return;
            }
            
            // 获取用户信息
            FsUser user = fsUserService.getUserByProjectAndUserKey(projectKey, userId);
            if (user == null || StringUtils.isBlank(user.getUnionId())) {
                log.warn("未找到用户信息或用户unionId为空，userId: {}", userId);
                return;
            }
            
            // 获取飞书应用配置
            String appId = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.BIND_APP_ID);
            String appSecret = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.BIND_APP_SECRET);
            
            if (StringUtils.isBlank(appId) || StringUtils.isBlank(appSecret)) {
                log.warn("飞书应用配置不完整，无法发送通知");
                return;
            }
            
            // 构建消息内容
            String title, content, buttonText;
            
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
            String formattedDate = dateFormat.format(record.getWorkPeriodStart())+"-"+dateFormat.format(record.getWorkPeriodEnd());
            
            if (isApprove) {
                // 审批通过
                title = "审批通过提醒";
                content = "恭喜您提交的工时数据审批通过。<br>"
                        + "审批通过日期：" + formattedDate + "<br>"
                        + "审批意见："+(StringUtils.isNotBlank(rejectReason) ? rejectReason :"工时数据提交很及时");
                buttonText = "查看详情";
            } else {
                // 审批驳回
                title = "拒绝提醒";
                content = "很遗憾，您提交的工时数据被拒绝。<br>"
                        + "拒绝日期：" + formattedDate + "<br>"
                        + "拒绝原因：" + (StringUtils.isNotBlank(rejectReason) ? rejectReason : "工时数据不准确");
                buttonText = "查看详情";
            }
            
            // 发送消息
            log.info("发送审批结果通知，用户: {}, 是否通过: {}", user.getName(), isApprove);
            FeiShuMessageService.instance(appId, appSecret)
                    .sendMessage(user.getUnionId(), title, content, buttonText, buttonUrl);
            
        } catch (Exception e) {
            log.error("发送审批结果通知失败", e);
        }
    }
} 