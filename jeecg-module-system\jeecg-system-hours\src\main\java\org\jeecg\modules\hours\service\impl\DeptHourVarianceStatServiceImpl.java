package org.jeecg.modules.hours.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.UUIDGenerator;
import org.jeecg.modules.hours.dto.DeptVarianceDataDTO;
import org.jeecg.modules.hours.entity.DeptHourVarianceStat;
import org.jeecg.modules.hours.mapper.DeptHourVarianceStatMapper;
import org.jeecg.modules.hours.service.IDeptHourVarianceStatService;
import org.jeecg.modules.hours.vo.DeptHourVarianceReportVO;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DeptHourVarianceStatServiceImpl extends ServiceImpl<DeptHourVarianceStatMapper, DeptHourVarianceStat> 
        implements IDeptHourVarianceStatService {

    @Autowired
    private DeptHourVarianceStatMapper deptHourVarianceStatMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int calculateMonthlyVarianceStat(int year, int month) {
        log.info("开始计算{}年{}月的部门工时差值统计", year, month);
        
        try {
            // 删除该月已有的统计数据
            LambdaQueryWrapper<DeptHourVarianceStat> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(DeptHourVarianceStat::getStatYear, year)
                        .eq(DeptHourVarianceStat::getStatMonth, month);
            this.remove(deleteWrapper);
            
            // 计算包含假日的统计数据
            List<DeptVarianceDataDTO> varianceDataWithHoliday = 
                deptHourVarianceStatMapper.calculateMonthlyVarianceData(year, month, true);
            
            // 计算不包含假日的统计数据
            List<DeptVarianceDataDTO> varianceDataWithoutHoliday = 
                deptHourVarianceStatMapper.calculateMonthlyVarianceData(year, month, false);
            
            List<DeptHourVarianceStat> statRecords = new ArrayList<>();
            
            // 处理包含假日的数据
            for (DeptVarianceDataDTO data : varianceDataWithHoliday) {
                DeptHourVarianceStat stat = createStatRecord(data, year, month, true);
                statRecords.add(stat);
            }
            
            // 处理不包含假日的数据
            for (DeptVarianceDataDTO data : varianceDataWithoutHoliday) {
                DeptHourVarianceStat stat = createStatRecord(data, year, month, false);
                statRecords.add(stat);
            }
            
            // 批量保存
            if (!statRecords.isEmpty()) {
                this.saveBatch(statRecords);
            }
            
            log.info("完成{}年{}月的部门工时差值统计，共生成{}条记录", year, month, statRecords.size());
            return statRecords.size();
            
        } catch (Exception e) {
            log.error("计算部门工时差值统计失败: {}年{}月", year, month, e);
            throw new RuntimeException("计算部门工时差值统计失败", e);
        }
    }

    private DeptHourVarianceStat createStatRecord(DeptVarianceDataDTO data, int year, int month, boolean includeHoliday) {
        DeptHourVarianceStat stat = new DeptHourVarianceStat();
        stat.setId(UUIDGenerator.generate());
        stat.setDeptId(data.getDeptId());
        stat.setDeptName(data.getDeptName());
        stat.setStatYear(year);
        stat.setStatMonth(month);
        stat.setIncludeHoliday(includeHoliday);
        
        // 处理数值字段
        stat.setActualHours(data.getActualHours() != null ? data.getActualHours() : BigDecimal.ZERO);
        stat.setRequiredHours(data.getRequiredHours() != null ? data.getRequiredHours() : BigDecimal.ZERO);
        
        // 计算差值 = 可登记工时 - 实际工时
        stat.setVarianceHours(stat.getRequiredHours().subtract(stat.getActualHours()));
        
        return stat;
    }

    @Override
    public List<DeptHourVarianceReportVO> getVarianceReport(int year, List<String> deptIds, boolean includeHoliday) {
        // 查询指定年份的统计数据
        LambdaQueryWrapper<DeptHourVarianceStat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeptHourVarianceStat::getStatYear, year)
                   .eq(DeptHourVarianceStat::getIncludeHoliday, includeHoliday)
                   .orderByAsc(DeptHourVarianceStat::getDeptName)
                   .orderByAsc(DeptHourVarianceStat::getStatMonth);
        
        if (deptIds != null && !deptIds.isEmpty()) {
            queryWrapper.in(DeptHourVarianceStat::getDeptId, deptIds);
        }
        
        List<DeptHourVarianceStat> statList = this.list(queryWrapper);
        
        // 按部门分组
        Map<String, List<DeptHourVarianceStat>> deptGroupMap = statList.stream()
            .collect(Collectors.groupingBy(DeptHourVarianceStat::getDeptId));
        
        List<DeptHourVarianceReportVO> reportList = new ArrayList<>();
        
        for (Map.Entry<String, List<DeptHourVarianceStat>> entry : deptGroupMap.entrySet()) {
            List<DeptHourVarianceStat> deptStats = entry.getValue();
            if (deptStats.isEmpty()) continue;
            
            DeptHourVarianceReportVO reportVO = new DeptHourVarianceReportVO();
            reportVO.setDeptId(entry.getKey());
            reportVO.setDeptName(deptStats.get(0).getDeptName());
            reportVO.setStatYear(year);
            
            // 构建月度数据
            Map<String, BigDecimal> monthlyDataMap = new HashMap<>();
            BigDecimal totalActual = BigDecimal.ZERO;
            BigDecimal totalRequired = BigDecimal.ZERO;
            
            for (DeptHourVarianceStat stat : deptStats) {
                String monthKey = String.valueOf(stat.getStatMonth());
                
                monthlyDataMap.put(monthKey + "月份实际工时", stat.getActualHours());
                monthlyDataMap.put(monthKey + "月份可登记工时", stat.getRequiredHours());
                monthlyDataMap.put(monthKey + "月份差值", stat.getVarianceHours());
                
                totalActual = totalActual.add(stat.getActualHours());
                totalRequired = totalRequired.add(stat.getRequiredHours());
            }
            
            reportVO.setMonthlyData(monthlyDataMap);
            
            // 年度总计
            DeptHourVarianceReportVO.YearlyTotal yearlyTotal = new DeptHourVarianceReportVO.YearlyTotal();
            yearlyTotal.setTotalActualHours(totalActual);
            yearlyTotal.setTotalRequiredHours(totalRequired);
            yearlyTotal.setTotalVarianceHours(totalRequired.subtract(totalActual));
            reportVO.setYearlyTotal(yearlyTotal);
            
            reportList.add(reportVO);
        }
        
        return reportList;
    }

    @Override
    public void exportVarianceReport(int year, List<String> deptIds, boolean includeHoliday, HttpServletResponse response) {
        try {
            // 获取报表数据
            List<DeptHourVarianceReportVO> reportData = getVarianceReport(year, deptIds, includeHoliday);
            
            // 设置响应头
            String holidayText = includeHoliday ? "(含假日)" : "(不含假日)";
            String fileName = year + "年部门工时差值分析报表" + holidayText + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", 
                "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));

            // 导出Excel格式
            exportToExcel(reportData, year, includeHoliday, response.getOutputStream());
            
            log.info("部门工时差值分析报表导出完成: {}", fileName);

        } catch (Exception e) {
            log.error("导出部门工时差值分析报表失败", e);
            throw new RuntimeException("导出报表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出为Excel格式
     */
    private void exportToExcel(List<DeptHourVarianceReportVO> reportData, int year, boolean includeHoliday, OutputStream outputStream) 
            throws IOException {
        
        // 构建列定义
        List<ExcelExportEntity> colList = new ArrayList<>();
        
        // 部门列
        colList.add(new ExcelExportEntity("部门", "deptName", 20));
        
        // 动态添加12个月的列
        for (int month = 1; month <= 12; month++) {
            colList.add(new ExcelExportEntity(month + "月份实际工时", "month" + month + "Actual", 15));
            colList.add(new ExcelExportEntity(month + "月份可登记工时", "month" + month + "Required", 15));
            colList.add(new ExcelExportEntity(month + "月份差值", "month" + month + "Variance", 12));
        }
        
        // 年度总计列
        colList.add(new ExcelExportEntity("年度实际工时总计", "yearTotalActual", 15));
        colList.add(new ExcelExportEntity("年度可登记工时总计", "yearTotalRequired", 15));
        colList.add(new ExcelExportEntity("年度差值总计", "yearTotalVariance", 15));
        
        // 构建数据列表
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        // 添加数据行
        for (DeptHourVarianceReportVO item : reportData) {
            Map<String, Object> rowData = new HashMap<>();
            rowData.put("deptName", item.getDeptName());
            
            // 设置12个月的数据
            for (int month = 1; month <= 12; month++) {
                BigDecimal actualHours = item.getMonthlyData().get(month + "月份实际工时");
                BigDecimal requiredHours = item.getMonthlyData().get(month + "月份可登记工时");
                BigDecimal varianceHours = item.getMonthlyData().get(month + "月份差值");
                
                rowData.put("month" + month + "Actual", actualHours != null ? actualHours : BigDecimal.ZERO);
                rowData.put("month" + month + "Required", requiredHours != null ? requiredHours : BigDecimal.ZERO);
                rowData.put("month" + month + "Variance", varianceHours != null ? varianceHours : BigDecimal.ZERO);
            }
            
            // 设置年度总计
            if (item.getYearlyTotal() != null) {
                rowData.put("yearTotalActual", item.getYearlyTotal().getTotalActualHours());
                rowData.put("yearTotalRequired", item.getYearlyTotal().getTotalRequiredHours());
                rowData.put("yearTotalVariance", item.getYearlyTotal().getTotalVarianceHours());
            } else {
                rowData.put("yearTotalActual", BigDecimal.ZERO);
                rowData.put("yearTotalRequired", BigDecimal.ZERO);
                rowData.put("yearTotalVariance", BigDecimal.ZERO);
            }
            
            dataList.add(rowData);
        }
        
        // 创建导出参数
        String title = year + "年部门工时差值分析报表" + (includeHoliday ? "(含假日)" : "(不含假日)");
        ExportParams exportParams = new ExportParams(title, "部门差值分析");
        exportParams.setCreateHeadRows(true);
        
        // 生成Excel
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, dataList);
        workbook.write(outputStream);
        workbook.close();
    }
}