import { get } from '../utils/request';
import { getToken } from '../utils/auth';
import { API_CONFIG } from '../utils/config';
import type {
  ReportQueryParams,
  User,
  Instance,
  InstanceHoursReportResponse,
  EmployeeHoursReportResponse,
  EmployeeInstanceDistributionResponse,
  ApiResponse
} from '../types/report';

/**
 * 报表API服务
 * projectKey已通过全局请求header传递，无需在API调用中单独传递
 */
export class ReportApi {

  /**
   * 获取项目用户列表
   */
  static async getProjectUsers(): Promise<User[]> {
    const response = await get('/api/reports/users');
    return (response as any)?.result || [];
  }

  /**
   * 获取所有用户列表
   */
  static async getAllUsers(): Promise<User[]> {
    const response = await get<ApiResponse<User[]>>('/system/user/all');
    return response.result || [];
  }

  /**
   * 获取项目实例列表
   */
  static async getProjectInstances(): Promise<Instance[]> {
    const response = await get('/api/reports/instances');
    return (response as any)?.result || [];
  }

  /**
   * 获取实例工时周期汇总报表
   */
  static async getInstanceHoursReport(params: ReportQueryParams): Promise<InstanceHoursReportResponse> {
    const response = await get<ApiResponse<InstanceHoursReportResponse>>('/api/reports/instance-hours', {
      dimension: params.dimension,
      startDate: params.startDate,
      endDate: params.endDate,
      instanceIds: params.instanceIds,
      userIds: params.userIds,
      page: params.page || 1,
      size: params.size || 20
    });
    return response.result;
  }

  /**
   * 获取员工工时周期汇总报表
   */
  static async getEmployeeHoursReport(params: ReportQueryParams): Promise<EmployeeHoursReportResponse> {
    const response = await get<ApiResponse<EmployeeHoursReportResponse>>('/api/reports/employee-hours', {
      dimension: params.dimension,
      startDate: params.startDate,
      endDate: params.endDate,
      userIds: params.userIds,
      page: params.page || 1,
      size: params.size || 20
    });
    return response.result;
  }

  /**
   * 获取员工工时实例分布报表
   */
  static async getEmployeeInstanceDistributionReport(params: ReportQueryParams): Promise<EmployeeInstanceDistributionResponse> {
    const response = await get<ApiResponse<EmployeeInstanceDistributionResponse>>('/api/reports/employee-instance-distribution', {
      dimension: params.dimension,
      startDate: params.startDate,
      endDate: params.endDate,
      userIds: params.userIds,
      instanceIds: params.instanceIds,
      page: params.page || 1,
      size: params.size || 20
    });
    return response.result;
  }

  /**
   * 导出实例工时周期汇总报表
   */
  static async exportInstanceHoursReport(params: ReportQueryParams): Promise<void> {
    const queryParams = new URLSearchParams({
      dimension: params.dimension,
      startDate: params.startDate,
      endDate: params.endDate,
      ...(params.instanceIds && { instanceIds: params.instanceIds.join(',') }),
      ...(params.userIds && { userIds: params.userIds.join(',') })
    });

    const url = `/api/reports/instance-hours/exportXls?${queryParams.toString()}`;
    await this.downloadFile(url, `实例工时周期汇总报表_${params.startDate}_${params.endDate}.xlsx`);
  }

  /**
   * 导出员工工时周期汇总报表
   */
  static async exportEmployeeHoursReport(params: ReportQueryParams): Promise<void> {
    const queryParams = new URLSearchParams({
      dimension: params.dimension,
      startDate: params.startDate,
      endDate: params.endDate,
      ...(params.userIds && { userIds: params.userIds.join(',') })
    });

    const url = `/api/reports/employee-hours/exportXls?${queryParams.toString()}`;
    await this.downloadFile(url, `员工工时周期汇总报表_${params.startDate}_${params.endDate}.xlsx`);
  }

  /**
   * 导出员工工时实例分布报表
   */
  static async exportEmployeeInstanceDistributionReport(params: ReportQueryParams): Promise<void> {
    const queryParams = new URLSearchParams({
      dimension: params.dimension,
      startDate: params.startDate,
      endDate: params.endDate,
      ...(params.userIds && { userIds: params.userIds.join(',') }),
      ...(params.instanceIds && { instanceIds: params.instanceIds.join(',') })
    });

    const url = `/api/reports/employee-instance-distribution/exportXls?${queryParams.toString()}`;
    await this.downloadFile(url, `员工工时实例分布报表_${params.startDate}_${params.endDate}.xlsx`);
  }

  /**
   * 获取部门项目工时统计报表
   */
  static async getDeptProjectHourReport(params: DeptProjectHourReportParams): Promise<DeptProjectHourReportResult> {
    const response = await get<ApiResponse<DeptProjectHourReportResult>>('/hours/report/dept-project/list', {
      startDate: params.startDate,
      endDate: params.endDate,
      statPeriod: params.statPeriod || 'daily',
      ...(params.departmentIds && { departmentIds: params.departmentIds }),
      ...(params.projectIds && { projectIds: params.projectIds }),
      ...(params.isHoliday !== undefined && { isHoliday: params.isHoliday })
    });
    return response.result;
  }

  /**
   * 获取部门树状结构列表
   */
  static async getAllDepartments(): Promise<Department[]> {
    const response = await get<ApiResponse<Department[]>>('/system/department/tree');
    console.log('部门树状接口返回:', response);
    // 尝试多种数据字段
    return response.result || response.data || (response as any).records || [];
  }

  /**
   * 获取所有项目列表
   */
  static async getAllProjects(): Promise<Project[]> {
    const response = await get<ApiResponse<Project[]>>('/plm/project/availableForAdd');
    console.log('项目接口返回:', response);
    // 尝试多种数据字段
    return response.result || response.data || (response as any).records || [];
  }

  /**
   * 导出部门项目工时统计报表
   */
  static async exportDeptProjectHourReport(params: DeptProjectHourReportParams): Promise<void> {
    const postData = {
      startDate: params.startDate,
      endDate: params.endDate,
      statPeriod: params.statPeriod || 'daily',
      ...(params.departmentIds && { departmentIds: params.departmentIds }),
      ...(params.projectIds && { projectIds: params.projectIds }),
      ...(params.isHoliday !== undefined && { isHoliday: params.isHoliday })
    };

    const url = '/hours/report/dept-project/export';
    await this.downloadFilePost(url, postData, `部门项目工时统计_${params.startDate}_${params.endDate}.xlsx`);
  }

  /**
   * 获取人员项目工时统计报表
   */
  static async getUserProjectHourReport(params: UserProjectHourReportParams): Promise<UserProjectHourReportResult> {
    const response = await get<ApiResponse<UserProjectHourReportResult>>('/hours/userProjectHourReport/query', {
      startDate: params.startDate,
      endDate: params.endDate,
      statPeriod: params.statPeriod || 'daily',
      ...(params.userIds && { userIds: params.userIds }),
      ...(params.projectIds && { projectIds: params.projectIds }),
      ...(params.isHoliday !== undefined && { isHoliday: params.isHoliday })
    });
    return response.result;
  }

  /**
   * 导出人员项目工时统计报表
   */
  static async exportUserProjectHourReport(params: UserProjectHourReportParams): Promise<void> {
    const queryParams = new URLSearchParams({
      startDate: params.startDate,
      endDate: params.endDate,
      statPeriod: params.statPeriod || 'daily',
      ...(params.userIds && { userIds: params.userIds }),
      ...(params.projectIds && { projectIds: params.projectIds }),
      ...(params.isHoliday !== undefined && { isHoliday: params.isHoliday.toString() })
    });

    const url = `/hours/userProjectHourReport/export?${queryParams.toString()}`;
    await this.downloadFile(url, `人员项目工时统计_${params.startDate}_${params.endDate}.xlsx`);
  }

  /**
   * 下载文件的通用方法
   */
  private static async downloadFile(url: string, filename: string): Promise<void> {
    try {
      // 获取认证token
      const token = getToken();

      const fullUrl = API_CONFIG.getFullUrl(url);
      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          ...(token && { 'X-HOUR-TOKEN': token })
        }
      });

      if (!response.ok) {
        // 处理非200响应
        if (response.status === 401) {
          throw new Error('用户未登录或登录已过期');
        } else if (response.status === 403) {
          throw new Error('权限不足，无法下载文件');
        } else {
          const text = await response.text();
          throw new Error(`下载失败: ${response.status} ${response.statusText}\n${text}`);
        }
      }

      const blob = await response.blob();

      // 检查响应内容类型，确保是Excel文件
      const contentType = response.headers.get('content-type');
      if (!contentType || (!contentType.includes('spreadsheetml') && !contentType.includes('excel') && !contentType.includes('octet-stream'))) {
        // 可能是错误响应，尝试解析为文本
        const text = await blob.text();
        throw new Error(`服务器返回了非Excel文件: ${text}`);
      }

      const downloadUrl = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('文件下载失败:', error);
      throw error;
    }
  }

  private static async downloadFilePost(url: string, data: any, filename: string): Promise<void> {
    try {
      // 获取认证token
      const token = getToken();

      const fullUrl = API_CONFIG.getFullUrl(url);
      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'X-HOUR-TOKEN': token })
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        // 处理非200响应
        if (response.status === 401) {
          throw new Error('用户未登录或登录已过期');
        } else if (response.status === 403) {
          throw new Error('权限不足，无法下载文件');
        } else {
          const text = await response.text();
          throw new Error(`下载失败: ${response.status} ${response.statusText}\n${text}`);
        }
      }

      const blob = await response.blob();

      // 检查响应内容类型，确保是Excel文件
      const contentType = response.headers.get('content-type');
      if (!contentType || (!contentType.includes('spreadsheetml') && !contentType.includes('excel') && !contentType.includes('octet-stream'))) {
        // 可能是错误响应，尝试解析为文本
        const text = await blob.text();
        throw new Error(`服务器返回了非Excel文件: ${text}`);
      }

      const downloadUrl = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('文件下载失败:', error);
      throw error;
    }
  }

  /**
   * 获取部门项目每日工时分布报表
   */
  static async getDeptProjectDailyHourReport(params: DeptProjectDailyHourReportParams): Promise<DeptProjectDailyHourReportResult> {
    const response = await get<ApiResponse<DeptProjectDailyHourReportResult>>('/hours/report/dept-project-daily/query', {
      yearMonth: params.yearMonth,
      ...(params.departmentIds && { departmentIds: params.departmentIds }),
      ...(params.isHoliday !== undefined && { isHoliday: params.isHoliday }),
      ...(params.projectKeyword && { projectKeyword: params.projectKeyword })
    });
    return response.result;
  }

  /**
   * 导出部门项目每日工时分布报表
   */
  static async exportDeptProjectDailyHourReport(params: DeptProjectDailyHourReportParams): Promise<void> {
    const queryParams = new URLSearchParams({
      yearMonth: params.yearMonth,
      ...(params.departmentIds && { departmentIds: params.departmentIds }),
      ...(params.isHoliday !== undefined && { isHoliday: params.isHoliday.toString() }),
      ...(params.projectKeyword && { projectKeyword: params.projectKeyword })
    });

    const url = `/hours/report/dept-project-daily/export?${queryParams.toString()}`;
    await this.downloadFile(url, `部门项目每日工时分布_${params.yearMonth}.xlsx`);
  }

  /**
   * 获取人员项目每日工时分布报表
   */
  static async getUserProjectDailyHourReport(params: UserProjectDailyHourReportParams): Promise<UserProjectDailyHourReportResult> {
    const response = await get<ApiResponse<UserProjectDailyHourReportResult>>('/hours/report/user-project-daily/query', {
      yearMonth: params.yearMonth,
      ...(params.userIds && { userIds: params.userIds }),
      ...(params.departmentIds && { departmentIds: params.departmentIds }),
      ...(params.isHoliday !== undefined && { isHoliday: params.isHoliday }),
      ...(params.projectKeyword && { projectKeyword: params.projectKeyword })
    });
    return response.result;
  }

  /**
   * 导出人员项目每日工时分布报表
   */
  static async exportUserProjectDailyHourReport(params: UserProjectDailyHourReportParams): Promise<void> {
    const queryParams = new URLSearchParams({
      yearMonth: params.yearMonth,
      ...(params.userIds && { userIds: params.userIds }),
      ...(params.departmentIds && { departmentIds: params.departmentIds }),
      ...(params.isHoliday !== undefined && { isHoliday: params.isHoliday.toString() }),
      ...(params.projectKeyword && { projectKeyword: params.projectKeyword })
    });

    const url = `/hours/report/user-project-daily/export?${queryParams.toString()}`;
    await this.downloadFile(url, `人员项目每日工时分布_${params.yearMonth}.xlsx`);
  }

  /**
   * 获取部门工时差值分析报表
   */
  static async getDeptHourVarianceReport(params: DeptHourVarianceReportParams): Promise<DeptHourVarianceReportResult[]> {
    const response = await get<ApiResponse<DeptHourVarianceReportResult[]>>('/api/dept-variance/report', {
      year: params.year,
      ...(params.deptIds && { deptIds: params.deptIds }),
      ...(params.includeHoliday !== undefined && { includeHoliday: params.includeHoliday })
    });
    return response.result || [];
  }

  /**
   * 导出部门工时差值分析报表
   */
  static async exportDeptHourVarianceReport(params: DeptHourVarianceReportParams): Promise<void> {
    const queryParams = new URLSearchParams({
      year: params.year.toString(),
      ...(params.deptIds && { deptIds: params.deptIds }),
      ...(params.includeHoliday !== undefined && { includeHoliday: params.includeHoliday.toString() })
    });

    const holidayText = params.includeHoliday === true ? '(含假日)' : params.includeHoliday === false ? '(不含假日)' : '';
    const url = `/api/dept-variance/export?${queryParams.toString()}`;
    await this.downloadFile(url, `${params.year}年部门工时差值分析报表${holidayText}.xlsx`);
  }

  /**
   * 获取可选择的统计年份列表
   */
  static async getAvailableYears(): Promise<number[]> {
    const response = await get<ApiResponse<number[]>>('/api/dept-variance/years');
    return response.result || [];
  }

  /**
   * 获取人员工时差值分析报表
   */
  static async getUserHourVarianceReport(params: UserHourVarianceReportParams): Promise<UserHourVarianceReportResult[]> {
    const response = await get<ApiResponse<UserHourVarianceReportResult[]>>('/api/user-variance/report', {
      year: params.year,
      ...(params.userIds && { userIds: params.userIds }),
      ...(params.deptIds && { deptIds: params.deptIds }),
      ...(params.includeHoliday !== undefined && { includeHoliday: params.includeHoliday })
    });
    return response.result || [];
  }

  /**
   * 导出人员工时差值分析报表
   */
  static async exportUserHourVarianceReport(params: UserHourVarianceReportParams): Promise<void> {
    const queryParams = new URLSearchParams({
      year: params.year.toString(),
      ...(params.userIds && { userIds: params.userIds }),
      ...(params.deptIds && { deptIds: params.deptIds }),
      ...(params.includeHoliday !== undefined && { includeHoliday: params.includeHoliday.toString() })
    });

    const holidayText = params.includeHoliday === true ? '(含假日)' : params.includeHoliday === false ? '(不含假日)' : '';
    const url = `/api/user-variance/export?${queryParams.toString()}`;
    await this.downloadFile(url, `${params.year}年人员工时差值分析报表${holidayText}.xlsx`);
  }
}

// 部门项目工时统计报表相关接口定义
export interface DeptProjectHourReportParams {
  startDate: string;
  endDate: string;
  statPeriod?: 'daily' | 'weekly' | 'monthly';
  departmentIds?: string;
  projectIds?: string;
  isHoliday?: number;  // 假日统计选项：1-统计假日，0-不统计假日
}

export interface ProjectColumn {
  projectId: string;
  projectName: string;
  projectCode: string;
  totalHours: number;
  sortOrder: number;
  columnKey: string;
}

export interface DeptHourData {
  departmentId: string;
  departmentName: string;
  totalHours: number;
  projectHours: Record<string, number>;
  projectUserCounts: Record<string, number>;
}

export interface ReportSummary {
  dateRange: string;
  totalHours: number;
  deptCount: number;
  projectCount: number;
  userCount: number;
}

export interface DeptProjectHourReportResult {
  projectColumns: ProjectColumn[];
  deptHourData: DeptHourData[];
  totalRow: DeptHourData;
  summary: ReportSummary;
}

// 部门和项目类型定义
export interface Department {
  id: string;
  name: string;
  parentId?: string;
  children?: Department[];
}

export interface Project {
  id: string;
  projectCode: string;
  projectName: string;
  managerId?: string;
  managerName?: string;
  status?: string;
}

// 人员项目工时统计报表相关接口定义
export interface UserProjectHourReportParams {
  startDate: string;
  endDate: string;
  statPeriod?: 'daily' | 'weekly' | 'monthly';
  userIds?: string;
  projectIds?: string;
  isHoliday?: number;
}

export interface UserHourData {
  userId: string;
  userName: string;
  userCode: string;
  departmentId: string;
  departmentName: string;
  projectHours: Record<string, number>;
  totalHours: number;
}

export interface UserProjectHourReportResult {
  projectColumns: ProjectColumn[];
  userHourData: UserHourData[];
  totalRow: UserHourData;
  summary: ReportSummary;
}

// 部门项目每日工时分布报表相关接口定义
export interface DeptProjectDailyHourReportParams {
  yearMonth: string; // 格式: 2025-07
  departmentIds?: string; // 部门ID列表，多个用逗号分隔
  isHoliday?: number; // 是否统计假日数据 (0:不统计, 1:统计)
  projectKeyword?: string; // 项目关键字
}

// 人员项目每日工时分布报表相关接口定义
export interface UserProjectDailyHourReportParams {
  yearMonth: string; // 格式: 2025-07
  userIds?: string; // 用户ID列表，多个用逗号分隔
  departmentIds?: string; // 部门ID列表，多个用逗号分隔
  isHoliday?: number; // 是否统计假日数据 (0:不统计, 1:统计)
  projectKeyword?: string; // 项目关键字
}

export interface DayColumn {
  columnKey: string; // day_01, day_02, ...
  dayName: string; // 1, 2, 3, ...
  fullDate: string; // 2025-07-01
  isHoliday?: boolean;
}

export interface DeptProjectDailyHourData {
  departmentId: string;
  departmentName: string;
  projectId: string;
  projectName: string;
  projectCode: string;
  totalHours: number;
  dailyHours: Record<string, number>; // key: day_01, value: hours
  isTotalRow?: boolean;
}

export interface DeptProjectDailyHourReportResult {
  dayColumns: DayColumn[];
  deptProjectHourData: DeptProjectDailyHourData[];
  totalRow: DeptProjectDailyHourData;
  summary: {
    dateRange: string;
    totalHours: number;
    deptCount: number;
    projectCount: number;
    userCount: number;
    dayCount: number;
  };
}

export interface UserProjectDailyHourData {
  userId: string;
  userName: string;
  userCode: string;
  departmentId: string;
  departmentName: string;
  projectId: string;
  projectName: string;
  projectCode: string;
  totalHours: number;
  dailyHours: Record<string, number>; // key: day_01, value: hours
  isTotalRow?: boolean;
}

export interface UserProjectDailyHourReportResult {
  dayColumns: DayColumn[];
  userProjectHourData: UserProjectDailyHourData[];
  totalRow: UserProjectDailyHourData;
  summary: {
    dateRange: string;
    totalHours: number;
    userCount: number;
    projectCount: number;
    deptCount: number;
    dayCount: number;
  };
}

// 部门工时差值分析报表相关接口定义
export interface DeptHourVarianceReportParams {
  year: number; // 统计年份
  deptIds?: string; // 部门ID列表，多个用逗号分隔
  includeHoliday?: boolean; // 是否包含假日数据
}

export interface DeptHourVarianceReportResult {
  deptId: string;
  deptName: string;
  statYear: number;
  monthlyData: { [key: string]: number }; // key为字段名称(如"1月份实际工时")，value为数值
  yearlyTotal: {
    totalActualHours: number;
    totalRequiredHours: number;
    totalVarianceHours: number;
  };
}

// 人员工时差值分析报表相关接口定义
export interface UserHourVarianceReportParams {
  year: number; // 统计年份
  userIds?: string; // 用户ID列表，多个用逗号分隔
  deptIds?: string; // 部门ID列表，多个用逗号分隔
  includeHoliday?: boolean; // 是否包含假日数据
}

export interface UserHourVarianceReportResult {
  userId: string;
  userName: string;
  userCode: string;
  deptId: string;
  deptName: string;
  statYear: number;
  monthlyData: { [key: string]: number }; // key为字段名称(如"1月份实际工时")，value为数值
  yearlyTotal: {
    totalActualHours: number;
    totalRequiredHours: number;
    totalVarianceHours: number;
  };
}

// 简化的API调用方法
export const getDeptProjectHourReport = ReportApi.getDeptProjectHourReport.bind(ReportApi);
export const exportDeptProjectHourReport = ReportApi.exportDeptProjectHourReport.bind(ReportApi);
export const getUserProjectHourReport = ReportApi.getUserProjectHourReport.bind(ReportApi);
export const exportUserProjectHourReport = ReportApi.exportUserProjectHourReport.bind(ReportApi);
export const getAllDepartments = ReportApi.getAllDepartments.bind(ReportApi);
export const getAllProjects = ReportApi.getAllProjects.bind(ReportApi);
export const getAllUsers = ReportApi.getAllUsers.bind(ReportApi);
export const getDeptProjectDailyHourReport = ReportApi.getDeptProjectDailyHourReport.bind(ReportApi);
export const exportDeptProjectDailyHourReport = ReportApi.exportDeptProjectDailyHourReport.bind(ReportApi);
export const getUserProjectDailyHourReport = ReportApi.getUserProjectDailyHourReport.bind(ReportApi);
export const exportUserProjectDailyHourReport = ReportApi.exportUserProjectDailyHourReport.bind(ReportApi);
export const getDeptHourVarianceReport = ReportApi.getDeptHourVarianceReport.bind(ReportApi);
export const exportDeptHourVarianceReport = ReportApi.exportDeptHourVarianceReport.bind(ReportApi);
export const getUserHourVarianceReport = ReportApi.getUserHourVarianceReport.bind(ReportApi);
export const exportUserHourVarianceReport = ReportApi.exportUserHourVarianceReport.bind(ReportApi);
export const getAvailableYears = ReportApi.getAvailableYears.bind(ReportApi);
