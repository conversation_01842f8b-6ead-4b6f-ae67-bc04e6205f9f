package org.jeecg.modules.hours.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 工时单申请批次表
 */
@Data
@TableName("hour_record_apply")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "hour_record_apply对象", description = "工时单申请批次表")
public class HourRecordApply implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键，自增 */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键，自增")
    private Long id;

    /** 工时单ID */
    @ApiModelProperty(value = "工时单ID")
    private String recordId;

    /** 批次号/版本号 */
    @ApiModelProperty(value = "批次号/版本号")
    private String batchNo;

    /** 申请人ID */
    @ApiModelProperty(value = "申请人ID")
    private String applicantId;

    /** 申请人姓名 */
    @ApiModelProperty(value = "申请人姓名")
    private String applicantName;

    /** 申请时间 */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /** 申请说明 */
    @ApiModelProperty(value = "申请说明")
    private String description;

    /** 状态(0:审批中,1:已通过,2:已驳回) */
    @ApiModelProperty(value = "状态(0:审批中,1:已通过,2:已驳回)")
    private Integer status;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
} 