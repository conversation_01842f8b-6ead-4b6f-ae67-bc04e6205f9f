package org.jeecg.modules.hours.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.hours.service.IUserHourVarianceStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.YearMonth;

/**
 * 人员工时差值统计定时任务
 */
@Component
@Slf4j
public class UserHourVarianceStatJob {

    @Autowired
    private IUserHourVarianceStatService userHourVarianceStatService;

    /**
     * 每月1号凌晨2点执行上月的人员工时差值统计
     * cron表达式：秒 分 时 日 月 年
     */
//    @Scheduled(cron = "0 0 2 1 * ?")
    @Scheduled(cron = "0 */13 * * * ?")
    public void executeMonthlyVarianceStat() {
        LocalDate now = LocalDate.now();
        // 计算上个月的年月
        LocalDate lastMonth = now.minusMonths(1);
        int year = lastMonth.getYear();
        int month = lastMonth.getMonthValue();
        int monthValue = now.getMonthValue();
        try {
            log.info("开始执行人员工时差值统计定时任务：{}年{}月", year, month);
            
            int recordCount = userHourVarianceStatService.calculateMonthlyVarianceStat(year, month);

            int recordCount1 = userHourVarianceStatService.calculateMonthlyVarianceStat(year, monthValue);

            log.info("人员工时差值统计定时任务执行完成：{}年{}月，生成{}条记录", year, month, recordCount);
            
        } catch (Exception e) {
            log.error("执行人员工时差值统计定时任务失败：{}年{}月", year, month, e);
            // 这里可以添加告警通知机制
        }
    }
//    public static void main(String[] args) {
//        System.out.println(LocalDate.now().getMonthValue());
//    }
    /**
     * 手动执行指定年月的人员工时差值统计
     * @param year 年份
     * @param month 月份
     */
    public void executeManualVarianceStat(int year, int month) {
        try {
            log.info("手动执行人员工时差值统计：{}年{}月", year, month);
            
            int recordCount = userHourVarianceStatService.calculateMonthlyVarianceStat(year, month);
            
            log.info("手动执行人员工时差值统计完成：{}年{}月，生成{}条记录", year, month, recordCount);
            
        } catch (Exception e) {
            log.error("手动执行人员工时差值统计失败：{}年{}月", year, month, e);
            throw e;
        }
    }

    /**
     * 批量计算指定年份的所有月份统计
     * @param year 年份
     */
    public void executeYearlyVarianceStat(int year) {
        log.info("开始批量计算{}年的人员工时差值统计", year);
        
        int totalRecords = 0;
        for (int month = 1; month <= 12; month++) {
            try {
                int recordCount = userHourVarianceStatService.calculateMonthlyVarianceStat(year, month);
                totalRecords += recordCount;
                log.info("完成{}年{}月的人员工时差值统计，生成{}条记录", year, month, recordCount);
                
                // 避免数据库压力过大，每月之间稍作停顿
                Thread.sleep(1000);
                
            } catch (Exception e) {
                log.error("计算{}年{}月的人员工时差值统计失败", year, month, e);
                // 继续处理下一个月份，不中断整个批次
            }
        }
        
        log.info("批量计算{}年的人员工时差值统计完成，总共生成{}条记录", year, totalRecords);
    }
}