package org.jeecg.modules.hours.service.impl;

import org.jeecg.modules.hours.dto.UserProjectDailyHourReportDTO;
import org.jeecg.modules.hours.service.IUserProjectDailyHourExportService;
import org.jeecg.modules.hours.service.IUserProjectDailyHourStatisticsService;
import org.jeecg.modules.hours.vo.UserProjectDailyHourReportResult;
import org.jeecg.modules.hours.vo.UserProjectDailyHourReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.apache.poi.ss.usermodel.Workbook;

/**
 * @Description: 人员项目每日工时分布导出服务实现
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Service
@Slf4j
public class UserProjectDailyHourExportServiceImpl implements IUserProjectDailyHourExportService {

    @Autowired
    private IUserProjectDailyHourStatisticsService userProjectDailyHourStatisticsService;

    @Override
    public void exportUserProjectDailyHourReport(UserProjectDailyHourReportDTO reportDTO, HttpServletResponse response) {
        try {
            // 获取报表数据
            UserProjectDailyHourReportResult reportResult = 
                userProjectDailyHourStatisticsService.getUserProjectDailyHourReport(reportDTO);

            // 设置响应头
            String fileName = "人员项目每日工时分布报表_" + reportDTO.getYearMonth() + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", 
                "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));

            // 导出Excel格式
            exportToExcel(reportResult, response.getOutputStream());
            
            log.info("人员项目每日工时分布报表导出完成: {}", fileName);

        } catch (Exception e) {
            log.error("导出人员项目每日工时分布报表失败", e);
            throw new RuntimeException("导出报表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出为Excel格式
     */
    private void exportToExcel(UserProjectDailyHourReportResult reportResult, OutputStream outputStream) 
            throws IOException {
        
        // 构建列定义
        List<ExcelExportEntity> colList = new ArrayList<>();
        
        // 固定列
        colList.add(new ExcelExportEntity("姓名", "userName", 15));
        colList.add(new ExcelExportEntity("工号", "userCode", 15));
        colList.add(new ExcelExportEntity("项目", "projectName", 25));
        
        // 动态日期列 - 只添加当前月份实际存在的日期
        if (reportResult.getDayColumns() != null) {
            for (UserProjectDailyHourReportResult.DayColumnVO dayColumn : reportResult.getDayColumns()) {
                String columnTitle = dayColumn.getDayName();
                if (dayColumn.getIsHoliday() != null && dayColumn.getIsHoliday()) {
                    columnTitle += "(假)";
                }
                // 使用columnKey作为属性名
                colList.add(new ExcelExportEntity(columnTitle, dayColumn.getColumnKey(), 10));
            }
        }
        
        // 小计列
        colList.add(new ExcelExportEntity("小计", "totalHours", 15));
        
        // 构建数据列表 - 使用动态数据结构
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        // 添加数据行
        if (reportResult.getUserProjectHourData() != null) {
            for (UserProjectDailyHourReportVO row : reportResult.getUserProjectHourData()) {
                Map<String, Object> rowData = new HashMap<>();
                rowData.put("userName", row.getUserName());
                rowData.put("userCode", row.getUserCode());
                rowData.put("projectName", row.getProjectName() + 
                    (row.getProjectCode() != null ? " (" + row.getProjectCode() + ")" : ""));
                rowData.put("totalHours", row.getTotalHours());
                
                // 设置每日工时 - 动态设置
                if (reportResult.getDayColumns() != null && row.getDailyHours() != null) {
                    for (UserProjectDailyHourReportResult.DayColumnVO dayColumn : reportResult.getDayColumns()) {
                        BigDecimal hours = row.getDailyHours().get(dayColumn.getColumnKey());
                        rowData.put(dayColumn.getColumnKey(), hours != null ? hours : BigDecimal.ZERO);
                    }
                }
                
                dataList.add(rowData);
            }
        }
        
        // 添加总计行
        if (reportResult.getTotalRow() != null) {
            UserProjectDailyHourReportVO totalRow = reportResult.getTotalRow();
            Map<String, Object> totalData = new HashMap<>();
            totalData.put("userName", totalRow.getUserName());
            totalData.put("userCode", "");
            totalData.put("projectName", "");
            totalData.put("totalHours", totalRow.getTotalHours());
            
            // 设置总计的每日工时
            if (reportResult.getDayColumns() != null && totalRow.getDailyHours() != null) {
                for (UserProjectDailyHourReportResult.DayColumnVO dayColumn : reportResult.getDayColumns()) {
                    BigDecimal hours = totalRow.getDailyHours().get(dayColumn.getColumnKey());
                    totalData.put(dayColumn.getColumnKey(), hours != null ? hours : BigDecimal.ZERO);
                }
            }
            
            dataList.add(totalData);
        }
        
        // 创建导出参数
        ExportParams exportParams = new ExportParams("人员项目每日工时分布报表", "工时统计");
        exportParams.setCreateHeadRows(true);
        
        // 生成Excel
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, dataList);
        workbook.write(outputStream);
        workbook.close();
    }
}