package org.jeecg.hours;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import org.jeecg.modules.feishu.service.FeiShuUserService;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.attendance.v1.model.ChildItem;
import com.lark.oapi.service.attendance.v1.model.Item;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataRespBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UserStatsData;
import com.lark.oapi.service.attendance.v1.model.UserStatsDataCell;
import com.lark.oapi.service.attendance.v1.model.UserStatsView;
import com.lark.oapi.service.contact.v3.model.User;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 消息推送测试
 * @Author: lsq
 */
@Slf4j
//@ActiveProfiles("dev")
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class KaoQinTest2 {

	@Autowired
	private FsSyncTask faSyncTask;
	@Autowired
	private IFeishuAuthService feishuAuthService;

	private static final String appId = "cli_a7b2f4bbcb38500d";

	private static final String appSecret = "kzen0nGwHB15eEUVsDXkp1wn0YOF4HUK";
	
	
	@org.junit.jupiter.api.Test
	public void queryUser() throws Exception {
		
		String unionId = "on_a103bf00d2dd4e74022ceaa784a4654b";
		User fsUser = FeiShuUserService.instance(appId, appSecret).getUser(unionId);
//		on_03144b4670493fe27269925cf7e9cdbf
//		if(null!=fsUser) {
//			currentUserId = fsUser.getUserId();
//		}
		log.info("fsUser={}",fsUser);
		
		log.info("leader={}",fsUser.getLeaderUserId());
		
	}

//	@org.junit.jupiter.api.Test
	public void testname() throws Exception {

		// 考勤周期
		Integer start = 20250424;
		Integer end = 20250428;
		String currentUserId = "169ced82";
		String[] queryUserIds = new String[] { "b4b15ac7" };

		// 1.调用“查询统计表头”接口，查询目前考勤统计所支持查询的所有统计表头字段。开发者可以在这些字段中选出自己关注的字段。
		this.queryHeader01(start, end);
		// 2.调用“查询统计设置”接口，查询当前开发者账号已保存的统计表头字段。当开发者在调用“查询统计数据”接口时，所返回的统计数据就是这些表头字段的数据。
		UserStatsView userStatsView = this.queryStat02(currentUserId);
		// 3.调用“更新统计设置”接口，将需要查询的统计表头字段进行保存或更新。如果开发者有新的统计字段需要关注，或者希望剔除不感兴趣的统计字段，可以使用该接口更新表头字段。更新后，再次调用“查询统计数据”接口，就会返回包含了新字段或者剔除了不感兴趣字段的统计数据。
		// 需要采集的字段信息
		Map<String,String> fieldCodeMap = new HashMap<String, String>();
		if(null!=userStatsView) {
			// 设置过滤项
			for (Item item : userStatsView.getItems()) {
				boolean isBanCi = "班次信息".equals(item.getTitle());
				boolean isSt = "时长统计".equals(item.getTitle());
				ChildItem[] childItems = item.getChildItems();
				for (ChildItem childItem : childItems) {
					if(isBanCi && "日期".equals(childItem.getTitle())) {
						// 开关字段，0：关闭，1：开启
						childItem.setValue("1");
						fieldCodeMap.put("stat_date", childItem.getCode());
					}else if(isBanCi && "班次".equals(childItem.getTitle())) {
						// 开关字段，0：关闭，1：开启
						childItem.setValue("1");
						fieldCodeMap.put("shift", childItem.getCode());
					}else if(isSt && "加班时长".equals(childItem.getTitle())) {
						childItem.setValue("1");
						fieldCodeMap.put("over_work_hours", childItem.getCode());
					}else if(isSt && "班内工作时长".equals(childItem.getTitle())) {
						childItem.setValue("1");
						fieldCodeMap.put("work_hours", childItem.getCode());
					}
				}
				item.setChildItems(childItems);
//							log.info("item>>title={},code={}",item.getTitle(),item.getCode());
//							if(!("基本信息".equals(item.getTitle()) || "时长统计".equals(item.getTitle()) || "班次信息".equals(item.getTitle()))) {
//								item.setChildItems(setChildItem(item.getChildItems(), true));	
//							}
			}
			userStatsView.setItems(userStatsView.getItems());
			
			log.info("\n\nuserStatsView={}",Jsons.DEFAULT.toJson(userStatsView));
			
			this.queryUserStat03(currentUserId,userStatsView.getViewId(),userStatsView);
		}
		// 4.调用“查询统计数据”接口，进行统计数据的查询。
		QueryUserStatsDataRespBody resp = this.query04(start, end, currentUserId,queryUserIds);
		if(null!=resp) {
			
			
			for (UserStatsData userStat : resp.getUserDatas()) {
				Map<String,String> map = new HashMap<String, String>();
				// 用户ID、用户名称、日期、班内工作时长(小时)、加班时长、班次信息(确认是否是工作日?)
				map.put("userId", userStat.getUserId());
				map.put("userName", userStat.getName());
				
				Map<String,UserStatsDataCell> codeMap = this.parseMap(userStat.getDatas());
				// 动态字段
				for (String field : fieldCodeMap.keySet()) {
					String code = fieldCodeMap.get(field);
					
					UserStatsDataCell value = codeMap.get(code);
					// log.info("{}:{}",field,Jsons.DEFAULT.toJson(value));
					// 时长，这个字段是一个map，key位时间单位，value为对应的时长值
					if("over_work_hours".equals(field) || "work_hours".equals(field)) {
						map.put(field, value.getDurationNum().getHour());
					}else {
						map.put(field, value.getValue());
					}
					
					
				}
				log.info("userId={},name={},map={}\n\n",userStat.getUserId(),userStat.getName(),Jsons.DEFAULT.toJson(map));
			}
			
		}

		
	}
	
	private Map<String,UserStatsDataCell> parseMap(UserStatsDataCell[] userStatDatas){
		Map<String,UserStatsDataCell> map = new HashMap<String, UserStatsDataCell>();
		for (UserStatsDataCell userStatsDataCell : userStatDatas) {
			map.put(userStatsDataCell.getCode(), userStatsDataCell);
		}
		
		return map;
	}
	
	
	private ChildItem[] setChildItem(ChildItem[] childItem,boolean isClose) {
		for (ChildItem childItem2 : childItem) {
			// 开关字段，0：关闭，1：开启
			childItem2.setValue(isClose?"1":"0");
		}
		return childItem;
	}

	public QueryUserStatsDataRespBody query04(Integer start, Integer end, String currentUserId,String[] queryUserIds) throws Exception {

		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		QueryUserStatsDataReq req = QueryUserStatsDataReq.newBuilder().employeeType("employee_id")
				.queryUserStatsDataReqBody(QueryUserStatsDataReqBody.newBuilder().locale("zh").statsType("daily")
						.startDate(start).endDate(end).userIds(new String[] { "b4b15ac7" }).needHistory(true)
						.currentGroupOnly(true).userId(currentUserId).build())
				.build();

		// 发起请求
		QueryUserStatsDataResp resp = client.attendance().v1().userStatsData().query(req);

		// 处理服务端错误
		if (!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
							.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return null;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
		
		return resp.getData();

	}

	public void queryUserStat03(String currentUserId,String viewId,UserStatsView userStatsView) throws Exception {

		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		UpdateUserStatsViewReq req = UpdateUserStatsViewReq.newBuilder()
				.userStatsViewId(viewId)
				.employeeType(
						"employee_id")
				.updateUserStatsViewReqBody(
						UpdateUserStatsViewReqBody
								.newBuilder().view(
										userStatsView
//										UserStatsView.newBuilder()
//												.viewId("TnpNM05qUTFNVE0xTmpnM05UVTNNVEl3TXl0a1lXbHNlUT09")
//												.statsType("daily").userId(
//														currentUserId)
//												.items(new Item[] { Item.newBuilder().code("501")
//														.childItems(new ChildItem[] {
//																ChildItem.newBuilder().code("50101").value("1").build(),
//																ChildItem.newBuilder().code("50102").value("1").build(),
//																ChildItem.newBuilder().code("50111").value("0").build(),
//																ChildItem.newBuilder().code("50103").value("1").build(),
//																ChildItem.newBuilder().code("50104").value("0").build(),
//																ChildItem.newBuilder().code("50105").value("0").build(),
//																ChildItem.newBuilder().code("50107").value("0").build(),
//																ChildItem.newBuilder().code("9").value("1").build(),
//																ChildItem.newBuilder().code("50109").value("0").build(),
//																ChildItem.newBuilder().code("50110").value("0").build(),
//																ChildItem.newBuilder().code("101").value("0").build(),
//																ChildItem.newBuilder().code("93").value("0").build(),
//																ChildItem.newBuilder().code("94").value("0").build(),
//																ChildItem.newBuilder().code("95").value("0").build(),
//																ChildItem.newBuilder().code("96").value("0").build(),
//																ChildItem.newBuilder().code("84").value("0").build() })
//														.build(),
//														Item.newBuilder().code("512").childItems(new ChildItem[] {
//																ChildItem.newBuilder().code("51203").value("0").build(),
//																ChildItem.newBuilder().code("85").value("0").build(),
//																ChildItem.newBuilder().code("51201").value("0").build(),
//																ChildItem.newBuilder().code("51205").value("0").build(),
//																ChildItem.newBuilder().code("51204").value("0").build(),
//																ChildItem
//																		.newBuilder().code("51202").value("0")
//																		.build() })
//																.build(),
//														Item.newBuilder().code("515").childItems(new ChildItem[] {
//																ChildItem.newBuilder().code("51501").value("1").build(),
//																ChildItem.newBuilder().code("51504").value("1").build(),
//																ChildItem.newBuilder().code("51507").value("1").build(),
//																ChildItem.newBuilder().code("51502").value("1").build(),
//																ChildItem.newBuilder().code("51503").value("1").build(),
//																ChildItem.newBuilder().code("81").value("0").build(),
//																ChildItem.newBuilder().code("51505").value("0").build(),
//																ChildItem
//																		.newBuilder().code("51506").value("0")
//																		.build() })
//																.build(),
//														Item.newBuilder().code("513").childItems(new ChildItem[] {
//																ChildItem.newBuilder().code("51302").value("1").build(),
//																ChildItem.newBuilder().code("51303").value("1").build(),
//																ChildItem.newBuilder().code("51304").value("0").build(),
//																ChildItem.newBuilder().code("61").value("0").build(),
//																ChildItem.newBuilder().code("51315").value("0").build(),
//																ChildItem.newBuilder().code("51305").value("0").build(),
//																ChildItem.newBuilder().code("51313").value("0").build(),
//																ChildItem.newBuilder().code("51306").value("0").build(),
//																ChildItem.newBuilder().code("51316").value("0").build(),
//																ChildItem.newBuilder().code("51307").value("1").build(),
//																ChildItem.newBuilder().code("51308").value("0").build(),
//																ChildItem.newBuilder().code("51309").value("0").build(),
//																ChildItem.newBuilder().code("51310").value("0").build(),
//																ChildItem.newBuilder().code("51311").value("0").build(),
//																ChildItem.newBuilder().code("51312").value("0").build(),
//																ChildItem.newBuilder().code("51314").value("0").build(),
//																ChildItem.newBuilder().code("3001").value("0").build(),
//																ChildItem.newBuilder().code("3002").value("0").build(),
//																ChildItem.newBuilder().code("3003").value("0").build(),
//																ChildItem.newBuilder().code("3008").value("0").build(),
//																ChildItem.newBuilder().code("3011").value("0").build(),
//																ChildItem.newBuilder().code("3014").value("0").build(),
//																ChildItem.newBuilder().code("3009").value("0").build(),
//																ChildItem.newBuilder().code("3012").value("0").build(),
//																ChildItem.newBuilder().code("3015").value("0").build(),
//																ChildItem.newBuilder().code("3010").value("0").build(),
//																ChildItem.newBuilder().code("3013").value("0").build(),
//																ChildItem.newBuilder().code("3016").value("0")
//																		.build() })
//																.build(),
//														Item.newBuilder().code("514").childItems(new ChildItem[] {
//																ChildItem.newBuilder().code("51401").value("1").build(),
//																ChildItem.newBuilder().code("51402").value("0").build(),
//																ChildItem.newBuilder().code("51403").value("0").build(),
//																ChildItem.newBuilder().code("51404").value("0").build(),
//																ChildItem.newBuilder().code("87").value("0").build(),
//																ChildItem.newBuilder().code("51405").value("0").build(),
//																ChildItem.newBuilder().code("89").value("0").build(),
//																ChildItem.newBuilder().code("51406").value("0").build(),
//																ChildItem.newBuilder().code("51407").value("0").build(),
//																ChildItem.newBuilder().code("51408").value("0").build(),
//																ChildItem.newBuilder().code("51409").value("0")
//																		.build() })
//																.build(),
//														Item.newBuilder().code("1007").childItems(new ChildItem[] {
//
//														}).build() }).build()
												
												).build()).build();

		// 发起请求
		UpdateUserStatsViewResp resp = client.attendance().v1().userStatsView().update(req);

		// 处理服务端错误
		if (!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
							.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
	}

	public UserStatsView queryStat02(String currentUserId) throws Exception {

		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		QueryUserStatsViewReq req = QueryUserStatsViewReq.newBuilder().employeeType("employee_id")
				.queryUserStatsViewReqBody(QueryUserStatsViewReqBody.newBuilder().locale("zh").statsType("daily")
						.userId(currentUserId).build())
				.build();

		// 发起请求
		QueryUserStatsViewResp resp = client.attendance().v1().userStatsView().query(req);

		// 处理服务端错误
		if (!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
							.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return null;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
		
		return resp.getData().getView();

	}

	public void queryHeader01(Integer start, Integer end) throws Exception {
		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		QueryUserStatsFieldReq req = QueryUserStatsFieldReq.newBuilder().employeeType("employee_id")
				.queryUserStatsFieldReqBody(QueryUserStatsFieldReqBody.newBuilder().locale("zh").statsType("daily")
						.startDate(start).endDate(end).build())
				.build();

		// 发起请求
		QueryUserStatsFieldResp resp = client.attendance().v1().userStatsField().query(req);

		// 处理服务端错误
		if (!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
							.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
	}

}
