/**
 * API接口测试组件
 * 用于测试级联选择器的各个API接口是否正常返回分页数据
 */
import React, { useState } from 'react';
import { Card, Typography, Space, Button, Notification, Table, Tag } from '@douyinfe/semi-ui';
import { get } from '../../../utils/request';
import { ApiResponse } from '../types';

const { Title, Text } = Typography;

interface ApiTestResult {
  url: string;
  method: string;
  status: 'pending' | 'success' | 'error';
  response?: any;
  error?: string;
  timestamp?: string;
}

const ApiTest: React.FC = () => {
  const [testResults, setTestResults] = useState<ApiTestResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 添加测试结果
  const addTestResult = (result: ApiTestResult) => {
    setTestResults(prev => [...prev, {
      ...result,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  // 测试工作项接口
  const testWorkItems = async () => {
    const testResult: ApiTestResult = {
      url: '/hours/workItemCascade/workItems',
      method: 'GET',
      status: 'pending'
    };

    try {
      const response = await get<ApiResponse<any>>('/hours/workItemCascade/workItems');
      testResult.status = 'success';
      testResult.response = response;
      
      console.log('工作项接口响应:', response);
      
      if (response?.success) {
        Notification.success({
          title: '工作项接口测试成功',
          content: `返回 ${response.result?.length || 0} 条数据`
        });
      } else {
        Notification.warning({
          title: '工作项接口返回失败',
          content: response?.message || '未知错误'
        });
      }
    } catch (error) {
      testResult.status = 'error';
      testResult.error = String(error);
      console.error('工作项接口测试失败:', error);
      Notification.error({
        title: '工作项接口测试失败',
        content: String(error)
      });
    }

    addTestResult(testResult);
  };

  // 测试实例接口（分页）
  const testInstances = async () => {
    // 首先获取一个工作项ID
    try {
      const workItemsResponse = await get<ApiResponse<any>>('/hours/workItemCascade/workItems');
      if (!workItemsResponse?.success || !workItemsResponse.result?.length) {
        Notification.error({
          title: '测试失败',
          content: '无法获取工作项数据'
        });
        return;
      }

      const firstWorkItem = workItemsResponse.result[0];
      const workItemKey = firstWorkItem.value;

      const testResult: ApiTestResult = {
        url: `/hours/workItemCascade/instances?workItemKey=${workItemKey}&pageNo=1&pageSize=5`,
        method: 'GET',
        status: 'pending'
      };

      try {
        const params = new URLSearchParams();
        params.append('workItemKey', String(workItemKey));
        params.append('pageNo', '1');
        params.append('pageSize', '5');

        const response = await get<ApiResponse<any>>(`/hours/workItemCascade/instances?${params.toString()}`);
        testResult.status = 'success';
        testResult.response = response;
        
        console.log('实例接口响应:', response);
        
        if (response?.success) {
          const total = response.result?.total || 0;
          const records = response.result?.records?.length || 0;
          
          Notification.success({
            title: '实例接口测试成功',
            content: `返回 ${records} 条数据，总计 ${total} 条`
          });
        } else {
          Notification.warning({
            title: '实例接口返回失败',
            content: response?.message || '未知错误'
          });
        }
      } catch (error) {
        testResult.status = 'error';
        testResult.error = String(error);
        console.error('实例接口测试失败:', error);
        Notification.error({
          title: '实例接口测试失败',
          content: String(error)
        });
      }

      addTestResult(testResult);
    } catch (error) {
      console.error('获取工作项失败:', error);
      Notification.error({
        title: '测试失败',
        content: '无法获取工作项数据'
      });
    }
  };

  // 测试任务接口（分页）
  const testTasks = async () => {
    const testResult: ApiTestResult = {
      url: '/hours/workItemCascade/tasks?workItemKey=xxx&instanceKey=xxx&nodeKey=xxx&pageNo=1&pageSize=50',
      method: 'GET',
      status: 'pending'
    };

    try {
      // 这里使用模拟数据，实际应该从前面的选择中获取
      const params = new URLSearchParams();
      params.append('workItemKey', 'test_work_item');
      params.append('instanceKey', 'test_instance');
      params.append('nodeKey', 'test_node');
      params.append('pageNo', '1');
      params.append('pageSize', '50');

      const response = await get<ApiResponse<any>>(`/hours/workItemCascade/tasks?${params.toString()}`);
      testResult.status = 'success';
      testResult.response = response;
      
      console.log('任务接口响应:', response);
      
      if (response?.success) {
        const total = response.result?.total || 0;
        const records = response.result?.records?.length || 0;
        
        Notification.success({
          title: '任务接口测试成功',
          content: `返回 ${records} 条数据，总计 ${total} 条`
        });
      } else {
        Notification.warning({
          title: '任务接口返回失败',
          content: response?.message || '未知错误'
        });
      }
    } catch (error) {
      testResult.status = 'error';
      testResult.error = String(error);
      console.error('任务接口测试失败:', error);
      Notification.error({
        title: '任务接口测试失败',
        content: String(error)
      });
    }

    addTestResult(testResult);
  };

  // 运行所有测试
  const runAllTests = async () => {
    setLoading(true);
    setTestResults([]);
    
    try {
      await testWorkItems();
      await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒
      
      await testInstances();
      await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒
      
      await testTasks();
    } finally {
      setLoading(false);
    }
  };

  // 清空测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  // 表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      width: 100,
    },
    {
      title: '接口',
      dataIndex: 'url',
      width: 300,
      render: (text: string) => <Text code>{text}</Text>
    },
    {
      title: '方法',
      dataIndex: 'method',
      width: 80,
      render: (text: string) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status: string) => {
        const colorMap = {
          pending: 'orange',
          success: 'green',
          error: 'red'
        };
        return <Tag color={colorMap[status as keyof typeof colorMap]}>{status}</Tag>;
      }
    },
    {
      title: '响应',
      dataIndex: 'response',
      render: (response: any, record: ApiTestResult) => {
        if (record.status === 'error') {
          return <Text type="danger">{record.error}</Text>;
        }
        if (response?.success) {
          const total = response.result?.total;
          const records = response.result?.records?.length || response.result?.length || 0;
          return <Text type="success">{total ? `${records}/${total}` : `${records} 条`}</Text>;
        }
        return <Text type="warning">{response?.message || '失败'}</Text>;
      }
    }
  ];

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>API接口测试</Title>
        
        <Space vertical style={{ width: '100%' }} spacing={24}>
          {/* 操作按钮 */}
          <div>
            <Space>
              <Button 
                onClick={runAllTests} 
                type="primary" 
                loading={loading}
                disabled={loading}
              >
                运行所有测试
              </Button>
              <Button onClick={testWorkItems} disabled={loading}>
                测试工作项接口
              </Button>
              <Button onClick={testInstances} disabled={loading}>
                测试实例接口
              </Button>
              <Button onClick={testTasks} disabled={loading}>
                测试任务接口
              </Button>
              <Button onClick={clearResults} type="tertiary">
                清空结果
              </Button>
            </Space>
          </div>

          {/* 测试结果表格 */}
          <div>
            <Text strong>测试结果：</Text>
            <Table
              columns={columns}
              dataSource={testResults}
              pagination={false}
              size="small"
              style={{ marginTop: '8px' }}
              empty="暂无测试结果"
            />
          </div>

          {/* 说明 */}
          <div>
            <Text strong>测试说明：</Text>
            <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
              <li>工作项接口：测试是否能正常获取工作项列表</li>
              <li>实例接口：测试分页功能，每页5条记录</li>
              <li>任务接口：测试分页功能，每页50条记录</li>
              <li>检查响应中是否包含 total 字段用于分页判断</li>
            </ul>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default ApiTest;
