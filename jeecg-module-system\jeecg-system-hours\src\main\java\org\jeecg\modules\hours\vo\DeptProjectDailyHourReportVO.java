package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description: 部门项目每日工时分布报表VO
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DeptProjectDailyHourReportVO", description = "部门项目每日工时分布报表VO")
public class DeptProjectDailyHourReportVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private String departmentId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目代码
     */
    @ApiModelProperty(value = "项目代码")
    private String projectCode;

    /**
     * 行总工时（小计）
     */
    @ApiModelProperty(value = "行总工时")
    private BigDecimal totalHours;

    /**
     * 每日工时分布 Map<day, hours>
     * key: day_${day} (如: day_01, day_02, ...)
     * value: 工时数
     */
    @ApiModelProperty(value = "每日工时分布")
    private Map<String, BigDecimal> dailyHours;

    /**
     * 是否总计行
     */
    @ApiModelProperty(value = "是否总计行")
    private Boolean isTotalRow;

    // 以下字段用于查询结果映射，不是最终输出字段
    /**
     * 统计日期（查询结果字段）
     */
    @ApiModelProperty(value = "统计日期", hidden = true)
    private String statDate;

    /**
     * 单日工时（查询结果字段）
     */
    @ApiModelProperty(value = "单日工时", hidden = true)
    private BigDecimal dayHours;
}