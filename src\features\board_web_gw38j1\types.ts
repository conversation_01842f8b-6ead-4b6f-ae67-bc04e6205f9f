// 工时明细记录接口
export interface WorkDetail {
  key: string | number;
  type: string;
  typeInfo: string;
  businessLevel1: string;
  businessLevel2: string;
  status: string;
  hours: number;
  description: string;
  children?: WorkDetail[];
  workItemId: string;
  workItemName: string;
  instanceId: string;
  instanceName: string;
  nodeId: string;
  nodeName: string;
  taskId: string;
  taskName: string;
}

// 工作内容级联选择数据接口
export interface CascaderItem {
  label: string;
  value: string | number;
  isLeaf?: boolean;
  children?: CascaderItem[];
  loading?: boolean;
  [key: string]: any;
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
  success: boolean;
  result?: any;
}

// 工作项接口
export interface WorkItemData {
  value: string | number;
  label: string;
  status?: string;
  itemType?: string;
  [key: string]: any;
}

// 工作实例接口
export interface InstanceData {
  value: string | number;
  label: string;
  status?: string;
  [key: string]: any;
}

// 节点接口
export interface NodeData {
  value: string | number;
  label: string;
  status?: string;
  workflowId?: string;
  [key: string]: any;
}

// 任务接口
export interface TaskData {
  value: string | number;
  label: string;
  status?: string;
  assignee?: string;
  assigneeName?: string;
  estimateHours?: number;
  [key: string]: any;
}

// 分页结果接口
export interface PagedResult<T> {
  records: T[];
  total: number;
  pageNo: number;
  pageSize: number;
}

// 工时记录接口
export interface HourRecord {
  id?: number;
  userId: string;
  userName: string;
  workDate: string;
  hours: number;
  description: string;
  projectId: string;
  workItemId: string;
  workItemName: string;
  instanceId: string;
  instanceName: string;
  nodeId: string;
  nodeName: string;
  taskId: string;
  taskName: string;
  status: number;
  createdAt?: string;
  updatedAt?: string;
  submitTime?: string;
  approveTime?: string;
  approverId?: string;
  approverName?: string;
  rejectReason?: string;
} 