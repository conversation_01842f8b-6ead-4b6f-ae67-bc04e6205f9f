package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.entity.FsUserRole;
import org.jeecg.modules.hours.entity.FsRole;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 用户角色表
 * @Author: jeecg-boot
 * @Date:   2024-05-28
 * @Version: V1.0
 */
public interface IFsUserRoleService extends IService<FsUserRole> {
    
    /**
     * 为用户分配角色
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean assignRole(String userId, String roleId);
    
    boolean batchAssignRole(List<String> userIds,String roleId);
    
    /**
     * 移除用户角色
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean removeRole(String userId, String roleId);
    
    /**
     * 获取用户角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    List<FsRole> getUserRoles(String userId);
    
    /**
     * 检查用户是否具有指定角色
     * @param userId 用户ID
     * @param roleCode 角色编码
     * @return 是否具有该角色
     */
    boolean hasRole(String userId, String roleCode);
    
    /**
     * 检查当前用户是否具有指定角色
     * @param role 角色编码
     * @return 是否具有该角色
     */
    boolean hasRole(String role);
    
    /**
     * 批量分配角色
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean assignRoles(String userId, List<String> roleIds);
    
    /**
     * 获取角色的用户列表
     * @param roleId 角色ID
     * @return 用户角色关系列表
     */
    List<FsUserRole> getUserRolesByRoleId(String roleId);
    
    /**
     * 获取当前用户的角色列表
     * @return 角色列表
     */
    List<String> getCurrentUserRoles();
    
} 