package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 员工工时实例分布报表响应
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "EmployeeInstanceDistributionResponse", description = "员工工时实例分布报表响应")
public class EmployeeInstanceDistributionResponse {

    @ApiModelProperty(value = "报表数据")
    private List<EmployeeInstanceDistributionData> data;

    @ApiModelProperty(value = "实例列表")
    private List<String> instances;

    @ApiModelProperty(value = "总记录数")
    private Long total;

    @ApiModelProperty(value = "合计数据")
    private EmployeeInstanceSummary summary;

    /**
     * 员工工时实例分布数据
     */
    @Data
    @ApiModel(value = "EmployeeInstanceDistributionData", description = "员工工时实例分布数据")
    public static class EmployeeInstanceDistributionData {

        @ApiModelProperty(value = "员工姓名")
        private String employeeName;

        @ApiModelProperty(value = "员工ID")
        private String employeeId;

        @ApiModelProperty(value = "实例数据")
        private Map<String, Double> instances;

        @ApiModelProperty(value = "小计(小时)")
        private Double totalHours;

        @ApiModelProperty(value = "折算天数(天)")
        private Double totalDays;
    }

    /**
     * 员工工时实例分布合计数据
     */
    @Data
    @ApiModel(value = "EmployeeInstanceSummary", description = "员工工时实例分布合计数据")
    public static class EmployeeInstanceSummary {

        @ApiModelProperty(value = "各实例合计")
        private Map<String, Double> instances;

        @ApiModelProperty(value = "总工时")
        private Double grandTotalHours;

        @ApiModelProperty(value = "总天数")
        private Double grandTotalDays;
    }
}
