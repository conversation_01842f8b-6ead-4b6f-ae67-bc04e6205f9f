package org.jeecg.modules.plm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.UUIDGenerator;
import org.jeecg.modules.plm.entity.PlmProjectMember;
import org.jeecg.modules.plm.mapper.PlmProjectMemberMapper;
import org.jeecg.modules.plm.service.IPlmProjectMemberService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * PLM项目成员Service实现类
 */
@Service
@Slf4j
public class PlmProjectMemberServiceImpl extends ServiceImpl<PlmProjectMemberMapper, PlmProjectMember> implements IPlmProjectMemberService {

    @Override
    public List<PlmProjectMember> queryByProjectId(String projectId) {
        return baseMapper.queryByProjectId(projectId);
    }
    
    @Override
    public List<PlmProjectMember> queryByProjectIds(List<String> projectIds) {
        if (projectIds == null || projectIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        return baseMapper.queryByProjectIds(projectIds);
    }

    @Override
    public List<PlmProjectMember> queryByUserId(String userId) {
        return baseMapper.queryByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchInsert(List<PlmProjectMember> memberList) {
        if (memberList == null || memberList.isEmpty()) {
            return true;
        }
        
        // 为每个成员生成ID并设置创建时间
        for (PlmProjectMember member : memberList) {
            if (member.getId() == null || member.getId().isEmpty()) {
                member.setId(UUIDGenerator.generate());
            }
            member.setCreateTime(new Date());
        }
        
        return baseMapper.batchInsert(memberList) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProjectMembers(String projectId, String projectCode, List<PlmProjectMember> members) {
        try {
            // 删除原有成员
            LambdaQueryWrapper<PlmProjectMember> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PlmProjectMember::getProjectId, projectId);
            remove(queryWrapper);
            
            // 如果没有新成员，直接返回成功
            if (members == null || members.isEmpty()) {
                return true;
            }
            
            // 准备新成员数据
            List<PlmProjectMember> memberList = new ArrayList<>();
            Date now = new Date();
            
            for (PlmProjectMember member : members) {
                member.setId(UUIDGenerator.generate());
                member.setProjectId(projectId);
                member.setProjectCode(projectCode);
                member.setCreateTime(now);
                memberList.add(member);
            }
            
            // 批量插入新成员
            return saveBatch(memberList);
        } catch (Exception e) {
            log.error("更新项目成员异常, projectId: {}", projectId, e);
            return false;
        }
    }
}