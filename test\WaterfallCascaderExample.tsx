/**
 * WaterfallCascader 使用示例
 * 展示重构后基于Semi Design异步加载模式的用法
 */
import React, { useState } from 'react';
import { Card, Typography, Space, Button, Notification } from '@douyinfe/semi-ui';
import WaterfallCascader from './WaterfallCascader';
import { CascaderItem } from '../types';

const { Title, Text, Paragraph } = Typography;

const WaterfallCascaderExample: React.FC = () => {
  const [value, setValue] = useState<(string | number)[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<CascaderItem[]>([]);
  const [statisticsData, setStatisticsData] = useState<any>(null);

  // 处理级联选择器值变化
  const handleChange = (newValue: (string | number)[], options: CascaderItem[]) => {
    console.log('选择值变化:', newValue, options);
    setValue(newValue);
    setSelectedOptions(options);
  };

  // 处理统计信息加载
  const handleStatisticsLoad = (workItemKey: string, instanceKey: string, nodeKey: string) => {
    console.log('触发统计信息加载:', { workItemKey, instanceKey, nodeKey });
    
    // 这里可以调用实际的统计API
    setStatisticsData({
      workItemKey,
      instanceKey,
      nodeKey,
      loadTime: new Date().toLocaleString()
    });

    Notification.success({
      title: '统计信息加载',
      content: `已触发统计信息加载: ${workItemKey} -> ${instanceKey} -> ${nodeKey}`
    });
  };

  // 清空选择
  const handleClear = () => {
    setValue([]);
    setSelectedOptions([]);
    setStatisticsData(null);
  };

  // 自定义显示格式
  const displayRender = (labels: string[]) => {
    return labels.join(' → ');
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title heading={2}>WaterfallCascader 重构示例</Title>
      
      <Paragraph>
        基于Semi Design官方异步加载模式重构的瀑布流级联选择器，支持四级级联选择：
        工作项 → 实例 → 节点 → 任务
      </Paragraph>

      <Card title="基本用法" style={{ marginBottom: '24px' }}>
        <Space vertical style={{ width: '100%' }}>
          <WaterfallCascader
            value={value}
            onChange={handleChange}
            onStatisticsLoad={handleStatisticsLoad}
            placeholder="请选择工作内容（支持异步加载）"
            displayRender={displayRender}
            style={{ width: '400px' }}
          />
          
          <Space>
            <Button onClick={handleClear} type="secondary">
              清空选择
            </Button>
          </Space>
        </Space>
      </Card>

      <Card title="选择状态" style={{ marginBottom: '24px' }}>
        <Space vertical>
          <div>
            <Text strong>当前选择值: </Text>
            <Text code>{JSON.stringify(value)}</Text>
          </div>
          
          <div>
            <Text strong>选择路径: </Text>
            <Text>
              {selectedOptions.map(opt => opt.label).join(' → ') || '未选择'}
            </Text>
          </div>
          
          <div>
            <Text strong>选择级别: </Text>
            <Text>{value.length}/4 级</Text>
          </div>
        </Space>
      </Card>

      {statisticsData && (
        <Card title="统计信息" style={{ marginBottom: '24px' }}>
          <Space vertical>
            <div>
              <Text strong>工作项: </Text>
              <Text code>{statisticsData.workItemKey}</Text>
            </div>
            <div>
              <Text strong>实例: </Text>
              <Text code>{statisticsData.instanceKey}</Text>
            </div>
            <div>
              <Text strong>节点: </Text>
              <Text code>{statisticsData.nodeKey}</Text>
            </div>
            <div>
              <Text strong>触发时间: </Text>
              <Text>{statisticsData.loadTime}</Text>
            </div>
          </Space>
        </Card>
      )}

      <Card title="重构特性">
        <Space vertical>
          <div>
            <Text strong>✅ 异步加载: </Text>
            <Text>基于Semi Design官方loadData模式，无需"加载更多"按钮</Text>
          </div>
          <div>
            <Text strong>✅ 数据缓存: </Text>
            <Text>避免重复请求，提升性能</Text>
          </div>
          <div>
            <Text strong>✅ 错误处理: </Text>
            <Text>友好的错误提示和异常处理</Text>
          </div>
          <div>
            <Text strong>✅ 统计回调: </Text>
            <Text>选择完整路径后自动触发统计信息加载</Text>
          </div>
          <div>
            <Text strong>✅ 类型安全: </Text>
            <Text>完整的TypeScript类型定义</Text>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default WaterfallCascaderExample;
