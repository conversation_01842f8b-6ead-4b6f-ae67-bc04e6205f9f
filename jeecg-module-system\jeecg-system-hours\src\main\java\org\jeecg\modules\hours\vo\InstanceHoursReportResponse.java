package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 实例工时周期汇总报表响应
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "InstanceHoursReportResponse", description = "实例工时周期汇总报表响应")
public class InstanceHoursReportResponse {

    @ApiModelProperty(value = "报表数据")
    private List<InstanceHoursReportData> data;

    @ApiModelProperty(value = "时间周期列表")
    private List<String> periods;

    @ApiModelProperty(value = "总记录数")
    private Long total;

    @ApiModelProperty(value = "合计数据")
    private Map<String, Double> summary;

    /**
     * 实例工时报表数据
     */
    @Data
    @ApiModel(value = "InstanceHoursReportData", description = "实例工时报表数据")
    public static class InstanceHoursReportData {

        @ApiModelProperty(value = "实例名称")
        private String instanceName;

        @ApiModelProperty(value = "时间周期数据")
        private Map<String, Double> periods;

        @ApiModelProperty(value = "小计")
        private Double total;
    }
}
