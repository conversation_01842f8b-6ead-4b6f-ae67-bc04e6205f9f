package org.jeecg.modules.hours.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class HourRecordApplyServiceTest {

    @Autowired
    private IHourRecordApplyService hourRecordApplyService;

    @Test
    public void testGenerateBatchNo() {
        // 测试生成批次号
        String batchNo1 = hourRecordApplyService.generateBatchNo();
        String batchNo2 = hourRecordApplyService.generateBatchNo();
        String batchNo3 = hourRecordApplyService.generateBatchNo();
        
        // 验证批次号格式
        assertNotNull(batchNo1);
        assertNotNull(batchNo2);
        assertNotNull(batchNo3);
        assertTrue(batchNo1.matches("SP\\d{8}"), "批次号格式不正确: " + batchNo1);
        assertTrue(batchNo2.matches("SP\\d{8}"), "批次号格式不正确: " + batchNo2);
        assertTrue(batchNo3.matches("SP\\d{8}"), "批次号格式不正确: " + batchNo3);
        
        // 验证批次号不重复
        assertNotEquals(batchNo1, batchNo2, "批次号应该不重复");
        assertNotEquals(batchNo2, batchNo3, "批次号应该不重复");
        assertNotEquals(batchNo1, batchNo3, "批次号应该不重复");
        
        // 验证批次号递增
        String sequence1 = batchNo1.substring(8);
        String sequence2 = batchNo2.substring(8);
        String sequence3 = batchNo3.substring(8);
        int seq1 = Integer.parseInt(sequence1);
        int seq2 = Integer.parseInt(sequence2);
        int seq3 = Integer.parseInt(sequence3);
        assertEquals(seq1 + 1, seq2, "批次号序号应该递增");
        assertEquals(seq2 + 1, seq3, "批次号序号应该递增");
        
        System.out.println("生成的批次号1: " + batchNo1 + " (序号: " + seq1 + ")");
        System.out.println("生成的批次号2: " + batchNo2 + " (序号: " + seq2 + ")");
        System.out.println("生成的批次号3: " + batchNo3 + " (序号: " + seq3 + ")");
        
        // 验证日期前缀相同
        String prefix1 = batchNo1.substring(0, 8);
        String prefix2 = batchNo2.substring(0, 8);
        String prefix3 = batchNo3.substring(0, 8);
        assertEquals(prefix1, prefix2, "同一天的批次号前缀应该相同");
        assertEquals(prefix2, prefix3, "同一天的批次号前缀应该相同");
    }

    @Test
    public void testGenerateBatchNoWithGaps() {
        // 模拟场景：假设数据库中已有 SP25071100001, SP25071100003 (00002被删除了)
        // 这种情况下，新生成的批次号应该是 SP25071100004
        
        // 生成第一个批次号
        String batchNo1 = hourRecordApplyService.generateBatchNo();
        System.out.println("第一个批次号: " + batchNo1);
        
        // 生成第二个批次号
        String batchNo2 = hourRecordApplyService.generateBatchNo();
        System.out.println("第二个批次号: " + batchNo2);
        
        // 验证序号递增
        String sequence1 = batchNo1.substring(8);
        String sequence2 = batchNo2.substring(8);
        int seq1 = Integer.parseInt(sequence1);
        int seq2 = Integer.parseInt(sequence2);
        
        assertEquals(seq1 + 1, seq2, "即使有删除的记录，序号也应该正确递增");
        
        System.out.println("序号1: " + seq1 + ", 序号2: " + seq2);
    }
} 