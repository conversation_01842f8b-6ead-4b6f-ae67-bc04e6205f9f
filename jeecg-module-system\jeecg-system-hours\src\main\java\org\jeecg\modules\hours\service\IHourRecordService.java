package org.jeecg.modules.hours.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.jeecg.modules.hours.entity.HourRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.dto.AvailableDateDTO;
import org.jeecg.modules.hours.dto.HourRecordDTO;
import org.jeecg.modules.hours.dto.WorkPeriodDTO;
import org.jeecg.modules.hours.vo.AvailableDateVO;
import org.jeecg.modules.hours.vo.HourRecordBatchSaveVO;
import org.jeecg.modules.hours.vo.WorkPeriodVO;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Description: 工时记录表
 * @Author: jeecg-boot
 * @Date: 2023-07-01
 * @Version: V1.0
 */
public interface IHourRecordService extends IService<HourRecord> {
	
	IPage<HourRecord> queryPageList(Page<HourRecord> page,String startDate,String endDate,String deptIds,String status);
    
    /**
     * 根据用户ID和日期区间查询工时记录
     * @param page 分页参数
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分页结果
     */
    IPage<HourRecord> queryByUserAndDateRange(Page<HourRecord> page, String userId, String startDate, String endDate);
    
    /**
     * 根据项目ID查询工时统计
     * @param projectId 项目ID
     * @return 工时统计结果
     */
    List<Map<String, Object>> queryHoursSummaryByProject(String projectId);
    
    /**
     * 根据用户ID获取最近工时记录
     * @param userId 用户ID
     * @param limit 限制条数
     * @return 最近工时记录
     */
    List<HourRecord> queryRecentRecordsByUser(String userId, int limit);

    /**
     * 从考勤统计表获取可登记工时
     * @param unionIds 用户unionId列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 可登记工时总数
     */
    BigDecimal getScheduledHoursFromAttendance(List<String> unionIds, String startDate, String endDate);

    /**
     * 批量提交工时记录
     * @param ids 工时记录ID列表
     * @return 是否成功
     */
    boolean batchSubmitHourRecords(List<Long> ids);
    
    /**
     * 审批工时记录
     * @param id 工时记录ID
     * @param approverId 审批人ID
     * @param approverName 审批人姓名
     * @param approve 是否通过 true-通过 false-驳回
     * @param rejectReason 驳回原因
     * @return 是否成功
     */
    boolean approveHourRecord(Long id, String approverId, String approverName, boolean approve, String rejectReason);
    
    /**
     * 校验工时是否超出可登记上限
     * @param userId 用户ID
     * @param workDate 工作日期
     * @param hours 要登记的工时
     * @param recordId 排除的记录ID (更新时使用)
     * @return 是否超出上限
     */
    boolean checkHoursExceedLimit(String userId, String workDate, BigDecimal hours, Long recordId);
    
    /**
     * 获取已登记工时
     * 
     * @param projectKey 项目Key
     * @param workItemKey 工作项Key
     * @param instanceKey 实例Key
     * @param nodeKey 节点Key
     * @param taskKey 任务Key
     * @return 已登记工时(小时)
     */
//    BigDecimal getRecordedHours(String projectKey, String workItemKey, String instanceKey, String nodeKey, String taskKey);
    
    /**
     * 批量获取已登记工时
     * @param projectKey 项目Key
     * @param workItemKeys 工作项Key列表
     * @param instanceKeys 实例Key列表
     * @param nodeKeys 节点Key列表
     * @param taskKeys 任务Key列表
     * @return Map<String, BigDecimal> key为各级ID，value为对应的已登记工时
     */
//    Map<String, BigDecimal> getRecordedHoursBatch(String projectKey, List<String> workItemKeys,
//        List<String> instanceKeys, List<String> nodeKeys, List<String> taskKeys);

    /**
     * 获取当日已登记工时
     * @param projectKey 项目Key
     * @param userId 用户ID
     * @param workDate 工作日期
     * @return 当日已登记工时总数
     */
    BigDecimal getDailyRecordedHours(String projectKey, String userId, String workDate);

    /**
     * 批量保存工时记录
     *
     * @param hourRecordDTO 工时记录DTO
     */
    void batchSave(HourRecordDTO hourRecordDTO);

    /**
     * 批量提交工时记录
     *
     * @param ids 工时记录ID列表
     */
    void batchSubmit(List<String> ids);

    /**
     * 删除工时记录
     *
     * @param id 工时记录ID
     */
    void deleteHourRecord(String id);

    /**
     * 获取可登记工时日期
     *
     * @param month     月份
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 可登记工时日期列表
     */
    List<AvailableDateVO> getAvailableDates(String month, String startDate, String endDate,String userKey);

    /**
     * 获取工作周期选项
     *
     * @param month 月份
     * @return 工作周期选项列表
     */
    List<WorkPeriodVO> getWorkPeriods(String month);

    /**
     * 批量保存工时记录
     *
     * @param batchSaveVO 批量保存参数
     */
    void batchSave(HourRecordBatchSaveVO batchSaveVO);

    /**
     * 批量提交工时记录
     *
     * @param batchSaveVO 批量提交参数
     */
    void batchSaveSubmit(HourRecordBatchSaveVO batchSaveVO);
    
    void batchSubmit(String ids);
}