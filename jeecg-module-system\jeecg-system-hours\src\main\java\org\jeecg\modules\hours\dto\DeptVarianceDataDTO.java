package org.jeecg.modules.hours.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 部门工时差值数据传输对象
 */
@Data
public class DeptVarianceDataDTO {
    
    /**
     * 部门ID
     */
    private String deptId;
    
    /**
     * 部门名称
     */
    private String deptName;
    
    /**
     * 实际工时
     */
    private BigDecimal actualHours;
    
    /**
     * 可登记工时
     */
    private BigDecimal requiredHours;
}