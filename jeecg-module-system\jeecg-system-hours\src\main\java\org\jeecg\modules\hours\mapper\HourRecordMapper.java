package org.jeecg.modules.hours.mapper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.HourRecord;
import org.jeecg.modules.hours.vo.HourRecordVO;
import org.jeecg.modules.hours.vo.ProjectColumnVO;
import org.jeecg.modules.hours.vo.UserProjectHourReportVO;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: 工时记录表
 * @Author: jeecg-boot
 * @Date: 2023-07-01
 * @Version: V1.0
 */
public interface HourRecordMapper extends BaseMapper<HourRecord> {
	
	IPage<HourRecord> queryPageList(Page<HourRecord> page, @Param("projectKey") String projectKey,
			@Param("startDate") String startDate,
			@Param("endDate") String endDate,
			@Param("userKey") String userKey,@Param("isAdmin") boolean isAdmin,
			@Param("status") String status,
			@Param("deptIdList") List<String> deptIdList);
    
    /**
     * 根据用户ID和日期区间查询工时记录
     * @param page 分页参数
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分页结果
     */
    IPage<HourRecord> queryByUserAndDateRange(Page<HourRecord> page, 
                                          @Param("userId") String userId,
                                          @Param("startDate") String startDate,
                                          @Param("endDate") String endDate);
                                          
    /**
     * 根据项目ID查询工时统计
     * @param projectId 项目ID
     * @return 工时统计结果
     */
    List<Map<String, Object>> queryHoursSummaryByProject(@Param("projectId") String projectId);
    
    /**
     * 根据用户ID获取最近工时记录
     * @param userId 用户ID
     * @param limit 限制条数
     * @return 最近工时记录
     */
    List<HourRecord> queryRecentRecordsByUser(@Param("userId") String userId, @Param("limit") int limit);

    /**
     * 从考勤统计表获取可登记工时
     * @param unionIds 用户unionId列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 可登记工时总数
     */
    BigDecimal getScheduledHoursFromAttendance(@Param("unionIds") List<String> unionIds,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate);
    
    /**
     * 获取人员项目工时统计的项目列信息
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param userIds 用户ID列表
     * @param projectIds 项目ID列表
     * @return 项目列信息
     */
    List<ProjectColumnVO> getUserProjectColumns(@Param("startDate") Date startDate,
                                               @Param("endDate") Date endDate,
                                               @Param("userIds") List<String> userIds,
                                               @Param("projectIds") List<String> projectIds);
    
    /**
     * 获取人员项目工时统计报表数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param userIds 用户ID列表
     * @param projectIds 项目ID列表
     * @return 人员项目工时统计数据
     */
    List<UserProjectHourReportVO> getUserProjectHourReport(@Param("startDate") Date startDate,
                                                          @Param("endDate") Date endDate,
                                                          @Param("userIds") List<String> userIds,
                                                          @Param("projectIds") List<String> projectIds);
    
    /**
     * 获取人员项目工时统计总计行数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param userIds 用户ID列表
     * @param projectIds 项目ID列表
     * @return 总计行数据
     */
    UserProjectHourReportVO getUserProjectTotalRow(@Param("startDate") Date startDate,
                                                  @Param("endDate") Date endDate,
                                                  @Param("userIds") List<String> userIds,
                                                  @Param("projectIds") List<String> projectIds);
    
    /**
     * 从明细表查询指定日期的工时记录用于统计
     * @param statDate 统计日期
     * @return 工时记录明细列表
     */
    @MapKey("user_id")
    List<Map<String, Object>> getHourRecordDetailsForStatistics(@Param("statDate") Date statDate);
    
    /**
     * 从明细表查询指定日期的工时记录用于部门统计
     * @param statDate 统计日期
     * @return 工时记录明细列表（包含部门信息）
     */
    @MapKey("department_id")
    List<Map<String, Object>> getHourRecordDetailsForDeptStatistics(@Param("statDate") Date statDate);
    

}