package org.jeecg.modules.hours.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.HourRecordApply;
import org.jeecg.modules.hours.vo.HourRecordApplyVO;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

public interface HourRecordApplyMapper extends BaseMapper<HourRecordApply> {

	/**
	 * 分页查询审批相关的工时记录
	 * 
	 * @param page          分页参数
	 * @param startDate     开始日期
	 * @param endDate       结束日期
	 * @param employeeId    员工ID（多个用逗号分隔）
	 * @param tab           标签状态（pending-待办；approved-已办；all-全部）
	 * @param currentUserId 当前用户ID
	 * @return 分页结果
	 */
	IPage<HourRecordApplyVO> queryApprovalRecords(Page<HourRecordApplyVO> page, @Param("startDate") String startDate,
			@Param("endDate") String endDate, @Param("employeeIdList") List<String> employeeIdList,
			@Param("tab") String tab, @Param("currentUserId") String currentUserId,@Param("status") String status);
	
	// 分页查询待交接审批数据
	IPage<HourRecordApplyVO> queryHandoverApproverRecords(Page<HourRecordApplyVO> page, @Param("startDate") String startDate,
			@Param("endDate") String endDate, @Param("employeeIdList") List<String> employeeIdList,
			@Param("tab") String tab, @Param("status") String status,
			@Param("projectId") String projectId);

}