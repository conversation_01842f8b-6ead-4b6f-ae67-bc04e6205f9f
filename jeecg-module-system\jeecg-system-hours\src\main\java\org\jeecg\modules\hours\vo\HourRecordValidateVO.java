package org.jeecg.modules.hours.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 工时记录验证VO
 * @Author: jeecg-boot
 * @Date: 2023-07-01
 * @Version: V1.0
 */
@Data
@ApiModel(value="工时记录验证对象", description="工时记录验证对象")
public class HourRecordValidateVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "工作日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date workDate;
    
    @ApiModelProperty(value = "登记工时数量")
    private BigDecimal hours;
    
    @ApiModelProperty(value = "工作描述")
    private String description;
    
    @ApiModelProperty(value = "工作项ID")
    private String workItemId;
    
    @ApiModelProperty(value = "实例/迭代ID")
    private String instanceId;
    
    @ApiModelProperty(value = "节点ID")
    private String nodeId;
    
    @ApiModelProperty(value = "任务ID")
    private String taskId;
    
    @ApiModelProperty(value = "类型信息")
    private Object typeInfo;
} 