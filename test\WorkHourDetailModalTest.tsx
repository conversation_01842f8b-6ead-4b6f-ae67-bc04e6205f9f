import React, { useState } from 'react';
import { Button, Card, Typography, Space, Input, Form } from '@douyinfe/semi-ui';
import WorkHourDetailModal from '../src/features/board_web_gw38j1/components/WorkHourDetailModal';

const { Title, Text } = Typography;

/**
 * 工时明细模态窗测试页面
 * 
 * 测试功能：
 * 1. 模态窗的显示和隐藏
 * 2. 传递不同的参数组合
 * 3. API数据加载和渲染
 * 4. 错误处理和模拟数据显示
 */
const WorkHourDetailModalTest: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [workItemId, setWorkItemId] = useState('672d784c9a71acdb4f8180ef');
  const [instanceId, setInstanceId] = useState('616578422');
  const [nodeId, setNodeId] = useState('state_0');
  const [taskId, setTaskId] = useState('616581875');

  const handleOpenModal = () => {
    console.log('打开工时明细模态窗，参数:', {
      workItemId,
      instanceId,
      nodeId,
      taskId
    });
    setModalVisible(true);
  };

  const handleCloseModal = () => {
    console.log('关闭工时明细模态窗');
    setModalVisible(false);
  };

  const testCases = [
    {
      name: '完整参数测试',
      params: {
        workItemId: '672d784c9a71acdb4f8180ef',
        instanceId: '616578422',
        nodeId: 'state_0',
        taskId: '616581875'
      }
    },
    {
      name: '只有工作项测试',
      params: {
        workItemId: '672d784c9a71acdb4f8180ef',
        instanceId: '',
        nodeId: '',
        taskId: ''
      }
    },
    {
      name: '到节点级别测试',
      params: {
        workItemId: '672d784c9a71acdb4f8180ef',
        instanceId: '616578422',
        nodeId: 'state_0',
        taskId: ''
      }
    }
  ];

  const applyTestCase = (testCase: any) => {
    setWorkItemId(testCase.params.workItemId);
    setInstanceId(testCase.params.instanceId);
    setNodeId(testCase.params.nodeId);
    setTaskId(testCase.params.taskId);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1000px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>工时明细模态窗测试</Title>
        
        <div style={{ marginBottom: '24px' }}>
          <Text>
            此测试页面用于验证工时明细模态窗的功能，包括：
          </Text>
          <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
            <li>模态窗的正常显示和关闭</li>
            <li>从后端API加载真实工时明细数据</li>
            <li>工作内容和统计信息的显示</li>
            <li>工时记录表格的渲染</li>
            <li>加载状态和错误处理</li>
            <li>不同参数组合的测试</li>
            <li>空数据状态的处理</li>
          </ul>
        </div>

        {/* 参数配置 */}
        <div style={{ marginBottom: '24px' }}>
          <Title heading={5}>参数配置</Title>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginTop: '16px' }}>
            <Form.Label>
              工作项ID (workItemId)
              <Input
                value={workItemId}
                onChange={setWorkItemId}
                placeholder="请输入工作项ID"
              />
            </Form.Label>
            <Form.Label>
              实例ID (instanceId)
              <Input
                value={instanceId}
                onChange={setInstanceId}
                placeholder="请输入实例ID"
              />
            </Form.Label>
            <Form.Label>
              节点ID (nodeId)
              <Input
                value={nodeId}
                onChange={setNodeId}
                placeholder="请输入节点ID"
              />
            </Form.Label>
            <Form.Label>
              任务ID (taskId)
              <Input
                value={taskId}
                onChange={setTaskId}
                placeholder="请输入任务ID"
              />
            </Form.Label>
          </div>
        </div>

        {/* 测试用例 */}
        <div style={{ marginBottom: '24px' }}>
          <Title heading={5}>预设测试用例</Title>
          <Space wrap style={{ marginTop: '16px' }}>
            {testCases.map((testCase, index) => (
              <Button
                key={index}
                type="secondary"
                onClick={() => applyTestCase(testCase)}
              >
                {testCase.name}
              </Button>
            ))}
          </Space>
        </div>

        {/* 操作按钮 */}
        <Space>
          <Button 
            type="primary" 
            onClick={handleOpenModal}
          >
            打开工时明细
          </Button>
          
          <Button 
            type="secondary" 
            onClick={() => {
              console.log('模拟单个登记页面中的查看明细点击');
              setModalVisible(true);
            }}
          >
            模拟单个登记中的查看明细
          </Button>
        </Space>

        {/* 模拟单个登记页面的已登记工时显示 */}
        <div style={{ 
          marginTop: '24px', 
          padding: '16px', 
          backgroundColor: 'var(--semi-color-fill-0)',
          borderRadius: '6px'
        }}>
          <Title heading={5}>模拟单个登记页面显示</Title>
          <div style={{ marginTop: '16px' }}>
            <Form.Label style={{fontWeight:'normal'}}>已登记工时</Form.Label>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Input
                value="7.00小时(0.88人天)"
                readOnly
                disabled
                style={{ width: '100%', backgroundColor: 'var(--semi-color-disabled-bg)', cursor: 'not-allowed' }}
              />
              <Button
                type="tertiary"
                theme="borderless"
                size="small"
                style={{ 
                  marginLeft: '8px',
                  padding: '0 4px',
                  height: 'auto',
                  fontSize: '12px',
                  color: 'var(--semi-color-primary)',
                  textDecoration: 'underline'
                }}
                onClick={handleOpenModal}
              >
                查看明细
              </Button>
            </div>
          </div>
        </div>

        <div style={{ marginTop: '24px' }}>
          <Title heading={5}>API接口</Title>
          <Text>
            模态窗会调用以下API获取工时明细：<br/>
            <code>GET /hours/hourRecord/detail?workItemId={workItemId}&instanceId={instanceId}&nodeId={nodeId}&taskId={taskId}</code>
          </Text>
        </div>

        <div style={{ marginTop: '16px' }}>
          <Title heading={5}>预期行为</Title>
          <Text>
            - 如果API返回成功，显示实际的工时明细数据<br/>
            - 如果API失败或返回空数据，显示"暂无工时明细数据"<br/>
            - 工作内容显示为级联路径<br/>
            - 实际登记工时显示总计和人天换算<br/>
            - 表格显示详细的工时记录列表，包含状态信息
          </Text>
        </div>
      </Card>

      {/* 工时明细模态窗 */}
      <WorkHourDetailModal
        visible={modalVisible}
        onClose={handleCloseModal}
        workItemId={workItemId}
        instanceId={instanceId}
        nodeId={nodeId}
        taskId={taskId}
      />
    </div>
  );
};

export default WorkHourDetailModalTest;
