package com.lark.project.service.user.group;

import com.lark.project.core.httpclient.HttpTransport;
import com.lark.project.core.httpclient.IHttpTransport;
import com.lark.project.core.request.RawRequest;
import com.lark.project.core.response.RawResponse;
import com.lark.project.core.utils.Jsons;
import com.lark.project.core.utils.Lists;
import com.lark.project.core.utils.UnmarshalRespUtil;

import io.micrometer.core.instrument.util.StringUtils;

import com.lark.project.core.Config;
import com.lark.project.core.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.HttpClients;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
public class UserGroupService {

    /**
     * 获取所有分页的user_members
     * @param token X-PLUGIN-TOKEN
     * @param userKey X-USER-KEY
     * @return 所有user_members的id列表
     */
    public static List<String> getMembers(String token, String userKey,String projectKey,String userGroupType) {
        List<String> members = new ArrayList<>();
        String url = "https://project.feishu.cn/open_api/"+projectKey+"/user_groups/members/page";
        int pageNum = 1;
        int pageSize = 50; // 可调整
        boolean hasMore = true;
        IHttpTransport httpTransport = new HttpTransport(HttpClients.createDefault());
        Config config = new Config();
        config.setHttpTransport(httpTransport);
        while (hasMore) {
            Map<String, Object> body = new HashMap<>();
			if(StringUtils.isEmpty(userGroupType)){
				body.put("user_group_type", "PROJECT_ADMIN");
			}else{
				body.put("user_group_type", userGroupType);
			}
            // body.put("user_group_type", "PROJECT_MEMBER");
            body.put("page_num", pageNum);
            body.put("page_size", pageSize);
            RawRequest request = new RawRequest();
            request.setReqUrl(url);
            request.setHttpMethod("POST");
            request.setBody(body);
            Map<String, List<String>> headers = new HashMap<>();
            headers.put(Constants.HTTP_HEADER_ACCESS_TOKEN, Lists.newArrayList(token));
            headers.put(Constants.HTTP_HEADER_USER_KEY, Lists.newArrayList(userKey));
            request.setHeaders(headers);
            request.setConfig(config);
            try {
                RawResponse resp = httpTransport.execute(request);
                if (resp.getStatusCode() != 200) {
                    log.error("Request failed, code: {}, resp={}", resp.getStatusCode(),Jsons.DEFAULT.toJson(resp));
                    break;
                }
                String respBody = new String(resp.getBody(), StandardCharsets.UTF_8);
                Map<String, Object> respMap = Jsons.DEFAULT.fromJson(respBody, Map.class);
                Map<String, Object> data = (Map<String, Object>) respMap.get("data");
                if (data == null) break;
                List<Map<String, Object>> list = (List<Map<String, Object>>) data.get("list");
                if (list != null) {
                    for (Map<String, Object> item : list) {
                        List<String> userMembers = (List<String>) item.get("user_members");
                        if (userMembers != null) {
                            members.addAll(userMembers);
                        }
                    }
                }
                Map<String, Object> pagination = (Map<String, Object>) data.get("pagination");
                if (pagination != null && Boolean.TRUE.equals(pagination.get("has_more"))) {
                    pageNum++;
                } else {
                    hasMore = false;
                }
            } catch (Exception e) {
                log.error("Error fetching user group members", e);
                break;
            }
        }
        return members;
    }
}
