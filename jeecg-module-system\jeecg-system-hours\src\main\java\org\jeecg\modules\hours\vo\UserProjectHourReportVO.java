package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description: 人员项目工时统计报表VO
 * @Author: jeecg-boot
 * @Date: 2025-07-08
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "UserProjectHourReportVO", description = "人员项目工时统计报表VO")
public class UserProjectHourReportVO {
    
    @ApiModelProperty(value = "用户ID")
    private String userId;
    
    @ApiModelProperty(value = "用户姓名")
    private String userName;
    
    @ApiModelProperty(value = "工号")
    private String userCode;
    
    @ApiModelProperty(value = "部门ID")
    private String departmentId;
    
    @ApiModelProperty(value = "部门名称")
    private String departmentName;
    
    @ApiModelProperty(value = "项目工时数据(key: project_projectId, value: 工时数)")
    private Map<String, BigDecimal> projectHours;
    
    @ApiModelProperty(value = "总工时")
    private BigDecimal totalHours;
    
    // 以下字段用于SQL查询结果映射
    @ApiModelProperty(value = "项目ID（查询时用）")
    private String projectId;
    
    @ApiModelProperty(value = "项目工时（查询时用）")
    private BigDecimal projectHours2;
    
    @ApiModelProperty(value = "项目key（查询时用）")
    private String projectKey;
}