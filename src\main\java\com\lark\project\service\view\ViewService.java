/*
 * Copyright (c) 2023 Lark Technologies Pte. Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lark.project.service.view;

import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.view.builder.*;

public interface ViewService {

    // 创建固定视图
    public CreateFixViewResp createFixView(CreateFixViewReq req, RequestOptions reqOptions) throws Exception;

    // 删除固定视图
    public DeleteFixViewResp deleteFixView(DeleteFixViewReq req, RequestOptions reqOptions) throws Exception;

    // 获取视图下工作项列表（全景视图）
    public QueryWorkItemDetailsByViewIDResp queryWorkItemDetailsByViewID(QueryWorkItemDetailsByViewIDReq req, RequestOptions reqOptions) throws Exception;

    // 更新固定视图
    public UpdateFixViewResp updateFixView(UpdateFixViewReq req, RequestOptions reqOptions) throws Exception;

    // 获取视图列表
    public ViewListResp viewList(ViewListReq req, RequestOptions reqOptions) throws Exception;

    // 获取视图下工作项列表
    public WorkItemListResp workItemList(WorkItemListReq req, RequestOptions reqOptions) throws Exception;

}