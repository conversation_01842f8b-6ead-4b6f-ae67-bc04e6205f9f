<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.HourRecordApplyMapper">

	    <!-- 分页查询审批相关的工时记录 -->
    <select id="queryApprovalRecords" resultType="org.jeecg.modules.hours.vo.HourRecordApplyVO">
        SELECT b.id, 
		hr.id as record_id,hr.user_id,hr.user_name,hr.work_month,hr.work_period_start,hr.work_period_end,hr.hours,
		hr.status,hr.create_time,hr.update_time,hr.create_by,hr.update_by,hr.description,
		b.applicant_name ,b.apply_time,b.id as apply_id,
		b.batch_no,b.status as apply_status,a.status deal_status,
        CASE b.status 
            WHEN 0 THEN '审批中'
            WHEN 1 THEN '已通过'
            WHEN 2 THEN '已驳回'
            ELSE '未知' END as apply_status_name,
        CASE a.status 
            WHEN 0 THEN '待审批'
            WHEN 1 THEN '已通过'
            WHEN 2 THEN '已拒绝'
            WHEN 3 THEN '已移交'
            ELSE '未知' END as deal_status_name
		FROM hour_record hr
		left join hour_record_apply b on hr.id = b.record_id 
		left join hour_record_approver a on a.apply_id = b.id 
		WHERE 1=1
        <!-- 添加日期范围筛选 -->
        <if test="startDate != null and startDate != ''">
            AND hr.work_period_start >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND hr.work_period_end &lt;= #{endDate}
        </if>
        <!-- hour_record_apply的status -->
        <if test="status != null and status != ''">
            AND b.status = #{status}
        </if>

        <!-- 添加员工筛选 -->
        <if test="employeeIdList != null and employeeIdList.size()>0">
            AND hr.user_id IN
            <foreach collection="employeeIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <!-- 根据tab状态添加不同的查询条件 -->
        AND a.approver_id = #{currentUserId} 
        <choose>
            <when test="'pending'.equals(tab)">
                <!-- 待办需要我审批(hour_record_approver中approver_id=我) -->
                <!-- record状态(0:待提交, 1:审批中, 2:已通过, 3:已驳回) -->
                AND hr.status IN (1) and b.status=0
                <!-- 审批状态(0:待审批,1:已通过,2:已拒绝,3:已移交) -->
                AND (a.status = 0 OR a.status = 3)
            </when>
            <when test="'approved'.equals(tab)">
                <!-- 已办 -->
                <!-- record状态(0:待提交, 1:审批中, 2:已通过, 3:已驳回) -->
                AND hr.status IN (1, 2, 3) 
                <!-- 审批状态(0:待审批,1:已通过,2:已拒绝,3:已移交) -->
                AND (a.status = 1 OR a.status = 2)
            </when>
            <otherwise>
                <!-- 全部 -->
                <!-- record状态(0:待提交, 1:审批中, 2:已通过, 3:已驳回) -->
                AND hr.status IN (1, 2, 3)
                <!-- 审批状态(0:待审批,1:已通过,2:已拒绝,3:已移交) -->
                AND a.approver_id = #{currentUserId}
            </otherwise>
        </choose>

        <!-- 按创建时间倒序排序 -->
        ORDER BY b.apply_time desc,hr.create_time DESC
    </select>
    
    
    
    
	<!-- 分页查询待交接审批数据 -->
    <select id="queryHandoverApproverRecords" resultType="org.jeecg.modules.hours.vo.HourRecordApplyVO">
        SELECT b.id, 
		hr.id as record_id,hr.user_id,hr.user_name,hr.work_month,hr.work_period_start,hr.work_period_end,hr.hours,
		hr.status,hr.create_time,hr.update_time,hr.create_by,hr.update_by,hr.description,
		b.applicant_name ,b.apply_time,b.id as apply_id,
		b.batch_no,b.status as apply_status,a.status deal_status,
        CASE b.status 
            WHEN 0 THEN '审批中'
            WHEN 1 THEN '已通过'
            WHEN 2 THEN '已驳回'
            ELSE '未知' END as apply_status_name,
        CASE a.status 
            WHEN 0 THEN '待审批'
            WHEN 1 THEN '已通过'
            WHEN 2 THEN '已拒绝'
            WHEN 3 THEN '已移交'
            ELSE '未知' END as deal_status_name,
            a.approver_name,a.id as hour_record_approver_id
		FROM hour_record hr
		left join hour_record_apply b on hr.id = b.record_id 
		left join hour_record_approver a on a.apply_id = b.id 
		WHERE 1=1
        <!-- 添加日期范围筛选 -->
        <if test="startDate != null and startDate != ''">
            AND hr.work_period_start >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND hr.work_period_end &lt;= #{endDate}
        </if>
        <!-- hour_record_apply的status -->
        <if test="status != null and status != ''">
            AND b.status = #{status}
        </if>
        <if test="projectId != null and projectId != ''">
	        and exists(
				select 0 from hour_record_detail hrd where hrd.record_id =hr.id 
				and hrd.project_id =#{projectId} 
			)
        </if>

        <!-- 添加审批人筛选 -->
        <if test="employeeIdList != null and employeeIdList.size()>0">
            AND a.approver_id IN
            <foreach collection="employeeIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        
        
        <choose>
            <when test="'pending'.equals(tab)">
                <!-- 待办需要我审批(hour_record_approver中approver_id=我) -->
                <!-- record状态(0:待提交, 1:审批中, 2:已通过, 3:已驳回) -->
                AND hr.status IN (1) and b.status=0
                <!-- 审批状态(0:待审批,1:已通过,2:已拒绝,3:已移交) -->
                AND (a.status = 0 OR a.status = 3)
            </when>
            <when test="'approved'.equals(tab)">
                <!-- 已办 -->
                <!-- record状态(0:待提交, 1:审批中, 2:已通过, 3:已驳回) -->
                AND hr.status IN (1, 2, 3) 
                <!-- 审批状态(0:待审批,1:已通过,2:已拒绝,3:已移交) -->
                AND (a.status = 1 OR a.status = 2)
            </when>
            <otherwise>
                <!-- 全部 -->
                <!-- record状态(0:待提交, 1:审批中, 2:已通过, 3:已驳回) -->
                AND hr.status IN (1, 2, 3)
                <!-- 审批状态(0:待审批,1:已通过,2:已拒绝,3:已移交) -->
                AND a.approver_id = #{currentUserId}
            </otherwise>
        </choose>

        <!-- 按创建时间倒序排序 -->
        ORDER BY b.apply_time desc,hr.create_time DESC
    </select>

</mapper> 