package org.jeecg.modules.hours.controller;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.hours.constant.ConfigKeyConstant;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.entity.HourRecord;
import org.jeecg.modules.hours.entity.HourRecordDetail;
import org.jeecg.modules.hours.entity.SysUserAttendanceStat;
import org.jeecg.modules.hours.service.IFsUserService;
import org.jeecg.modules.hours.service.IHourRecordDetailService;
import org.jeecg.modules.hours.service.IHourRecordLogService;
import org.jeecg.modules.hours.service.IHourRecordService;
import org.jeecg.modules.hours.service.IHourRecordApproverService;
import org.jeecg.modules.hours.service.ISysConfigService;
import org.jeecg.modules.hours.service.ISysUserAttendanceStatService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.jeecg.modules.hours.utils.UserInfoUtils;
import org.jeecg.modules.hours.vo.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.hours.mapper.HourRecordMapper;

/**
 * @Description: 工时记录表
 * @Author: jeecg-boot
 * @Date: 2023-07-01
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "工时管理")
@RestController
@RequestMapping("/hapi/hours/hourRecord")
public class HourRecordController extends JeecgController<HourRecord, IHourRecordService> {

	@Autowired
	private IHourRecordService hourRecordService;
	@Autowired
	private IFsUserService fsUserService;
	@Autowired
	private UserInfoUtils userInfoUtils;
	@Autowired
	private ISysConfigService sysConfigService;
	@Autowired
	private ISysUserAttendanceStatService sysUserAttendanceStatService;
	@Autowired
	private IHourRecordDetailService hourRecordDetailService;

	/**
	 * 计算总计可登记工时
	 */
	private BigDecimal calculateTotalScheduledHours(String projectKey, String startDate, String endDate,
			List<String> userKeyList, boolean isAttendanceEnabled) {

		if (isAttendanceEnabled) {
			// 考勤开启：从考勤统计表获取数据
			return calculateScheduledHoursFromAttendance(projectKey, startDate, endDate, userKeyList);
		} else {
			// 考勤关闭：按工作日×人数×每日工时上限计算
			return calculateScheduledHoursFromConfig(projectKey, startDate, endDate, userKeyList);
		}
	}

	/**
	 * 从考勤统计表计算可登记工时
	 */
	private BigDecimal calculateScheduledHoursFromAttendance(String projectKey, String startDate, String endDate,
			List<String> userKeyList) {

		try {
			// 获取用户的unionId列表
			List<String> unionIds = getUserUnionIds(projectKey, userKeyList);
			if (unionIds.isEmpty()) {
				return BigDecimal.ZERO;
			}

			// 查询考勤统计数据
			return hourRecordService.getScheduledHoursFromAttendance(unionIds, startDate, endDate);

		} catch (Exception e) {
			log.error("从考勤统计表计算可登记工时失败", e);
			return BigDecimal.ZERO;
		}
	}

	/**
	 * 从配置计算可登记工时
	 */
	private BigDecimal calculateScheduledHoursFromConfig(String projectKey, String startDate, String endDate,
			List<String> userKeyList) {

		try {
			// 获取每日工时上限配置
			String maxHourPerDayStr = sysConfigService.getConfigValue(projectKey,
					ConfigKeyConstant.MAX_WORK_HOUR_PER_DAY);
			BigDecimal maxHourPerDay = StringUtils.isNotBlank(maxHourPerDayStr) ? new BigDecimal(maxHourPerDayStr)
					: new BigDecimal("8");

			// 计算日期天数
			long dayCount = calculateWorkDays(startDate, endDate);

			// 获取用户数量
			int userCount = getUserCount(projectKey, userKeyList);

			// 计算总工时：天数 × 人数 × 每日工时上限
			return maxHourPerDay.multiply(new BigDecimal(dayCount)).multiply(new BigDecimal(userCount));

		} catch (Exception e) {
			log.error("从配置计算可登记工时失败", e);
			return BigDecimal.ZERO;
		}
	}

	/**
	 * 获取用户的unionId列表
	 */
	private List<String> getUserUnionIds(String projectKey, List<String> userKeyList) {
		if (userKeyList == null || userKeyList.isEmpty()) {
			// 获取项目下所有用户的unionId
			return fsUserService.getUnionIds(projectKey);
		} else {
			// 根据userKey列表获取对应的unionId
			List<FsUser> users = fsUserService.getUserByProjectAndUserKeys(projectKey, userKeyList);
			return users.stream().filter(user -> StringUtils.isNotBlank(user.getUnionId())).map(FsUser::getUnionId)
					.collect(Collectors.toList());
		}
	}

	/**
	 * 获取用户数量
	 */
	private int getUserCount(String projectKey, List<String> userKeyList) {
		if (userKeyList == null || userKeyList.isEmpty()) {
			// 获取项目下所有活跃用户数量
			return Math.toIntExact(fsUserService.count(
					new LambdaQueryWrapper<FsUser>().eq(FsUser::getProjectKey, projectKey).eq(FsUser::getStatus, "1")));
		} else {
			return userKeyList.size();
		}
	}

	/**
	 * 计算工作日天数（简单实现，不考虑节假日）
	 */
	private long calculateWorkDays(String startDate, String endDate) {
		try {
			LocalDate start = LocalDate.parse(startDate);
			LocalDate end = LocalDate.parse(endDate);
			return ChronoUnit.DAYS.between(start, end) + 1; // 包含结束日期
		} catch (Exception e) {
			log.error("计算工作日天数失败", e);
			return 0;
		}
	}

	/**
	 * 获取当日工时登记信息
	 */
	@GetMapping("/dailyInfo")
	@ApiOperation(value = "获取当日工时登记信息", notes = "获取指定日期的可登记工时和已登记工时信息")
	public Result<Map<String, Object>> getDailyInfo(@RequestParam String workDate) {

		try {
			// 从请求头获取项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			if (projectKey == null) {
				return Result.error("未提供有效的项目Key");
			}

			// 获取当前用户ID
			String currentUserKey = userInfoUtils.getCurrentUserKey();
			if (currentUserKey == null) {
				return Result.error("无法获取当前用户信息");
			}

			log.info("获取当日工时信息: 项目[{}], 日期[{}], 用户[{}]", projectKey, workDate, currentUserKey);

			// 获取考勤开关配置
			String attendanceSwitch = sysConfigService.getConfigValue(projectKey,
					ConfigKeyConstant.FEISHU_ATTENDANCE_VALIDATE_SWITCH);
			boolean isAttendanceEnabled = "1".equals(attendanceSwitch);

			BigDecimal dailyScheduledHours;

			if (isAttendanceEnabled) {
				// 考勤开启：从考勤统计表获取当日可登记工时
				dailyScheduledHours = calculateDailyScheduledHoursFromAttendance(projectKey, currentUserKey, workDate);
			} else {
				// 考勤关闭：使用配置的每日工时上限
				String maxHourPerDayStr = sysConfigService.getConfigValue(projectKey,
						ConfigKeyConstant.MAX_WORK_HOUR_PER_DAY);
				dailyScheduledHours = StringUtils.isNotBlank(maxHourPerDayStr) ? new BigDecimal(maxHourPerDayStr)
						: new BigDecimal("8");
			}

			// 获取当日已登记工时
			BigDecimal dailyRecordedHours = hourRecordService.getDailyRecordedHours(projectKey, currentUserKey,
					workDate);

			// 计算剩余可登记工时
			BigDecimal remainingHours = dailyScheduledHours.subtract(dailyRecordedHours);
			if (remainingHours.compareTo(BigDecimal.ZERO) < 0) {
				remainingHours = BigDecimal.ZERO;
			}

			// 构建返回结果
			Map<String, Object> result = new HashMap<>();
			result.put("dailyScheduledHours", dailyScheduledHours);
			result.put("dailyRecordedHours", dailyRecordedHours);
			result.put("remainingHours", remainingHours);

			log.info("当日工时信息: 可登记[{}], 已登记[{}], 剩余[{}]", dailyScheduledHours, dailyRecordedHours, remainingHours);

			return Result.OK(result);

		} catch (Exception e) {
			log.error("获取当日工时信息失败", e);
			return Result.error("获取当日工时信息失败: " + e.getMessage());
		}
	}

	/**
	 * 从考勤统计表计算当日可登记工时
	 */
	private BigDecimal calculateDailyScheduledHoursFromAttendance(String projectKey, String userKey, String workDate) {
		try {
			// 根据用户ID获取unionId
			FsUser user = fsUserService.getUserByProjectAndUserKey(projectKey, userKey);
			if (user == null || StringUtils.isBlank(user.getUnionId())) {
				log.warn("用户[{}]未找到或缺少unionId", userKey);
				return BigDecimal.ZERO;
			}

			// 查询考勤统计数据
			SysUserAttendanceStat attendance = sysUserAttendanceStatService.getByUserAndDate(user.getUnionId(),
					java.sql.Date.valueOf(workDate));

			if (attendance == null) {
				log.warn("用户[{}]在日期[{}]无考勤数据", userKey, workDate);
				return BigDecimal.ZERO;
			}

			// 获取工时规则配置
//			String workHourRule = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.WORK_HOUR_RULE);

			BigDecimal scheduledHours = attendance.getRealAttendHours();
//			if (StringUtils.isEmpty(workHourRule) || "2".equals(workHourRule)) {
				// 班内工作时长 + 加班时长
//				scheduledHours = new BigDecimal(attendance.getWorkHours())
//						.add(new BigDecimal(attendance.getOverWorkHours()));
//			} else {
				// 仅班内工作时长
//				scheduledHours = new BigDecimal(attendance.getWorkHours());
//			}

			return scheduledHours;

		} catch (Exception e) {
			log.error("从考勤统计表计算当日可登记工时失败", e);
			return BigDecimal.ZERO;
		}
	}

	/**
	 * 分页列表查询
	 *
	 * @param hourRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "工时记录-分页列表查询")
	@ApiOperation(value = "工时记录-分页列表查询", notes = "工时记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(HourRecord hourRecord,
			@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(name = "startDate", required = false) String startDate,
			@RequestParam(name = "endDate", required = false) String endDate,
			@RequestParam(name = "deptIds", required = false) String deptIds,
			@RequestParam(name = "status", required = false) String status,
			HttpServletRequest req) {
		// 使用UserInfoUtils获取当前用户ID
//		String userId = userInfoUtils.getCurrentUserKey();
//		QueryWrapper<HourRecord> queryWrapper = QueryGenerator.initQueryWrapper(hourRecord, req.getParameterMap());
//		queryWrapper.eq("user_id", userId);
//		if (org.apache.commons.lang3.StringUtils.isNotBlank(startDate)) {
//			queryWrapper.ge("work_period_start", startDate);
//		}
//		if (org.apache.commons.lang3.StringUtils.isNotBlank(endDate)) {
//			queryWrapper.le("work_period_end", endDate);
//		}
		Page<HourRecord> page = new Page<>(pageNo, pageSize);
		// IPage<HourRecord> pageList = hourRecordService.page(page, queryWrapper);
		IPage<HourRecord> pageList = hourRecordService.queryPageList(page,startDate,endDate,deptIds,status);
		// 为每条记录补充statusName
		if (pageList != null && pageList.getRecords() != null) {
			for (HourRecord record : pageList.getRecords()) {
				String statusName;
				switch (record.getStatus() != null ? record.getStatus() : -1) {
				case 0:
					statusName = "待提交";
					break;
				case 1:
					statusName = "审批中";
					break;
				case 2:
					statusName = "审批通过";
					break;
				case 3:
					statusName = "审批退回";
					break;
				default:
					statusName = "未知";
					break;
				}
				record.setStatusName(statusName);
			}
		}
		return Result.OK(pageList);
	}


		/**
	 * 分页列表查询
	 *
	 * @param sysUserAttendanceStatVo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "工作台-待登记工时查询")
	@ApiOperation(value = "工时记录-分页列表查询", notes = "工作台-待登记工时查询")
	@GetMapping(value = "/pendingRegisteredHoursList")
	public Result<?> pendingRegisteredHoursList(
			SysUserAttendanceStatVo sysUserAttendanceStatVo,
			@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
			HttpServletRequest req) {
		// 使用UserInfoUtils获取当前用户ID
		//String userId = userInfoUtils.getCurrentUserKey();
		Page<SysUserAttendanceStatVo> page = new Page<>(pageNo, pageSize);
		IPage<SysUserAttendanceStatVo> pageList = sysUserAttendanceStatService.getPendingRegisteredHoursList(page, sysUserAttendanceStatVo);
		return Result.OK(pageList);
	}

	/**
	 * 根据日期查询当前用户工时
	 */
	@AutoLog(value = "工时记录表-按日期查询当前用户工时")
	@GetMapping(value = "/queryByDate")
	public Result<?> queryByDate(@RequestParam(name = "workDate") String workDate) {
		// 使用UserInfoUtils获取当前用户ID
		String userId = userInfoUtils.getCurrentUserKey();
		if (userId.isEmpty()) {
			return Result.error("未获取到有效的用户信息，请重新授权");
		}

		QueryWrapper<HourRecord> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("user_id", userId).eq("work_date", workDate).orderByDesc("created_at");

		List<HourRecord> list = hourRecordService.list(queryWrapper);

		// 计算汇总工时
		BigDecimal totalHours = BigDecimal.ZERO;
		for (HourRecord record : list) {
			totalHours = totalHours.add(record.getHours());
		}

		Map<String, Object> result = new HashMap<>();
		result.put("records", list);
		result.put("totalHours", totalHours);

		return Result.OK(result);
	}

	/**
	 * 获取工时明细 根据工作内容的层级参数查询相关的工时记录明细
	 */
	@ApiOperation(value = "获取工时明细", notes = "根据工作内容参数查询工时记录明细")
	@GetMapping(value = "/detail")
	public Result<?> getWorkHourDetail(@RequestParam(name = "workItemId", required = false) String workItemId,
			@RequestParam(name = "instanceId", required = false) String instanceId,
			@RequestParam(name = "nodeId", required = false) String nodeId,
			@RequestParam(name = "taskId", required = false) String taskId) {

		try {
			// 从请求头获取项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			if (projectKey == null) {
				return Result.error("未提供有效的项目Key");
			}

			log.info("获取工时明细: 项目[{}], 工作项[{}], 实例[{}], 节点[{}], 任务[{}]", projectKey, workItemId, instanceId, nodeId,
					taskId);

			// 构建查询条件
			QueryWrapper<HourRecord> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("project_id", projectKey);

			// 根据传入的参数构建查询条件
			if (StringUtils.isNotBlank(workItemId)) {
				queryWrapper.eq("work_item_id", workItemId);
			}
			if (StringUtils.isNotBlank(instanceId)) {
				queryWrapper.eq("instance_id", instanceId);
			}
			if (StringUtils.isNotBlank(nodeId)) {
				queryWrapper.eq("node_id", nodeId);
			}
			if (StringUtils.isNotBlank(taskId)) {
				queryWrapper.eq("task_id", taskId);
			}

			// 按创建时间倒序排列
			queryWrapper.orderByDesc("created_at");

			// 查询工时记录
			List<HourRecord> records = hourRecordService.list(queryWrapper);

			// 计算统计信息
			BigDecimal totalHours = BigDecimal.ZERO;
			for (HourRecord record : records) {
				if (record.getHours() != null) {
					totalHours = totalHours.add(record.getHours());
				}
			}

			// 获取每天标准工时配置
			String workHourPerDayStr = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.WORK_HOUR_PER_DAY);
			BigDecimal workHourPerDay = new BigDecimal("8"); // 默认8小时
			if (StringUtils.isNotBlank(workHourPerDayStr)) {
				try {
					workHourPerDay = new BigDecimal(workHourPerDayStr);
				} catch (NumberFormatException e) {
					log.warn("工时配置格式错误，使用默认值8: {}", workHourPerDayStr);
				}
			}

			// 计算总人天
			BigDecimal totalDays = totalHours.divide(workHourPerDay, 2, BigDecimal.ROUND_HALF_UP);

			// 构建工作内容路径（根据传入参数）
			String workContent = buildWorkContentPath(records, workItemId, instanceId, nodeId, taskId);

			// 构建返回结果
			Map<String, Object> result = new HashMap<>();
			result.put("workContent", workContent);
			result.put("totalHours", totalHours);
			result.put("totalDays", totalDays);
			result.put("records", records);

			log.info("工时明细查询成功: 总工时[{}], 总人天[{}], 记录数[{}]", totalHours, totalDays, records.size());

			return Result.OK(result);

		} catch (Exception e) {
			log.error("获取工时明细失败", e);
			return Result.error("获取工时明细失败: " + e.getMessage());
		}
	}

	/**
	 * 构建工作内容路径（根据传入参数）
	 */
	private String buildWorkContentPath(List<HourRecord> records, String workItemId, String instanceId, String nodeId,
			String taskId) {
		if (records == null || records.isEmpty()) {
			return "未知工作内容";
		}

//		HourRecord firstRecord = records.get(0);
		StringBuilder pathBuilder = new StringBuilder();

		return pathBuilder.length() > 0 ? pathBuilder.toString() : "未知工作内容";
	}

	/**
	 * 添加
	 *
	 * @param hourRecord 工时记录对象
	 * @return
	 */
	@AutoLog(value = "工时记录-添加")
	@ApiOperation(value = "工时记录-添加", notes = "工时记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody HourRecord hourRecord) {
		hourRecordService.save(hourRecord);
		return Result.OK("添加成功！");
	}

	/**
	 * 编辑
	 *
	 * @param hourRecord
	 * @return
	 */
	@AutoLog(value = "工时记录-编辑")
	@ApiOperation(value = "工时记录-编辑", notes = "工时记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody HourRecord hourRecord) {
		hourRecordService.updateById(hourRecord);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "工时记录-通过id删除")
	@ApiOperation(value = "工时记录-通过id删除", notes = "工时记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
		hourRecordService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "工时记录-批量删除")
	@ApiOperation(value = "工时记录-批量删除", notes = "工时记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
		this.hourRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 获取可登记工时日期
	 *
	 * @param month     月份
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @return
	 */
	@AutoLog(value = "工时记录-获取可登记工时日期")
	@ApiOperation(value = "工时记录-获取可登记工时日期", notes = "工时记录-获取可登记工时日期")
	@GetMapping(value = "/availableDates")
	public Result<List<AvailableDateVO>> getAvailableDates(@RequestParam String month,
			@RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate,
			@RequestParam(required = false, name = "userKey") String userKey) {
		if (StringUtils.isEmpty(userKey)) {
			FsUser loginUser = userInfoUtils.getLoginUser();
			if (loginUser == null) {
				throw new JeecgBootException("用户未登录");
			}
			userKey = loginUser.getUserKey();
		}

		return Result.OK(hourRecordService.getAvailableDates(month, startDate, endDate, userKey));
	}

	/**
	 * 获取工作周期选项
	 *
	 * @param month 月份
	 * @return
	 */
	@AutoLog(value = "工时记录-获取工作周期选项")
	@ApiOperation(value = "工时记录-获取工作周期选项", notes = "工时记录-获取工作周期选项")
	@GetMapping(value = "/workPeriods")
	public Result<List<WorkPeriodVO>> getWorkPeriods(@RequestParam(name = "month") String month) {
		List<WorkPeriodVO> periods = hourRecordService.getWorkPeriods(month);
		return Result.OK(periods);
	}

	/**
	 * 批量保存工时记录
	 *
	 * @param batchSaveVO
	 * @return
	 */
	@AutoLog(value = "工时记录-批量保存")
	@ApiOperation(value = "工时记录-批量保存", notes = "工时记录-批量保存")
	@PostMapping(value = "/batchSave")
	public Result<?> batchSave(@RequestBody HourRecordBatchSaveVO batchSaveVO) {
		if (StringUtils.isEmpty(batchSaveVO.getId())) {
			FsUser loginUser = userInfoUtils.getLoginUser();
			// 验证同一用户+同一周期不允许重复添加
			LambdaQueryWrapper<HourRecord> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(HourRecord::getUserId, loginUser.getUserKey())
					.eq(HourRecord::getWorkMonth, batchSaveVO.getWorkMonth())
					.eq(HourRecord::getWorkPeriodStart, batchSaveVO.getStartDate())
					.eq(HourRecord::getWorkPeriodEnd, batchSaveVO.getEndDate());
			List<HourRecord> existingRecords = hourRecordService.list(queryWrapper);
			if (CollectionUtils.isNotEmpty(existingRecords)) {
				return Result.warn("当前周期已存在,不允许重复添加");
			}
		}

		batchSaveVO.setStatus(0);
		hourRecordService.batchSave(batchSaveVO);
		return Result.OK("保存成功!");
	}

	/**
	 * 批量提交工时记录
	 *
	 * @param batchSaveVO
	 * @return
	 */
	@AutoLog(value = "工时记录-批量提交")
	@ApiOperation(value = "工时记录-批量提交", notes = "工时记录-批量提交")
	@PostMapping(value = "/batchSaveSubmit")
	public Result<?> batchSaveSubmit(@RequestBody HourRecordBatchSaveVO batchSaveVO) {
		if (StringUtils.isEmpty(batchSaveVO.getId())) {
			FsUser loginUser = userInfoUtils.getLoginUser();
			// 验证同一用户+同一周期不允许重复添加
			LambdaQueryWrapper<HourRecord> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(HourRecord::getUserId, loginUser.getUserKey())
					.eq(HourRecord::getWorkMonth, batchSaveVO.getWorkMonth())
					.eq(HourRecord::getWorkPeriodStart, batchSaveVO.getStartDate())
					.eq(HourRecord::getWorkPeriodEnd, batchSaveVO.getEndDate());
			List<HourRecord> existingRecords = hourRecordService.list(queryWrapper);
			if (CollectionUtils.isNotEmpty(existingRecords)) {
				return Result.warn("当前周期已存在,不允许重复添加");
			}
		}
		batchSaveVO.setStatus(1);
		hourRecordService.batchSaveSubmit(batchSaveVO);
		return Result.OK("提交成功!");
	}

	@AutoLog(value = "工时记录-批量提交")
	@ApiOperation(value = "工时记录-批量提交", notes = "工时记录-批量提交")
	@PostMapping(value = "/batchSubmit")
	public Result<?> batchSubmit(@RequestParam("ids") String ids) {
		if (StringUtils.isEmpty(ids)) {
			return Result.warn("请选择需要提交的数据!");
		}
		try {
			hourRecordService.batchSubmit(ids);
		}catch(JeecgBootException e) {
			log.error(e.getMessage(),e);
			return Result.warn(e.getMessage());
		}
		return Result.OK("提交成功!");
	}

	/**
	 * 工时记录表单详情
	 * 
	 * @param recordId 工时记录ID
	 * @return hour_record主表和hour_record_detail明细集合
	 */
	@ApiOperation(value = "工时记录表单详情", notes = "获取工时主表和明细集合")
	@GetMapping("/formDetail")
	public Result<?> getFormDetail(@RequestParam String recordId) {
		HourRecord hourRecord = hourRecordService.getById(recordId);
		if (hourRecord == null) {
			return Result.error("未找到工时记录");
		}
		List<HourRecordDetail> details = hourRecordDetailService
				.list(new QueryWrapper<HourRecordDetail>().eq("record_id", recordId));
		Map<String, Object> result = new HashMap<>();
		result.put("hourRecord", hourRecord);
		result.put("hourRecordDetail", details);
		return Result.OK(result);
	}

}