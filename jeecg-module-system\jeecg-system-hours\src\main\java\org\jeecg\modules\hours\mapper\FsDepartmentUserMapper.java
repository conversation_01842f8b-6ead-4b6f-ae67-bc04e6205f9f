package org.jeecg.modules.hours.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.FsDepartmentUser;

import java.util.List;
import java.util.Collection;
import java.util.Map;

public interface FsDepartmentUserMapper extends BaseMapper<FsDepartmentUser> {
    /**
     * 根据用户ID查询关联的部门ID列表
     * @param userId 用户ID
     * @return 部门ID列表
     */
    List<String> selectDepartIdsByUserId(@Param("userId") String userId);

    /**
     * 根据unionId批量查询部门名（多个部门用逗号拼接）
     */
    List<Map<String, String>> selectDeptNamesByUnionIds(@Param("unionIds") Collection<String> unionIds);
}
