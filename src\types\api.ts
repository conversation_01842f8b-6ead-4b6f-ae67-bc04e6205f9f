// 通用API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  code: number;
  result: T;
  timestamp: number;
}

// 工时记录类型
export interface WorkHoursRecord {
  id: string;
  workDate: string;
  worker: string;
  registeredHours: number;
  availableHours: number;
  type: string;
  typeInfo: string;
  businessLevel1: string;
  businessLevel2: string;
  status: 'draft' | 'pending' | 'approved' | 'rejected';
  hours: number;
  description: string;
  createdAt: string;
  updatedAt: string;
}

// 工时统计类型
export interface WorkHoursStats {
  totalHours: number;
  approvedHours: number;
  pendingHours: number;
  rejectedHours: number;
}

// 审批记录类型
export interface ApprovalRecord {
  id: string;
  workHoursId: string;
  applicant: string;
  approver: string;
  status: 'pending' | 'approved' | 'rejected';
  comment: string;
  createdAt: string;
  updatedAt: string;
}

// 基础数据配置类型
export interface BasicConfig {
  id: string;
  stageName: string;
  status: 'enabled' | 'disabled';
  sort: number;
  remarks: string;
}

// 飞书集成配置类型
export interface FeishuConfig {
  appId: string;
  appSecret: string;
  enabled: boolean;
  webhookUrl: string;
  notifyEvents: string[];
}

// 用户信息类型
export interface UserInfo {
  id: string;
  name: string;
  email: string;
  department: string;
  role: string;
  permissions: string[];
}

// 分页请求参数类型
export interface PaginationParams {
  page: number;
  pageSize: number;
  keyword?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
}

// 分页响应类型
export interface PaginationResponse<T> {
  records: T[];
  total: number;
  current: number;
  size: number;
  pages?: number;
}

// 登记提醒配置类型
export interface ReminderConfig {
  id?: number;
  projectKey: string;
  reminderType: 'daily' | 'weekly' | 'monthly';
  repeatMode: 'current_day' | 'next_day' | 'current_week' | 'next_week' | 'current_month' | 'next_month';
  reminderDay?: string; // 周几或月份日期
  reminderTime: string; // HH:MM格式
  remark?: string;
  status: '启用' | '暂停';
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  delFlag?: number; // 删除标志：0-正常，1-删除
}

// 登记提醒配置请求参数类型
export interface ReminderConfigParams {
  reminderType?: 'daily' | 'weekly' | 'monthly';
  status?: '启用' | '暂停';
  page?: number;
  pageSize?: number;
  keyword?: string;
}

// 登记提醒配置创建/更新参数类型
export interface ReminderConfigFormData {
  reminderType: 'daily' | 'weekly' | 'monthly';
  repeatMode: 'current_day' | 'next_day' | 'current_week' | 'next_week' | 'current_month' | 'next_month';
  reminderDay?: string;
  reminderTime: string;
  remark?: string;
  status: '启用' | '暂停';
}