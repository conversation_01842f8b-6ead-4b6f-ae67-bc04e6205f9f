import React, { useState } from 'react';
import { Tabs } from '@douyinfe/semi-ui';
import './workPage.css';
import InstanceHoursReport from './components/InstanceHoursReport';
import EmployeeHoursReport from './components/EmployeeHoursReport';
import EmployeeInstanceDistributionReport from './components/EmployeeInstanceDistributionReport';
import DeptProjectHoursReport from './components/DeptProjectHoursReport';
import UserProjectHoursReport from './components/UserProjectHoursReport';
import DeptProjectDailyHoursReport from './components/DeptProjectDailyHoursReport';
import UserProjectDailyHoursReport from './components/UserProjectDailyHoursReport';
import DeptHourVarianceReport from './components/DeptHourVarianceReport';
import UserHourVarianceReport from './components/UserHourVarianceReport';

const { TabPane } = Tabs;

/**
 * 报表管理页面
 * 提供实例工时、员工工时、员工工时实例分布三种报表
 * projectKey已通过全局请求header传递，无需在组件中单独传递
 */
const ReportPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dept-project-hours');

  return (
    <div className="work-list-root">
      <Tabs
        type="line"
        activeKey={activeTab}
        onChange={setActiveTab}
      >
        {/*<TabPane*/}
        {/*  tab="实例工时周期汇总"*/}
        {/*  itemKey="instance-hours"*/}
        {/*>*/}
        {/*  <InstanceHoursReport />*/}
        {/*</TabPane>*/}
        {/*<TabPane*/}
        {/*  tab="员工工时周期汇总"*/}
        {/*  itemKey="employee-hours"*/}
        {/*>*/}
        {/*  <EmployeeHoursReport />*/}
        {/*</TabPane>*/}
        {/*<TabPane*/}
        {/*  tab="员工工时实例分布"*/}
        {/*  itemKey="employee-instance-distribution"*/}
        {/*>*/}
        {/*  <EmployeeInstanceDistributionReport />*/}
        {/*</TabPane>*/}
        <TabPane
          tab="部门/项目工时统计"
          itemKey="dept-project-hours"
        >
          <DeptProjectHoursReport />
        </TabPane>
        <TabPane
          tab="人员/项目工时统计"
          itemKey="user-project-hours"
        >
          <UserProjectHoursReport />
        </TabPane>
        <TabPane
          tab="部门每日工时分布"
          itemKey="dept-project-daily-hours"
        >
          <DeptProjectDailyHoursReport />
        </TabPane>
        <TabPane
          tab="人员每日工时分布"
          itemKey="user-project-daily-hours"
        >
          <UserProjectDailyHoursReport />
        </TabPane>
        {/*<TabPane*/}
        {/*  tab="部门差值分析"*/}
        {/*  itemKey="dept-hour-variance"*/}
        {/*>*/}
        {/*  <DeptHourVarianceReport />*/}
        {/*</TabPane>*/}
        {/*<TabPane*/}
        {/*  tab="人员差值分析"*/}
        {/*  itemKey="user-hour-variance"*/}
        {/*>*/}
        {/*  <UserHourVarianceReport />*/}
        {/*</TabPane>*/}
      </Tabs>
    </div>
  );
};

export default ReportPage;
