# Profile配置说明

## ReminderJobInitializer启动控制

### 配置概述
`ReminderJobInitializer`是工时提醒任务的初始化器，在不同环境下有不同的启动策略：

### 环境配置

#### 1. dev环境（开发环境）
- **状态**: 🚫 **不启动**
- **原因**: 避免在开发调试时启动定时任务，影响开发效率
- **配置**: `@Profile("!dev")` 排除dev环境

#### 2. test环境（测试环境）
- **状态**: ✅ **正常启动**
- **原因**: 测试环境需要验证定时任务功能
- **配置**: 无特殊限制

#### 3. prod环境（生产环境）
- **状态**: ✅ **正常启动**
- **原因**: 生产环境需要正常的工时提醒功能
- **配置**: 无特殊限制

### 配置文件

#### application-dev.yml
```yaml
# 开发环境配置
reminder:
  job:
    enabled: false  # 开发环境禁用提醒任务

spring:
  quartz:
    auto-startup: false  # 不自动启动Quartz
```

#### ReminderJobInitializer.java
```java
@Component
@Profile("!dev") // 排除dev环境
@ConditionalOnProperty(name = "reminder.job.enabled", havingValue = "true", matchIfMissing = true)
public class ReminderJobInitializer implements ApplicationRunner {
    // ...
}
```

### 手动控制

如果需要在特定环境中手动控制ReminderJobInitializer的启动，可以通过以下配置：

```yaml
reminder:
  job:
    enabled: false  # 设置为false禁用
```

或者在启动参数中指定：
```bash
java -jar app.jar --reminder.job.enabled=false
```

### 测试验证

可以通过以下方式验证配置是否生效：

```java
@Test
public void testReminderJobInitializerNotLoadedInDevProfile() {
    boolean hasReminderJobInitializer = applicationContext.containsBean("reminderJobInitializer");
    assert !hasReminderJobInitializer : "ReminderJobInitializer不应该在dev环境中加载";
}
```

### 注意事项

1. **开发环境**: 默认不启动定时任务，专注于功能开发
2. **测试环境**: 可以正常测试定时任务功能
3. **生产环境**: 正常启动所有定时任务
4. **配置优先级**: 环境变量 > application.yml > 默认值

### 环境切换

通过以下方式切换环境：

```bash
# 启动dev环境
java -jar app.jar --spring.profiles.active=dev

# 启动test环境  
java -jar app.jar --spring.profiles.active=test

# 启动prod环境
java -jar app.jar --spring.profiles.active=prod
```
