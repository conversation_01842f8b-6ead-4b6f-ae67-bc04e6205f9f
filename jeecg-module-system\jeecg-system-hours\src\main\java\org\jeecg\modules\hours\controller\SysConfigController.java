package org.jeecg.modules.hours.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.hours.entity.SysConfig;
import org.jeecg.modules.hours.service.ISysConfigService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 系统配置 控制器
 */
@Slf4j
@Api(tags="系统配置")
@RestController
@RequestMapping("/hapi/hours/sys/config")
public class SysConfigController extends JeecgController<SysConfig, ISysConfigService> {

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 飞书空间ID的请求头名称
     */
    private static final String SPACE_ID_HEADER = RequestHeaderUtil.SPACE_ID_HEADER;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "系统配置-分页列表查询")
    @ApiOperation(value="系统配置-分页列表查询", notes="系统配置-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(
            SysConfig sysConfig,
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.isNotBlank(sysConfig.getProjectKey())) {
            queryWrapper.eq("project_key", sysConfig.getProjectKey());
        }
        if (StringUtils.isNotBlank(sysConfig.getConfigGroup())) {
            queryWrapper.eq("config_group", sysConfig.getConfigGroup());
        }
        if (StringUtils.isNotBlank(sysConfig.getConfigKey())) {
            queryWrapper.like("config_key", sysConfig.getConfigKey());
        }
        
        queryWrapper.orderByAsc("config_group", "sort_order");
        
        Page<SysConfig> page = new Page<>(pageNo, pageSize);
        IPage<SysConfig> pageList = sysConfigService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 获取配置组列表
     */
    @AutoLog(value = "系统配置-获取配置组列表")
    @ApiOperation(value="系统配置-获取配置组列表", notes="系统配置-获取配置组列表")
    @GetMapping(value = "/groups")
    public Result<?> getAllGroups() {
        List<String> groups = sysConfigService.getAllGroups();
        return Result.OK(groups);
    }

    /**
     * 根据分组获取配置
     */
    @AutoLog(value = "系统配置-根据分组获取配置")
    @ApiOperation(value="系统配置-根据分组获取配置", notes="系统配置-根据分组获取配置")
    @GetMapping(value = "/group/{group}")
    public Result<?> getConfigsByGroup(
            @PathVariable("group") String group,
            @RequestParam(name = "projectKey", required = false) String projectKey) {
        List<SysConfig> configs = sysConfigService.getConfigsByGroup(projectKey, group);
        return Result.OK(configs);
    }

    /**
     * 获取工时基础设置
     */
    @AutoLog(value = "系统配置-获取工时基础设置")
    @ApiOperation(value="系统配置-获取工时基础设置", notes="系统配置-获取工时基础设置")
    @GetMapping(value = "/workHour/settings")
    public Result<?> getWorkHourSettings(@RequestHeader(value = SPACE_ID_HEADER, required = false) String spaceId) {
        Map<String, Object> settings = sysConfigService.getWorkHourSettings(spaceId);
        return Result.OK(settings);
    }

    /**
     * 根据配置键获取配置值
     */
    @AutoLog(value = "系统配置-根据配置键获取配置值")
    @ApiOperation(value="系统配置-根据配置键获取配置值", notes="系统配置-根据配置键获取配置值")
    @GetMapping(value = "/getConfigValue")
    public Result<?> getConfigValue(@RequestParam String configKey,
                                   @RequestHeader(value = SPACE_ID_HEADER, required = false) String spaceId) {
        String configValue = sysConfigService.getConfigValue(spaceId, configKey);
        return Result.OK(configValue);
    }

    /**
     * 保存工时基础设置
     */
    @AutoLog(value = "系统配置-保存工时基础设置")
    @ApiOperation(value="系统配置-保存工时基础设置", notes="系统配置-保存工时基础设置")
    @PostMapping(value = "/workHour/settings")
    public Result<?> saveWorkHourSettings(@RequestBody SysConfig config, @RequestHeader(value = SPACE_ID_HEADER, required = false) String spaceId) {
        // 设置配置分组
        config.setConfigGroup("work-hour");
        config.setStatus(true);  // true表示正常状态
        config.setValueType("number");  // 工时配置都是数字类型
        config.setDefaultValue(config.getConfigValue());  // 默认值设置为当前值
        config.setSortOrder(0);  // 默认排序号
        config.setProjectKey(spaceId);  // 从请求头设置spaceId
        
        // 根据configKey和spaceId查询是否存在
        SysConfig existConfig = sysConfigService.getOne(
            new QueryWrapper<SysConfig>()
                .eq("config_key", config.getConfigKey())
                .eq(StringUtils.isNotBlank(spaceId), "project_key", spaceId)
        );
        
        if (existConfig != null) {
            // 存在则更新
            config.setId(existConfig.getId());
            config.setUpdateTime(new Date());
            sysConfigService.updateById(config);
        } else {
            // 不存在则新增
            config.setCreateTime(new Date());
            sysConfigService.save(config);
        }
        
        return Result.OK("保存成功！");
    }

    /**
     * 添加
     */
    @AutoLog(value = "系统配置-添加")
    @ApiOperation(value="系统配置-添加", notes="系统配置-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody SysConfig sysConfig) {
        sysConfigService.saveConfig(sysConfig);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @AutoLog(value = "系统配置-编辑")
    @ApiOperation(value="系统配置-编辑", notes="系统配置-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody SysConfig sysConfig) {
        sysConfigService.saveConfig(sysConfig);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "系统配置-通过id删除")
    @ApiOperation(value="系统配置-通过id删除", notes="系统配置-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        sysConfigService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "系统配置-批量删除")
    @ApiOperation(value="系统配置-批量删除", notes="系统配置-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.sysConfigService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 批量添加
     */
    @AutoLog(value = "系统配置-批量添加")
    @ApiOperation(value="系统配置-批量添加", notes="系统配置-批量添加")
    @PostMapping(value = "/batchAdd")
    public Result<?> batchAdd(@RequestBody List<SysConfig> configs) {
        sysConfigService.batchSaveConfig(configs);
        return Result.OK("批量添加成功！");
    }

    /**
     * 根据配置键获取配置
     */
    @AutoLog(value = "系统配置-根据配置键获取配置")
    @ApiOperation(value="系统配置-根据配置键获取配置", notes="系统配置-根据配置键获取配置")
    @GetMapping(value = "/getByKey/{configKey}")
    public Result<?> getByKey(
            @PathVariable("configKey") String configKey,
            @RequestHeader(value = SPACE_ID_HEADER, required = false) String spaceId) {
        SysConfig config = sysConfigService.getOne(
            new QueryWrapper<SysConfig>()
                .eq("config_key", configKey)
                .eq(StringUtils.isNotBlank(spaceId), "project_key", spaceId)
        );
        return Result.OK(config);
    }

    /**
     * 获取工时登记规则富文本
     */
    @AutoLog(value = "系统配置-获取工时登记规则富文本")
    @ApiOperation(value="系统配置-获取工时登记规则富文本", notes="系统配置-获取工时登记规则富文本")
    @GetMapping(value = "/rules/richtext")
    public Result<?> getWorkHourRules(@RequestHeader(value = SPACE_ID_HEADER, required = false) String spaceId) {
        SysConfig config = sysConfigService.getOne(
            new QueryWrapper<SysConfig>()
                .eq("config_key", "work_hour_record_rules")
                .eq(StringUtils.isNotBlank(spaceId), "project_key", spaceId)
        );
        
        if (config != null) {
            return Result.OK(config.getConfigValue());
        } else {
            // 如果没有配置，返回默认内容
            String defaultContent = "{\"doc\":\"<p>1. 工时填写需真实反映实际工作情况，禁止弄虚作假。</p>" +
                "<p>2. 每日工时填写不得超过当日考勤工时。</p>" +
                "<p>3. 请在当天或次日完成工时填写，月底务必在规定时间前完成。</p>" +
                "<p>4. 工时需经项目负责人和直属上级审批通过。</p>" +
                "<p>5. 请假当天不得填写工时。</p>\",\"doc_html\":\"\",\"doc_text\":\"\",\"is_empty\":false}";
            return Result.OK(defaultContent);
        }
    }

    /**
     * 保存工时登记规则富文本
     */
    @AutoLog(value = "系统配置-保存工时登记规则富文本")
    @ApiOperation(value="系统配置-保存工时登记规则富文本", notes="系统配置-保存工时登记规则富文本")
    @PostMapping(value = "/rules/richtext")
    public Result<?> saveWorkHourRules(@RequestBody Map<String, String> params, 
                                      @RequestHeader(value = SPACE_ID_HEADER, required = false) String spaceId) {
        String content = params.get("content");
        
        try {
            // 记录收到的原始内容
            log.info("收到富文本内容，长度: {}", content != null ? content.length() : 0);
            
            // 检查内容是否为空
            if (StringUtils.isBlank(content)) {
                log.warn("接收到空的富文本内容，使用默认内容");
                content = "{\"doc\":\"<p>1. 工时填写需真实反映实际工作情况，禁止弄虚作假。</p>" +
                    "<p>2. 每日工时填写不得超过当日考勤工时。</p>" +
                    "<p>3. 请在当天或次日完成工时填写，月底务必在规定时间前完成。</p>" +
                    "<p>4. 工时需经项目负责人和直属上级审批通过。</p>" +
                    "<p>5. 请假当天不得填写工时。</p>\",\"doc_html\":\"\",\"doc_text\":\"\",\"is_empty\":false}";
            }
            
            // 使用新的服务方法保存配置
            Map<String, Object> extraParams = new HashMap<>();
            extraParams.put("configName", "工时登记规则说明");
            extraParams.put("configGroup", "rules");
            extraParams.put("valueType", "json");  // 修改为json类型
            extraParams.put("defaultValue", content);
            extraParams.put("sortOrder", 10);
            extraParams.put("remark", "工时登记规则富文本说明");
            
            boolean success = sysConfigService.saveOrUpdateConfigWithFallback(
                spaceId, "work_hour_record_rules", content, extraParams);
            
            if (success) {
                log.info("保存富文本配置成功");
                return Result.OK("保存成功");
            } else {
                log.error("保存富文本配置失败");
                return Result.error("保存失败");
            }
        } catch (Exception e) {
            log.error("保存富文本内容失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    @PostMapping("/save")
    public Result<?> saveConfig(
            @RequestBody Map<String, String> params,
            @RequestHeader(value = SPACE_ID_HEADER, required = false) String spaceId) {
        String configKey = params.get("configKey");
        String groupCode = params.get("groupCode");
        String configValue = params.get("configValue");

        if (configKey == null || groupCode == null || configValue == null) {
            return Result.error("参数不完整");
        }

        try {
            // 使用新的服务方法保存配置
            Map<String, Object> extraParams = new HashMap<>();
            extraParams.put("configGroup", groupCode);
            
            boolean success = sysConfigService.saveOrUpdateConfigWithFallback(
                spaceId, configKey, configValue, extraParams);
            
            return success ? Result.OK("保存成功") : Result.error("保存失败");
        } catch (Exception e) {
            log.error("保存配置失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }
    
    
    
    /** 
     * 批量保存配置
     * @param params
     * @param spaceId
     * <AUTHOR>
     * @date 2025年6月7日 
     * @version V0.1 
     * 
     */
    @PostMapping("/saveBatchByKeys")
    public Result<?> saveBatchByKeys(
            @RequestBody List<Map<String, String>> params,
            @RequestHeader(value = SPACE_ID_HEADER, required = false) String spaceId) {
        if (null == params || params.isEmpty()) {
            return Result.error("参数不完整");
        }

        try {
            boolean success = sysConfigService.saveBatchByKeys(params, spaceId);
            
            return success ? Result.OK("保存成功") : Result.error("保存失败");
        } catch (Exception e) {
            log.error("保存配置失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "系统配置-获取系统设置")
    @ApiOperation(value="系统配置-获取系统设置", notes="系统配置-获取系统设置")
    @GetMapping(value = "/sys/settings")
    public Result<?> getSysSettings(@RequestHeader(value = SPACE_ID_HEADER, required = false) String spaceId) {
        Map<String, Object> settings = sysConfigService.getSysSettings(spaceId);
        return Result.OK(settings);
    }
} 