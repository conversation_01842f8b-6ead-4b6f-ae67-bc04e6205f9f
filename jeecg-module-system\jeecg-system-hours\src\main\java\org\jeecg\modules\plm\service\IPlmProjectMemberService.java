package org.jeecg.modules.plm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.plm.entity.PlmProjectMember;

import java.util.List;

/**
 * PLM项目成员Service接口
 */
public interface IPlmProjectMemberService extends IService<PlmProjectMember> {

    /**
     * 根据项目ID查询项目成员
     *
     * @param projectId 项目ID
     * @return 项目成员列表
     */
    List<PlmProjectMember> queryByProjectId(String projectId);

    /**
     * 根据项目ID列表批量查询项目成员
     *
     * @param projectIds 项目ID列表
     * @return 项目成员列表
     */
    List<PlmProjectMember> queryByProjectIds(List<String> projectIds);

    /**
     * 根据用户ID查询参与的项目
     *
     * @param userId 用户ID
     * @return 项目成员列表
     */
    List<PlmProjectMember> queryByUserId(String userId);

    /**
     * 批量新增项目成员
     *
     * @param memberList 项目成员列表
     * @return 是否成功
     */
    boolean batchInsert(List<PlmProjectMember> memberList);

    /**
     * 更新项目成员
     *
     * @param projectId 项目ID
     * @param members   成员列表
     * @return 是否成功
     */
    boolean updateProjectMembers(String projectId, String projectCode, List<PlmProjectMember> members);
} 