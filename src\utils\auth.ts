/**
 * 飞书授权工具
 * 用于获取授权码、存储和管理token等
 */
import { STORAGE_KEYS, API_CONFIG, HTTP_HEADERS } from './config';

// 从配置中导入存储键
const TOKEN_KEY = STORAGE_KEYS.TOKEN;
const USER_KEY = STORAGE_KEYS.USER_ID;
const USER_INFO_KEY = STORAGE_KEYS.USER_INFO;
const SPACE_ID_KEY = STORAGE_KEYS.SPACE_ID;
const PROJECT_KEY_KEY = STORAGE_KEYS.PROJECT_KEY;

// 用户信息接口
export interface UserInfo {
  userId: string;
  name: string;
  email: string;
  mobile: string;
  deptId?: string;
  token: string;
  expireTime: string | Date;
}

/**
 * 检查登录状态
 */
async function isLogin() {
  const token = getToken();
  
  if (!token) return false;
  
  try {
    // 获取空间ID
    const spaceId = getFeishuSpaceId();
    if (!spaceId) {
      console.error('未找到飞书空间ID');
      return false;
    }
    
    // 调用验证token接口
    const response = await fetch(API_CONFIG.getFullUrl(API_CONFIG.API_PATHS.VERIFY_TOKEN) + `?token=${token}`, {
      method: 'GET',
      headers: {
        [HTTP_HEADERS.CONTENT_TYPE]: 'application/json',
        [HTTP_HEADERS.SPACE_ID]: spaceId
      }
    });
    
    if (!response.ok) {
      // 清除无效token
      clearAuth();
      return false;
    }
    
    const result = await response.json();
    
    if (result.code !== 200) {
      clearAuth();
      return false;
    }
    
    // 更新用户信息
    saveUserInfo(result.data);
    
    return true;
  } catch (e) {
    console.error('验证登录状态失败:', e);
    return false;
  }
}

/**
 * 保存用户信息
 */
export function saveUserInfo(userInfo: UserInfo): void {
  try {
    console.log('正在保存用户信息:', userInfo);
    
    // 检查userInfo中是否有token字段
    if (!userInfo.token) {
      console.error('用户信息中缺少token字段:', userInfo);
      return;
    }
    
    // 保存token
    localStorage.setItem(TOKEN_KEY, userInfo.token);
    console.log('保存token:', userInfo.token, '到', TOKEN_KEY);
    
    // 保存用户ID
    localStorage.setItem(USER_KEY, userInfo.userId);
    console.log('保存用户ID:', userInfo.userId, '到', USER_KEY);
    
    // 保存完整用户信息
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
    console.log('保存完整用户信息到', USER_INFO_KEY);
    
    // 手动检查token是否已正确保存
    const savedToken = localStorage.getItem(TOKEN_KEY);
    if (savedToken !== userInfo.token) {
      console.error('Token保存失败! 预期:', userInfo.token, '实际:', savedToken);
    }
    
  } catch (e) {
    console.error('保存用户信息失败:', e);
  }
}

/**
 * 获取用户信息
 */
export function getUserInfo(): UserInfo | null {
  try {
    const userInfoStr = localStorage.getItem(USER_INFO_KEY);
    if (!userInfoStr) return null;
    return JSON.parse(userInfoStr);
  } catch (e) {
    console.error('获取用户信息失败:', e);
    return null;
  }
}

// 您可以在需要授权的位置调用该方法进行授权
export async function authorize() {
  const logined = await isLogin();
  if (logined) {
    console.log('用户已登录，无需重新授权');
    return true;
  }
  
  console.log('开始获取授权码...');
  // Step1. 获取授权码
  const { code } = await window.JSSDK.utils.getAuthCode();
  console.log('获取到授权码:', code);

  try {
    console.log('正在请求登录接口...');
    const requestUrl = API_CONFIG.getAuthUrl(code);
    console.log('登录请求URL:', requestUrl);
    
    // 获取空间ID
    const spaceId = getFeishuSpaceId();
    if (!spaceId) {
      console.error('未找到飞书空间ID');
      return false;
    }
    
    // Step2. 请求登录接口，传入授权码
    const response = await fetch(requestUrl, {
      method: 'GET',
      headers: {
        [HTTP_HEADERS.CONTENT_TYPE]: 'application/json',
        [HTTP_HEADERS.SPACE_ID]: spaceId
      }
    });
    
    if (!response.ok) {
      console.error('登录请求失败, HTTP状态码:', response.status);
      return false;
    }
    
    const result = await response.json();
    console.log('登录接口返回:', result);
    
    if (result.code !== 200) {
      console.error('登录失败:', result.message);
      return false;
    }
    
    // 打印登录返回的用户信息
    console.log('登录成功，用户信息:', result.result);
    
    // 保存用户信息，适配API返回的data或result字段
    if (result.result) {
      saveUserInfo(result.result);
    } else if (result.data) {
      saveUserInfo(result.data);
    } else {
      console.error('API返回中未找到用户信息:', result);
      return false;
    }
    
    // 确认token已正确保存
    const savedToken = getToken();
    console.log('保存后的token:', savedToken);
    
    // 调用初始化配置接口
    try {
      console.log('正在获取系统配置...');
      
      // 确保 token 不为空
      if (!savedToken) {
        console.error('获取配置失败: token 为空');
        return true;
      }
      
      const initResponse = await fetch(API_CONFIG.getFullUrl(API_CONFIG.API_PATHS.INIT_CONFIG), {
        method: 'GET',
        headers: {
          [HTTP_HEADERS.CONTENT_TYPE]: 'application/json',
          [HTTP_HEADERS.SPACE_ID]: spaceId,
          [HTTP_HEADERS.HOUR_TOKEN]: savedToken
        }
      });
      
      if (!initResponse.ok) {
        console.error('获取配置失败, HTTP状态码:', initResponse.status);
      } else {
        const initResult = await initResponse.json();
        if (initResult.code !== 200) {
          console.error('获取配置失败:', initResult.message);
        } else {
          console.log('获取配置成功:', initResult.result);
          // TODO: 处理配置数据
        }
      }
    } catch (e) {
      console.error('获取系统配置异常:', e);
      // 不影响登录流程
    }
    
    return true;
  } catch (e) {
    // 授权登录失败
    console.error('授权失败:', e);
    return false;
  }
}

/**
 * 检查是否已授权
 */
export const isAuthenticated = (): boolean => {
  return !!getToken();
};

/**
 * 获取token
 */
export const getToken = (): string | null => {
  const token = localStorage.getItem(TOKEN_KEY);
  console.log('从 localStorage 获取 token:', token);
  return token;
};

/**
 * 获取用户ID
 */
export const getUserId = (): string | null => {
  return localStorage.getItem(USER_KEY);
};

/**
 * 注销登录
 */
export const logout = async (): Promise<boolean> => {
  const token = getToken();
  if (!token) {
    clearAuth();
    return true;
  }
  
  try {
    // 获取空间ID
    const spaceId = getFeishuSpaceId();
    if (!spaceId) {
      console.error('未找到飞书空间ID');
      clearAuth();
      return true;
    }
    
    // 调用注销接口
    const response = await fetch(API_CONFIG.getFullUrl(API_CONFIG.API_PATHS.LOGOUT) + `?token=${token}`, {
      method: 'GET',
      headers: {
        [HTTP_HEADERS.CONTENT_TYPE]: 'application/json',
        [HTTP_HEADERS.SPACE_ID]: spaceId,
        [HTTP_HEADERS.HOUR_TOKEN]: token
      }
    });
    
    // 清除本地存储的认证信息
    clearAuth();
    
    return response.ok;
  } catch (e) {
    console.error('注销失败:', e);
    clearAuth();
    return false;
  }
};

/**
 * 清除授权信息
 */
export const clearAuth = (): void => {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(USER_KEY);
  localStorage.removeItem(USER_INFO_KEY);
};

/**
 * 获取并保存飞书空间ID
 */
export const saveFeishuSpaceId = async (): Promise<string | null> => {
  try {
    if (window.JSSDK && window.JSSDK.page) {
      const pageContext = await window.JSSDK.page.getContext();
      if (pageContext && pageContext.spaceId) {
        // 保存空间ID到localStorage
        localStorage.setItem(SPACE_ID_KEY, pageContext.spaceId);
        console.log('已保存飞书空间ID到localStorage:', pageContext.spaceId);
        return pageContext.spaceId;
      }
    }
    return null;
  } catch (error) {
    console.error('获取飞书空间ID失败:', error);
    return null;
  }
};

/**
 * 获取飞书空间ID
 */
export const getFeishuSpaceId = (): string | null => {
  try {
    // 从localStorage中获取
    const spaceId = localStorage.getItem(SPACE_ID_KEY);
    if (spaceId) {
      return spaceId;
    }
    
    // 尝试从URL中获取
    const urlParams = new URLSearchParams(window.location.search);
    const spaceIdFromUrl = urlParams.get('space_id') || urlParams.get('spaceId');
    if (spaceIdFromUrl) {
      // 顺便保存到localStorage
      localStorage.setItem(SPACE_ID_KEY, spaceIdFromUrl);
      return spaceIdFromUrl;
    }
    
    return null;
  } catch (error) {
    console.error('获取飞书空间ID失败:', error);
    return null;
  }
};

/**
 * 保存飞书项目Key
 */
export const saveFeishuProjectKey = (projectKey: string): void => {
  if (!projectKey || projectKey.trim() === '') {
    console.warn('尝试保存空的项目Key，已忽略');
    return;
  }
  
  try {
    localStorage.setItem(PROJECT_KEY_KEY, projectKey);
    console.log('已保存飞书项目Key到localStorage:', projectKey);
  } catch (error) {
    console.error('保存飞书项目Key失败:', error);
  }
};

/**
 * 获取飞书项目Key
 * 尝试返回真实的项目Key，在开发环境中也可以使用保存的值
 * @returns 项目Key或null
 */
export const getFeishuProjectKey = (): string | null => {
  try {
    // 从localStorage中获取
    const projectKey = localStorage.getItem(PROJECT_KEY_KEY);
    
    // 检查是否有有效值
    if (projectKey && projectKey.trim() !== '') {
      return projectKey;
    }
    
    // 未找到有效的项目Key
    return null;
  } catch (error) {
    console.error('获取飞书项目Key失败:', error);
    return null;
  }
}; 