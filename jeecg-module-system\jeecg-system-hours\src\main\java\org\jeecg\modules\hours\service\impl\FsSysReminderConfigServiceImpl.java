package org.jeecg.modules.hours.service.impl;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.jeecg.modules.hours.entity.FsSysReminderConfig;
import org.jeecg.modules.hours.mapper.FsSysReminderConfigMapper;
import org.jeecg.modules.hours.service.IFsSysReminderConfigService;
import org.jeecg.modules.hours.service.IReminderScheduleService;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 登记提醒配置表
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Slf4j
@Service
public class FsSysReminderConfigServiceImpl extends ServiceImpl<FsSysReminderConfigMapper, FsSysReminderConfig> implements IFsSysReminderConfigService {

    @Autowired
    private FsSysReminderConfigMapper reminderConfigMapper;

    @Autowired
    private IReminderScheduleService reminderScheduleService;

    // 时间格式验证正则表达式
    private static final Pattern TIME_PATTERN = Pattern.compile("^([01]?[0-9]|2[0-3]):[0-5][0-9]$");
    
    // 有效的提醒类型
    private static final List<String> VALID_REMINDER_TYPES = Arrays.asList("daily", "weekly", "monthly");
    
    // 有效的重复方式
    private static final List<String> VALID_REPEAT_MODES = Arrays.asList(
        "current_day", "next_day", "current_week", "next_week", "current_month", "next_month"
    );
    
    // 有效的星期
    private static final List<String> VALID_WEEK_DAYS = Arrays.asList(
        "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"
    );

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(FsSysReminderConfig entity) {
        log.info("新增提醒配置，项目: {}, 类型: {}, 状态: {}",
                entity.getProjectKey(), entity.getReminderType(), entity.getStatus());

        // 验证配置数据
        String validationError = validateReminderConfig(entity);
        if (validationError != null) {
            throw new RuntimeException("配置验证失败: " + validationError);
        }

        // 保存到数据库
        boolean saveResult = super.save(entity);
        if (!saveResult) {
            log.error("保存提醒配置到数据库失败");
            return false;
        }

        // 如果状态为启用，则启动定时任务
        if ("启用".equals(entity.getStatus())) {
            try {
                boolean jobResult = reminderScheduleService.startReminderJob(entity);
                log.info("新增配置启动定时任务结果: {}, 配置ID: {}", jobResult, entity.getId());
            } catch (Exception e) {
                log.error("新增配置启动定时任务时发生异常，配置ID: {}", entity.getId(), e);
                // 定时任务启动失败不影响配置保存
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(FsSysReminderConfig entity) {
        log.info("更新提醒配置，ID: {}, 项目: {}, 类型: {}, 状态: {}",
                entity.getId(), entity.getProjectKey(), entity.getReminderType(), entity.getStatus());

        // 获取更新前的配置
        FsSysReminderConfig oldConfig = this.getById(entity.getId());
        if (oldConfig == null) {
            log.error("未找到要更新的提醒配置，ID: {}", entity.getId());
            return false;
        }

        // 验证配置数据
        String validationError = validateReminderConfig(entity);
        if (validationError != null) {
            throw new RuntimeException("配置验证失败: " + validationError);
        }

        // 更新到数据库
        boolean updateResult = super.updateById(entity);
        if (!updateResult) {
            log.error("更新提醒配置到数据库失败，ID: {}", entity.getId());
            return false;
        }

        // 管理定时任务
        try {
            String oldStatus = oldConfig.getStatus();
            String newStatus = entity.getStatus();

            // 判断是否需要管理定时任务
            if ("启用".equals(newStatus)) {
                if (!"启用".equals(oldStatus)) {
                    // 从禁用变为启用：启动定时任务
                    boolean jobResult = reminderScheduleService.startReminderJob(entity);
                    log.info("配置启用，启动定时任务结果: {}, 配置ID: {}", jobResult, entity.getId());
                } else {
                    // 已启用状态下的更新：更新定时任务
                    boolean jobResult = reminderScheduleService.updateReminderJob(entity);
                    log.info("配置更新，更新定时任务结果: {}, 配置ID: {}", jobResult, entity.getId());
                }
            } else if ("暂停".equals(newStatus) && "启用".equals(oldStatus)) {
                // 从启用变为禁用：停止定时任务
                boolean jobResult = reminderScheduleService.stopReminderJob(entity);
                log.info("配置禁用，停止定时任务结果: {}, 配置ID: {}", jobResult, entity.getId());
            }
        } catch (Exception e) {
            log.error("更新配置管理定时任务时发生异常，配置ID: {}", entity.getId(), e);
            // 定时任务操作失败不影响配置更新
        }

        return true;
    }

    @Override
    public IPage<FsSysReminderConfig> queryPageList(Page<FsSysReminderConfig> page, FsSysReminderConfig reminderConfig, String projectKey) {
        LambdaQueryWrapper<FsSysReminderConfig> queryWrapper = new LambdaQueryWrapper<>();
        
        // 工作空间过滤
        queryWrapper.eq(StringUtils.isNotBlank(projectKey), FsSysReminderConfig::getProjectKey, projectKey);
        
        // 提醒类型过滤
        queryWrapper.eq(StringUtils.isNotBlank(reminderConfig.getReminderType()), 
                       FsSysReminderConfig::getReminderType, reminderConfig.getReminderType());
        
        // 状态过滤
        queryWrapper.eq(StringUtils.isNotBlank(reminderConfig.getStatus()), 
                       FsSysReminderConfig::getStatus, reminderConfig.getStatus());
        
        // 备注模糊查询
        queryWrapper.like(StringUtils.isNotBlank(reminderConfig.getRemark()), 
                         FsSysReminderConfig::getRemark, reminderConfig.getRemark());
        
        // 按创建时间倒序
        queryWrapper.orderByDesc(FsSysReminderConfig::getCreateTime);
        
        return this.page(page, queryWrapper);
    }

    @Override
    public List<FsSysReminderConfig> queryByProjectKey(String projectKey) {
        return reminderConfigMapper.selectByProjectKey(projectKey);
    }

    @Override
    public List<FsSysReminderConfig> queryByProjectKeyAndStatus(String projectKey, String status) {
        return reminderConfigMapper.selectByProjectKeyAndStatus(projectKey, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addReminderConfig(FsSysReminderConfig reminderConfig, String projectKey, String createBy) {
        // 验证数据
        String validationError = validateReminderConfig(reminderConfig);
        if (validationError != null) {
            throw new RuntimeException(validationError);
        }
        
        // 检查是否存在相同配置
        if (existsSameConfig(reminderConfig, projectKey)) {
            throw new RuntimeException("已存在相同的提醒配置");
        }
        
        // 设置基础信息
        reminderConfig.setProjectKey(projectKey);
        reminderConfig.setCreateBy(createBy);
        reminderConfig.setCreateTime(new Date());
        reminderConfig.setDelFlag(0);
        
        // 默认状态为启用
        if (StringUtils.isBlank(reminderConfig.getStatus())) {
            reminderConfig.setStatus("启用");
        }
        
        return this.save(reminderConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateReminderConfig(FsSysReminderConfig reminderConfig, String updateBy) {
        // 验证数据
        String validationError = validateReminderConfig(reminderConfig);
        if (validationError != null) {
            throw new RuntimeException(validationError);
        }
        
        // 设置更新信息
        reminderConfig.setUpdateBy(updateBy);
        reminderConfig.setUpdateTime(new Date());
        
        return this.updateById(reminderConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteReminderConfig(Long id, String deleteBy) {
        log.info("删除提醒配置，ID: {}, 操作人: {}", id, deleteBy);

        // 获取要删除的配置
        FsSysReminderConfig reminderConfig = this.getById(id);
        if (reminderConfig == null) {
            log.error("未找到要删除的提醒配置，ID: {}", id);
            return false;
        }

        // 如果配置是启用状态，先停止定时任务
        if ("启用".equals(reminderConfig.getStatus())) {
            try {
                boolean jobResult = reminderScheduleService.stopReminderJob(reminderConfig);
                log.info("删除配置前停止定时任务结果: {}, 配置ID: {}", jobResult, id);
            } catch (Exception e) {
                log.error("删除配置前停止定时任务时发生异常，配置ID: {}", id, e);
                // 定时任务停止失败不影响配置删除
            }
        }

        // 逻辑删除配置
        LambdaUpdateWrapper<FsSysReminderConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FsSysReminderConfig::getId, id)
                    .set(FsSysReminderConfig::getDelFlag, 1)
                    .set(FsSysReminderConfig::getUpdateBy, deleteBy)
                    .set(FsSysReminderConfig::getUpdateTime, new Date());

        return this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteReminderConfig(List<Long> ids, String deleteBy) {
        log.info("批量删除提醒配置，IDs: {}, 操作人: {}", ids, deleteBy);

        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 获取要删除的配置列表
        List<FsSysReminderConfig> configs = this.listByIds(ids);
        if (configs.isEmpty()) {
            log.warn("未找到任何要删除的提醒配置，IDs: {}", ids);
            return false;
        }

        // 批量停止启用状态的定时任务
        for (FsSysReminderConfig config : configs) {
            if ("启用".equals(config.getStatus())) {
                try {
                    boolean jobResult = reminderScheduleService.stopReminderJob(config);
                    log.info("批量删除前停止定时任务结果，配置ID: {}, 结果: {}", config.getId(), jobResult);
                } catch (Exception e) {
                    log.error("批量删除前停止定时任务时发生异常，配置ID: {}", config.getId(), e);
                    // 定时任务停止失败不影响配置删除
                }
            }
        }

        // 批量逻辑删除配置
        LambdaUpdateWrapper<FsSysReminderConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(FsSysReminderConfig::getId, ids)
                    .set(FsSysReminderConfig::getDelFlag, 1)
                    .set(FsSysReminderConfig::getUpdateBy, deleteBy)
                    .set(FsSysReminderConfig::getUpdateTime, new Date());

        return this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleStatus(Long id, String status, String updateBy) {
        log.info("切换提醒配置状态，ID: {}, 状态: {}, 操作人: {}", id, status, updateBy);

        if (!Arrays.asList("启用", "暂停").contains(status)) {
            throw new RuntimeException("无效的状态值");
        }

        // 获取当前配置
        FsSysReminderConfig reminderConfig = this.getById(id);
        if (reminderConfig == null) {
            log.error("未找到提醒配置，ID: {}", id);
            return false;
        }

        // 更新数据库状态
        LambdaUpdateWrapper<FsSysReminderConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FsSysReminderConfig::getId, id)
                    .set(FsSysReminderConfig::getStatus, status)
                    .set(FsSysReminderConfig::getUpdateBy, updateBy)
                    .set(FsSysReminderConfig::getUpdateTime, new Date());

        boolean updateResult = this.update(updateWrapper);
        if (!updateResult) {
            log.error("更新提醒配置状态失败，ID: {}", id);
            return false;
        }

        // 更新配置对象状态
        reminderConfig.setStatus(status);

        // 管理定时任务
        try {
            if ("启用".equals(status)) {
                // 启用时启动定时任务
                boolean jobResult = reminderScheduleService.startReminderJob(reminderConfig);
                log.info("启动定时任务结果: {}", jobResult);
            } else {
                // 禁用时停止定时任务
                boolean jobResult = reminderScheduleService.stopReminderJob(reminderConfig);
                log.info("停止定时任务结果: {}", jobResult);
            }
        } catch (Exception e) {
            log.error("管理定时任务时发生异常，配置ID: {}", id, e);
            // 定时任务操作失败不影响状态更新
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchToggleStatus(List<Long> ids, String status, String updateBy) {
        log.info("批量切换提醒配置状态，IDs: {}, 状态: {}, 操作人: {}", ids, status, updateBy);

        if (ids == null || ids.isEmpty()) {
            return false;
        }

        if (!Arrays.asList("启用", "暂停").contains(status)) {
            throw new RuntimeException("无效的状态值");
        }

        // 获取所有配置
        List<FsSysReminderConfig> configs = this.listByIds(ids);
        if (configs.isEmpty()) {
            log.warn("未找到任何提醒配置，IDs: {}", ids);
            return false;
        }

        // 批量更新数据库状态
        int updateCount = reminderConfigMapper.batchUpdateStatus(ids, status, updateBy);
        if (updateCount <= 0) {
            log.error("批量更新提醒配置状态失败，IDs: {}", ids);
            return false;
        }

        // 批量管理定时任务
        for (FsSysReminderConfig config : configs) {
            try {
                config.setStatus(status); // 更新状态

                if ("启用".equals(status)) {
                    // 启用时启动定时任务
                    boolean jobResult = reminderScheduleService.startReminderJob(config);
                    log.info("启动定时任务结果，配置ID: {}, 结果: {}", config.getId(), jobResult);
                } else {
                    // 禁用时停止定时任务
                    boolean jobResult = reminderScheduleService.stopReminderJob(config);
                    log.info("停止定时任务结果，配置ID: {}, 结果: {}", config.getId(), jobResult);
                }
            } catch (Exception e) {
                log.error("管理定时任务时发生异常，配置ID: {}", config.getId(), e);
                // 定时任务操作失败不影响状态更新
            }
        }

        return true;
    }

    @Override
    public String validateReminderConfig(FsSysReminderConfig reminderConfig) {
        // 验证提醒类型
        if (StringUtils.isBlank(reminderConfig.getReminderType())) {
            return "提醒类型不能为空";
        }
        if (!VALID_REMINDER_TYPES.contains(reminderConfig.getReminderType())) {
            return "无效的提醒类型";
        }
        
        // 验证重复方式
        if (StringUtils.isBlank(reminderConfig.getRepeatMode())) {
            return "重复方式不能为空";
        }
        if (!VALID_REPEAT_MODES.contains(reminderConfig.getRepeatMode())) {
            return "无效的重复方式";
        }
        
        // 验证提醒时间
        if (StringUtils.isBlank(reminderConfig.getReminderTime())) {
            return "提醒时间不能为空";
        }
        if (!TIME_PATTERN.matcher(reminderConfig.getReminderTime()).matches()) {
            return "提醒时间格式不正确，请使用HH:MM格式";
        }
        
        // 验证提醒日期（周提醒和月提醒需要）
        if ("weekly".equals(reminderConfig.getReminderType()) || "monthly".equals(reminderConfig.getReminderType())) {
            if (StringUtils.isBlank(reminderConfig.getReminderDay())) {
                return "周提醒和月提醒必须设置提醒日期";
            }
            
            if ("weekly".equals(reminderConfig.getReminderType())) {
                if (!VALID_WEEK_DAYS.contains(reminderConfig.getReminderDay())) {
                    return "无效的星期设置";
                }
            } else if ("monthly".equals(reminderConfig.getReminderType())) {
                if (!"last_day".equals(reminderConfig.getReminderDay())) {
                    try {
                        int day = Integer.parseInt(reminderConfig.getReminderDay());
                        if (day < 1 || day > 31) {
                            return "月份日期必须在1-31之间或设置为last_day";
                        }
                    } catch (NumberFormatException e) {
                        return "无效的月份日期设置";
                    }
                }
            }
        }
        
        // 验证提醒类型和重复方式的组合
        String reminderType = reminderConfig.getReminderType();
        String repeatMode = reminderConfig.getRepeatMode();
        
        if ("daily".equals(reminderType) && !Arrays.asList("current_day", "next_day").contains(repeatMode)) {
            return "每天提醒只能选择当天或次日";
        }
        if ("weekly".equals(reminderType) && !Arrays.asList("current_week", "next_week").contains(repeatMode)) {
            return "每周提醒只能选择本周或次周";
        }
        if ("monthly".equals(reminderType) && !Arrays.asList("current_month", "next_month").contains(repeatMode)) {
            return "每月提醒只能选择本月或次月";
        }
        
        return null; // 验证通过
    }

    @Override
    public List<FsSysReminderConfig> queryReminderConfigsToExecute() {
        LocalDateTime now = LocalDateTime.now();
        String currentTime = now.format(DateTimeFormatter.ofPattern("HH:mm"));
        int dayOfWeek = now.getDayOfWeek().getValue(); // 1-7，1为周一
        int dayOfMonth = now.getDayOfMonth();
        
        // 判断是否为月末
        boolean isLastDayOfMonth = now.toLocalDate().equals(
            now.toLocalDate().withDayOfMonth(now.toLocalDate().lengthOfMonth())
        );
        
        return reminderConfigMapper.selectReminderConfigsToExecute(currentTime, dayOfWeek, dayOfMonth, isLastDayOfMonth);
    }

    @Override
    public List<FsSysReminderConfig> queryEnabledByReminderType(String reminderType) {
        return reminderConfigMapper.selectEnabledByReminderType(reminderType);
    }

    @Override
    public int countByProjectKey(String projectKey) {
        return reminderConfigMapper.countByProjectKey(projectKey);
    }

    @Override
    public boolean existsSameConfig(FsSysReminderConfig reminderConfig, String projectKey) {
        LambdaQueryWrapper<FsSysReminderConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FsSysReminderConfig::getProjectKey, projectKey)
                   .eq(FsSysReminderConfig::getReminderType, reminderConfig.getReminderType())
                   .eq(FsSysReminderConfig::getRepeatMode, reminderConfig.getRepeatMode())
                   .eq(FsSysReminderConfig::getReminderTime, reminderConfig.getReminderTime());
        
        // 如果有提醒日期，也要匹配
        if (StringUtils.isNotBlank(reminderConfig.getReminderDay())) {
            queryWrapper.eq(FsSysReminderConfig::getReminderDay, reminderConfig.getReminderDay());
        } else {
            queryWrapper.isNull(FsSysReminderConfig::getReminderDay);
        }
        
        // 如果是更新操作，排除当前记录
        if (reminderConfig.getId() != null) {
            queryWrapper.ne(FsSysReminderConfig::getId, reminderConfig.getId());
        }
        
        return this.count(queryWrapper) > 0;
    }
}
