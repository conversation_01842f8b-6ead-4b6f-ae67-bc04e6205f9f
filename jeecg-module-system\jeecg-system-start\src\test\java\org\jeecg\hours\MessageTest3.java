package org.jeecg.hours;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.feishu.service.FeiShuMessageService;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.attendance.v1.model.ChildItem;
import com.lark.oapi.service.attendance.v1.model.Item;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UserStatsView;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 消息推送测试
 * @Author: lsq
 */
@Slf4j
//@ActiveProfiles("dev")
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class MessageTest3 {

	@Autowired
	private FsSyncTask faSyncTask;
	@Autowired
	private IFeishuAuthService feishuAuthService;
	
	private static final String appId = "cli_a7b2f4bbcb38500d";
	
	private static final String appSecret = "kzen0nGwHB15eEUVsDXkp1wn0YOF4HUK";

	@org.junit.jupiter.api.Test
	public void testname() throws Exception {
		
		 // 构建消息内容
        String title, content, buttonText;
        String buttonUrl = "https://project.feishu.cn/xqr1jq/meegoPlg/MII_6861E7637D49001C_board_ihqi64";
        
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
        String formattedDate = "07.07-07.09";
        
        // 审批通过
        title = "审批待办提醒";
        content = "【任涛】提交了工时数据待您审批。<br>"
                + "待审批周期：" + formattedDate + "";
        buttonText = "查看详情";
        
        // 发送消息
        FeiShuMessageService.instance(appId, appSecret)
                .sendMessage("on_a103bf00d2dd4e74022ceaa784a4654b", title, content, buttonText, buttonUrl);
	}
	
}
