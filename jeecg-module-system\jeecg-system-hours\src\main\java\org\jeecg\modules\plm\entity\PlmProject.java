package org.jeecg.modules.plm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * PLM项目实体类
 */
@Data
@TableName("plm_project")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "PlmProject对象", description = "PLM项目")
public class PlmProject implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 项目编码
     */
    @Excel(name = "项目编码", width = 15)
    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称", width = 20)
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目状态
     */
    @Excel(name = "项目状态", width = 10, dicCode = "project_status")
    @ApiModelProperty(value = "项目状态")
    @Dict(dicCode = "project_status")
    private String projectStatus;

    /**
     * 项目级别
     */
    @Excel(name = "项目级别", width = 10, dicCode = "project_level")
    @ApiModelProperty(value = "项目级别")
    @Dict(dicCode = "project_level")
    private String projectLevel;

    /**
     * 项目描述
     */
    @Excel(name = "项目描述", width = 30)
    @ApiModelProperty(value = "项目描述")
    private String projectDesc;

    /**
     * 项目经理ID
     */
    @ApiModelProperty(value = "项目经理ID")
    private String managerId;

    /**
     * 项目经理名称
     */
    @Excel(name = "项目经理", width = 15)
    @ApiModelProperty(value = "项目经理名称")
    private String managerName;

    /**
     * 同步时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "同步时间")
    private Date syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @Dict(dictTable = "fs_user", dicText = "name", dicCode = "user_key")
    private String updateBy;
    @TableField(exist = false)
    @ApiModelProperty(value = "更新人")
    private String updateByName;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 50)
    @ApiModelProperty(value = "备注")
    private String remark;
    
    /**
     * 员工名称
     */
    @TableField(exist = false)
    @Excel(name = "员工名称", width = 50)
    @ApiModelProperty(value = "员工名称")
    private String memberNames;
} 