import React, { useState, useEffect, useCallback } from 'react';
import { DatePicker } from '@douyinfe/semi-ui';
import { generateWeekOptions } from './utils';
import { Toast,Table,Banner, Button, Input,TextArea, InputNumber, Typography, Select, Modal, Radio, Notification, Form, Card } from '@douyinfe/semi-ui';
import WorkHourRulesModal from './components/WorkHourRulesModal';
import { IconPlus } from '@douyinfe/semi-icons';
const { Title } = Typography;
import { get, post } from '../../utils/request';
import { getUserInfo } from '../../utils/auth';
import { fmtDate } from '../../utils/date';
import { getWeekRange } from '../../utils/weekUtils';
import './hourRecordForm.css'; // 导入自定义样式表


import { WorkDetail } from './types';

// 工时登记表单属性接口
export interface HourRecordFormProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (data: any) => void;
  onSubmit: (data: any) => void;
  initialData?: {
    workDate?: Date;
    worker?: string;
    workDetails?: WorkDetail[];
    workContent?: (string | number)[];
    projectKey?: string;
    recordId?: string | number; // 添加记录ID，用于编辑时标识记录
    projectName?: string;
    instanceName?: string;
    nodeName?: string;
    taskName?: string;
  };
  isEdit?: boolean; // 标识当前是否为编辑模式
}

// mock 工时明细数据
const initialWorkDetails: WorkDetail[] = [
  {
    key: '',
    type: '',
    typeInfo: '',
    businessLevel1: '',
    businessLevel2: '',
    status: '正常',
    hours: 0,
    description: '',
    workItemId: '',
    workItemName: '',
    instanceId: '',
    instanceName: '',
    nodeId: '',
    nodeName: '',
    taskId: '',
    taskName: '',
    children: []
  }
];

// 更新接口返回数据的类型定义
interface AvailableDateVO {
  date: string;
  week: string;
  attendanceHours: number;
  isWorkday: boolean;
}

// 添加 WeekRange 接口定义
interface WeekRange {
  start: Date;
  end: Date;
}

// 公共方法：合并 tableData，仅保留新 dynamicColumns 字段和静态字段
function mergeTableDataWithDynamicColumns(newTableData: any[], prevTableData: any[], dynamicColumns: any[], allProjects: any[], extraProjects: any[] = []) {
  const prevMap = new Map((prevTableData || []).map(item => [item.key, item]));
  const newMap = new Map((newTableData || []).map(item => [item.key, item]));
  const dynamicKeys = dynamicColumns.map(col => col.dataIndex);
  // 合并 allProjects 和 extraProjects（去重）
  const allMap = new Map();
  (allProjects || []).forEach(p => allMap.set(p.key, p));
  (extraProjects || []).forEach(p => {
    if (!allMap.has(p.key)) allMap.set(p.key, p);
  });
  const mergedProjects = Array.from(allMap.values());
  const result = mergedProjects.map(project => {
    const key = project.key;
    const newItem = newMap.get(key) || {};
    const oldItem = prevMap.get(key) || {};
    const staticFields = { key, workItemName: project.workItemName, description: newItem.description || oldItem.description || '' };
    const dynamicFields: Record<string, any> = {};
    for (const k of dynamicKeys) {
      dynamicFields[k] = newItem[k] !== undefined ? newItem[k] : (oldItem[k] !== undefined ? oldItem[k] : undefined);
    }
    return { ...staticFields, ...dynamicFields };
  });
  return result;
}

const HourRecordForm: React.FC<HourRecordFormProps> = ({
  visible,
  onCancel,
  onSave = () => { },
  onSubmit = () => { },
  initialData = {},
  isEdit = false
}) => {
  // 获取当前登录用户
  const currentUser = getUserInfo();

  // 状态定义
  const [workDate, setWorkDate] = useState<Date>(initialData.workDate || new Date());
  const [worker, setWorker] = useState<string>(initialData.worker || (currentUser?.name || ''));
  const [workDetails, setWorkDetails] = useState<WorkDetail[]>(
    initialData.workDetails || initialWorkDetails
  );
  const [recordId] = useState<string | number | undefined>(initialData.recordId); // 记录ID，用于编辑时
  // 工时登记规则说明模态窗状态
  const [rulesModalVisible, setRulesModalVisible] = useState(false);
  
  // 从BatchRecordForm迁移的状态
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [submitting, setSubmitting] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);
  const [workHourPerDay, setWorkHourPerDay] = useState<number>(8);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [projectOptions, setProjectOptions] = useState<any[]>([]);
  const [selectedProjectIds, setSelectedProjectIds] = useState<string[]>([]);
  // 备注内容状态
  const [remark, setRemark] = useState('');

  



  // 从BatchRecordForm迁移的方法
  const calculateNetHours = (item: any) => {
    const totalHours = item.hours || 0;
    const childrenHours = sumChildrenHours(item);
    return Math.max(0, totalHours - childrenHours);
  };

  const sumChildrenHours = (item: any) => {
    if (!item.children || item.children.length === 0) return 0;
    return item.children.reduce((sum: number, child: any) => sum + (child.hours || 0), 0);
  };

  const validateBatchRecords = async (records: any[]) => {
    try {
      // 将数组转换为 Map 格式
      const recordsMap = records.reduce((acc, record, index) => {
        acc[index.toString()] = record;
        return acc;
      }, {} as Record<string, any>);

      const response = await post<any>('/hours/hourRecord/validate', recordsMap, { skipErrorHandler: true });
      if (!response.success) {
        Toast.error(response.message || '批量工时记录校验失败，请检查输入');
        return false;
      }
      return true;
    } catch (error: any) {
      console.error('校验批量工时记录失败:', error);
      // 将错误消息改为通用提示，以区分来源
      const errorMessage = '批量工时校验请求发生网络或未知错误，请稍后重试';
      Toast.error(errorMessage);
      return false;
    }
  };

  

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    //loadTableData(currentWeek.start);
  };

  // 处理每页显示数量变化
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // 重置为第一页
    //loadTableData(currentWeek.start);
  };

  // 加载表格数据
  // const loadTableData = async (date: Date) => {
  //   setLoading(true);
  //   try {
  //     const response = await get('/hours/hourRecord/list', {
  //       workDate: fmtDate(date),
  //       page: currentPage,
  //       size: pageSize
  //     });
      
  //     if (response.success) {
  //       setTableData(response.result.records);
  //       setTotal(response.result.total);
  //     }
  //   } catch (error) {
  //     console.error('加载表格数据失败:', error);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const submitBatchRecords = async (records: any[]) => {
    if (submitting) return false;
    
    setSubmitting(true);
    try {
      // 转换为净工时记录
      const netHoursRecords = records.map(record => ({
        ...record,
        hours: calculateNetHours(record)
      })).filter(record => record.hours > 0);

      if (netHoursRecords.length === 0) {
        Toast.warning('没有有效的工时记录需要提交');
        return false;
      }

      // 先校验
      const isValid = await validateBatchRecords(netHoursRecords);
      if (!isValid) {
        return false;
      }

      // 提交记录
      const response = await post<any>('/hours/hourRecord/batchSave', netHoursRecords, { skipErrorHandler: true });
      if (response.success) {
        Toast.info('批量工时记录已成功提交');
        return true;
      } else {
        Toast.warning(response.message || '批量工时记录提交失败');
        return false;
      }
    } catch (error) {
      console.error('提交批量工时记录失败:', error);
      Toast.error('批量工时记录提交失败，请稍后重试');
      return false;
    } finally {
      setSubmitting(false);
    }
  };

  // 组装 HourRecordBatchSaveVO 参数
  function buildHourRecordBatchSaveVO(currentMonth: Date, currentWeek: WeekRange, tableData: any[], dynamicColumns: any[], remark: string) {
    const workMonth = `${currentMonth.getFullYear()}-${(currentMonth.getMonth() + 1).toString().padStart(2, '0')}`;
    const startDate = fmtDate(currentWeek.start);
    const endDate = fmtDate(currentWeek.end);
    const dateKeys = dynamicColumns.map((col: any) => col.dataIndex);
    const projects = tableData.map(row => ({
      projectId: row.key,
      projectName: row.workItemName,
      description: row.description || '',
      details: dateKeys.map(date => ({
        date,
        percentage: typeof row[date] === 'number' ? row[date] : null
      }))
    }));
    return {
      workMonth,
      startDate,
      endDate,
      projects,
      description: remark
    };
  }

  // 校验所有日期的工时百分比总和（保存：≤100%，提交：=100%）
  function validatePercentagesForSave(tableData: any[], dynamicColumns: any[]): string | null {
    if (!dynamicColumns.length) return null;
    const dateKeys = dynamicColumns.map(col => col.dataIndex);
    for (const date of dateKeys) {
      let sum = 0;
      let hasInput = false;
      for (const row of tableData) {
        if (typeof row[date] === 'number') {
          sum += row[date];
          hasInput = true;
        }
      }
      if (hasInput && sum > 100) {
        return `日期 ${date} 的工时百分比总和为${sum}，不能大于100`;
      }
    }
    return null;
  }
  function validatePercentagesForSubmit(tableData: any[], dynamicColumns: any[]): string | null {
    if (!dynamicColumns.length) return null;
    const dateKeys = dynamicColumns.map(col => col.dataIndex);
    for (const date of dateKeys) {
      let sum = 0;
      let hasInput = false;
      for (const row of tableData) {
        if (typeof row[date] === 'number') {
          sum += row[date];
          hasInput = true;
        }
      }
      if (hasInput && sum !== 100) {
        return `日期 ${date} 的工时百分比总和为${sum}，必须等于100`;
      }
    }
    return null;
  }

  // 校验是否有任何工时百分比被填写
  function hasAnyWorkHourData(tableData: any[], dynamicColumns: any[]): boolean {
    if (!dynamicColumns.length) return false;
    const dateKeys = dynamicColumns.map(col => col.dataIndex);
    for (const row of tableData) {
      for (const date of dateKeys) {
        if (typeof row[date] === 'number' && !isNaN(row[date])) {
          return true;
        }
      }
    }
    return false;
  }

  // 处理保存（标记为待提交）
  const handleSave = async () => {
    if (!hasAnyWorkHourData(tableData, dynamicColumns)) {
      Toast.warning('请先填写工时数据');
      return;
    }
    const errMsg = validatePercentagesForSave(tableData, dynamicColumns);
    if (errMsg) {
      Toast.error(errMsg);
      return;
    }
    const params = buildHourRecordBatchSaveVO(currentMonth, currentWeek, tableData, dynamicColumns, remark);
    setSubmitting(true);
    try {
      const response: any = await post('/hours/hourRecord/batchSave', params, { skipErrorHandler: true });
      if (response.success) {
        Toast.success('保存成功');
        onSave && onSave({});
      } else {
        Toast.error( response.message || response.result || '保存失败' );
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 处理提交（标记为审核中）
  const handleSubmit = async () => {
    if (!hasAnyWorkHourData(tableData, dynamicColumns)) {
      Toast.warning('请先填写工时数据');
      return;
    }
    const errMsg = validatePercentagesForSubmit(tableData, dynamicColumns);
    if (errMsg) {
      Toast.error(errMsg);
      return;
    }
    const params = buildHourRecordBatchSaveVO(currentMonth, currentWeek, tableData, dynamicColumns, remark);
    setSubmitting(true);
    try {
      const response: any = await post('/hours/hourRecord/batchSaveSubmit', params, { skipErrorHandler: true });
      if (response.success) {
        Toast.success('提交成功' );
        onSubmit && onSubmit({});
      } else {
        Toast.error(response.message || response.result || '提交失败' );
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 处理工作人员选择变化
  const handleWorkerChange = (value: string) => {
    setWorker(value);
  };

  // 记录编辑模式的初始化情况
  useEffect(() => {
    if (isEdit && initialData) {
      console.log('编辑模式初始化数据:', {
        recordId: initialData.recordId,
        workContent: initialData.workContent,
        projectName: initialData.projectName,
        instanceName: initialData.instanceName,
        nodeName: initialData.nodeName,
        taskName: initialData.taskName
      });
    }
  }, [isEdit, initialData]);

  // 在组件显示时重置表单
  useEffect(() => {
    if (visible) {
      // 如果不是编辑模式，重置所有表单数据
      if (!isEdit) {
        setWorkDate(new Date());
        setWorkDetails([{
          key: Date.now(),
          type: '',
          typeInfo: '',
          businessLevel1: '',
          businessLevel2: '',
          status: '正常',
          hours: 0,
          description: '',
          workItemId: '',
          workItemName: '',
          instanceId: '',
          instanceName: '',
          nodeId: '',
          nodeName: '',
          taskId: '',
          taskName: ''
        }]);
        // 设置当前登录用户
        const user = getUserInfo();
        setWorker(user?.name || '');
      }
    }
  }, [visible, isEdit]);

  // 状态定义
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [currentWeek, setCurrentWeek] = useState<WeekRange>(getWeekRange(new Date()));
  const [weeksData, setWeeksData] = useState<WeekRange[]>([]);
  const [dynamicColumns, setDynamicColumns] = useState<any[]>([]);
  const [allProjects, setAllProjects] = useState<any[]>([]);
  const [pendingTableData, setPendingTableData] = useState<any[] | null>(null);

  // 初始化加载数据
  useEffect(() => {
    // 初始化当前月份和周期
    const today = new Date();
    setCurrentMonth(today);
    
    // 初始化加载当前月份的周期数据
    const monthStr = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}`;
    loadWorkPeriods(monthStr);
    
  }, []);

    // 组件加载时输出初始化信息
    useEffect(() => {
      // 调用用户项目列表接口
      loadUserProjects();
      
    console.log('======= 组件初始化信息 =======');
    console.log('isEdit:', isEdit);
    console.log('initialData.recordId:', initialData.recordId);
    console.log('recordId 状态:', recordId);
    console.log('===============================');
    }, [visible]);

  // 监听 dynamicColumns、pendingTableData、allProjects，合并tableData
  useEffect(() => {
    if (pendingTableData && dynamicColumns.length > 0 && allProjects.length > 0) {
      // 取 allProjects 和 pendingTableData 并集，去重
      const extraProjects = pendingTableData.filter(
        d => !allProjects.some(p => p.key === d.key)
      ).map(d => ({ key: d.key, workItemName: d.workItemName }));
      setTableData(prev => mergeTableDataWithDynamicColumns(pendingTableData, prev, dynamicColumns, allProjects, extraProjects));
      setPendingTableData(null);
    }
  }, [pendingTableData, dynamicColumns, allProjects]);

  // 加载用户项目列表
  const loadUserProjects = () => {
    get('/plm/project/userProjectList')
      .then((response: any) => {
        if (response.success) {
          const projectData = response.result.map((project: any) => ({
            key: project.id,
            workItemName: project.projectName
          }));
          setAllProjects(projectData);
          setPendingTableData(projectData); // 只暂存，合并后赋值
        }
      })
      .catch(error => {
        console.error('获取用户项目列表失败:', error);
      });
  };

  // 加载周期数据
  const loadWorkPeriods = (monthStr: string) => {
    get('/hours/hourRecord/workPeriods', { month: monthStr })
      .then((response: any) => {
        if (response.success && response.result && response.result.length > 0) {
          const weeks = response.result.map((item: any) => ({
            start: new Date(item.startDate.replace(/\./g, '-')),
            end: new Date(item.endDate.replace(/\./g, '-'))
          }));
          setWeeksData(weeks);
          
          // 设置默认选中第一个周期
          if (weeks.length > 0) {
            setCurrentWeek(weeks[0]);
            setWorkDate(weeks[0].start);
            
            // 加载该周期的可用日期
            loadAvailableDates(monthStr, fmtDate(weeks[0].start), fmtDate(weeks[0].end));
          }
        }
      })
      .catch(error => {
        console.error('获取可登记日期失败:', error);
      });
  };

  // 加载可用日期数据
  const loadAvailableDates = (monthStr: string, startDateStr: string, endDateStr: string) => {
    setLoading(true);
    
    get('/hours/hourRecord/availableDates', {
      month: monthStr,
      startDate: startDateStr,
      endDate: endDateStr
    })
      .then((response: any) => {
        if (response.success && response.result) {
          const availableDates: AvailableDateVO[] = response.result;
          
          // 生成动态列
          const dynamicColumns: any[] = [];
          
          availableDates.forEach(dateInfo => {
            dynamicColumns.push({
              title: (
                <div>
                  <div>{dateInfo.week}</div>
                  <div>{dateInfo.date}</div>
                </div>
              ),
              dataIndex: dateInfo.date,
              key: dateInfo.date,
              width: 100,
              render: (text: any, record: any) => (
                <div className="hour-record-form-input-number">
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    {dateInfo.isWorkday && dateInfo.attendanceHours > 0 ? (
                      <>
                        <InputNumber 
                          min={0} 
                          max={100} 
                          precision={0}
                          value={record[dateInfo.date]}
                          onChange={(value) => handlePercentageChange(record.key, dateInfo.date, value as number)}
                          style={{ width: '80px' }}
                        />
                        <span style={{ marginLeft: '4px' }}>%</span>
                      </>
                    ) : (
                      <span style={{ textAlign: 'center', width: '100%' }}>--</span>
                    )}
                  </div>
                </div>
              )
            });
          });
          
          setDynamicColumns(dynamicColumns);
          
          // 保留现有workItemName数据或初始化
          const newTableData = tableData.length > 0 
            ? tableData.map(item => ({
                workItemName: item.workItemName,
                key: item.key
              }))
            : [{ workItemName: '', key: Date.now() }];
          
          setTableData(newTableData);
        } else {
          Toast.warning(response.message || '请求失败');
        }
        setLoading(false);
      })
      .catch(error => {
        console.error('获取可用日期失败:', error);
        Toast.error('获取可用日期失败');
        setLoading(false);
      });
  };

  // 处理月份变化
  const handleMonthChange = (date: Date) => {
    console.log('handleMonthChange::date=', date);
    setCurrentMonth(date);

    // 调用获取可登记日期接口
    const monthStr = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
    loadWorkPeriods(monthStr);
  };

  // 在handleWeekChange方法中修改动态列生成逻辑
  const handleWeekChange = (week: { start: Date, end: Date }) => {
    setCurrentWeek(week);
    setWorkDate(week.start);
    
    // 使用月份表单控件的值作为month参数
    const monthStr = `${currentMonth.getFullYear()}-${(currentMonth.getMonth() + 1).toString().padStart(2, '0')}`;
    
    // 格式化日期参数
    const startDateStr = fmtDate(week.start);
    const endDateStr = fmtDate(week.end);
    
    // 加载该周期的可用日期
    loadAvailableDates(monthStr, startDateStr, endDateStr);
  };

  // 添加处理百分比变化的回调
  const handlePercentageChange = (rowKey: string, date: string, value: number) => {
    setTableData(prev =>
      prev.map(row =>
        row.key === rowKey
          ? { ...row, [date]: value }
          : row
      )
    );
  };

  // 新增按钮事件
  const openAddModal = () => {
    setSelectedProjectIds([]); // 打开前重置
    const excludeIds = allProjects.map(item => item.key).join(',');
    get(`/plm/project/availableForAdd?excludeProjectIds=${excludeIds}`)
      .then((res: any) => {
        if (res.success) setProjectOptions(res.result);
        setAddModalVisible(true);
      })
      .catch(() => {
        Toast.error('获取可添加项目失败');
      });
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Title heading={6} style={{ margin: 0 }}>{isEdit ? '编辑工时' : "工时登记"}</Title>
        </div>
      }
      visible={visible}
      onCancel={onCancel}
      width={800}
      height="auto"
      maskClosable={false}
      className="hour-record-modal"
      bodyStyle={{
        padding: 0,
        position: 'relative',
        overflowY: 'auto', // 只允许纵向滚动
        height: 'auto',
        maxHeight: '80vh'
      }}
      footer={
        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
          <Button type="tertiary" onClick={onCancel} disabled={submitting}>取消</Button>
          <Button type="secondary" onClick={handleSave} loading={submitting} disabled={submitting}>保存</Button>
          <Button type="primary" onClick={handleSubmit} loading={submitting} disabled={submitting}>提交</Button>
        </div>
      }
    >
      <div className="hour-record-container">

        {/* 工时规则提示 */}
        <div className="hour-record-info">
          <div style={{ display: 'flex', gap: '16px', marginBottom: '12px' }}>
            <div>
              <Form.Label style={{fontWeight:'normal'}}>所属月份</Form.Label><br/>
              <DatePicker
                defaultValue={new Date()}
                type="month"
                style={{width:'280px'}}
                value={currentMonth}
                onChange={(date: Date) => handleMonthChange(date)}
                disabledDate={(date: Date) => {
                  // 当前月以后的日期不可选择
                  const now = new Date();
                  const currentYear = now.getFullYear();
                  const currentMonth = now.getMonth();
                  return date.getFullYear() > currentYear || 
                         (date.getFullYear() === currentYear && date.getMonth() > currentMonth);
                }}
              />
            </div>
            <div>
              <Form.Label style={{fontWeight:'normal'}}>填写周期</Form.Label><br/>
              <Select
                style={{width:'280px'}}
                value={`${fmtDate(currentWeek.start)} 至 ${fmtDate(currentWeek.end)}`}
                onChange={(value) => {
                  const selectedWeek = weeksData
                    .find(w => fmtDate(w.start) === value);
                  if (selectedWeek) {
                    handleWeekChange(selectedWeek);
                    
                    //loadTableData(selectedWeek.start);
                  }
                }}
              >
                {weeksData.map(week => (
                  <Select.Option
                    key={`${fmtDate(week.start)}-${fmtDate(week.end)}`}
                    value={fmtDate(week.start)}
                  >
                    {`${fmtDate(week.start)} 至 ${fmtDate(week.end)}`}
                  </Select.Option>
                ))}
              </Select>
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '16px', marginBottom: '12px' }}>
            {/* 工作人员 */}
            <div>
              <Form.Label style={{fontWeight:'normal'}}>工作人员</Form.Label><br/>
              <Input disabled={true} defaultValue={currentUser?.name || ''} style={{width:'280px'}} />
            </div>
          </div>
          
          <Banner fullMode={false} type="info" bordered icon={null} closeIcon={null}
            title={<div style={{ fontWeight: 600, fontSize: '14px', lineHeight: '20px', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span>
                对于可登记工时有疑问可联系管理员处理
              </span>
              <Button
                type="tertiary"
                theme="borderless"
                size="small"
                style={{
                  padding: '0 4px',
                  height: 'auto',
                  fontSize: '12px',
                  color: 'var(--semi-color-primary)',
                  textDecoration: 'underline'
                }}
                onClick={() => setRulesModalVisible(true)}
              >
                工时登记规则
              </Button>
            </div>}
          />
        </div>

        {/* 表单内容区域 - 不可滚动，只有表格可滚动 */}
        <div className="hour-record-content">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <Form.Label style={{fontWeight:'normal', margin: 0}}>工时登记明细</Form.Label>
            <Button type="primary" icon={<IconPlus />} onClick={openAddModal}>新增</Button>
          </div>
          <Table
            columns={[
              {
                title: '工作内容',
                dataIndex: 'workItemName',
                key: 'workItemName',
                width: 200,
                fixed: 'left'
              },
              ...dynamicColumns
            ]}
            dataSource={tableData}
            loading={loading}
            scroll={{ x: 'max-content', y: 400 }}
            rowKey="key"
          />
          {/* 备注 */}
          <div style={{ width: '100%' }}>
            <Form.Label style={{fontWeight:'normal'}}>备注</Form.Label>
            <TextArea defaultValue={''} value={remark} onChange={setRemark} />
          </div>

        </div>

        <div style={{ display: 'flex', gap: '16px', marginBottom: '12px' }}>
          
        </div>
      </div>

      {/* 底部按钮 - 固定在底部 */}
      {/* <div className="hour-record-form-footer-fixed"> ... </div> 移除该div */}

      {/* 工时登记规则说明模态窗 */}
      <WorkHourRulesModal
        visible={rulesModalVisible}
        onClose={() => setRulesModalVisible(false)}
      />

      {/* 新增项目模态窗 */}
      <Modal
        title="选择项目"
        visible={addModalVisible}
        onCancel={() => {
          setAddModalVisible(false);
          setSelectedProjectIds([]);
        }}
        onOk={() => {
          const dateKeys = dynamicColumns.map((col: any) => col.dataIndex);
          const newRows = projectOptions
            .filter(p => selectedProjectIds.includes(p.id))
            .map(p => {
              const row: any = { key: p.id, workItemName: p.projectName };
              dateKeys.forEach(date => { row[date] = undefined; });
              return row;
            });
          setPendingTableData([...(pendingTableData || []), ...newRows]); // 触发合并
          setAllProjects(prev => [...prev, ...newRows]);
          setAddModalVisible(false);
          setSelectedProjectIds([]);
        }}
        okText="确定"
        cancelText="取消"
      >
        <Select
          multiple
          style={{ width: '100%' }}
          placeholder="请选择要添加的项目"
          value={selectedProjectIds}
          onChange={setSelectedProjectIds}
        >
          {projectOptions.map(p => (
            <Select.Option key={p.id} value={p.id}>{p.projectName}</Select.Option>
          ))}
        </Select>
      </Modal>
    </Modal>
  );
};

export default HourRecordForm;