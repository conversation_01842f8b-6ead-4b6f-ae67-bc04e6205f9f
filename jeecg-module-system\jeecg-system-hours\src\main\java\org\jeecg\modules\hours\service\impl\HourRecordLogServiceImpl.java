package org.jeecg.modules.hours.service.impl;

import java.util.Date;

import org.springframework.stereotype.Service;
import org.jeecg.modules.hours.entity.HourRecord;
import org.jeecg.modules.hours.entity.HourRecordLog;
import org.jeecg.modules.hours.mapper.HourRecordLogMapper;
import org.jeecg.modules.hours.service.IHourRecordLogService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 工时记录日志表
 * @Author: jeecg-boot
 * @Date: 2023-07-01
 * @Version: V1.0
 */
@Service
public class HourRecordLogServiceImpl extends ServiceImpl<HourRecordLogMapper, HourRecordLog> implements IHourRecordLogService {

    @Override
    public void logCreate(HourRecord record, String operatorId, String operatorName) {
        HourRecordLog log = new HourRecordLog();
        log.setRecordId(Long.valueOf(record.getId()));
        log.setOperationType(1); // 创建
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setOperationTime(new Date());
        log.setNewContent(JSON.toJSONString(record));
        save(log);
    }

    @Override
    public void logUpdate(HourRecord oldRecord, HourRecord newRecord, String operatorId, String operatorName) {
        HourRecordLog log = new HourRecordLog();
        log.setRecordId(Long.valueOf(newRecord.getId()));
        log.setOperationType(2); // 修改
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setOperationTime(new Date());
        log.setOldContent(JSON.toJSONString(oldRecord));
        log.setNewContent(JSON.toJSONString(newRecord));
        save(log);
    }

    @Override
    public void logDelete(HourRecord record, String operatorId, String operatorName) {
        HourRecordLog log = new HourRecordLog();
        log.setRecordId(Long.valueOf(record.getId()));
        log.setOperationType(3); // 删除
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setOperationTime(new Date());
        log.setOldContent(JSON.toJSONString(record));
        save(log);
    }

    @Override
    public void logSubmit(HourRecord record, String operatorId, String operatorName) {
        HourRecordLog log = new HourRecordLog();
        log.setRecordId(Long.valueOf(record.getId()));
        log.setOperationType(4); // 提交
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setOperationTime(new Date());
        log.setNewContent(JSON.toJSONString(record));
        save(log);
    }

    @Override
    public void logApprove(HourRecord record, boolean approved, String operatorId, String operatorName, String remarks) {
        HourRecordLog log = new HourRecordLog();
        log.setRecordId(Long.valueOf(record.getId()));
        log.setOperationType(approved ? 5 : 6); // 5-审批通过 6-审批驳回
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setOperationTime(new Date());
        log.setNewContent(JSON.toJSONString(record));
        log.setRemark(remarks);
        save(log);
    }
} 