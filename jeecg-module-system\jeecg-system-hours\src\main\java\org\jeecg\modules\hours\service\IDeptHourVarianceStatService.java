package org.jeecg.modules.hours.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.hours.entity.DeptHourVarianceStat;
import org.jeecg.modules.hours.vo.DeptHourVarianceReportVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IDeptHourVarianceStatService extends IService<DeptHourVarianceStat> {

    /**
     * 计算指定年月的部门工时差值统计
     * @param year 年份
     * @param month 月份
     * @return 处理的记录数
     */
    int calculateMonthlyVarianceStat(int year, int month);

    /**
     * 查询部门工时差值报表数据
     * @param year 年份
     * @param deptIds 部门ID列表（可选）
     * @param includeHoliday 是否包含假日
     * @return 报表数据
     */
    List<DeptHourVarianceReportVO> getVarianceReport(int year, List<String> deptIds, boolean includeHoliday);

    /**
     * 导出部门工时差值报表
     * @param year 年份
     * @param deptIds 部门ID列表（可选）
     * @param includeHoliday 是否包含假日
     * @param response HTTP响应对象
     */
    void exportVarianceReport(int year, List<String> deptIds, boolean includeHoliday, HttpServletResponse response);
}