import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ReminderSettings from '../ReminderSettings';
import * as reminderConfigApi from '../../../services/api/reminderConfig';

// Mock API functions
jest.mock('../../../services/api/reminderConfig', () => ({
  getReminderConfigList: jest.fn(),
  addReminderConfig: jest.fn(),
  updateReminderConfig: jest.fn(),
  deleteReminderConfig: jest.fn(),
  batchDeleteReminderConfig: jest.fn(),
  toggleReminderConfigStatus: jest.fn(),
  getReminderTypeOptions: jest.fn(() => [
    { label: '每天', value: 'daily' },
    { label: '每周', value: 'weekly' },
    { label: '每月', value: 'monthly' }
  ]),
  getRepeatModeOptions: jest.fn((type) => {
    const options = {
      daily: [
        { label: '当天', value: 'current_day' },
        { label: '次日', value: 'next_day' }
      ],
      weekly: [
        { label: '本周', value: 'current_week' },
        { label: '次周', value: 'next_week' }
      ],
      monthly: [
        { label: '本月', value: 'current_month' },
        { label: '次月', value: 'next_month' }
      ]
    };
    return options[type] || [];
  }),
  getReminderDayOptions: jest.fn(() => []),
  validateReminderConfig: jest.fn(() => []),
  formatReminderConfigText: jest.fn(() => '每天提醒，当天 09:00')
}));

const mockReminderList = [
  {
    id: '1',
    projectKey: 'test-project',
    reminderType: 'daily',
    repeatMode: 'current_day',
    reminderTime: '09:00',
    status: 'enabled',
    remark: '测试提醒',
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: '2',
    projectKey: 'test-project',
    reminderType: 'weekly',
    repeatMode: 'current_week',
    reminderDay: 'monday',
    reminderTime: '10:00',
    status: 'disabled',
    remark: '周提醒',
    createTime: '2024-01-02 10:00:00'
  }
];

describe('ReminderSettings', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (reminderConfigApi.getReminderConfigList as jest.Mock).mockResolvedValue({
      success: true,
      result: {
        list: mockReminderList,
        total: 2
      }
    });
  });

  test('renders reminder settings component', async () => {
    render(<ReminderSettings visible={true} />);
    
    expect(screen.getByText('新增提醒')).toBeInTheDocument();
    expect(screen.getByText('批量删除')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(reminderConfigApi.getReminderConfigList).toHaveBeenCalled();
    });
  });

  test('opens add modal when clicking add button', async () => {
    render(<ReminderSettings visible={true} />);
    
    const addButton = screen.getByText('新增提醒');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(screen.getByText('新增提醒配置')).toBeInTheDocument();
    });
  });

  test('loads reminder list on mount', async () => {
    render(<ReminderSettings visible={true} />);
    
    await waitFor(() => {
      expect(reminderConfigApi.getReminderConfigList).toHaveBeenCalledWith({
        page: 1,
        pageSize: 10
      });
    });
  });

  test('handles form validation', async () => {
    const mockValidate = reminderConfigApi.validateReminderConfig as jest.Mock;
    mockValidate.mockReturnValue(['请选择提醒类型']);
    
    render(<ReminderSettings visible={true} />);
    
    const addButton = screen.getByText('新增提醒');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      const saveButton = screen.getByText('保存');
      fireEvent.click(saveButton);
    });
    
    expect(mockValidate).toHaveBeenCalled();
  });

  test('handles delete operation', async () => {
    (reminderConfigApi.deleteReminderConfig as jest.Mock).mockResolvedValue({
      success: true
    });
    
    render(<ReminderSettings visible={true} />);
    
    await waitFor(() => {
      const deleteButtons = screen.getAllByText('删除');
      if (deleteButtons.length > 0) {
        fireEvent.click(deleteButtons[0]);
      }
    });
  });

  test('handles status toggle', async () => {
    (reminderConfigApi.toggleReminderConfigStatus as jest.Mock).mockResolvedValue({
      success: true
    });
    
    render(<ReminderSettings visible={true} />);
    
    await waitFor(() => {
      const switches = screen.getAllByRole('switch');
      if (switches.length > 0) {
        fireEvent.click(switches[0]);
      }
    });
  });

  test('does not render when visible is false', () => {
    render(<ReminderSettings visible={false} />);
    
    expect(screen.queryByText('新增提醒')).not.toBeInTheDocument();
  });

  test('handles API errors gracefully', async () => {
    (reminderConfigApi.getReminderConfigList as jest.Mock).mockRejectedValue(
      new Error('API Error')
    );
    
    render(<ReminderSettings visible={true} />);
    
    await waitFor(() => {
      expect(reminderConfigApi.getReminderConfigList).toHaveBeenCalled();
    });
  });

  test('handles batch delete operation', async () => {
    (reminderConfigApi.batchDeleteReminderConfig as jest.Mock).mockResolvedValue({
      success: true
    });
    
    render(<ReminderSettings visible={true} />);
    
    await waitFor(() => {
      // 模拟选择行
      const checkboxes = screen.getAllByRole('checkbox');
      if (checkboxes.length > 0) {
        fireEvent.click(checkboxes[0]);
      }
    });
    
    const batchDeleteButton = screen.getByText('批量删除');
    fireEvent.click(batchDeleteButton);
  });

  test('handles pagination', async () => {
    render(<ReminderSettings visible={true} />);
    
    await waitFor(() => {
      expect(reminderConfigApi.getReminderConfigList).toHaveBeenCalledWith({
        page: 1,
        pageSize: 10
      });
    });
  });
});

// 测试API服务层
describe('ReminderConfig API', () => {
  test('validates reminder config correctly', () => {
    const validConfig = {
      reminderType: 'daily' as const,
      repeatMode: 'current_day' as const,
      reminderTime: '09:00',
      status: 'enabled' as const,
      remark: ''
    };
    
    const errors = reminderConfigApi.validateReminderConfig(validConfig);
    expect(errors).toEqual([]);
  });

  test('validates invalid time format', () => {
    const invalidConfig = {
      reminderType: 'daily' as const,
      repeatMode: 'current_day' as const,
      reminderTime: '25:00', // 无效时间
      status: 'enabled' as const,
      remark: ''
    };
    
    const mockValidate = jest.requireActual('../../../services/api/reminderConfig').validateReminderConfig;
    const errors = mockValidate(invalidConfig);
    expect(errors.length).toBeGreaterThan(0);
  });

  test('formats reminder config text correctly', () => {
    const config = {
      id: '1',
      projectKey: 'test',
      reminderType: 'daily' as const,
      repeatMode: 'current_day' as const,
      reminderTime: '09:00',
      status: 'enabled' as const
    };
    
    const mockFormat = jest.requireActual('../../../services/api/reminderConfig').formatReminderConfigText;
    const text = mockFormat(config);
    expect(text).toContain('每天提醒');
    expect(text).toContain('09:00');
  });
});
