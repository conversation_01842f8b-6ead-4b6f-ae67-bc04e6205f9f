package org.jeecg.modules.hours.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.hours.entity.SysUserAttendanceStat;
import org.jeecg.modules.hours.vo.SysUserAttendanceStatVo;

import java.util.List;
import java.util.Date;
import java.math.BigDecimal;

/**
 * @Description: 用户考勤统计服务接口
 * @Author: jeecg-boot
 * @Date: 2023-07-01
 * @Version: V1.0
 */
public interface ISysUserAttendanceStatService extends IService<SysUserAttendanceStat> {
    /**
     * 批量保存或更新考勤统计数据
     * @param stats 考勤统计数据列表
     * @return 是否成功
     */
    boolean saveOrUpdateBatch(List<SysUserAttendanceStat> stats);

    /**
     * 根据用户UnionID和日期查询考勤统计
     *
     * @param unionId 用户UnionID
     * @param statDate 统计日期
     * @return 考勤统计信息
     */
    SysUserAttendanceStat getByUserAndDate(String unionId, Date statDate);
    
    List<SysUserAttendanceStat> getByUserDate(String userId,String startDate,String endDate);
    /**
     * 工作台-待登记工时查询
     * */
    IPage<SysUserAttendanceStatVo> getPendingRegisteredHoursList(Page<SysUserAttendanceStatVo> page, SysUserAttendanceStatVo sysUserAttendanceStatVo);
}