package org.jeecg.modules.hours.vo;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * 部门工时差值导出VO
 */
@Data
public class DeptVarianceExportVO {

    @Excel(name = "部门", width = 20, orderNum = "1")
    private String deptName;

    // 1月
    @Excel(name = "1月份实际工时", width = 15, orderNum = "2")
    private BigDecimal month1Actual;

    @Excel(name = "1月份可登记工时", width = 15, orderNum = "3")
    private BigDecimal month1Required;

    @Excel(name = "1月份差值", width = 12, orderNum = "4")
    private BigDecimal month1Variance;

    // 2月
    @Excel(name = "2月份实际工时", width = 15, orderNum = "5")
    private BigDecimal month2Actual;

    @Excel(name = "2月份可登记工时", width = 15, orderNum = "6")
    private BigDecimal month2Required;

    @Excel(name = "2月份差值", width = 12, orderNum = "7")
    private BigDecimal month2Variance;

    // 3月
    @Excel(name = "3月份实际工时", width = 15, orderNum = "8")
    private BigDecimal month3Actual;

    @Excel(name = "3月份可登记工时", width = 15, orderNum = "9")
    private BigDecimal month3Required;

    @Excel(name = "3月份差值", width = 12, orderNum = "10")
    private BigDecimal month3Variance;

    // 4月
    @Excel(name = "4月份实际工时", width = 15, orderNum = "11")
    private BigDecimal month4Actual;

    @Excel(name = "4月份可登记工时", width = 15, orderNum = "12")
    private BigDecimal month4Required;

    @Excel(name = "4月份差值", width = 12, orderNum = "13")
    private BigDecimal month4Variance;

    // 5月
    @Excel(name = "5月份实际工时", width = 15, orderNum = "14")
    private BigDecimal month5Actual;

    @Excel(name = "5月份可登记工时", width = 15, orderNum = "15")
    private BigDecimal month5Required;

    @Excel(name = "5月份差值", width = 12, orderNum = "16")
    private BigDecimal month5Variance;

    // 6月
    @Excel(name = "6月份实际工时", width = 15, orderNum = "17")
    private BigDecimal month6Actual;

    @Excel(name = "6月份可登记工时", width = 15, orderNum = "18")
    private BigDecimal month6Required;

    @Excel(name = "6月份差值", width = 12, orderNum = "19")
    private BigDecimal month6Variance;

    // 7月
    @Excel(name = "7月份实际工时", width = 15, orderNum = "20")
    private BigDecimal month7Actual;

    @Excel(name = "7月份可登记工时", width = 15, orderNum = "21")
    private BigDecimal month7Required;

    @Excel(name = "7月份差值", width = 12, orderNum = "22")
    private BigDecimal month7Variance;

    // 8月
    @Excel(name = "8月份实际工时", width = 15, orderNum = "23")
    private BigDecimal month8Actual;

    @Excel(name = "8月份可登记工时", width = 15, orderNum = "24")
    private BigDecimal month8Required;

    @Excel(name = "8月份差值", width = 12, orderNum = "25")
    private BigDecimal month8Variance;

    // 9月
    @Excel(name = "9月份实际工时", width = 15, orderNum = "26")
    private BigDecimal month9Actual;

    @Excel(name = "9月份可登记工时", width = 15, orderNum = "27")
    private BigDecimal month9Required;

    @Excel(name = "9月份差值", width = 12, orderNum = "28")
    private BigDecimal month9Variance;

    // 10月
    @Excel(name = "10月份实际工时", width = 15, orderNum = "29")
    private BigDecimal month10Actual;

    @Excel(name = "10月份可登记工时", width = 15, orderNum = "30")
    private BigDecimal month10Required;

    @Excel(name = "10月份差值", width = 12, orderNum = "31")
    private BigDecimal month10Variance;

    // 11月
    @Excel(name = "11月份实际工时", width = 15, orderNum = "32")
    private BigDecimal month11Actual;

    @Excel(name = "11月份可登记工时", width = 15, orderNum = "33")
    private BigDecimal month11Required;

    @Excel(name = "11月份差值", width = 12, orderNum = "34")
    private BigDecimal month11Variance;

    // 12月
    @Excel(name = "12月份实际工时", width = 15, orderNum = "35")
    private BigDecimal month12Actual;

    @Excel(name = "12月份可登记工时", width = 15, orderNum = "36")
    private BigDecimal month12Required;

    @Excel(name = "12月份差值", width = 12, orderNum = "37")
    private BigDecimal month12Variance;

    // 年度总计
    @Excel(name = "年度实际工时总计", width = 15, orderNum = "38")
    private BigDecimal yearTotalActual;

    @Excel(name = "年度可登记工时总计", width = 15, orderNum = "39")
    private BigDecimal yearTotalRequired;

    @Excel(name = "年度差值总计", width = 15, orderNum = "40")
    private BigDecimal yearTotalVariance;
}