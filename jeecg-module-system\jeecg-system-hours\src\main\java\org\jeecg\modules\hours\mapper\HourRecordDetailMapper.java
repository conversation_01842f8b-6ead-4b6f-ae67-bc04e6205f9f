package org.jeecg.modules.hours.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.HourRecordDetail;

import java.util.List;

/**
 * 工时记录明细Mapper接口
 */
@Mapper
public interface HourRecordDetailMapper extends BaseMapper<HourRecordDetail> {

    /**
     * 批量插入工时记录明细
     *
     * @param details 工时记录明细列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<HourRecordDetail> details);
} 