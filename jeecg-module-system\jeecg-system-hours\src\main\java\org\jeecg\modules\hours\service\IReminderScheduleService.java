package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.entity.FsSysReminderConfig;

/**
 * @Description: 提醒定时任务调度服务接口
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface IReminderScheduleService {

    /**
     * 启动提醒定时任务
     * 
     * @param reminderConfig 提醒配置
     * @return 是否成功
     */
    boolean startReminderJob(FsSysReminderConfig reminderConfig);

    /**
     * 停止提醒定时任务
     * 
     * @param reminderConfig 提醒配置
     * @return 是否成功
     */
    boolean stopReminderJob(FsSysReminderConfig reminderConfig);

    /**
     * 更新提醒定时任务
     * 
     * @param reminderConfig 提醒配置
     * @return 是否成功
     */
    boolean updateReminderJob(FsSysReminderConfig reminderConfig);

    /**
     * 检查定时任务是否存在
     * 
     * @param reminderConfig 提醒配置
     * @return 是否存在
     */
    boolean isJobExists(FsSysReminderConfig reminderConfig);

    /**
     * 生成Job名称
     * 
     * @param reminderConfig 提醒配置
     * @return Job名称
     */
    String generateJobName(FsSysReminderConfig reminderConfig);

    /**
     * 生成Job组名
     * 
     * @param reminderConfig 提醒配置
     * @return Job组名
     */
    String generateJobGroup(FsSysReminderConfig reminderConfig);

    /**
     * 根据提醒配置生成Cron表达式
     * 
     * @param reminderConfig 提醒配置
     * @return Cron表达式
     */
    String generateCronExpression(FsSysReminderConfig reminderConfig);
}
