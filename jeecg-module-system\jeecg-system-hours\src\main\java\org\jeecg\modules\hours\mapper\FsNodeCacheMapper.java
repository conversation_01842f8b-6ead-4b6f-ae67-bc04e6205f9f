package org.jeecg.modules.hours.mapper;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.FsNodeCache;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 飞书项目节点缓存表
 * @Author: jeecg-boot
 * @Date: 2023-08-20
 * @Version: V1.0
 */
public interface FsNodeCacheMapper extends BaseMapper<FsNodeCache> {

    /**
     * 汇总节点工时到实例层级
     * @param projectKey 项目标识
     * @param workItemKey 工作项键值
     * @param instanceKey 实例键值
     * @return 影响的行数
     */
    int aggregateNodeEstimateHoursToInstance(@Param("projectKey") String projectKey, 
                                            @Param("workItemKey") String workItemKey, 
                                            @Param("instanceKey") String instanceKey);
    
    /**
     * 汇总节点排期到实例层级
     * @param projectKey 项目标识
     * @param workItemKey 工作项键值
     * @param instanceKey 实例键值
     * @return 影响的行数
     */
    int aggregateNodeScheduleToInstance(@Param("projectKey") String projectKey, 
                                       @Param("workItemKey") String workItemKey, 
                                       @Param("instanceKey") String instanceKey);
    
    /**
     * 一次性汇总节点的工时和排期到实例
     * @param projectKey 项目标识
     * @param workItemKey 工作项键值
     * @param instanceKey 实例键值
     * @return 影响的行数
     */
    int aggregateNodeDataToInstance(@Param("projectKey") String projectKey, 
                                   @Param("workItemKey") String workItemKey, 
                                   @Param("instanceKey") String instanceKey);
} 