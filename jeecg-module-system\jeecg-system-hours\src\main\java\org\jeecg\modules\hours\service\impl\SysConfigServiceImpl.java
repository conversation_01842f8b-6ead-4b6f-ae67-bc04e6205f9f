package org.jeecg.modules.hours.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.hours.entity.SysConfig;
import org.jeecg.modules.hours.mapper.SysConfigMapper;
import org.jeecg.modules.hours.service.ISysConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import java.util.*;

/**
 * 系统配置表 服务实现类
 */
@Service
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements ISysConfigService {

    @Override
    public String getConfigValue(String projectKey, String configKey) {
        return getConfigValue(projectKey, configKey, null);
    }

    @Override
    public String getConfigValue(String projectKey, String configKey, String defaultValue) {
        SysConfig config = baseMapper.getConfigByKey(projectKey, configKey);
        if (config != null && StringUtils.isNotBlank(config.getConfigValue())) {
            return config.getConfigValue();
        }
        return defaultValue;
    }

    @Override
    public List<SysConfig> getConfigsByGroup(String projectKey, String configGroup) {
        return baseMapper.getConfigsByGroup(projectKey, configGroup);
    }

    @Override
    public List<String> getAllGroups() {
        return baseMapper.getAllGroups();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveConfig(SysConfig config) {
        if (StringUtils.isBlank(config.getId())) {
            // 新增
            config.setCreateTime(new Date());
            return save(config);
        } else {
            // 更新
            config.setUpdateTime(new Date());
            return updateById(config);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveConfig(List<SysConfig> configs) {
        if (configs == null || configs.isEmpty()) {
            return false;
        }
        
        Date now = new Date();
        
        // 先查询所有已存在的配置，避免唯一键冲突
        Set<String> projectKeys = configs.stream()
            .map(SysConfig::getProjectKey)
            .collect(Collectors.toSet());
            
        Set<String> configKeys = configs.stream()
            .map(SysConfig::getConfigKey)
            .collect(Collectors.toSet());
            
        List<SysConfig> existingConfigs = baseMapper.selectList(
            new QueryWrapper<SysConfig>()
                .in("project_key", projectKeys)
                .in("config_key", configKeys)
        );
        
        Map<String, SysConfig> existingConfigMap = existingConfigs.stream()
            .collect(Collectors.toMap(
                c -> c.getProjectKey() + "_" + c.getConfigKey(),
                Function.identity()
            ));
        
        // 设置ID和更新时间
        for (SysConfig config : configs) {
            String key = config.getProjectKey() + "_" + config.getConfigKey();
            if (existingConfigMap.containsKey(key)) {
                // 已存在则更新
                config.setId(existingConfigMap.get(key).getId());
                config.setUpdateTime(now);
            } else {
                // 不存在则新增
                config.setCreateTime(now);
            }
        }
        
        return saveOrUpdateBatch(configs);
    }

    @Override
    public Map<String, Object> getWorkHourSettings(String projectKey) {
        List<SysConfig> configs = getConfigsByGroup(projectKey, "work-hour");
        Map<String, Object> settings = new HashMap<>();
        
        for (SysConfig config : configs) {
            String value = config.getConfigValue();
            switch (config.getValueType()) {
                case "number":
                    settings.put(config.getConfigKey(), Double.parseDouble(value));
                    break;
                case "boolean":
                    settings.put(config.getConfigKey(), Boolean.parseBoolean(value));
                    break;
                default:
                    settings.put(config.getConfigKey(), value);
            }
        }
        
        return settings;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveWorkHourSettings(String projectKey, Map<String, Object> settings) {
        if (settings == null || settings.isEmpty()) {
            return false;
        }
        
        List<SysConfig> configs = new ArrayList<>();
        Date now = new Date();
        
        settings.forEach((key, value) -> {
            SysConfig config = baseMapper.getConfigByKey(projectKey, key);
            if (config == null) {
                config = new SysConfig();
                config.setProjectKey(projectKey);
                config.setConfigKey(key);
                config.setConfigGroup("work_hour");
                config.setCreateTime(now);
            } else {
                config.setUpdateTime(now);
            }
            config.setConfigValue(String.valueOf(value));
            configs.add(config);
        });
        
        return batchSaveConfig(configs);
    }

    @Override
    public boolean saveOrUpdateConfigWithFallback(String projectKey, String configKey, String configValue, Map<String, Object> extraParams) {
        if (extraParams == null) {
            extraParams = new HashMap<>();
        }
        
        // 查询现有配置
        SysConfig config = baseMapper.selectOne(
            new QueryWrapper<SysConfig>()
                .eq("config_key", configKey)
                .eq(StringUtils.isNotBlank(projectKey), "project_key", projectKey)
        );
        
        Date now = new Date();
        if (config == null) {
            // 当前projectKey下不存在该配置，尝试从默认配置(projectKey='0')复制
            SysConfig defaultConfig = baseMapper.selectOne(
                new QueryWrapper<SysConfig>()
                    .eq("config_key", configKey)
                    .eq("project_key", "0")
            );
            
            // 创建新配置
            config = new SysConfig();
            config.setConfigKey(configKey);
            config.setConfigValue(configValue);
            config.setProjectKey(projectKey);
            config.setCreateTime(now);
            config.setStatus(true);
            
            // 如果存在默认配置，复制其他属性
            if (defaultConfig != null) {
                config.setConfigName(defaultConfig.getConfigName());
                config.setConfigGroup(defaultConfig.getConfigGroup());
                config.setValueType(defaultConfig.getValueType());
                config.setDefaultValue(configValue); // 使用当前值作为默认值
                config.setSortOrder(defaultConfig.getSortOrder());
                config.setRemark(defaultConfig.getRemark());
            }
            
            // 应用额外参数，覆盖默认值
            if (extraParams.containsKey("configName")) {
                config.setConfigName((String) extraParams.get("configName"));
            }
            if (extraParams.containsKey("configGroup")) {
                config.setConfigGroup((String) extraParams.get("configGroup"));
            }
            if (extraParams.containsKey("valueType")) {
                config.setValueType((String) extraParams.get("valueType"));
            }
            if (extraParams.containsKey("defaultValue")) {
                config.setDefaultValue((String) extraParams.get("defaultValue"));
            }
            if (extraParams.containsKey("sortOrder")) {
                config.setSortOrder((Integer) extraParams.get("sortOrder"));
            }
            if (extraParams.containsKey("remark")) {
                config.setRemark((String) extraParams.get("remark"));
            }
            
            return save(config);
        } else {
            // 存在则更新
            config.setConfigValue(configValue);
            config.setUpdateTime(now);
            
            return updateById(config);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyConfigs(String targetProjectKey) {
        if (StringUtils.isBlank(targetProjectKey)) {
            return false;
        }
        
        // 查询所有默认配置(project_key='0')
        List<SysConfig> defaultConfigs = baseMapper.selectList(
            new QueryWrapper<SysConfig>()
                .eq("project_key", "0")
        );
        
        if (defaultConfigs == null || defaultConfigs.isEmpty()) {
            return false;
        }
        
        // 批量查询目标工作空间的现有配置
        List<SysConfig> existingConfigs = baseMapper.selectList(
            new QueryWrapper<SysConfig>()
                .eq("project_key", targetProjectKey)
        );
        
        Map<String, SysConfig> existingConfigMap = existingConfigs.stream()
            .collect(Collectors.toMap(SysConfig::getConfigKey, Function.identity()));
        
        // 批量处理配置复制
        List<SysConfig> configsToSave = new ArrayList<>();
        Date now = new Date();
        
        for (SysConfig defaultConfig : defaultConfigs) {
            SysConfig existingConfig = existingConfigMap.get(defaultConfig.getConfigKey());
            
            if (existingConfig == null) {
                // 不存在则创建新配置
                SysConfig newConfig = new SysConfig();
                newConfig.setProjectKey(targetProjectKey);
                newConfig.setConfigKey(defaultConfig.getConfigKey());
                newConfig.setConfigValue(defaultConfig.getConfigValue());
                newConfig.setConfigName(defaultConfig.getConfigName());
                newConfig.setConfigGroup(defaultConfig.getConfigGroup());
                newConfig.setValueType(defaultConfig.getValueType());
                newConfig.setDefaultValue(defaultConfig.getDefaultValue());
                newConfig.setSortOrder(defaultConfig.getSortOrder());
                newConfig.setRemark(defaultConfig.getRemark());
                newConfig.setCreateTime(now);
                newConfig.setStatus(true);
                
                configsToSave.add(newConfig);
            } else {
                // 存在则更新配置
                existingConfig.setConfigValue(defaultConfig.getConfigValue());
                existingConfig.setConfigName(defaultConfig.getConfigName());
                existingConfig.setConfigGroup(defaultConfig.getConfigGroup());
                existingConfig.setValueType(defaultConfig.getValueType());
                existingConfig.setDefaultValue(defaultConfig.getDefaultValue());
                existingConfig.setSortOrder(defaultConfig.getSortOrder());
                existingConfig.setRemark(defaultConfig.getRemark());
                existingConfig.setUpdateTime(now);
                
                configsToSave.add(existingConfig);
            }
        }
        
        // 批量保存或更新
        return batchSaveConfig(configsToSave);
    }

	@Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchByKeys(List<Map<String, String>> params, String projectKey) {
        if (params == null || params.isEmpty()) {
            return false;
        }
        
        // 收集所有需要处理的configKey
        Set<String> configKeys = params.stream()
            .map(item->item.get("key"))
            .collect(Collectors.toSet());
            
        // 查询现有配置
        List<SysConfig> existingConfigs = baseMapper.selectList(
            new QueryWrapper<SysConfig>()
                .eq("project_key", projectKey)
                .in("config_key", configKeys)
        );
        
        // 查询默认配置(project_key='0')
        List<SysConfig> defaultConfigs = baseMapper.selectList(
            new QueryWrapper<SysConfig>()
                .eq("project_key", "0")
                .in("config_key", configKeys)
        );
        
        // 转换为Map方便查找
        Map<String, SysConfig> existingConfigMap = existingConfigs.stream()
            .collect(Collectors.toMap(SysConfig::getConfigKey, Function.identity()));
            
        Map<String, SysConfig> defaultConfigMap = defaultConfigs.stream()
            .collect(Collectors.toMap(SysConfig::getConfigKey, Function.identity()));
        
        // 准备批量保存的配置列表
        List<SysConfig> toSaveOrUpdate = new ArrayList<>();
        Date now = new Date();
        
        for (Map<String, String> param : params) {
                String configKey = param.get("key");
                String configValue = param.get("value");
                
                SysConfig config = existingConfigMap.get(configKey);
                if (config == null) {
                    // 从默认配置复制
                    SysConfig defaultConfig = defaultConfigMap.get(configKey);
                    if (defaultConfig != null) {
                        config = new SysConfig();
                        config.setProjectKey(projectKey);
                        config.setConfigKey(configKey);
                        config.setConfigValue(configValue);
                        config.setConfigName(defaultConfig.getConfigName());
                        config.setConfigGroup(defaultConfig.getConfigGroup());
                        config.setValueType(defaultConfig.getValueType());
                        config.setDefaultValue(defaultConfig.getDefaultValue());
                        config.setSortOrder(defaultConfig.getSortOrder());
                        config.setRemark(defaultConfig.getRemark());
                        config.setCreateTime(now);
                        config.setStatus(true);
                    } else {
                        // 没有默认配置则创建新配置
                        config = new SysConfig();
                        config.setProjectKey(projectKey);
                        config.setConfigKey(configKey);
                        config.setConfigValue(configValue);
                        config.setCreateTime(now);
                        config.setStatus(true);
                    }
                } else {
                    // 更新配置
                    config.setConfigValue(configValue);
                    config.setUpdateTime(now);
                }
                
                toSaveOrUpdate.add(config);
        }
        
        return saveOrUpdateBatch(toSaveOrUpdate);
    }

	@Override
	public Map<String, Object> getSysSettings(String projectKey) {
		List<SysConfig> configs = baseMapper.selectList(
	            new QueryWrapper<SysConfig>()
	                .eq("project_key", projectKey)
	        );
		
		// 转换为Map，key为config_key，value为config_value
		return configs.stream()
		    .collect(Collectors.toMap(
		        SysConfig::getConfigKey,
		        SysConfig::getConfigValue
		    ));
	}
	
}