<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.HourRecordDetailMapper">

    <!-- 批量插入工时记录明细 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO hour_record_detail (
            id, record_id, work_date, project_id, project_name, 
            description, percentage, actual_hours, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.recordId}, #{item.workDate}, #{item.projectId}, #{item.projectName}, 
                #{item.description}, #{item.percentage}, #{item.actualHours}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>
    
</mapper> 