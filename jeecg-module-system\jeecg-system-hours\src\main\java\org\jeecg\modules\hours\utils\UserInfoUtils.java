package org.jeecg.modules.hours.utils;

import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.service.IFsUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 用户信息工具类
 * 用于统一获取当前用户信息，优先使用飞书授权信息
 */
@Slf4j
@Component
public class UserInfoUtils {

    @Lazy
    @Autowired
    private IFeishuAuthService feishuAuthService;
    
    @Lazy
    @Autowired
    private IFsUserService fsUserService;
    
    /**
     * 获取当前用户标识
     * @return 当前用户的飞书标识(userKey)
     */
    public String getCurrentUserKey() {
        return feishuAuthService.getCurrentUserKey();
    }
    
    /**
     * 获取当前用户在系统中的ID（fs_user表中的id字段）
     * 此方法会将飞书的userKey转换为系统内部的用户ID
     * @return 系统内部的用户ID，获取失败返回null
     */
    public String getUserSystemId() {
        try {
            // 获取飞书用户标识
            String userKey = getCurrentUserKey();
            if (userKey == null) {
                log.warn("无法获取有效的用户标识");
                return null;
            }
            
            // 根据飞书用户标识查询系统用户ID
            LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUser::getUserKey, userKey);
            FsUser user = fsUserService.getOne(queryWrapper);
            
            if (user != null) {
                return user.getId();
            }
            
            log.warn("未找到用户[{}]对应的系统用户记录", userKey);
            return null;
        } catch (Exception e) {
            log.error("获取系统用户ID失败", e);
            return null;
        }
    }
    
    /**
     * 获取当前用户名
     * @return 用户名，获取失败返回null
     */
    public String getUserName() {
        try {
            // 获取飞书用户标识
            String userKey = getCurrentUserKey();
            if (userKey == null) {
                log.warn("无法获取有效的用户标识");
                return null;
            }
            
            // 查询飞书用户信息
            FsUser user = getFsUserByUserKey(userKey);
            if (user != null && user.getName() != null && !user.getName().isEmpty()) {
                return user.getName();
            }
            
            // 如果没有找到用户名，返回userKey作为用户名
            return userKey;
        } catch (Exception e) {
            log.error("获取用户名失败", e);
            return null;
        }
    }
    
    /**
     * 根据userKey查询飞书用户信息
     */
    private FsUser getFsUserByUserKey(String userKey) {
        if (fsUserService == null) {
            log.warn("fsUserService未注入，无法查询飞书用户信息");
            return null;
        }
        
        try {
            LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUser::getUserKey, userKey);
            return fsUserService.getOne(queryWrapper);
        } catch (Exception e) {
            log.error("查询飞书用户信息失败", e);
            return null;
        }
    }
    
    /**
     * 获取当前登录用户对象
     * @return 当前登录用户对象，获取失败返回null
     */
    public FsUser getLoginUser() {
        try {
            // 获取飞书用户标识
            String userKey = getCurrentUserKey();
            if (userKey == null) {
                log.warn("无法获取有效的用户标识");
                return null;
            }
            
            // 查询用户信息
            return getFsUserByUserKey(userKey);
        } catch (Exception e) {
            log.error("获取登录用户对象失败", e);
            return null;
        }
    }
} 