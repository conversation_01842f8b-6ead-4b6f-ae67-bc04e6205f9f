/**
 * 统计接口调用测试页面
 * 用于验证统计接口是否只被调用一次，避免重复调用问题
 */
import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Notification } from '@douyinfe/semi-ui';
import WaterfallCascader from '../src/features/board_web_gw38j1/components/WaterfallCascader';
import { CascaderItem } from '../src/features/board_web_gw38j1/types';

const { Title, Text } = Typography;

const StatisticsCallTest: React.FC = () => {
  const [workContent, setWorkContent] = useState<(string | number)[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<CascaderItem[]>([]);
  const [callCount, setCallCount] = useState(0);
  const [callHistory, setCallHistory] = useState<Array<{
    timestamp: string;
    params: any;
    source: string;
  }>>([]);

  // 模拟统计接口调用
  const mockFetchStatistics = (workItemKey?: string, instanceKey?: string, nodeKey?: string, taskKey?: string, source: string = 'unknown') => {
    const timestamp = new Date().toLocaleTimeString();
    const params = { workItemKey, instanceKey, nodeKey, taskKey };
    
    console.log(`[${timestamp}] 统计接口调用 (来源: ${source}):`, params);
    
    setCallCount(prev => prev + 1);
    setCallHistory(prev => [...prev, {
      timestamp,
      params,
      source
    }]);

    Notification.info({
      title: '统计接口调用',
      content: `第${callCount + 1}次调用 (来源: ${source})`,
      duration: 2
    });
  };

  // 监听工作内容变化 - 模拟 SingleRecordForm 中的 useEffect
  useEffect(() => {
    if (workContent && workContent.length > 0) {
      const [workItemKey, instanceKey, nodeKey, taskKey] = workContent;
      // 只有当选择到最后一个层级（任务）或倒数第二个层级（节点）时才调用统计接口
      if (taskKey || (nodeKey && !taskKey)) {
        mockFetchStatistics(
          workItemKey as string,
          instanceKey as string,
          nodeKey as string,
          taskKey as string,
          'useEffect监听'
        );
      }
    }
  }, [workContent, callCount]);

  // 处理级联选择器变化
  const handleWorkContentChange = (value: (string | number)[], options: CascaderItem[]) => {
    console.log('级联选择器值变化:', value, options);
    setWorkContent(value);
    setSelectedOptions(options);
  };

  // 清空测试数据
  const handleClear = () => {
    setWorkContent([]);
    setSelectedOptions([]);
    setCallCount(0);
    setCallHistory([]);
    Notification.success({
      title: '已清空',
      content: '测试数据已清空'
    });
  };

  // 自定义显示格式
  const displayRender = (labels: string[]) => {
    return labels.join(' → ');
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>统计接口调用测试</Title>
      <Text type="secondary">
        此页面用于测试统计接口是否存在重复调用问题。正常情况下，每次选择完整路径应该只调用一次统计接口。
      </Text>

      <Card style={{ marginTop: '24px' }}>
        <Title level={4}>级联选择器</Title>
        <div style={{ marginBottom: '16px' }}>
          <WaterfallCascader
            placeholder="请选择工作内容"
            style={{ width: '100%' }}
            value={workContent}
            onChange={handleWorkContentChange}
            displayRender={displayRender}
          />
        </div>
        <Button onClick={handleClear} type="secondary">
          清空测试数据
        </Button>
      </Card>

      <Card style={{ marginTop: '24px' }}>
        <Title level={4}>调用统计</Title>
        <div style={{ marginBottom: '16px' }}>
          <Text strong>总调用次数: </Text>
          <Text style={{ 
            color: callCount > 1 ? '#ff4d4f' : '#52c41a',
            fontSize: '18px',
            fontWeight: 'bold'
          }}>
            {callCount}
          </Text>
          {callCount > 1 && (
            <Text type="danger" style={{ marginLeft: '8px' }}>
              ⚠️ 检测到重复调用！
            </Text>
          )}
        </div>
        
        <div>
          <Text strong>当前选择: </Text>
          <Text code>{JSON.stringify(workContent)}</Text>
        </div>
      </Card>

      <Card style={{ marginTop: '24px' }}>
        <Title level={4}>调用历史</Title>
        {callHistory.length === 0 ? (
          <Text type="secondary">暂无调用记录</Text>
        ) : (
          <div>
            {callHistory.map((call, index) => (
              <div key={index} style={{ 
                padding: '8px', 
                border: '1px solid #d9d9d9', 
                borderRadius: '4px', 
                marginBottom: '8px',
                backgroundColor: index === callHistory.length - 1 ? '#f6ffed' : '#fff'
              }}>
                <div>
                  <Text strong>第{index + 1}次调用</Text>
                  <Text type="secondary" style={{ marginLeft: '8px' }}>
                    {call.timestamp}
                  </Text>
                  <Text type="secondary" style={{ marginLeft: '8px' }}>
                    来源: {call.source}
                  </Text>
                </div>
                <div style={{ marginTop: '4px' }}>
                  <Text code style={{ fontSize: '12px' }}>
                    {JSON.stringify(call.params, null, 2)}
                  </Text>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};

export default StatisticsCallTest;
