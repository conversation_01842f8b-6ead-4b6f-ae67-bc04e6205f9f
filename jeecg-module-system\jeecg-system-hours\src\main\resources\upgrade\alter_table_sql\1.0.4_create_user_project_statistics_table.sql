-- 创建人员项目工时统计表
-- 版本：1.0.4
-- 日期：2025-07-08
-- 说明：用于存储按人员和项目维度的工时统计数据，支持报表查询

CREATE TABLE `user_project_hour_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_period` varchar(10) NOT NULL DEFAULT 'daily' COMMENT '统计周期(daily-日, weekly-周, monthly-月)',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户姓名',
  `user_code` varchar(50) DEFAULT NULL COMMENT '工号',
  `department_id` varchar(32) DEFAULT NULL COMMENT '部门ID',
  `department_name` varchar(50) DEFAULT NULL COMMENT '部门名称',
  `project_id` varchar(32) NOT NULL COMMENT '项目ID',
  `project_name` varchar(100) DEFAULT NULL COMMENT '项目名称',
  `project_code` varchar(50) DEFAULT NULL COMMENT '项目编码',
  `total_hours` decimal(10,2) DEFAULT '0.00' COMMENT '总工时',
  `record_count` int(11) DEFAULT '0' COMMENT '记录数量',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_holiday` tinyint(1) DEFAULT '0' COMMENT '是否假日：默认 否',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_user_project` (`stat_date`,`stat_period`,`user_id`,`project_id`),
  KEY `idx_stat_date_period` (`stat_date`,`stat_period`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_department_id` (`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='人员项目工时统计表';
