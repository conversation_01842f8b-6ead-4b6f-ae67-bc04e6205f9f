import { get, post, put, del } from '../../utils/request';
import { SysConfig, SysConfigQuery, PageResult, SysConfigGroupStat } from '../../types/systemConfig';
import { API_CONFIG } from '../../utils/config';

// 系统配置相关接口
export const configApi = {
  /**
   * 获取配置列表（分页）
   * @param params 查询参数
   */
  getConfigList: (params: SysConfigQuery) => 
    get<PageResult<SysConfig>>(API_CONFIG.API_PATHS.SYS_CONFIG.LIST, params),
  
  /**
   * 根据ID获取配置
   * @param id 配置ID
   */
  getConfigById: (id: string) => 
    get<SysConfig>(API_CONFIG.API_PATHS.SYS_CONFIG.GET_BY_ID, { id }),
  
  /**
   * 根据配置键获取配置
   * @param configKey 配置键
   */
  getConfigByKey: (configKey: string) => 
    get<SysConfig>(`${API_CONFIG.API_PATHS.SYS_CONFIG.GET_BY_KEY}/${configKey}`),
  
  /**
   * 根据分组获取配置列表
   * @param configGroup 配置分组
   */
  getConfigsByGroup: (configGroup: string) => 
    get<SysConfig[]>(`${API_CONFIG.API_PATHS.SYS_CONFIG.GET_BY_GROUP}/${configGroup}`),
  
  /**
   * 添加配置
   * @param data 配置数据
   */
  addConfig: (data: SysConfig) => 
    post<boolean>(API_CONFIG.API_PATHS.SYS_CONFIG.ADD, data),
  
  /**
   * 更新配置
   * @param data 配置数据
   */
  updateConfig: (data: SysConfig) => 
    put<boolean>(API_CONFIG.API_PATHS.SYS_CONFIG.EDIT, data),
  
  /**
   * 删除配置
   * @param id 配置ID
   */
  deleteConfig: (id: string) => 
    del<boolean>(`${API_CONFIG.API_PATHS.SYS_CONFIG.DELETE}?id=${id}`),
  
  /**
   * 批量删除配置
   * @param ids ID列表，逗号分隔
   */
  deleteBatch: (ids: string) => 
    del<boolean>(`${API_CONFIG.API_PATHS.SYS_CONFIG.DELETE_BATCH}?ids=${ids}`),

  /**
   * 获取配置分组统计
   */
  getConfigGroupStats: () => 
    get<SysConfigGroupStat[]>(API_CONFIG.API_PATHS.SYS_CONFIG.GROUP_STATS),
  
  /**
   * 检查健康状态
   */
  checkHealth: () =>
    get<any>(API_CONFIG.API_PATHS.SYS_CONFIG.HEALTH)
}; 