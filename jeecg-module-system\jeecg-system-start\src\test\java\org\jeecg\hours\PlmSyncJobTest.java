package org.jeecg.hours;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.plm.job.PlmSyncJob;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 消息推送测试
 * @Author: lsq
 */
@Slf4j
@ActiveProfiles("dev")
//@ActiveProfiles("prod")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class PlmSyncJobTest {

	@Autowired
	private PlmSyncJob plmSyncJob;

	@org.junit.jupiter.api.Test
	public void testname() throws Exception {

		plmSyncJob.initSync();

	}

//	@org.junit.jupiter.api.Test
	public void initCrement() throws Exception {

		plmSyncJob.incrementalSync();

	}

}
