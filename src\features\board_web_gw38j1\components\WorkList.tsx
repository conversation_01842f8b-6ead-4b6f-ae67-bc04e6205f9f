import React, { useState, useEffect, useCallback, useImperative<PERSON><PERSON>le, forwardRef } from 'react';
import {Table, Button, Select, Tag, Notification, Pagination, Popconfirm, Tooltip, Typography} from '@douyinfe/semi-ui';
import { get, request } from '../../../utils/request';
import HourRecordForm from '../HourRecordForm';
import DateRangePicker from '../components/DateRangePicker';

import HourRecordEditForm from '../HourRecordEditForm';
const {Title, Text} = Typography;
import './workList.scss';
import UserSelector from "./UserSelector";

// 添加可截断文本的展示组件
const TruncateText: React.FC<{ content: string, width?: number }> = ({ content, width }) => {
  if (!content) return <span>-</span>;
  
  return (
    <Tooltip content={content}>
      <span className="truncate-cell" style={{ width: width ? `${width}px` : '100%' }}>
        {content}
      </span>
    </Tooltip>
  );
};

// 接口返回的工时记录类型
interface HourRecord {
  id: string | number;
  userId: string;
  userName: string;
  workDate: string;
  hours: number;
  description: string;
  workItemId: string;       // 工作项ID
  workItemName: string;     // 工作项名称
  projectId: string;
  projectName: string;
  instanceId?: string;
  instanceName?: string;
  nodeId?: string;
  nodeName?: string;
  taskId?: string;
  taskName?: string;
  status: number | string;
  statusName: string;
  createdAt: string;
  submitTime?: string | null;
  approverName?: string | null;
  rejectReason?: string | null;
  workPath?: string;
  workPeriodStart?: string;
  workPeriodEnd?: string;
}

// 分页查询参数接口
interface QueryParams {
  pageNo: number;
  pageSize: number;
  startDate?: string;
  endDate?: string;
  userId?: string;
  projectId?: string;
  workContent?: string;
  status?: string;
}

// 分页结果接口
interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages?: number;
}


// 获取本周日期范围的工具函数（与DateRangePicker中"本周"逻辑一致）
const getThisWeekRange = (): [string, string] => {
//  return ['2025-01-01', '2025-10-07'];
  const now = new Date();
  // 本周开始：当前日期减去（星期几-1）天，即周一
  const startDate = new Date(now.getTime() - (now.getDay() - 1) * 24 * 60 * 60 * 1000);
  // 本周结束：当前日期加上（7-星期几）天，即周日
  const endDate = new Date(now.getTime() + (7 - now.getDay()) * 24 * 60 * 60 * 1000);

  // 格式化为YYYY-MM-DD
  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  return [formatDate(startDate), formatDate(endDate)];
};

// 更新 API 接口定义，匹配实际返回结构
interface ApiResponse<T> {
  success: boolean;
  message: string;
  code: number;
  result: PageResult<T>;
  timestamp: number;
}

// 调用工时记录接口
const getHourRecordList = (params: QueryParams): Promise<ApiResponse<PageResult<HourRecord>>> => {
  return get('/hours/hourRecord/pendingRegisteredHoursList', params);
};

// 更新删除记录的接口函数，使用查询字符串传递id参数
const deleteHourRecord = (id: string | number): Promise<any> => {
  return request('DELETE', `/hours/hourRecord/delete?id=${id}`);
};

function toFormInitialData(record: HourRecord | null): any {
  if (!record) return undefined;
  return {
    workDate: record.workDate ? new Date(record.workDate) : undefined,
    worker: record.userId,
    workContent: [record.workItemId, record.instanceId, record.nodeId, record.taskId].filter(Boolean),
    projectKey: record.projectId,
    recordId: record.id,
    projectName: record.projectName,
    instanceName: record.instanceName,
    nodeName: record.nodeName,
    taskName: record.taskName
  };
}

// 将 WorkList 改为 forwardRef 组件
const WorkList = forwardRef((props: any, ref) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<HourRecord[]>([]);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    pageNo: 1,
    pageSize: 10,
  });
  // 日期范围状态 - 使用DateRangePicker格式
  const [dateRange, setDateRange] = useState<[string, string]>(() => {
    // 默认为本周（与DateRangePicker中"本周"快捷方式逻辑一致）
   // return getThisWeekRange();
    return ['',''];
  });
  // 用户选择状态
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  // 添加记录模式状态，用于控制HourRecordForm的登记模式
  const [recordMode, setRecordMode] = useState<'single' | 'batch'>('single');
  // 添加当前编辑记录的状态
  const [currentRecord, setCurrentRecord] = useState<HourRecord | null>(null);
  // 添加工作内容数组的状态
  const [workContent, setWorkContent] = useState<(string | number)[]>([]);
  // 添加editMode状态区分'add'和'edit'
  const [editMode, setEditMode] = useState<'add' | 'edit' | 'view'>('add');

  // 获取工时记录列表
  const fetchRecords = useCallback(async (params?: Partial<QueryParams>) => {
    setLoading(true);
    
    try {
      const mergedParams = { ...queryParams, ...params };
      if (params) {
        setQueryParams(mergedParams as QueryParams);
      }
      
      const response = await getHourRecordList(mergedParams as QueryParams);
      console.log('API 完整响应数据:', response);

      // 恢复正确的数据解析逻辑
      if (response && response.result) {
        try {
          // 从result字段中获取分页数据
          const result = response.result;
          
          // 使用any类型断言来避免TypeScript类型错误
          if (result.records && Array.isArray(result.records)) {
            setRecords(result.records as any);
            setTotal(result.total || 0);
          } else {
            console.error('API返回的records不是数组或为空:', result.records);
            setRecords([]);
            setTotal(0);
          }
        } catch (parseError) {
          console.error('解析API返回数据出错:', parseError);
          setRecords([]);
          setTotal(0);
        }
      } else {
        console.error('API 返回格式不符合预期:', response);
        setRecords([]);
        setTotal(0);
      }
      
    } catch (error) {
      console.error('API 请求出错:', error);
      Notification.error({
        title: '获取工时记录失败',
        content: error.message
      });
    } finally {
      setLoading(false);
    }
  }, [queryParams]);

  // 用 useImperativeHandle 暴露 fetchRecords 方法
  useImperativeHandle(ref, () => ({
    fetchRecords,
  }));

  // 构建查询参数的辅助函数
  const buildQueryParams = useCallback((overrides: Partial<QueryParams> = {}) => {
    return {
      ...queryParams,
      startDate: dateRange[0],
      endDate: dateRange[1],
      userId: selectedUsers.length > 0 ? selectedUsers.join(',') : undefined,

      ...overrides
    };
  }, [queryParams, dateRange, selectedUsers]);

  // 处理日期范围变化的函数 - 适配DateRangePicker
  const handleDateRangeChange = useCallback((dates: [string, string]) => {
    setDateRange(dates);

    // 使用新的日期范围进行查询，保持其他筛选条件
    const params = buildQueryParams({
      startDate: dates[0],
      endDate: dates[1],
      pageNo: 1,

    });
    fetchRecords(params);

  }, [buildQueryParams, fetchRecords,  selectedUsers]);

  // 处理用户选择变化
  const handleUserChange = useCallback((userIds: string[]) => {
    setSelectedUsers(userIds);

    // 使用新的用户选择进行查询，保持其他筛选条件
    const params = buildQueryParams({
      userId: userIds.length > 0 ? userIds.join(',') : undefined,
      pageNo: 1
    });
    fetchRecords(params);

  }, [buildQueryParams, fetchRecords,  dateRange]);



  // 首次加载数据 - 使用默认日期范围
  useEffect(() => {
    // 使用默认的日期范围进行初始加载
    const params = buildQueryParams({ pageNo: 1 });
    fetchRecords(params);

  }, []); // 依赖空数组，仅在组件挂载时执行一次

  // 表格列定义（新版）
  const columns = [
    {
      title: '工作日期',
      dataIndex: 'workDate',
      width: 150,
    },
    {
      title: '工作人员',
      dataIndex: 'userName',
      width: 90,
      render: (userName: string) => <TruncateText content={userName || '-'} />
    },
    {
      title: '登记状态',
      dataIndex: 'statusName',
      width: 90,
      render: (statusName: string, record: HourRecord) => {
        let color = '#F44336';
        return <Tag color={color}>{statusName}</Tag>;
      },
    },
  ];



  // 处理分页变化
  const handlePageChange = (page: number) => {
    fetchRecords({
      pageNo: page,
      pageSize
    });
    setCurrentPage(page);
  };

  // 处理每页条数变化
  const handlePageSizeChange = (size: number) => {
    fetchRecords({
      pageNo: 1, // 改变页面大小时重置到第一页
      pageSize: size
    });
    setCurrentPage(1);
    setPageSize(size);
  };

  // 统一的分页处理函数
  const handlePaginationChange = (page: number, size: number) => {
    if (size !== pageSize) {
      // 页面大小改变
      handlePageSizeChange(size);
    } else {
      // 只是页码改变
      handlePageChange(page);
    }
  };

  // 处理打开批量登记
  const handleOpenBatchRecord = () => {
    setCurrentRecord(null);
    setWorkContent([]);
    setRecordMode('batch');
    setEditMode('add');
    setVisible(false);
    setTimeout(() => {
      setVisible(true);
    }, 10);
  };


  return (
    <div className="work-list-root WorkListView">
      {/* 工具栏 */}
      <div className="work-list-toolbar">
        {/* 左侧筛选区域 */}
        <div className="toolbar-filters">
          <Text className={'boxtitle'}>待登记工时</Text>

            <DateRangePicker

              value={dateRange}
              onChange={handleDateRangeChange}
              dimension="day"
              placeholder="请选择日期范围"
          />

          <div style={{width: 200}}>
            <UserSelector
                value={selectedUsers}
                onChange={handleUserChange}
                placeholder="全部人员"
            />
          </div>


        </div>

        {/* 右侧按钮组 */}
        <div className="toolbar-buttons">

          <Button
              theme="solid"
              type="primary"
              onClick={handleOpenBatchRecord}
          >
            批量登记
          </Button>
        </div>
      </div>

      {/* 工时记录表格 */}
      <Table
        columns={columns}
        dataSource={records}
        loading={loading}
        pagination={false}
        scroll={{ x: 'max-content' }}
        size="middle"
        style={{ marginTop: 16, width: '100%' }}
        tableLayout="auto"
        components={{
          header: {
            cell: (props) => <th {...props} style={{ ...props.style, whiteSpace: 'nowrap', overflow: 'visible' }} />
          }
        }}
      />

      {/* 分页 */}
      {total > 0 && (
        <div style={{
          marginTop: 16,
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-end'
        }}>
          <Pagination
            currentPage={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={handlePaginationChange}
            showSizeChanger
            showQuickJumper
            showTotal
            pageSizeOpts={[10, 20, 50]}
          />
        </div>
      )}

      {/* 工时登记表单 */}
      {editMode === 'edit' && (
        <HourRecordEditForm
          visible={visible}
          isEdit={true}
          initialData={toFormInitialData(currentRecord)}
          onCancel={() => setVisible(false)}
          onSave={() => { setVisible(false); fetchRecords(); }}
          onSubmit={() => { setVisible(false); fetchRecords(); }}
        />
      )}
      {/* 工时登记详情 */}
      {editMode === 'view' && (
        <HourRecordEditForm
          visible={visible}
          isEdit={false}
          readOnly={true}
          initialData={toFormInitialData(currentRecord)}
          onCancel={() => setVisible(false)}
          onSave={() => { setVisible(false); fetchRecords(); }}
          onSubmit={() => { setVisible(false); fetchRecords(); }}
        />
      )}
      {editMode === 'add' && (
        <HourRecordForm
          visible={visible}
          initialData={undefined}
          onCancel={() => setVisible(false)}
          onSave={() => { setVisible(false); fetchRecords(); }}
          onSubmit={() => { setVisible(false); fetchRecords(); }}
        />
      )}

    </div>
  );
});

export default WorkList;