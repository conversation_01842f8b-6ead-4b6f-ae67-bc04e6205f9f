package org.jeecg.modules.plm.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.plm.entity.PlmProject;

import java.util.List;
import java.util.Map;

/**
 * PLM项目Service接口
 */
public interface IPlmProjectService extends IService<PlmProject> {

    /**
     * 分页查询项目列表
     *
     * @param page         分页参数
     * @param projectCode  项目编码
     * @param projectName  项目名称
     * @param projectStatus 项目状态
     * @return 项目列表
     */
    IPage<PlmProject> queryProjectList(Page<PlmProject> page);

    /**
     * 根据项目编码查询项目
     *
     * @param projectCode 项目编码
     * @return 项目信息
     */
    PlmProject queryByProjectCode(String projectCode);

    /**
     * 根据项目编码获取项目
     *
     * @param projectCode 项目编码
     * @return 项目信息
     */
    PlmProject getByProjectCode(String projectCode);

    /**
     * 统计项目状态分布
     *
     * @return 项目状态统计
     */
    List<Map<String, Object>> statisticsByStatus();

} 