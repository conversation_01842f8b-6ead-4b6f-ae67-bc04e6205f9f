/**
 * 报表相关类型定义
 */

// 报表查询参数
export interface ReportQueryParams {
  reportType?: 'instance-hours' | 'employee-hours' | 'employee-instance-distribution';
  dimension: 'month' | 'week' | 'day';
  startDate: string;
  endDate: string;
  instanceIds?: string[];
  userIds?: string[];
  page?: number;
  size?: number;
}

// 用户信息
export interface User {
  id: string;
  name: string;
  code: string;
  realname?: string;
  username?: string;
}

// 实例信息
export interface Instance {
  id: string;
  workItemName: string;
  projectKey: string;
}

// 实例工时报表数据
export interface InstanceHoursReportData {
  instanceName: string;
  periods: { [key: string]: number };
  total: number;
}

// 实例工时报表响应
export interface InstanceHoursReportResponse {
  data: InstanceHoursReportData[];
  periods: string[];
  total: number;
  summary: { [key: string]: number };
}

// 员工工时报表数据
export interface EmployeeHoursReportData {
  employeeName: string;
  employeeId: string;
  periods: { [key: string]: number };
  totalHours: number;
  totalDays: number;
}

// 员工工时报表响应
export interface EmployeeHoursReportResponse {
  data: EmployeeHoursReportData[];
  periods: string[];
  total: number;
  summary: {
    periods: { [key: string]: number };
    grandTotalHours: number;
    grandTotalDays: number;
  };
}

// 员工工时实例分布数据
export interface EmployeeInstanceDistributionData {
  employeeName: string;
  employeeId: string;
  instances: { [key: string]: number };
  totalHours: number;
  totalDays: number;
}

// 员工工时实例分布响应
export interface EmployeeInstanceDistributionResponse {
  data: EmployeeInstanceDistributionData[];
  instances: string[];
  total: number;
  summary: {
    instances: { [key: string]: number };
    grandTotalHours: number;
    grandTotalDays: number;
  };
}

// API响应基础结构
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  code: number;
  result: T;
  timestamp: number;
}
