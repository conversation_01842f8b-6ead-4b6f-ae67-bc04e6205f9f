package org.jeecg.modules.plm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.plm.entity.PlmSyncLog;

/**
 * PLM同步日志Mapper接口
 */
public interface PlmSyncLogMapper extends BaseMapper<PlmSyncLog> {

    /**
     * 分页查询同步日志
     *
     * @param page     分页参数
     * @param syncType 同步类型
     * @param status   同步状态
     * @return 同步日志列表
     */
    IPage<PlmSyncLog> querySyncLogList(Page<PlmSyncLog> page,
                                      @Param("syncType") String syncType,
                                      @Param("status") String status);

    /**
     * 查询最近一次成功的同步记录
     *
     * @param syncType 同步类型
     * @return 同步日志
     */
    PlmSyncLog queryLastSuccessLog(@Param("syncType") String syncType);
} 