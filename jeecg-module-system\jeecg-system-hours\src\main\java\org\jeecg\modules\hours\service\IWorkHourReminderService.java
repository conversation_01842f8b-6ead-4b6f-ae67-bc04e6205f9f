package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.entity.FsSysReminderConfig;
import org.jeecg.modules.hours.vo.UnsubmittedWorkHourVO;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 工时登记提醒服务接口
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface IWorkHourReminderService {

    /**
     * 执行提醒逻辑
     * 
     * @param reminderConfig 提醒配置
     */
    void executeReminder(FsSysReminderConfig reminderConfig);

    /**
     * 查询未提交工时的用户和日期
     * 
     * @param projectKey 项目标识
     * @param businessDates 业务日期列表
     * @return 未提交工时的用户和日期信息
     */
    List<UnsubmittedWorkHourVO> findUnsubmittedWorkHours(String projectKey, List<LocalDate> businessDates);

    /**
     * 发送提醒消息
     * 
     * @param reminderConfig 提醒配置
     * @param unsubmittedList 未提交工时列表
     */
    void sendReminderMessages(FsSysReminderConfig reminderConfig, List<UnsubmittedWorkHourVO> unsubmittedList);

    /**
     * 生成提醒消息标题
     * 
     * @param reminderType 提醒类型
     * @return 消息标题
     */
    String generateMessageTitle(String reminderType);

    /**
     * 生成提醒消息内容
     * 
     * @param reminderConfig 提醒配置
     * @param unsubmittedDates 未提交的日期列表
     * @return 消息内容
     */
    String generateMessageContent(FsSysReminderConfig reminderConfig, List<LocalDate> unsubmittedDates);

    /**
     * 生成按钮文本
     * 
     * @param reminderType 提醒类型
     * @return 按钮文本
     */
    String generateButtonText(String reminderType);

    /**
     * 获取按钮跳转URL
     * 
     * @return 跳转URL
     */
    String getButtonUrl();
}
