package org.jeecg.modules.hours.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.hours.entity.FsTaskCache;
import org.jeecg.modules.hours.mapper.FsTaskCacheMapper;
import org.jeecg.modules.hours.service.IFsTaskCacheService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 飞书项目任务缓存表实现类
 * @Author: jeecg-boot
 * @Date: 2023-08-20
 * @Version: V1.0
 */
@Slf4j
@Service
public class FsTaskCacheServiceImpl extends ServiceImpl<FsTaskCacheMapper, FsTaskCache> implements IFsTaskCacheService {

    /** 默认批处理大小 */
    private static final int DEFAULT_BATCH_SIZE = 1000;

    @Override
    public List<FsTaskCache> getTasksByProjectAndNode(String projectKey, String nodeKey) {
        LambdaQueryWrapper<FsTaskCache> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(projectKey), FsTaskCache::getProjectKey, projectKey);
        queryWrapper.eq(StringUtils.isNotBlank(nodeKey), FsTaskCache::getNodeKey, nodeKey);
        queryWrapper.orderByDesc(FsTaskCache::getUpdateTime);
        return this.list(queryWrapper);
    }

    @Override
    public FsTaskCache getTaskDetail(String projectKey, String taskKey) {
        LambdaQueryWrapper<FsTaskCache> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(projectKey), FsTaskCache::getProjectKey, projectKey);
        queryWrapper.eq(StringUtils.isNotBlank(taskKey), FsTaskCache::getTaskKey, taskKey);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<FsTaskCache> getTasksByAssignee(String assignee) {
        LambdaQueryWrapper<FsTaskCache> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(assignee), FsTaskCache::getAssignee, assignee);
        queryWrapper.orderByDesc(FsTaskCache::getUpdateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<FsTaskCache> getTasksByStatus(String status) {
        LambdaQueryWrapper<FsTaskCache> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(status), FsTaskCache::getStatus, status);
        queryWrapper.orderByDesc(FsTaskCache::getUpdateTime);
        return this.list(queryWrapper);
    }

    @Override
    public Page<FsTaskCache> pageList(Page<FsTaskCache> page, QueryWrapper<FsTaskCache> queryWrapper) {
        return this.page(page, queryWrapper);
    }

    @Override
    public Map<String, Object> statTasks(String projectKey) {
        Map<String, Object> result = new HashMap<>(8);
        
        // 查询项目下所有任务
        LambdaQueryWrapper<FsTaskCache> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(projectKey), FsTaskCache::getProjectKey, projectKey);
        List<FsTaskCache> taskList = this.list(queryWrapper);
        
        if (taskList == null || taskList.isEmpty()) {
            result.put("totalTasks", 0);
            result.put("completedTasks", 0);
            result.put("pendingTasks", 0);
            result.put("totalEstimateHours", BigDecimal.ZERO);
            return result;
        }
        
        // 统计任务总数
        int totalTasks = taskList.size();
        result.put("totalTasks", totalTasks);
        
        // 统计已完成任务数
        long completedTasks = taskList.stream()
                .filter(task -> "completed".equals(task.getStatus()) || "done".equals(task.getStatus()))
                .count();
        result.put("completedTasks", completedTasks);
        
        // 统计待处理任务数
        long pendingTasks = taskList.stream()
                .filter(task -> !"completed".equals(task.getStatus()) && !"done".equals(task.getStatus()))
                .count();
        result.put("pendingTasks", pendingTasks);
        
        // 统计预估工时总计
        BigDecimal totalEstimateHours = taskList.stream()
                .filter(task -> task.getEstimateHours() != null)
                .map(FsTaskCache::getEstimateHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("totalEstimateHours", totalEstimateHours);
        
        // 统计不同负责人的任务数
        Map<String, Long> assigneeTaskCount = taskList.stream()
                .filter(task -> StringUtils.isNotBlank(task.getAssignee()))
                .collect(Collectors.groupingBy(FsTaskCache::getAssignee, Collectors.counting()));
        result.put("assigneeTaskCount", assigneeTaskCount);
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatch(List<FsTaskCache> taskList) {
        if (taskList != null && !taskList.isEmpty()) {
            Date now = new Date();
            for (FsTaskCache task : taskList) {
                task.setUpdateTime(now);
            }
            return super.saveOrUpdateBatch(taskList);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatchByUniqueKey(List<FsTaskCache> taskList) {
        return saveOrUpdateBatchByUniqueKey(taskList, DEFAULT_BATCH_SIZE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatchByUniqueKey(List<FsTaskCache> taskList, int batchSize) {
        if (taskList == null || taskList.isEmpty()) {
            return false;
        }

        // 确保批次大小合理
        if (batchSize <= 0) {
            batchSize = DEFAULT_BATCH_SIZE;
        }

        try {
            // 1. 获取所有任务的 projectKey
            List<String> projectKeys = taskList.stream()
                .map(FsTaskCache::getProjectKey)
                .distinct()
                .collect(Collectors.toList());

            // 2. 查询数据库中已存在的记录
            LambdaQueryWrapper<FsTaskCache> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(FsTaskCache::getProjectKey, projectKeys);
            List<FsTaskCache> existingTasks = this.list(queryWrapper);

            // 3. 构建Map用于快速查找已存在的记录 (projectKey_taskKey -> task)
            Map<String, FsTaskCache> existingTaskMap = existingTasks.stream()
                .collect(Collectors.toMap(
                    task -> generateUniqueKey(task.getProjectKey(), task.getTaskKey()),
                    task -> task,
                    (v1, v2) -> v1
                ));

            // 4. 分离需要更新和需要插入的数据
            List<FsTaskCache> tasksToUpdate = new ArrayList<>();
            List<FsTaskCache> tasksToInsert = new ArrayList<>();

            Date now = new Date();
            for (FsTaskCache task : taskList) {
                task.setUpdateTime(now);
                String uniqueKey = generateUniqueKey(task.getProjectKey(), task.getTaskKey());
                if (existingTaskMap.containsKey(uniqueKey)) {
                    // 设置ID用于更新
                    task.setId(existingTaskMap.get(uniqueKey).getId());
                    tasksToUpdate.add(task);
                } else {
                    tasksToInsert.add(task);
                }
            }

            // 5. 批量更新和插入，按照指定的批次大小处理
            boolean updateResult = true;
            boolean insertResult = true;

            // 分批处理更新
            if (!tasksToUpdate.isEmpty()) {
                for (int i = 0; i < tasksToUpdate.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, tasksToUpdate.size());
                    List<FsTaskCache> batch = tasksToUpdate.subList(i, endIndex);
                    updateResult = updateResult && this.updateBatchById(batch);
                    log.info("处理更新批次: {}/{}, 大小: {}, 结果: {}", 
                        (i / batchSize) + 1, 
                        (tasksToUpdate.size() + batchSize - 1) / batchSize,
                        batch.size(),
                        updateResult);
                }
            }

            // 分批处理插入
            if (!tasksToInsert.isEmpty()) {
                for (int i = 0; i < tasksToInsert.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, tasksToInsert.size());
                    List<FsTaskCache> batch = tasksToInsert.subList(i, endIndex);
                    insertResult = insertResult && this.saveBatch(batch);
                    log.info("处理插入批次: {}/{}, 大小: {}, 结果: {}", 
                        (i / batchSize) + 1, 
                        (tasksToInsert.size() + batchSize - 1) / batchSize,
                        batch.size(),
                        insertResult);
                }
            }

            return updateResult && insertResult;
        } catch (Exception e) {
            log.error("批量保存或更新任务缓存异常", e);
            throw e;
        }
    }

    /**
     * 生成唯一键
     */
    private String generateUniqueKey(String projectKey, String taskKey) {
        return projectKey + "_" + taskKey;
    }

    @Override
    public List<FsTaskCache> getTasksByKeys(String projectKey, String workItemKey, String instanceKey, String nodeKey) {
        LambdaQueryWrapper<FsTaskCache> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        queryWrapper.eq(StringUtils.isNotBlank(projectKey), FsTaskCache::getProjectKey, projectKey)
                   .eq(StringUtils.isNotBlank(workItemKey), FsTaskCache::getWorkItemKey, workItemKey)
                   .eq(StringUtils.isNotBlank(instanceKey), FsTaskCache::getInstanceKey, instanceKey)
                   .eq(StringUtils.isNotBlank(nodeKey), FsTaskCache::getNodeKey, nodeKey)
                   .orderByDesc(FsTaskCache::getUpdateTime);
        
        return this.list(queryWrapper);
    }
} 