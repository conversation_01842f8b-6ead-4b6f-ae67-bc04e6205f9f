package org.jeecg.modules.hours.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.modules.hours.vo.ProjectHours;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 工时记录表
 * @Author: jeecg-boot
 * @Date: 2023-07-01
 * @Version: V1.0
 */
@Data
@TableName("hour_record")
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "hour_record对象", description = "工时记录表")
public class HourRecord implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.ASSIGN_ID)
	@ApiModelProperty(value = "主键")
	private String id;

	/**
	 * 用户ID
	 */
	@Excel(name = "用户ID", width = 15)
	@ApiModelProperty(value = "用户ID")
	private String userId;

	/**
	 * 用户姓名
	 */
	@Excel(name = "用户姓名", width = 15)
	@ApiModelProperty(value = "用户姓名")
	private String userName;

	/**
	 * 工作月份(格式：yyyy-MM)
	 */
	@Excel(name = "工作月份", width = 15)
	@ApiModelProperty(value = "工作月份(格式：yyyy-MM)")
	private String workMonth;

	/**
	 * 工作周期开始日期
	 */
	@Excel(name = "工作周期开始日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "工作周期开始日期")
	private Date workPeriodStart;

	/**
	 * 工作周期结束日期
	 */
	@Excel(name = "工作周期结束日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "工作周期结束日期")
	private Date workPeriodEnd;

	/**
	 * 总工时(小时)
	 */
	@Excel(name = "总工时", width = 15)
	@ApiModelProperty(value = "总工时(小时)")
	private BigDecimal hours;

	/**
	 * 状态(0:待提交, 1:审批中, 2:已通过, 3:已驳回)
	 */
	@Excel(name = "状态", width = 15)
	@ApiModelProperty(value = "状态(0:待提交, 1:审批中, 2:已通过, 3:已驳回)")
	private Integer status;

	@TableField(exist = false)
	private String statusName;

	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createBy;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	@Dict(dictTable = "fs_user", dicText = "name", dicCode = "user_key")
	private String updateBy;

	/**
	 * 项目工时汇总（非数据库字段）
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "项目工时汇总")
	private List<ProjectHours> projects;

	/**
	 * 描述
	 */
	@ApiModelProperty(value = "描述")
	private String description;

	@TableField(exist = false)
	@ApiModelProperty(value = "审批批次申请ID")
	private String applyId;
	
}