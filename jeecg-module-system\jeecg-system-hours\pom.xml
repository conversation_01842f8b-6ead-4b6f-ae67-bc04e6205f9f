<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>org.jeecgframework.boot</groupId>
		<artifactId>jeecg-module-system</artifactId>
		<version>3.7.3</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>jeecg-system-hours</artifactId>

	<dependencies>
		<dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-base-core</artifactId>
        </dependency>
		<dependency>
			<groupId>org.jeecgframework.boot</groupId>
			<artifactId>jeecg-system-local-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jeecgframework.boot</groupId>
			<artifactId>hibernate-re</artifactId>
		</dependency>

		<!-- 积木BI
		<dependency>
			<groupId>org.jeecgframework.jimureport</groupId>
			<artifactId>jimubi-spring-boot-starter</artifactId>
		</dependency> -->
		
		<dependency>
			<artifactId>httpclient</artifactId>
			<groupId>org.apache.httpcomponents</groupId>
			<version>4.5.13</version>
		</dependency>
		<dependency>
			<artifactId>httpmime</artifactId>
			<groupId>org.apache.httpcomponents</groupId>
			<version>4.5.13</version>
		</dependency>
		<dependency>
			<artifactId>logback-classic</artifactId>
			<groupId>ch.qos.logback</groupId>
			<version>1.2.11</version>
		</dependency>

		<!-- 飞书项目SDK -->
		<dependency>
            <groupId>com.larksuite.project</groupId>
            <artifactId>oapi-sdk</artifactId>
            <version>1.0.15</version>
        </dependency>
		<dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-module-feishu</artifactId>
			<version>3.7.3</version>
        </dependency>

		
	</dependencies>
	
</project>
