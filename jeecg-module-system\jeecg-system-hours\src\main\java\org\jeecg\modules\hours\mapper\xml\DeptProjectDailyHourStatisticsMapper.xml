<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.DeptProjectDailyHourStatisticsMapper">

    <!-- 生成月度统计数据 -->
    <insert id="generateMonthlyStatistics">
        INSERT INTO dept_project_daily_hour_statistics (
            id, stat_month, department_id, department_name, project_id, project_name, project_code,
            total_hours, daily_hours_json, is_holiday, create_time, update_time
        )
        SELECT 
            REPLACE(UUID(), '-', '') as id,
            #{statMonth} as stat_month,
            department_id,
            department_name,
            project_id,
            project_name,
            project_code,
            SUM(total_hours) as total_hours,
            CONCAT('{',
                GROUP_CONCAT(
                    DISTINCT CONCAT('"', DAY(stat_date), '": ', IFNULL(total_hours, 0))
                    ORDER BY DAY(stat_date)
                    SEPARATOR ', '
                ),
            '}') as daily_hours_json,
            #{isHoliday} as is_holiday,
            NOW() as create_time,
            NOW() as update_time
        FROM dept_project_hour_statistics
        WHERE DATE_FORMAT(stat_date, '%Y-%m') = #{statMonth}
          AND stat_period = 'daily'
          <if test="isHoliday != null">
          AND is_holiday = #{isHoliday}
          </if>
        GROUP BY department_id, department_name, project_id, project_name, project_code
        HAVING total_hours > 0
    </insert>

    <!-- 删除指定月份的统计数据 -->
    <delete id="deleteByStatMonth">
        DELETE FROM dept_project_daily_hour_statistics 
        WHERE stat_month = #{statMonth}
        <if test="isHoliday != null">
        AND is_holiday = #{isHoliday}
        </if>
    </delete>

    <!-- 获取日期列信息 -->
    <select id="getDayColumns" resultType="org.jeecg.modules.hours.vo.DeptProjectDailyHourReportResult$DayColumnVO">
        <![CDATA[
        WITH RECURSIVE date_series AS (
            SELECT 
                DATE(CONCAT(#{startDate}, '-01')) as date_val,
                LAST_DAY(DATE(CONCAT(#{startDate}, '-01'))) as last_day
            UNION ALL
            SELECT 
                DATE_ADD(date_val, INTERVAL 1 DAY),
                last_day
            FROM date_series
            WHERE date_val < last_day
        )
        SELECT 
            CONCAT('day_', LPAD(DAY(date_val), 2, '0')) as columnKey,
            CAST(DAY(date_val) AS CHAR) as dayName,
            DATE_FORMAT(date_val, '%Y-%m-%d') as fullDate,
            CASE 
                WHEN DAYOFWEEK(date_val) IN (1, 7) THEN 1 
                ELSE 0 
            END as isHoliday
        FROM date_series
        ]]>
        <if test="isHoliday != null and isHoliday == 0">
        WHERE DAYOFWEEK(date_val) NOT IN (1, 7)
        </if>
        ORDER BY date_val
    </select>

    <!-- 获取部门项目每日工时分布报表数据 -->
    <select id="getDeptProjectDailyHourReport" resultType="org.jeecg.modules.hours.vo.DeptProjectDailyHourReportVO">
        SELECT 
            department_id as departmentId,
            department_name as departmentName,
            project_id as projectId,
            project_name as projectName,
            project_code as projectCode,
            total_hours as totalHours,
            daily_hours_json as dailyHoursJson,
            0 as isTotalRow
        FROM dept_project_daily_hour_statistics
        WHERE stat_month = DATE_FORMAT(#{startDate}, '%Y-%m')
        <if test="departmentIds != null and departmentIds.size > 0">
        AND department_id IN 
        <foreach collection="departmentIds" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
        </if>
        <if test="isHoliday != null">
        AND is_holiday = #{isHoliday}
        </if>
        <if test="projectKeyword != null and projectKeyword != ''">
        AND (project_name LIKE CONCAT('%', #{projectKeyword}, '%') 
             OR project_code LIKE CONCAT('%', #{projectKeyword}, '%'))
        </if>
        ORDER BY department_name, project_name
    </select>

    <!-- 获取总计行数据 -->
    <select id="getTotalRow" resultType="org.jeecg.modules.hours.vo.DeptProjectDailyHourReportVO">
        SELECT 
            'total' as departmentId,
            '总计' as departmentName,
            'total' as projectId,
            '' as projectName,
            '' as projectCode,
            SUM(total_hours) as totalHours,
            CONCAT('{',
                GROUP_CONCAT(
                    DISTINCT CONCAT('"', day_key, '": ', day_total)
                    ORDER BY CAST(day_key AS UNSIGNED)
                    SEPARATOR ', '
                ),
            '}') as dailyHoursJson,
            1 as isTotalRow
        FROM (
            SELECT 
                total_hours,
                day_data.day_key,
                SUM(CAST(day_data.day_value AS DECIMAL(10,2))) as day_total
            FROM dept_project_daily_hour_statistics d
            CROSS JOIN JSON_TABLE(
                d.daily_hours_json,
                '$.*' COLUMNS(
                    day_key VARCHAR(10) PATH '$',
                    day_value DECIMAL(10,2) PATH '$'
                )
            ) as day_data
            WHERE d.stat_month = DATE_FORMAT(#{startDate}, '%Y-%m')
            <if test="departmentIds != null and departmentIds.size > 0">
            AND d.department_id IN 
            <foreach collection="departmentIds" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
            </if>
            <if test="isHoliday != null">
            AND d.is_holiday = #{isHoliday}
            </if>
            <if test="projectKeyword != null and projectKeyword != ''">
            AND (d.project_name LIKE CONCAT('%', #{projectKeyword}, '%') 
                 OR d.project_code LIKE CONCAT('%', #{projectKeyword}, '%'))
            </if>
            GROUP BY day_data.day_key
        ) summary
    </select>

    <!-- 获取统计摘要数据 -->
    <select id="getSummaryData" resultType="org.jeecg.modules.hours.vo.DeptProjectDailyHourReportResult$ReportSummaryVO">
        SELECT 
            CONCAT(DATE_FORMAT(#{startDate}, '%Y-%m'), ' ~ ', DATE_FORMAT(#{endDate}, '%Y-%m')) as dateRange,
            SUM(total_hours) as totalHours,
            COUNT(DISTINCT department_id) as deptCount,
            COUNT(DISTINCT project_id) as projectCount,
            0 as userCount
        FROM dept_project_daily_hour_statistics
        WHERE stat_month = DATE_FORMAT(#{startDate}, '%Y-%m')
        <if test="departmentIds != null and departmentIds.size > 0">
        AND department_id IN 
        <foreach collection="departmentIds" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
        </if>
        <if test="isHoliday != null">
        AND is_holiday = #{isHoliday}
        </if>
        <if test="projectKeyword != null and projectKeyword != ''">
        AND (project_name LIKE CONCAT('%', #{projectKeyword}, '%') 
             OR project_code LIKE CONCAT('%', #{projectKeyword}, '%'))
        </if>
    </select>

</mapper>