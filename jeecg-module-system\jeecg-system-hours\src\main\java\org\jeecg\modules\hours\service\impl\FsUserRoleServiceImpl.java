package org.jeecg.modules.hours.service.impl;

import org.jeecg.modules.hours.entity.FsUserRole;
import org.jeecg.modules.hours.entity.FsRole;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.mapper.FsUserRoleMapper;
import org.jeecg.modules.hours.service.IFsUserRoleService;
import org.jeecg.modules.hours.service.IFsRoleService;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.utils.UserInfoUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import java.util.Set;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.hours.service.IFsUserService;
import java.util.Collections;

/**
 * @Description: 用户角色表
 * @Author: jeecg-boot
 * @Date:   2024-05-28
 * @Version: V1.0
 */
@Slf4j
@Service
public class FsUserRoleServiceImpl extends ServiceImpl<FsUserRoleMapper, FsUserRole> implements IFsUserRoleService {

	@Lazy
    @Autowired
    private IFsRoleService roleService;
    
    @Autowired
    private CommonAPI commonApi;
    
    @Autowired(required = false)
    private RedisUtil redisUtil;
    
    @Lazy
    @Autowired
    private IFeishuAuthService feishuAuthService;
    @Lazy
    @Autowired
    private UserInfoUtils userInfoUtils;
    @Lazy
    @Autowired
    private IFsUserService fsUserService;
    
    /**
     * 获取当前登录用户信息
     */
    private String getCurrentUserId() {
        try {
            // 使用UserInfoUtils获取系统内部用户ID
            String userId = userInfoUtils.getUserSystemId();
            if (userId != null && !userId.isEmpty()) {
                return userId;
            }
            
            // 备用方案：如果UserInfoUtils获取失败，尝试从飞书认证服务获取
            String userKey = userInfoUtils.getCurrentUserKey(); // 使用UserInfoUtils获取用户标识
            if (userKey != null && !userKey.isEmpty() && !"system".equals(userKey)) {
                // 将飞书userKey转换为系统用户ID
                FsUser user = getUserByUserKey(userKey);
                if (user != null) {
                    return user.getId();
                }
            }
        } catch (Exception e) {
            log.error("获取当前登录用户信息失败", e);
        }
        return "system"; // 默认为系统用户
    }
    
    /**
     * 根据飞书userKey获取系统用户
     */
    private FsUser getUserByUserKey(String userKey) {
        try {
            LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUser::getUserKey, userKey);
            return fsUserService.getOne(queryWrapper);
        } catch (Exception e) {
            log.error("根据userKey获取用户失败: {}", userKey, e);
            return null;
        }
    }

    @Override
    public boolean assignRole(String userId, String roleId) {
        try {
            // 检查用户是否已有该角色
            LambdaQueryWrapper<FsUserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUserRole::getUserId, userId);
            queryWrapper.eq(FsUserRole::getRoleId, roleId);
            
            // 如果已存在用户角色关系，则不再重复添加
            if (this.count(queryWrapper) > 0) {
                return true;
            }
            
            // 获取当前登录用户作为创建人
            String currentUser = getCurrentUserId();
            
            // 创建新的用户角色关系
            FsUserRole userRole = new FsUserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);
            userRole.setCreatedAt(new Date());
            userRole.setCreateBy(currentUser); // 设置创建人
            
            return this.save(userRole);
        } catch (Exception e) {
            log.error("为用户分配角色失败: userId={}, roleId={}", userId, roleId, e);
            return false;
        }
    }
    
    @Override
	public boolean batchAssignRole(List<String> userIds, String roleId) {
		try {
			if (CollectionUtils.isEmpty(userIds) || StringUtils.isBlank(roleId)) {
				log.warn("批量分配角色参数为空");
				return false;
			}
			
			// 查询已有角色关系
			LambdaQueryWrapper<FsUserRole> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.in(FsUserRole::getUserId, userIds);
			queryWrapper.eq(FsUserRole::getRoleId, roleId);
			List<FsUserRole> existingRoles = this.list(queryWrapper);
			
			// 过滤掉已存在的用户角色关系
			Set<String> existingUserIds = existingRoles.stream()
				.map(FsUserRole::getUserId)
				.collect(Collectors.toSet());
			
			List<FsUserRole> toAddRoles = userIds.stream()
				.filter(userId -> !existingUserIds.contains(userId))
				.map(userId -> {
					FsUserRole userRole = new FsUserRole();
					userRole.setUserId(userId);
					userRole.setRoleId(roleId);
					userRole.setCreateBy("system");
					userRole.setCreatedAt(Calendar.getInstance().getTime());
					return userRole;
				})
				.collect(Collectors.toList());
			
			// 批量保存
			if (!toAddRoles.isEmpty()) {
				return this.saveBatch(toAddRoles);
			}
			return true;
		} catch (Exception e) {
			log.error("批量分配角色失败", e);
			return false;
		}
	}

	@Override
    public boolean removeRole(String userId, String roleId) {
        try {
            LambdaQueryWrapper<FsUserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUserRole::getUserId, userId);
            queryWrapper.eq(FsUserRole::getRoleId, roleId);
            
            return this.remove(queryWrapper);
        } catch (Exception e) {
            log.error("移除用户角色失败: userId={}, roleId={}", userId, roleId, e);
            return false;
        }
    }
    
    @Override
    public List<FsRole> getUserRoles(String userId) {
        try {
            // 查询用户角色关系
            LambdaQueryWrapper<FsUserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUserRole::getUserId, userId);
            List<FsUserRole> userRoles = this.list(queryWrapper);
            
            if (userRoles == null || userRoles.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 获取角色ID列表
            List<String> roleIds = userRoles.stream()
                .map(FsUserRole::getRoleId)
                .collect(Collectors.toList());
            
            // 查询角色详情
            return roleService.listByIds(roleIds);
        } catch (Exception e) {
            log.error("获取用户角色列表失败: userId={}", userId, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public boolean hasRole(String userId, String roleCode) {
        try {
            // 根据角色编码获取角色
            FsRole role = roleService.getRoleByCode(roleCode);
            if (role == null) {
                log.warn("角色不存在: roleCode={}", roleCode);
                return false;
            }
            
            // 查询用户是否有该角色
            LambdaQueryWrapper<FsUserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUserRole::getUserId, userId);
            queryWrapper.eq(FsUserRole::getRoleId, role.getId());
            
            return this.count(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("查询用户角色失败: userId={}, roleCode={}", userId, roleCode, e);
            return false;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRoles(String userId, List<String> roleIds) {
        try {
            // 删除用户现有的所有角色
            LambdaQueryWrapper<FsUserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUserRole::getUserId, userId);
            this.remove(queryWrapper);
            
            // 批量添加新角色
            if (roleIds != null && !roleIds.isEmpty()) {
                List<FsUserRole> userRoles = new ArrayList<>();
                Date now = new Date();
                String currentUser = getCurrentUserId(); // 获取当前用户
                
                for (String roleId : roleIds) {
                    FsUserRole userRole = new FsUserRole();
                    userRole.setUserId(userId);
                    userRole.setRoleId(roleId);
                    userRole.setCreatedAt(now);
                    userRole.setCreateBy(currentUser); // 设置创建人
                    userRoles.add(userRole);
                }
                
                return this.saveBatch(userRoles);
            }
            
            return true;
        } catch (Exception e) {
            log.error("批量分配用户角色失败: userId={}", userId, e);
            throw e; // 事务回滚
        }
    }
    
    @Override
    public List<FsUserRole> getUserRolesByRoleId(String roleId) {
        try {
            // 查询指定角色的所有用户-角色关系
            LambdaQueryWrapper<FsUserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUserRole::getRoleId, roleId);
            
            // 按创建时间倒序排序，确保最新的记录优先
            queryWrapper.orderByDesc(FsUserRole::getCreatedAt);
            
            return this.list(queryWrapper);
        } catch (Exception e) {
            log.error("获取角色用户关系失败: roleId={}", roleId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查当前用户是否具有指定角色
     * @param role 角色名称
     * @return 是否具有该角色
     */
    @Override
    public boolean hasRole(String role) {
        // 使用UserInfoUtils获取用户标识
        String userKey = userInfoUtils.getCurrentUserKey();
        if (userKey == null) {
            log.warn("无法获取有效的用户标识");
            return false;
        }
        
        try {
            // 查询用户角色
            LambdaQueryWrapper<FsUserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUserRole::getUserId, userKey)
                       .eq(FsUserRole::getRoleId, role);
            
            return baseMapper.selectCount(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查用户[{}]角色[{}]失败", userKey, role, e);
            return false;
        }
    }
    
    /**
     * 获取当前用户的角色列表
     * @return 角色列表
     */
    @Override
    public List<String> getCurrentUserRoles() {
        // 获取当前用户标识
        String userKey = userInfoUtils.getCurrentUserKey();
        if (userKey == null) {
            log.warn("未获取到有效的用户信息");
            return new ArrayList<>();
        }
        
        try {
            // 查询用户角色
            LambdaQueryWrapper<FsUserRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FsUserRole::getUserId, userKey);
            
            List<FsUserRole> roles = baseMapper.selectList(queryWrapper);
            return roles.stream()
                       .map(FsUserRole::getRoleId)
                       .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户[{}]角色列表失败", userKey, e);
            return new ArrayList<>();
        }
    }
}