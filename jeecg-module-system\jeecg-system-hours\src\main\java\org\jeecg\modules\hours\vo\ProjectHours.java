package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 项目工时汇总VO
 */
@Data
@ApiModel(value = "项目工时汇总", description = "项目工时汇总")
public class ProjectHours implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    private BigDecimal hours;
} 