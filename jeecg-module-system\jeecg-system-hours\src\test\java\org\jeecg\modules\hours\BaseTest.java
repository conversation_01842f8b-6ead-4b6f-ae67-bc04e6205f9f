package org.jeecg.modules.hours;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * 测试基类
 * 统一配置开发环境，避免启动不必要的组件
 */
@SpringBootTest(properties = {
    "reminder.job.enabled=false",           // 禁用提醒任务初始化器
    "spring.quartz.auto-startup=false",     // 禁用Quartz自动启动
    "spring.task.scheduling.pool.size=0",   // 禁用任务调度线程池
    "logging.level.org.jeecg=WARN",         // 减少日志输出
    "logging.level.com.baomidou=WARN"       // 减少MyBatis-Plus日志
})
@ActiveProfiles("dev")   // 激活dev配置文件
@Transactional           // 测试事务回滚
public abstract class BaseTest {
    
    // 可以在这里添加通用的测试工具方法
    
    /**
     * 等待一段时间，用于异步操作测试
     */
    protected void waitFor(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 打印测试分隔线
     */
    protected void printTestSeparator(String testName) {
        String separator = "==================================================";
        System.out.println(separator);
        System.out.println("测试: " + testName);
        System.out.println(separator);
    }
}
