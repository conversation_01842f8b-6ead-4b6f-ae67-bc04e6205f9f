package org.jeecg.modules.hours.controller;

import java.util.Arrays;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.hours.entity.FsSysReminderConfig;
import org.jeecg.modules.hours.service.IFsSysReminderConfigService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.jeecg.modules.hours.utils.UserInfoUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 登记提醒配置表
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Api(tags="登记提醒配置管理")
@RestController
@RequestMapping("/hapi/hours/fsSysReminderConfig")
@Slf4j
public class FsSysReminderConfigController extends JeecgController<FsSysReminderConfig, IFsSysReminderConfigService> {

    @Autowired
    private IFsSysReminderConfigService fsSysReminderConfigService;
    @Lazy
    @Autowired
    private UserInfoUtils userInfoUtils;

    /**
     * 分页列表查询
     *
     * @param reminderConfig 查询条件
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param req 请求对象
     * @return 分页结果
     */
    @AutoLog(value = "登记提醒配置-分页列表查询")
    @ApiOperation(value="登记提醒配置-分页列表查询", notes="登记提醒配置-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<FsSysReminderConfig>> queryPageList(FsSysReminderConfig reminderConfig,
                                                           @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                           @RequestParam(name="pageSize", defaultValue="100") Integer pageSize,
                                                           HttpServletRequest req) {
        // 获取工作空间标识
        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
        if (StringUtils.isBlank(projectKey)) {
            return Result.error("未提供有效的工作空间标识");
        }

        Page<FsSysReminderConfig> page = new Page<>(pageNo, pageSize);
        IPage<FsSysReminderConfig> pageList = fsSysReminderConfigService.queryPageList(page, reminderConfig, projectKey);
        
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param reminderConfig 提醒配置
     * @return 操作结果
     */
    @AutoLog(value = "登记提醒配置-添加")
    @ApiOperation(value="登记提醒配置-添加", notes="登记提醒配置-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody FsSysReminderConfig reminderConfig) {
        try {
            // 获取工作空间标识
            String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
            if (StringUtils.isBlank(projectKey)) {
                return Result.error("未提供有效的工作空间标识");
            }

            // 获取当前用户
            String createBy = userInfoUtils.getUserSystemId();
            
            boolean success = fsSysReminderConfigService.addReminderConfig(reminderConfig, projectKey, createBy);
            if (success) {
                return Result.OK("添加成功！");
            } else {
                return Result.error("添加失败！");
            }
        } catch (Exception e) {
            log.error("添加登记提醒配置失败", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param reminderConfig 提醒配置
     * @return 操作结果
     */
    @AutoLog(value = "登记提醒配置-编辑")
    @ApiOperation(value="登记提醒配置-编辑", notes="登记提醒配置-编辑")
    @PutMapping(value = "/edit")
    public Result<String> edit(@RequestBody FsSysReminderConfig reminderConfig) {
        try {
            // 获取当前用户
            String updateBy = userInfoUtils.getUserSystemId();
            
            boolean success = fsSysReminderConfigService.updateReminderConfig(reminderConfig, updateBy);
            if (success) {
                return Result.OK("编辑成功！");
            } else {
                return Result.error("编辑失败！");
            }
        } catch (Exception e) {
            log.error("编辑登记提醒配置失败", e);
            return Result.error("编辑失败：" + e.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id 配置ID
     * @return 操作结果
     */
    @AutoLog(value = "登记提醒配置-通过id删除")
    @ApiOperation(value="登记提醒配置-通过id删除", notes="登记提醒配置-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id",required=true) Long id) {
        try {
            // 获取当前用户
            String deleteBy = userInfoUtils.getUserSystemId();
            
            boolean success = fsSysReminderConfigService.deleteReminderConfig(id, deleteBy);
            if (success) {
                return Result.OK("删除成功!");
            } else {
                return Result.error("删除失败!");
            }
        } catch (Exception e) {
            log.error("删除登记提醒配置失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除
     *
     * @param ids 配置ID列表，逗号分隔
     * @return 操作结果
     */
    @AutoLog(value = "登记提醒配置-批量删除")
    @ApiOperation(value="登记提醒配置-批量删除", notes="登记提醒配置-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        try {
            if (StringUtils.isBlank(ids)) {
                return Result.error("参数不能为空");
            }
            
            List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .collect(java.util.stream.Collectors.toList());
            // 获取当前用户
            String deleteBy = userInfoUtils.getUserSystemId();

            boolean success = fsSysReminderConfigService.batchDeleteReminderConfig(idList, deleteBy);
            if (success) {
                return Result.OK("批量删除成功!");
            } else {
                return Result.error("批量删除失败!");
            }
        } catch (Exception e) {
            log.error("批量删除登记提醒配置失败", e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 通过id查询
     *
     * @param id 配置ID
     * @return 查询结果
     */
    @AutoLog(value = "登记提醒配置-通过id查询")
    @ApiOperation(value="登记提醒配置-通过id查询", notes="登记提醒配置-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<FsSysReminderConfig> queryById(@RequestParam(name="id",required=true) Long id) {
        FsSysReminderConfig reminderConfig = fsSysReminderConfigService.getById(id);
        if (reminderConfig == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(reminderConfig);
    }

    /**
     * 切换状态
     *
     * @param id 配置ID
     * @param status 状态
     * @return 操作结果
     */
    @AutoLog(value = "登记提醒配置-切换状态")
    @ApiOperation(value="登记提醒配置-切换状态", notes="登记提醒配置-切换状态")
    @PutMapping(value = "/toggleStatus")
    public Result<String> toggleStatus(@RequestParam(name="id",required=true) Long id,
                                      @RequestParam(name="status",required=true) String status) {
        try {
            // 获取当前用户
            String updateBy = userInfoUtils.getUserSystemId();
            
            boolean success = fsSysReminderConfigService.toggleStatus(id, status, updateBy);
            if (success) {
                return Result.OK("状态更新成功!");
            } else {
                return Result.error("状态更新失败!");
            }
        } catch (Exception e) {
            log.error("切换登记提醒配置状态失败", e);
            return Result.error("状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 批量切换状态
     *
     * @param ids 配置ID列表，逗号分隔
     * @param status 状态
     * @return 操作结果
     */
    @AutoLog(value = "登记提醒配置-批量切换状态")
    @ApiOperation(value="登记提醒配置-批量切换状态", notes="登记提醒配置-批量切换状态")
    @PutMapping(value = "/batchToggleStatus")
    public Result<String> batchToggleStatus(@RequestParam(name="ids",required=true) String ids,
                                           @RequestParam(name="status",required=true) String status) {
        try {
            if (StringUtils.isBlank(ids)) {
                return Result.error("参数不能为空");
            }
            
            List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .collect(java.util.stream.Collectors.toList());
            // 获取当前用户
            String updateBy = userInfoUtils.getUserSystemId();

            boolean success = fsSysReminderConfigService.batchToggleStatus(idList, status, updateBy);
            if (success) {
                return Result.OK("批量状态更新成功!");
            } else {
                return Result.error("批量状态更新失败!");
            }
        } catch (Exception e) {
            log.error("批量切换登记提醒配置状态失败", e);
            return Result.error("批量状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取提醒配置统计信息
     *
     * @return 统计信息
     */
    @AutoLog(value = "登记提醒配置-获取统计信息")
    @ApiOperation(value="登记提醒配置-获取统计信息", notes="登记提醒配置-获取统计信息")
    @GetMapping(value = "/statistics")
    public Result<?> getStatistics() {
        try {
            // 获取工作空间标识
            String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
            if (StringUtils.isBlank(projectKey)) {
                return Result.error("未提供有效的工作空间标识");
            }

            int totalCount = fsSysReminderConfigService.countByProjectKey(projectKey);
            int enabledCount = fsSysReminderConfigService.queryByProjectKeyAndStatus(projectKey, "启用").size();
            int disabledCount = fsSysReminderConfigService.queryByProjectKeyAndStatus(projectKey, "暂停").size();
            
            return Result.OK(new Object() {
                public final int total = totalCount;
                public final int enabled = enabledCount;
                public final int disabled = disabledCount;
            });
        } catch (Exception e) {
            log.error("获取登记提醒配置统计信息失败", e);
            return Result.error("获取统计信息失败：" + e.getMessage());
        }
    }
}
