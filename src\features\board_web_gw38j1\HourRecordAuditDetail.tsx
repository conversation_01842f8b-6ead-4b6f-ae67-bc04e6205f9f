import React, { useState, useEffect, useCallback } from 'react';
import { DatePicker } from '@douyinfe/semi-ui';
import { Toast,Table,Banner, Button, Input,TextArea, InputNumber, Typography, Select, Modal, Form, Timeline, Tag } from '@douyinfe/semi-ui';
import WorkHourRulesModal from './components/WorkHourRulesModal';
import { IconPlus } from '@douyinfe/semi-icons';
const { Title } = Typography;
import { get, post } from '../../utils/request';
import { getUserInfo } from '../../utils/auth';
import { fmtDate } from '../../utils/date';
import { getWeekRange } from '../../utils/weekUtils';
import './hourRecordForm.css'; // 导入自定义样式表

import { WorkDetail } from './types';

// 工时登记表单属性接口
export interface HourRecordAuditDetailProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (data: any) => void;
  onSubmit: (data: any) => void;
  initialData?: {
    workDate?: Date;
    worker?: string;
    workDetails?: WorkDetail[];
    workContent?: (string | number)[];
    projectKey?: string;
    recordId?: string | number; // 添加记录ID，用于编辑时标识记录
    projectName?: string;
    instanceName?: string;
    nodeName?: string;
    taskName?: string;
    applyId?: number; // 添加申请批次ID
  };
  isAudit?: boolean; // 标识当前是否为编辑模式
  readOnly?: boolean;
}

// mock 工时明细数据
const initialWorkDetails: WorkDetail[] = [
  {
    key: '',
    type: '',
    typeInfo: '',
    businessLevel1: '',
    businessLevel2: '',
    status: '正常',
    hours: 0,
    description: '',
    workItemId: '',
    workItemName: '',
    instanceId: '',
    instanceName: '',
    nodeId: '',
    nodeName: '',
    taskId: '',
    taskName: '',
    children: []
  }
];

// 更新接口返回数据的类型定义
interface AvailableDateVO {
  date: string;
  week: string;
  attendanceHours: number;
  isWorkday: boolean;
}

// 添加 WeekRange 接口定义
interface WeekRange {
  start: Date;
  end: Date;
}

// 工具函数：将日期字符串转为 MM.DD 格式
function toMMDD(dateStr: string): string {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  const mm = (d.getMonth() + 1).toString().padStart(2, '0');
  const dd = d.getDate().toString().padStart(2, '0');
  return `${mm}.${dd}`;
}

const HourRecordAuditDetail: React.FC<HourRecordAuditDetailProps> = ({
  visible,
  onCancel,
  onSave = () => { },
  onSubmit = () => { },
  initialData = {},
  isAudit = false,
  readOnly = false
}) => {
  // 获取当前登录用户
  const currentUser = getUserInfo();

  // 兜底：无数据时不渲染
  if (!initialData 
//    || !initialData.applyId
  ) {
    console.log('兜底：无数据时不渲染,initialData=', initialData,';!initialData=', !initialData,';!initialData.applyId=', !initialData.applyId);
    return null;
  }

  // 状态定义
  const [workDate, setWorkDate] = useState<Date>(initialData.workDate || new Date());
  const [worker, setWorker] = useState<string>(initialData.worker || (currentUser?.name || ''));
  const [workDetails, setWorkDetails] = useState<WorkDetail[]>(
    initialData.workDetails || initialWorkDetails
  );
  const [recordId] = useState<string | number | undefined>(initialData.recordId); // 记录ID，用于编辑时
  // 工时登记规则说明模态窗状态
  const [rulesModalVisible, setRulesModalVisible] = useState(false);
  
  // 从BatchRecordForm迁移的状态
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [submitting, setSubmitting] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [projectOptions, setProjectOptions] = useState<any[]>([]);
  const [selectedProjectIds, setSelectedProjectIds] = useState<string[]>([]);
  // 备注内容状态
  const [remark, setRemark] = useState('');

  const [approvalFlow, setApprovalFlow] = useState<any[]>([]);
  const [approvalLoading, setApprovalLoading] = useState(false);
  const [approveModalVisible, setApproveModalVisible] = useState(false);
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [approveComment, setApproveComment] = useState('');
  const [rejectReason, setRejectReason] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  // 从BatchRecordForm迁移的方法
  const calculateNetHours = (item: any) => {
    const totalHours = item.hours || 0;
    const childrenHours = sumChildrenHours(item);
    return Math.max(0, totalHours - childrenHours);
  };

  const sumChildrenHours = (item: any) => {
    if (!item.children || item.children.length === 0) return 0;
    return item.children.reduce((sum: number, child: any) => sum + (child.hours || 0), 0);
  };

  const validateBatchRecords = async (records: any[]) => {
    try {
      // 将数组转换为 Map 格式
      const recordsMap = records.reduce((acc, record, index) => {
        acc[index.toString()] = record;
        return acc;
      }, {} as Record<string, any>);

      const response = await post<any>('/hours/hourRecord/validate', recordsMap, { skipErrorHandler: true });
      if (!response.success) {
        Toast.error(response.message || '批量工时记录校验失败，请检查输入');
        return false;
      }
      return true;
    } catch (error: any) {
      console.error('校验批量工时记录失败:', error);
      // 将错误消息改为通用提示，以区分来源
      const errorMessage = '批量工时校验请求发生网络或未知错误，请稍后重试';
      Toast.error(errorMessage);
      return false;
    }
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    //loadTableData(currentWeek.start);
  };

  // 处理每页显示数量变化
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // 重置为第一页
    //loadTableData(currentWeek.start);
  };

  const submitBatchRecords = async (records: any[]) => {
    if (submitting) return false;
    
    setSubmitting(true);
    try {
      // 转换为净工时记录
      const netHoursRecords = records.map(record => ({
        ...record,
        hours: calculateNetHours(record)
      })).filter(record => record.hours > 0);

      if (netHoursRecords.length === 0) {
        Toast.warning('没有有效的工时记录需要提交');
        return false;
      }

      // 先校验
      const isValid = await validateBatchRecords(netHoursRecords);
      if (!isValid) {
        return false;
      }

      // 提交记录
      const response = await post<any>('/hours/hourRecord/batchSave', netHoursRecords, { skipErrorHandler: true });
      if (response.success) {
        Toast.info('批量工时记录已成功提交');
        return true;
      } else {
        Toast.warning(response.message || '批量工时记录提交失败');
        return false;
      }
    } catch (error) {
      console.error('提交批量工时记录失败:', error);
      Toast.error('批量工时记录提交失败，请稍后重试');
      return false;
    } finally {
      setSubmitting(false);
    }
  };

  // 组装 HourRecordBatchSaveVO 参数
  function buildHourRecordBatchSaveVO(currentMonth: Date, currentWeek: WeekRange, tableData: any[], dynamicColumns: any[], remark: string) {
    const workMonth = `${currentMonth.getFullYear()}-${(currentMonth.getMonth() + 1).toString().padStart(2, '0')}`;
    const startDate = fmtDate(currentWeek.start);
    const endDate = fmtDate(currentWeek.end);
    const dateKeys = dynamicColumns.map((col: any) => col.dataIndex);
    const projects = tableData.map(row => ({
      
      projectId: row.key,
      projectName: row.workItemName,
      description: row.description || '',
      details: dateKeys.map(date => ({
        date,
        percentage: typeof row[date] === 'number' ? row[date] : null
      }))
    }));
    return {
      id: recordId,
      workMonth,
      startDate,
      endDate,
      projects,
      description: remark
    };
  }

  // 校验所有日期的工时百分比总和为100
  function validateAllDatePercentages(tableData: any[], dynamicColumns: any[]): string | null {
    if (!dynamicColumns.length) return null;
    const dateKeys = dynamicColumns.map(col => col.dataIndex);
    for (const date of dateKeys) {
      let sum = 0;
      let hasInput = false;
      for (const row of tableData) {
        if (typeof row[date] === 'number') {
          sum += row[date];
          hasInput = true;
        }
      }
      if (hasInput && sum !== 100) {
        return `日期 ${date} 的工时百分比总和为${sum}，必须等于100`;
      }
    }
    return null;
  }

  // 校验是否有任何工时百分比被填写
  function hasAnyWorkHourData(tableData: any[], dynamicColumns: any[]): boolean {
    if (!dynamicColumns.length) return false;
    const dateKeys = dynamicColumns.map(col => col.dataIndex);
    for (const row of tableData) {
      for (const date of dateKeys) {
        if (typeof row[date] === 'number' && !isNaN(row[date])) {
          return true;
        }
      }
    }
    return false;
  }

  // 处理保存（标记为待提交）
  const handleSave = async () => {
    console.log('handleSave::tableData=', tableData);
    if (!hasAnyWorkHourData(tableData, dynamicColumns)) {
      Toast.warning('请先填写工时数据');
      return;
    }
    const errMsg = validateAllDatePercentages(tableData, dynamicColumns);
    if (errMsg) {
      Toast.error(errMsg);
      return;
    }
    const params = buildHourRecordBatchSaveVO(currentMonth, currentWeek, tableData, dynamicColumns, remark);
    setSubmitting(true);
    try {
      const response: any = await post('/hours/hourRecord/batchSave', params, { skipErrorHandler: true });
      if (response.success) {
        Toast.success('保存成功');
        onSave && onSave({});
      } else {
        Toast.error( response.message || response.result || '保存失败' );
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 处理提交（标记为审核中）
  const handleSubmit = async () => {
    if (!hasAnyWorkHourData(tableData, dynamicColumns)) {
      Toast.warning('请先填写工时数据');
      return;
    }
    const errMsg = validateAllDatePercentages(tableData, dynamicColumns);
    if (errMsg) {
      Toast.error(errMsg);
      return;
    }
    const params = buildHourRecordBatchSaveVO(currentMonth, currentWeek, tableData, dynamicColumns, remark);
    setSubmitting(true);
    try {
      const response: any = await post('/hours/hourRecord/batchSubmit', params, { skipErrorHandler: true });
      if (response.success) {
        Toast.success('提交成功' );
        onSubmit && onSubmit({});
      } else {
        Toast.error(response.message || response.result || '提交失败' );
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 处理工作人员选择变化
  const handleWorkerChange = (value: string) => {
    setWorker(value);
  };
  // 在组件显示时重置表单
  useEffect(() => {
    console.log('useEffect::visible=', visible);
    if (visible) {
      // 如果不是编辑模式，重置所有表单数据
      // if (!isAudit) {
        setWorkDate(new Date());
        setWorkDetails([{
          key: Date.now(),
          type: '',
          typeInfo: '',
          businessLevel1: '',
          businessLevel2: '',
          status: '正常',
          hours: 0,
          description: '',
          workItemId: '',
          workItemName: '',
          instanceId: '',
          instanceName: '',
          nodeId: '',
          nodeName: '',
          taskId: '',
          taskName: ''
        }]);
        // 设置当前登录用户
        const user = getUserInfo();
        setWorker(user?.name || '');
      // }
    }
  }, [visible, isAudit]);

  // 状态定义
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [currentWeek, setCurrentWeek] = useState<WeekRange>(getWeekRange(new Date()));
  const [weeksData, setWeeksData] = useState<WeekRange[]>([]);
  const [dynamicColumns, setDynamicColumns] = useState<any[]>([]);

  // 初始化加载数据
  useEffect(() => {
    console.log('useEffect::初始化加载数据');
    // 调用用户项目列表接口
    loadUserProjects();

    // 初始化当前月份和周期
    const today = new Date();
    setCurrentMonth(today);
    
    // 初始化加载当前月份的周期数据
    const monthStr = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}`;
    loadWorkPeriods(monthStr);
    
  }, []);

  // 编辑模式下 useEffect 串联调用
  useEffect(() => {
    console.log('useEffect::isAudit=', isAudit, 'recordId=', initialData.recordId,';applyId=', initialData.applyId);
    // if ((isAudit||readOnly) && initialData.recordId && initialData.applyId) {
    if(visible){  
      let recordId = initialData.recordId;
      let applyId = initialData.applyId;
      
      // 拉取申请详情
      if(applyId){
        getApplyById(applyId.toString());
      }
      // 拉取审批流
      if(recordId && applyId){
        getApprovalFlow(recordId.toString(), applyId.toString());
      }

    }
  }, [isAudit,visible, initialData]);

  // 拉取审批流
  // useEffect(() => {
  //   let recordId = initialData.recordId;
  //   let applyId = initialData.applyId;
  //   if (recordId && applyId) {

  //   }
  // }, [initialData]);

  const getApprovalFlow = (recordId: string, applyId: string) => {

    setApprovalLoading(true);
    get('/hours/hourRecordApprover/approvalFlow', { recordId, applyId })
      .then((res: any) => {
        if (res && res.success) {
          setApprovalFlow(res.result || []);
        } else {
          setApprovalFlow([]);
        }
      })
      .then(() => setApprovalLoading(false));

  };

  const getApplyById = (applyId: string) => {
    // let applyId = initialData.applyId;
      setLoading(true);
      get(`/hours/hourRecordApply/getById`, { applyId })
        .then((res: any) => {
          if (res.success && res.result) {
            const { hourRecord, hourRecordDetail, applyInfo } = res.result;
            setHeadInfo({
              ...hourRecord,
              applyInfo: applyInfo // 追加applyInfo到头部信息
            });

            if (hourRecord.workMonth) {
              setCurrentMonth(new Date(hourRecord.workMonth + '-01'));
              // 先加载周期，再赋值 currentWeek
              loadWorkPeriods(hourRecord.workMonth, true).then((weeks: WeekRange[]) => {
                // 找到与 hourRecord.startDate/endDate 匹配的周期
                const matched = weeks.find(
                  w => fmtDate(w.start) === hourRecord.workPeriodStart && fmtDate(w.end) === hourRecord.workPeriodEnd
                );
                console.log('matched=', matched,';weeks=',weeks,';hourRecord=',hourRecord);
                if (matched) {
                  setCurrentWeek(matched);
                  setWorkDate(matched.start);
                  // 加载可用日期和明细
                  loadAvailableDates(hourRecord.workMonth, hourRecord.workPeriodStart, hourRecord.workPeriodEnd, hourRecordDetail, hourRecord.userId);
                }

                console.log('hourRecordDetails=', hourRecordDetail);

                // 将 hourRecordDetails 按照 projectId 分组 形成 Map<String,List<HourRecordDetail>>
                const projectMap: Record<string, any[]> = {};
                if (Array.isArray(hourRecordDetail)) {
                  hourRecordDetail.forEach(detail => {
                    if (!projectMap[detail.projectId]) {
                      projectMap[detail.projectId] = [];
                    }
                    projectMap[detail.projectId].push(detail);
                  });
                  console.log('projectMap=', projectMap);
                  

                  // 渲染 setTableData,根据 projectMap 形成 tableData
                  const tableData = Object.values(projectMap).map(details => ({
                    key: details[0].projectId,
                    workItemName: details[0].projectName,
                    ...details.reduce((acc, detail) => {
                      // 将 workDate 转换为 MM.DD 格式
                      const mmdd = toMMDD(detail.workDate);
                      acc[mmdd] = detail.percentage;
                      return acc;
                    }, {})
                  }));
                  console.log('\n\n\n tableData=', tableData);
                  setTableData(tableData);
                } else {
                  setTableData([]);
                }

              });
            }
            setRemark(hourRecord.description || '');
            setWorker(hourRecord.worker || '');
          }else{
            setHeadInfo({});
          }
        })
        .catch(() => {})
        .then(() => setLoading(false));

  };

  // 加载用户项目列表
  const loadUserProjects = () => {
    get('/plm/project/userProjectList')
      .then((response: any) => {
        if (response.success) {
          // 将项目名称映射到表格数据
          const projectData = response.result.map((project: any) => ({
            key: project.id,
            workItemName: project.projectName
          }));
          setTableData(projectData);
        }
      })
      .catch(error => {
        console.error('获取用户项目列表失败:', error);
      });
  };

  // 修改 loadWorkPeriods 增加 initOnly 参数
  const loadWorkPeriods = (monthStr: string, initOnly = false): Promise<WeekRange[]> => {
    return new Promise((resolve, reject) => {
      get('/hours/hourRecord/workPeriods', { month: monthStr })
        .then((response: any) => {
          if (response.success && response.result && response.result.length > 0) {
            const weeks = response.result.map((item: any) => ({
              start: new Date(item.startDate.replace(/\./g, '-')),
              end: new Date(item.endDate.replace(/\./g, '-'))
            }));
            setWeeksData(weeks);
            if (!initOnly && weeks.length > 0) {
              setCurrentWeek(weeks[0]);
              setWorkDate(weeks[0].start);
              loadAvailableDates(monthStr, fmtDate(weeks[0].start), fmtDate(weeks[0].end));
            }
            resolve(weeks);
          } else {
            reject(response.message || '无周期数据');
          }
        })
        .catch(reject);
    });
  };

  // 加载可用日期数据
  const loadAvailableDates = (
    monthStr: string,
    startDateStr: string,
    endDateStr: string,
    hourRecordDetails?: any[],
    userKey?: string,
  ) => {
    setLoading(true);
    get('/hours/hourRecord/availableDates', {
      month: monthStr,
      startDate: startDateStr,
      endDate: endDateStr,
      userKey: userKey
    })
      .then((response: any) => {
        if (response.success && response.result) {
          const availableDates: AvailableDateVO[] = response.result;
          
          // 生成动态列
          const dynamicColumns: any[] = [];
          
          availableDates.forEach(dateInfo => {
            const mmdd = toMMDD(dateInfo.date);
            dynamicColumns.push({
              title: (
                <div>
                  <div>{dateInfo.week}</div>
                  <div>{dateInfo.date}</div>
                </div>
              ),
              dataIndex: mmdd,
              key: mmdd,
              width: 100,
              render: (text: any, record: any) => (
                <div className="hour-record-form-input-number">
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    {dateInfo.attendanceHours > 0 ? (
                      <>
                        <InputNumber 
                          min={0} 
                          max={100} 
                          precision={0}
                          value={record[mmdd]}
                          onChange={(value) => handlePercentageChange(record.key, mmdd, value as number)}
                          style={{ width: '80px' }}
                          disabled={readOnly}
                        />
                        <span style={{ marginLeft: '4px' }}>%</span>
                      </>
                    ) : (
                      <span style={{ textAlign: 'center', width: '100%' }}>--</span>
                    )}
                  </div>
                </div>
              )
            });
          });
          
          setDynamicColumns(dynamicColumns);
          
          // 明细转换
          let newTableData: any[] = [];
          if (hourRecordDetails) {
            // 将 hourRecordDetails 按照 projectId 分组 形成 Map<String,List<HourRecordDetail>>
            const projectMap: Record<string, any[]> = {};
            hourRecordDetails.forEach(detail => {
              if (!projectMap[detail.projectId]) {
                projectMap[detail.projectId] = [];
              }
              projectMap[detail.projectId].push(detail);
            });

            // 渲染 setTableData,根据 projectMap 形成 tableData
            newTableData = Object.values(projectMap).map(details => ({
              key: details[0].projectId,
              workItemName: details[0].projectName,
              ...details.reduce((acc, detail) => {
                // 将 workDate 转换为 MM.DD 格式
                const mmdd = toMMDD(detail.workDate);
                acc[mmdd] = detail.percentage;
                return acc;
              }, {})
            }));
            setTableData(newTableData);
          } else {
            // 新增页逻辑
            newTableData = tableData.length > 0 
              ? tableData.map(item => ({
                  workItemName: item.workItemName,
                  key: item.key
                }))
              : [{ workItemName: '', key: Date.now() }];
          }
          setTableData(newTableData);
        } else {
          Toast.warning(response.message || '请求失败');
        }
        setLoading(false);
      })
      .catch(error => {
        console.error('获取可用日期失败:', error);
        Toast.error('获取可用日期失败');
        setLoading(false);
      });
  };

  // 处理月份变化
  const handleMonthChange = (date: Date) => {
    console.log('handleMonthChange::date=', date);
    setCurrentMonth(date);

    // 调用获取可登记日期接口
    const monthStr = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
    loadWorkPeriods(monthStr);
  };

  // 在handleWeekChange方法中修改动态列生成逻辑
  const handleWeekChange = (week: { start: Date, end: Date }) => {
    setCurrentWeek(week);
    setWorkDate(week.start);
    
    // 使用月份表单控件的值作为month参数
    const monthStr = `${currentMonth.getFullYear()}-${(currentMonth.getMonth() + 1).toString().padStart(2, '0')}`;
    
    // 格式化日期参数
    const startDateStr = fmtDate(week.start);
    const endDateStr = fmtDate(week.end);
    
    // 加载该周期的可用日期
    loadAvailableDates(monthStr, startDateStr, endDateStr);
  };

  // 添加处理百分比变化的回调
  const handlePercentageChange = (rowKey: string, date: string, value: number) => {
    setTableData(prev =>
      prev.map(row =>
        row.key === rowKey
          ? { ...row, [date]: value }
          : row
      )
    );
  };

  // 新增按钮事件
  const openAddModal = () => {
    setSelectedProjectIds([]); // 打开前重置
    const excludeIds = tableData.map(item => item.key).join(',');
    get(`/plm/project/availableForAdd?excludeProjectIds=${excludeIds}`)
      .then((res: any) => {
        if (res.success) setProjectOptions(res.result);
        setAddModalVisible(true);
      })
      .catch(() => {
        Toast.error('获取可添加项目失败');
      });
  };

  

  // 当前用户是否为待审批人
  console.log('approvalFlow=', approvalFlow,';currentUser=',currentUser);
  const isCurrentUserPendingApprover = approvalFlow.some(
    item => item.type === 'pending' && Array.isArray(item.userIds) && item.userIds.includes(currentUser?.userId)
  );

  const handleApprove = async () => {
    setActionLoading(true);
    console.log('handleApprove::initialData.recordId=', initialData.recordId);
    try {
      await post('/hours/hourRecordApprover/approve', {
        recordId: initialData.recordId,
        applyId: initialData.applyId,
        action: 'approve',
        remarks: approveComment,
        approverId: currentUser?.userId,
        approverName: currentUser?.name
      });
      setApproveModalVisible(false);
      onSave && onSave({});
    } finally {
      setActionLoading(false);
    }
  };
  const handleReject = async () => {
    if (!rejectReason.trim()) return;
    setActionLoading(true);
   // console.log('handleReject::initialData.recordId=', initialData);
    try {
      await post('/hours/hourRecordApprover/approve', {
        recordId: initialData.recordId,
        applyId: initialData.applyId,
        action: 'reject',
        remarks: rejectReason,
        approverId: currentUser?.userId,
        approverName: currentUser?.name
      });
      setRejectModalVisible(false);
      onSave && onSave({});
    } finally {
      setActionLoading(false);
    }
  };

  // 头部信息state
  const [headInfo, setHeadInfo] = useState<any>({});



  const statusMap = {
    0: { text: '审核中', color: 'default' },
    1: { text: '已通过', color: 'primary' },
    2: { text: '已拒绝', color: 'success' },
    // 3: { text: '已拒绝', color: 'danger' },
  };
  const statusInfo = statusMap[headInfo.applyInfo?.status] || { text: '未知', color: 'default' };

  return (
    <Modal
      title={<Title heading={6} style={{ margin: 0 }}>审批</Title>}
      visible={visible}
      onCancel={onCancel}
      width={800}
      height="auto"
      maskClosable={false}
      className="hour-record-modal"
      bodyStyle={{
        padding: 0,
        position: 'relative',
        overflowY: 'auto', // 只允许纵向滚动
        height: 'auto',
        maxHeight: '80vh'
      }}
      footer={null}
    >
        
          {/* 原有工时明细内容区域 */}
          <div className="hour-record-container">

                {/* 工时单头部信息Banner */}
                <Banner
                  fullMode={false}
                  bordered
                  icon={null}
                  closeIcon={null}
                  style={{ marginBottom: 12, padding: 0 }}
                  type="info"
                  description={
                    <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', width: '100%' }}>
                      <div>
                        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                          <span style={{ fontWeight: 600, fontSize: 16 }}>{headInfo.applyInfo?.batchNo || '--'}</span>
                          <Tag type={statusInfo.color}>{statusInfo.text}</Tag>
                        </div>
                        <div style={{ color: '#888', fontSize: 12, marginTop: 2 }}>
                          {headInfo.applyInfo?.applicantName ? `申请人：${headInfo.applyInfo?.applicantName}` : ''}
                          {headInfo.createTime ? `  创建于 ${headInfo.createTime}` : ''}
                        </div>
                        <div style={{ color: '#888', fontSize: 12, marginTop: 2 }}>
                          {headInfo.worker ? `  工作人员：${headInfo.worker}` : ''}
                        </div>
                      </div>
                      { isAudit && headInfo.status === 1 && isCurrentUserPendingApprover && (
                        <div style={{ display: 'flex', gap: 8 }}>
                          <Button type="primary" onClick={() => setApproveModalVisible(true)}>通过</Button>
                          <Button type="danger" onClick={() => setRejectModalVisible(true)}>拒绝</Button>
                        </div>
                      )}
                    </div>
                  }
                />

            {/* 工时规则提示 */}
            <div className="hour-record-info">
              <div style={{ display: 'flex', gap: '16px', marginBottom: '12px' }}>
                <div>
                  <Form.Label style={{fontWeight:'normal'}}>所属月份</Form.Label><br/>
                  <DatePicker
                    style={{width:'280px'}}
                    defaultValue={new Date()}
                    type="month"
                    value={currentMonth}
                    disabled={readOnly}
                    onChange={(date: Date) => handleMonthChange(date)}
                    disabledDate={(date: Date) => {
                      // 当前月以后的日期不可选择
                      const now = new Date();
                      const currentYear = now.getFullYear();
                      const currentMonth = now.getMonth();
                      return date.getFullYear() > currentYear || 
                             (date.getFullYear() === currentYear && date.getMonth() > currentMonth);
                    }}
                  />
                </div>
                <div>
                  <Form.Label style={{fontWeight:'normal'}}>填写周期</Form.Label><br/>
                  <Select
                    style={{width:'280px'}}
                    value={`${fmtDate(currentWeek.start)} 至 ${fmtDate(currentWeek.end)}`}
                    disabled={readOnly}
                    onChange={(value) => {
                      const selectedWeek = weeksData
                        .find(w => fmtDate(w.start) === value);
                      if (selectedWeek) {
                        handleWeekChange(selectedWeek);
                        
                        //loadTableData(selectedWeek.start);
                      }
                    }}
                  >
                    {weeksData.map(week => (
                      <Select.Option
                        key={`${fmtDate(week.start)}-${fmtDate(week.end)}`}
                        value={fmtDate(week.start)}
                      >
                        {`${fmtDate(week.start)} 至 ${fmtDate(week.end)}`}
                      </Select.Option>
                    ))}
                  </Select>
                </div>
              </div>
              
              <div style={{ display: 'flex', gap: '16px', marginBottom: '12px' }}>
                {/* 工作人员 */}
                <div>
                  <Form.Label style={{fontWeight:'normal'}}>工作人员</Form.Label><br/>
                  <Input disabled={readOnly} defaultValue={currentUser?.name || ''} style={{width:'280px'}} />
                </div>
              </div>
              
              {/* <Banner fullMode={false} type="info" bordered icon={null} closeIcon={null}
                title={<div style={{ fontWeight: 600, fontSize: '14px', lineHeight: '20px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span>
                    对于可登记工时有疑问可联系管理员处理
                  </span>
                  <Button
                    type="tertiary"
                    theme="borderless"
                    size="small"
                    style={{
                      padding: '0 4px',
                      height: 'auto',
                      fontSize: '12px',
                      color: 'var(--semi-color-primary)',
                      textDecoration: 'underline'
                    }}
                    onClick={() => setRulesModalVisible(true)}
                  >
                    工时登记规则
                  </Button>

                </div>}
              /> */}
            </div>

            {/* 表单内容区域 - 不可滚动，只有表格可滚动 */}
            <div className="hour-record-content">
                
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                <Form.Label style={{fontWeight:'normal', margin: 0}}>工时登记明细</Form.Label>
                {!readOnly && (
                  <Button type="primary" icon={<IconPlus />} onClick={openAddModal}>新增</Button>
                )}
              </div>
              <Table
                columns={[
                  {
                    title: '工作内容',
                    dataIndex: 'workItemName',
                    key: 'workItemName',
                    width: 200,
                    fixed: 'left'
                  },
                  ...dynamicColumns
                ]}
                dataSource={tableData}
                loading={loading}
                scroll={{ x: 'max-content', y: 400 }}
                rowKey="key"
              />
              {/* 备注 */}
              <div style={{ width: '100%' }}>
                <Form.Label style={{fontWeight:'normal'}}>备注</Form.Label>
                <TextArea disabled={readOnly} defaultValue={''} value={remark} onChange={setRemark} />
              </div>

            </div>
            {/* 审批流节点流，直接展示在工时明细下方 */}
            <div style={{ marginTop: 24 }}>
              <Form.Label style={{fontWeight:'normal', marginBottom: 8}}>审批流</Form.Label>
              <Timeline mode="left" style={{ marginTop: 8 }}>
                {approvalFlow.map((item, idx) => {
                  if (item.type === 'start') {
                    return (
                      <Timeline.Item
                        key={idx}
                        time={item.time || ''}
                        type="success"
                        extra={null}
                      >
                        <span>{item.userName} <span style={{ color: '#888', marginLeft: 8 }}>{item.desc}</span></span>
                      </Timeline.Item>
                    );
                  } else if (item.type === 'approved' || item.type === 'rejected') {
                    const type = item.type === 'approved' ? 'success' : 'error';
                    return (
                      <Timeline.Item
                        key={idx}
                        time={item.time || ''}
                        type={type}
                        extra={<span style={{ color: type === 'success' ? '#4CAF50' : '#F44336' }}>{item.remarks && <span style={{ color: '#666', fontSize: 13, marginLeft: 8 }}>意见：{item.remarks}</span>}</span>}
                      >
                        <span>{item.userName} <span style={{ color: type === 'success' ? '#4CAF50' : '#F44336' }}>{type === 'success' ? '审批通过' : '拒绝'}</span></span>
                      </Timeline.Item>
                    );
                  } else if (item.type === 'pending') {
                    return (
                      <Timeline.Item
                        key={idx}
                        type="default"
                        extra={<span style={{ color: '#888' }}>待 {item.userNames} 审批</span>}
                      >
                        
                      </Timeline.Item>
                    );
                  }
                  return null;
                })}
              </Timeline>
            </div>
          </div>
        

      {/* 工时登记规则说明模态窗 */}
      <WorkHourRulesModal
        visible={rulesModalVisible}
        onClose={() => setRulesModalVisible(false)}
      />

      {/* 新增项目模态窗 */}
      <Modal
        title="选择项目"
        visible={addModalVisible}
        onCancel={() => {
          setAddModalVisible(false);
          setSelectedProjectIds([]);
        }}
        onOk={() => {
          const dateKeys = dynamicColumns.map((col: any) => col.dataIndex);
          const newRows = projectOptions
            .filter(p => selectedProjectIds.includes(p.id))
            .map(p => {
              const row: any = { key: p.id, workItemName: p.projectName };
              dateKeys.forEach(date => { row[date] = undefined; });
              return row;
            });
          setTableData([...tableData, ...newRows]);
          setAddModalVisible(false);
          setSelectedProjectIds([]);
        }}
        okText="确定"
        cancelText="取消"
      >
        <Select
          multiple
          style={{ width: '100%' }}
          placeholder="请选择要添加的项目"
          value={selectedProjectIds}
          onChange={setSelectedProjectIds}
        >
          {projectOptions.map(p => (
            <Select.Option key={p.id} value={p.id}>{p.projectName}</Select.Option>
          ))}
        </Select>
      </Modal>

      {/* 审批通过弹窗 */}
      <Modal
        title="审批通过"
        visible={approveModalVisible}
        onOk={handleApprove}
        onCancel={() => setApproveModalVisible(false)}
        okText="确认通过"
        cancelText="取消"
        confirmLoading={actionLoading}
      >
        <Form>
          <Form.TextArea
            field="approveComment"
            label="审批意见（非必填）"
            initValue={approveComment}
            onChange={setApproveComment}
            placeholder="请输入审批意见"
            rows={4}
          />
        </Form>
      </Modal>
      {/* 审批拒绝弹窗 */}
      <Modal
        title="审批拒绝"
        visible={rejectModalVisible}
        onOk={handleReject}
        onCancel={() => setRejectModalVisible(false)}
        okText="确认拒绝"
        cancelText="取消"
        confirmLoading={actionLoading}
      >
        <Form>
          <Form.TextArea
            field="rejectReason"
            label="拒绝原因（必填）"
            initValue={rejectReason}
            onChange={setRejectReason}
            placeholder="请输入拒绝原因"
            rules={[
              { required: true, message: '请输入拒绝原因' }
            ]}
            rows={4}
          />
        </Form>
      </Modal>
    </Modal>
  );
};

export default HourRecordAuditDetail;