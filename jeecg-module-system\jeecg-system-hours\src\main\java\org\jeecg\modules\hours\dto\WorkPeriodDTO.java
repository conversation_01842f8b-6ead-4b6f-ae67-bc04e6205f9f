package org.jeecg.modules.hours.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 工作周期选项DTO
 */
@Data
@ApiModel(value = "工作周期选项DTO", description = "工作周期选项DTO")
public class WorkPeriodDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private String label;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private String value;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDate;
} 