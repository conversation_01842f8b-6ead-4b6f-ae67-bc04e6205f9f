package org.jeecg.modules.hours.constant;

/**
 * 登记提醒相关常量
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ReminderConstant {

    /**
     * 提醒类型常量
     */
    public static class ReminderType {
        /** 每天提醒 */
        public static final String DAILY = "daily";
        /** 每周提醒 */
        public static final String WEEKLY = "weekly";
        /** 每月提醒 */
        public static final String MONTHLY = "monthly";
    }

    /**
     * 重复方式常量
     */
    public static class RepeatMode {
        /** 当天 */
        public static final String CURRENT_DAY = "current_day";
        /** 次日 */
        public static final String NEXT_DAY = "next_day";
        /** 本周 */
        public static final String CURRENT_WEEK = "current_week";
        /** 次周 */
        public static final String NEXT_WEEK = "next_week";
        /** 本月 */
        public static final String CURRENT_MONTH = "current_month";
        /** 次月 */
        public static final String NEXT_MONTH = "next_month";
    }

    /**
     * 星期常量
     */
    public static class WeekDay {
        /** 周一 */
        public static final String MONDAY = "monday";
        /** 周二 */
        public static final String TUESDAY = "tuesday";
        /** 周三 */
        public static final String WEDNESDAY = "wednesday";
        /** 周四 */
        public static final String THURSDAY = "thursday";
        /** 周五 */
        public static final String FRIDAY = "friday";
        /** 周六 */
        public static final String SATURDAY = "saturday";
        /** 周日 */
        public static final String SUNDAY = "sunday";
    }

    /**
     * 月份日期常量
     */
    public static class MonthDay {
        /** 月末 */
        public static final String LAST_DAY = "last_day";
    }

    /**
     * 状态常量
     */
    public static class Status {
        /** 启用 */
        public static final String ENABLED = "启用";
        /** 禁用 */
        public static final String DISABLED = "暂停";
    }

    /**
     * 提醒消息模板
     */
    public static class MessageTemplate {
        /** 工时登记提醒标题 */
        public static final String REMINDER_TITLE = "工时登记提醒";
        
        /** 每天提醒消息模板 */
        public static final String DAILY_REMINDER_TEMPLATE = "请及时填写今日工时，确保工时数据的准确性。";
        
        /** 每周提醒消息模板 */
        public static final String WEEKLY_REMINDER_TEMPLATE = "请检查并完善本周工时记录，确保所有工作内容都已登记。";
        
        /** 每月提醒消息模板 */
        public static final String MONTHLY_REMINDER_TEMPLATE = "月末工时提醒：请确认本月工时数据完整性，及时补充遗漏的工时记录。";
        
        /** 自定义提醒消息模板 */
        public static final String CUSTOM_REMINDER_TEMPLATE = "工时登记提醒：{remark}";
    }

    /**
     * 时间格式常量
     */
    public static class TimeFormat {
        /** 时间格式：HH:mm */
        public static final String TIME_PATTERN = "HH:mm";
        /** 日期时间格式：yyyy-MM-dd HH:mm:ss */
        public static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
        /** 日期格式：yyyy-MM-dd */
        public static final String DATE_PATTERN = "yyyy-MM-dd";
    }

    /**
     * 验证规则常量
     */
    public static class Validation {
        /** 时间格式正则表达式 */
        public static final String TIME_REGEX = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$";
        /** 备注最大长度 */
        public static final int REMARK_MAX_LENGTH = 500;
        /** 工作空间标识最大长度 */
        public static final int PROJECT_KEY_MAX_LENGTH = 64;
    }

    /**
     * 缓存相关常量
     */
    public static class Cache {
        /** 提醒配置缓存前缀 */
        public static final String REMINDER_CONFIG_PREFIX = "reminder:config:";
        /** 提醒配置缓存过期时间（秒） */
        public static final long REMINDER_CONFIG_EXPIRE = 3600;
        /** 提醒执行锁前缀 */
        public static final String REMINDER_LOCK_PREFIX = "reminder:lock:";
        /** 提醒执行锁过期时间（秒） */
        public static final long REMINDER_LOCK_EXPIRE = 300;
    }

    /**
     * 任务调度相关常量
     */
    public static class Schedule {
        /** 提醒任务执行间隔（分钟） */
        public static final int REMINDER_TASK_INTERVAL = 1;
        /** 提醒任务线程池大小 */
        public static final int REMINDER_THREAD_POOL_SIZE = 5;
        /** 提醒任务队列大小 */
        public static final int REMINDER_QUEUE_SIZE = 100;
    }

    /**
     * 错误消息常量
     */
    public static class ErrorMessage {
        /** 提醒类型不能为空 */
        public static final String REMINDER_TYPE_REQUIRED = "提醒类型不能为空";
        /** 无效的提醒类型 */
        public static final String INVALID_REMINDER_TYPE = "无效的提醒类型";
        /** 重复方式不能为空 */
        public static final String REPEAT_MODE_REQUIRED = "重复方式不能为空";
        /** 无效的重复方式 */
        public static final String INVALID_REPEAT_MODE = "无效的重复方式";
        /** 提醒时间不能为空 */
        public static final String REMINDER_TIME_REQUIRED = "提醒时间不能为空";
        /** 提醒时间格式不正确 */
        public static final String INVALID_REMINDER_TIME_FORMAT = "提醒时间格式不正确，请使用HH:MM格式";
        /** 提醒日期必须设置 */
        public static final String REMINDER_DAY_REQUIRED = "周提醒和月提醒必须设置提醒日期";
        /** 无效的星期设置 */
        public static final String INVALID_WEEK_DAY = "无效的星期设置";
        /** 无效的月份日期设置 */
        public static final String INVALID_MONTH_DAY = "月份日期必须在1-31之间或设置为last_day";
        /** 已存在相同配置 */
        public static final String CONFIG_ALREADY_EXISTS = "已存在相同的提醒配置";
        /** 工作空间标识不能为空 */
        public static final String PROJECT_KEY_REQUIRED = "工作空间标识不能为空";
        /** 无效的状态值 */
        public static final String INVALID_STATUS = "无效的状态值";
        /** 提醒类型和重复方式不匹配 */
        public static final String TYPE_MODE_MISMATCH = "提醒类型和重复方式不匹配";
    }

    private ReminderConstant() {
        // 私有构造函数，防止实例化
    }
}
