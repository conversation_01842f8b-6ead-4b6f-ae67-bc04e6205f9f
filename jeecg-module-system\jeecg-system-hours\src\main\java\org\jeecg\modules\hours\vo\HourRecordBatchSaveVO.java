package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 工时记录批量保存VO
 *
 * <AUTHOR>
 * @date 2023-07-01
 */
@Data
@ApiModel(value = "工时记录批量保存VO", description = "工时记录批量保存VO")
public class HourRecordBatchSaveVO {
    
    /**
     * 工作月份
     */
    @ApiModelProperty(value = "工作月份")
    private String workMonth;
    
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDate;
    
    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDate;
    
    
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态",required = false)
    private Integer status;
    

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述",required = false)
    private String description;
    
    /**
     * 项目列表
     */
    @ApiModelProperty(value = "项目列表")
    private List<ProjectPercentageVO> projects;

    /**
     * 主键ID（用于区分新增/修改）
     */
    @ApiModelProperty(value = "主键ID", required = false)
    private String id;
} 