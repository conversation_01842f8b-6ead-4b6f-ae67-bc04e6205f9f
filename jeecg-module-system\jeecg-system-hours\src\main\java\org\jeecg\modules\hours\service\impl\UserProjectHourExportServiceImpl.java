package org.jeecg.modules.hours.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jeecg.modules.hours.service.IUserProjectHourExportService;
import org.jeecg.modules.hours.service.IUserProjectHourStatisticsService;
import org.jeecg.modules.hours.vo.ProjectColumnVO;
import org.jeecg.modules.hours.vo.UserProjectHourReportResult;
import org.jeecg.modules.hours.vo.UserProjectHourReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 人员项目工时统计报表导出服务实现
 * @Author: jeecg-boot
 * @Date: 2025-07-08
 * @Version: V1.0
 */
@Service
@Slf4j
public class UserProjectHourExportServiceImpl implements IUserProjectHourExportService {

    @Autowired
    private IUserProjectHourStatisticsService userProjectHourStatisticsService;

    @Override
    public void exportUserProjectHourReportToExcel(Date startDate, Date endDate, String statPeriod,
                                                  List<String> userIds, List<String> projectIds, Integer isHoliday,
                                                  HttpServletResponse response) {
        try {
            log.info("开始导出人员项目工时统计报表, startDate={}, endDate={}, isHoliday={}", startDate, endDate, isHoliday);

            // 1. 获取报表数据
            UserProjectHourReportResult reportData = userProjectHourStatisticsService.getUserProjectHourReport(
                    startDate, endDate, statPeriod, userIds, projectIds, isHoliday);

            if (reportData == null || reportData.getUserHourData().isEmpty()) {
                log.warn("报表数据为空，无法导出");
                response.getWriter().write("暂无数据可导出");
                return;
            }

            // 2. 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("人员项目工时统计报表");

            // 3. 创建样式
            CellStyle titleStyle = createTitleStyle(workbook);
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            CellStyle totalStyle = createTotalStyle(workbook);

            // 4. 写入数据
            int rowIndex = 0;
            
            // 标题行
            rowIndex = createTitleRow(sheet, titleStyle, reportData, isHoliday, rowIndex);
            
            // 空行
            rowIndex++;
            
            // 表头行（双行表头）
            rowIndex = createHeaderRows(sheet, headerStyle, reportData.getProjectColumns(), rowIndex);
            
            // 数据行
            rowIndex = createDataRows(sheet, dataStyle, reportData, rowIndex);
            
            // 总计行
            createTotalRow(sheet, totalStyle, reportData, rowIndex);

            // 5. 自动调整列宽
            autoSizeColumns(sheet, 3 + reportData.getProjectColumns().size() + 1);

            // 6. 设置响应头并输出文件
            setResponseHeaders(response, generateFileName(startDate, endDate));
            workbook.write(response.getOutputStream());
            workbook.close();

            log.info("人员项目工时统计报表导出完成");

        } catch (Exception e) {
            log.error("导出人员项目工时统计报表失败", e);
            try {
                response.getWriter().write("导出失败：" + e.getMessage());
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 创建标题行
     */
    private int createTitleRow(Sheet sheet, CellStyle titleStyle, 
                             UserProjectHourReportResult reportData, Integer isHoliday, int rowIndex) {
        Row titleRow = sheet.createRow(rowIndex++);
        Cell titleCell = titleRow.createCell(0);
        
        String holidayInfo = "";
        if (isHoliday != null) {
            holidayInfo = isHoliday == 1 ? "(统计假日)" : "(不统计假日)";
        }
        
        String title = "人员项目工时统计报表" + holidayInfo;
        if (reportData.getSummary() != null && reportData.getSummary().getDateRange() != null) {
            title += "（" + reportData.getSummary().getDateRange() + "）";
        }
        
        titleCell.setCellValue(title);
        titleCell.setCellStyle(titleStyle);
        
        // 合并标题行单元格
        int lastCol = 3 + reportData.getProjectColumns().size();
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 0, lastCol));
        
        return rowIndex;
    }

    /**
     * 创建表头行（双行表头）
     */
    private int createHeaderRows(Sheet sheet, CellStyle headerStyle, 
                               List<ProjectColumnVO> projectColumns, int rowIndex) {
        // 第一行表头 - 固定列 + 项目名称
        Row headerRow1 = sheet.createRow(rowIndex++);
        
        // 固定列
        Cell userNameCell = headerRow1.createCell(0);
        userNameCell.setCellValue("姓名");
        userNameCell.setCellStyle(headerStyle);
        
        Cell userCodeCell = headerRow1.createCell(1);
        userCodeCell.setCellValue("工号");
        userCodeCell.setCellStyle(headerStyle);
        
        Cell deptCell = headerRow1.createCell(2);
        deptCell.setCellValue("部门");
        deptCell.setCellStyle(headerStyle);

        // 项目名称列
        int colIndex = 3;
        for (ProjectColumnVO project : projectColumns) {
            Cell cell = headerRow1.createCell(colIndex++);
            cell.setCellValue(project.getProjectName());
            cell.setCellStyle(headerStyle);
        }

        // 小计列
        Cell totalCell = headerRow1.createCell(colIndex);
        totalCell.setCellValue("小计");
        totalCell.setCellStyle(headerStyle);

        // 第二行表头 - 项目编码
        Row headerRow2 = sheet.createRow(rowIndex++);
        
        // 项目编码列
        colIndex = 3;
        for (ProjectColumnVO project : projectColumns) {
            Cell cell = headerRow2.createCell(colIndex++);
            cell.setCellValue(project.getProjectCode());
            cell.setCellStyle(headerStyle);
        }

        // 合并固定列和小计列的两行
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 1, 0, 0)); // 人员
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 1, 1, 1)); // 工号
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 1, 2, 2)); // 部门
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 1, colIndex, colIndex)); // 小计
        
        return rowIndex;
    }

    /**
     * 创建数据行
     */
    private int createDataRows(Sheet sheet, CellStyle dataStyle,
                             UserProjectHourReportResult reportData, int rowIndex) {
        for (UserProjectHourReportVO userData : reportData.getUserHourData()) {
            Row dataRow = sheet.createRow(rowIndex++);
            
            // 人员信息
            Cell userNameCell = dataRow.createCell(0);
            userNameCell.setCellValue(userData.getUserName());
            userNameCell.setCellStyle(dataStyle);
            
            Cell userCodeCell = dataRow.createCell(1);
            userCodeCell.setCellValue(userData.getUserCode());
            userCodeCell.setCellStyle(dataStyle);
            
            Cell deptCell = dataRow.createCell(2);
            deptCell.setCellValue(userData.getDepartmentName());
            deptCell.setCellStyle(dataStyle);

            // 各项目工时
            int colIndex = 3;
            for (ProjectColumnVO project : reportData.getProjectColumns()) {
                Cell cell = dataRow.createCell(colIndex++);
                BigDecimal hours = userData.getProjectHours().get(project.getColumnKey());
                if (hours != null && hours.compareTo(BigDecimal.ZERO) > 0) {
                    cell.setCellValue(hours.doubleValue());
                } else {
                    cell.setCellValue("");
                }
                cell.setCellStyle(dataStyle);
            }

            // 小计
            Cell totalCell = dataRow.createCell(colIndex);
            if (userData.getTotalHours() != null) {
                totalCell.setCellValue(userData.getTotalHours().doubleValue());
            } else {
                totalCell.setCellValue(0);
            }
            totalCell.setCellStyle(dataStyle);
        }
        
        return rowIndex;
    }

    /**
     * 创建总计行
     */
    private void createTotalRow(Sheet sheet, CellStyle totalStyle,
                              UserProjectHourReportResult reportData, int rowIndex) {
        Row totalRow = sheet.createRow(rowIndex);
        
        // 总计标签
        Cell labelCell = totalRow.createCell(0);
        labelCell.setCellValue("总计");
        labelCell.setCellStyle(totalStyle);
        
        // 空单元格
        Cell emptyCell1 = totalRow.createCell(1);
        emptyCell1.setCellValue("");
        emptyCell1.setCellStyle(totalStyle);
        
        Cell emptyCell2 = totalRow.createCell(2);
        emptyCell2.setCellValue("");
        emptyCell2.setCellStyle(totalStyle);

        // 各项目工时总计
        int colIndex = 3;
        UserProjectHourReportVO totalData = reportData.getTotalRow();
        for (ProjectColumnVO project : reportData.getProjectColumns()) {
            Cell cell = totalRow.createCell(colIndex++);
            if (totalData != null && totalData.getProjectHours() != null) {
                BigDecimal hours = totalData.getProjectHours().get(project.getColumnKey());
                if (hours != null && hours.compareTo(BigDecimal.ZERO) > 0) {
                    cell.setCellValue(hours.doubleValue());
                } else {
                    cell.setCellValue("");
                }
            } else {
                cell.setCellValue("");
            }
            cell.setCellStyle(totalStyle);
        }

        // 总计小计
        Cell totalCell = totalRow.createCell(colIndex);
        if (totalData != null && totalData.getTotalHours() != null) {
            totalCell.setCellValue(totalData.getTotalHours().doubleValue());
        } else {
            totalCell.setCellValue(0);
        }
        totalCell.setCellStyle(totalStyle);
    }

    /**
     * 创建标题样式
     */
    private CellStyle createTitleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 16);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        return style;
    }

    /**
     * 创建总计样式
     */
    private CellStyle createTotalStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 设置边框
        style.setBorderTop(BorderStyle.MEDIUM);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // 设置背景色
        style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        return style;
    }

    /**
     * 自动调整列宽
     */
    private void autoSizeColumns(Sheet sheet, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            sheet.autoSizeColumn(i);
            int currentWidth = sheet.getColumnWidth(i);
            
            // 设置最小和最大宽度
            if (currentWidth < 2000) {
                sheet.setColumnWidth(i, 2000);
            }
            if (currentWidth > 8000) {
                sheet.setColumnWidth(i, 8000);
            }
        }
    }

    /**
     * 设置响应头
     */
    private void setResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        
        try {
            String encodedFileName = URLEncoder.encode(fileName + ".xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
        } catch (Exception e) {
            response.setHeader("Content-Disposition", "attachment; filename=\"user_project_hour_report.xlsx\"");
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(Date startDate, Date endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return "人员项目工时统计报表_" + sdf.format(startDate) + "_" + sdf.format(endDate);
    }
}