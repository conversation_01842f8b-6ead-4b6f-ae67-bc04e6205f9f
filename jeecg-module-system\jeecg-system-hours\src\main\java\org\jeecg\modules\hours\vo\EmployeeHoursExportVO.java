package org.jeecg.modules.hours.vo;

import lombok.Data;

import java.util.Map;

import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * 员工工时周期汇总报表导出VO
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class EmployeeHoursExportVO {

    @Excel(name = "员工姓名", width = 15, orderNum = "1")
    private String employeeName;

    @Excel(name = "小计(小时)", width = 15, orderNum = "998")
    private Double totalHours;

    @Excel(name = "折算天数(天)", width = 15, orderNum = "999")
    private Double totalDays;

    // 动态列将通过反射设置
    private Map<String, Double> periods;
}
