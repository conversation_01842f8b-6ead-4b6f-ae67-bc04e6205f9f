package org.jeecg.modules.hours.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang.StringUtils;
import org.jeecg.modules.hours.entity.SysUserAttendanceStat;
import org.jeecg.modules.hours.mapper.SysUserAttendanceStatMapper;
import org.jeecg.modules.hours.service.ISysUserAttendanceStatService;
import org.jeecg.modules.hours.vo.SysUserAttendanceStatVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 用户考勤统计服务实现类
 * @Author: jeecg-boot
 * @Date: 2023-07-01
 * @Version: V1.0
 */
import lombok.extern.slf4j.Slf4j;

/**
 * 排班统计ServiceImpl
 */
@Slf4j
@Service
public class SysUserAttendanceStatServiceImpl extends ServiceImpl<SysUserAttendanceStatMapper, SysUserAttendanceStat> implements ISysUserAttendanceStatService {
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatch(List<SysUserAttendanceStat> stats) {
        if (stats == null || stats.isEmpty()) {
            return false;
        }

        try {
            // 过滤无效记录
            List<SysUserAttendanceStat> validStats = stats.stream()
                    .filter(stat -> stat.getUnionId() != null && stat.getStatDate() != null)
                    .collect(Collectors.toList());

            if (validStats.isEmpty()) {
                log.warn("没有有效的考勤统计记录需要处理");
                return true;
            }

            // 批量查询已存在的记录，避免循环查询数据库
            Map<String, SysUserAttendanceStat> existingMap = getExistingRecordsMap(validStats);

            List<SysUserAttendanceStat> toInsert = new ArrayList<>();
            List<SysUserAttendanceStat> toUpdate = new ArrayList<>();
            Date now = new Date();

            // 根据查询结果分类处理
            for (SysUserAttendanceStat stat : validStats) {
                String key = buildKey(stat.getUnionId(), stat.getStatDate());
                SysUserAttendanceStat existing = existingMap.get(key);

                if (existing != null) {
                    // 存在则更新：设置ID并更新其他字段
                    stat.setId(existing.getId());
                    stat.setUpdatedAt(now);
                    toUpdate.add(stat);
                } else {
                    // 不存在则插入
                    stat.setCreatedAt(now);
                    stat.setUpdatedAt(now);
                    toInsert.add(stat);
                }
            }

            // 批量插入新记录
            if (!toInsert.isEmpty()) {
                boolean insertResult = super.saveBatch(toInsert);
                if (!insertResult) {
                    log.error("批量插入考勤统计记录失败，数量：{}", toInsert.size());
                    return false;
                }
                log.info("成功插入考勤统计记录：{} 条", toInsert.size());
            }

            // 批量更新已存在记录
            if (!toUpdate.isEmpty()) {
                boolean updateResult = super.updateBatchById(toUpdate);
                if (!updateResult) {
                    log.error("批量更新考勤统计记录失败，数量：{}", toUpdate.size());
                    return false;
                }
                log.info("成功更新考勤统计记录：{} 条", toUpdate.size());
            }

            log.info("考勤统计数据处理完成：插入 {} 条，更新 {} 条", toInsert.size(), toUpdate.size());
            return true;

        } catch (Exception e) {
            log.error("批量保存或更新考勤统计记录失败", e);
            throw e; // 重新抛出异常，让事务回滚
        }
    }

    /**
     * 批量查询已存在的记录，返回Map便于快速查找
     *
     * @param stats 待处理的统计记录列表
     * @return 已存在记录的Map，key为unionId_statDate，value为记录对象
     */
    private Map<String, SysUserAttendanceStat> getExistingRecordsMap(List<SysUserAttendanceStat> stats) {
        // 提取所有的unionId和statDate组合
        List<String> unionIds = stats.stream()
                .map(SysUserAttendanceStat::getUnionId)
                .distinct()
                .collect(Collectors.toList());

        List<Date> statDates = stats.stream()
                .map(SysUserAttendanceStat::getStatDate)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询已存在的记录
        LambdaQueryWrapper<SysUserAttendanceStat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUserAttendanceStat::getUnionId, unionIds)
                   .in(SysUserAttendanceStat::getStatDate, statDates);

        List<SysUserAttendanceStat> existingRecords = this.list(queryWrapper);

        // 转换为Map，便于快速查找
        return existingRecords.stream()
                .collect(Collectors.toMap(
                    record -> buildKey(record.getUnionId(), record.getStatDate()),
                    record -> record,
                    (existing, replacement) -> existing // 如果有重复key，保留第一个
                ));
    }

    /**
     * 构建查找key：unionId_statDate
     */
    private String buildKey(String unionId, Date statDate) {
        return unionId + "_" + statDate.getTime();
    }

    @Override
    public SysUserAttendanceStat getByUserAndDate(String unionId, Date statDate) {
        return lambdaQuery()
                .eq(SysUserAttendanceStat::getUnionId, unionId)
                .eq(SysUserAttendanceStat::getStatDate, statDate)
                .one();
    }

	@Override
	public List<SysUserAttendanceStat> getByUserDate(String unionId, String startDate, String endDate) {
		return lambdaQuery()
                .eq(SysUserAttendanceStat::getUnionId, unionId)
                .ge(SysUserAttendanceStat::getStatDate, startDate)
                .le(SysUserAttendanceStat::getStatDate, endDate)
                .list();
	}

    /**
     * 工作台-待登记工时查询
     *
     * @param page
     * @param sysUserAttendanceStatVo
     */
    @Override
    public IPage<SysUserAttendanceStatVo> getPendingRegisteredHoursList(Page<SysUserAttendanceStatVo> page, SysUserAttendanceStatVo sysUserAttendanceStatVo) {
        List<String> employeeIdList = new ArrayList<>();
        if(!StringUtils.isEmpty(sysUserAttendanceStatVo.getUserId())){
            employeeIdList= Arrays.asList(sysUserAttendanceStatVo.getUserId().split(","));
        }
        return this.baseMapper.getPendingRegisteredHoursList(page, sysUserAttendanceStatVo,employeeIdList);
    }
}