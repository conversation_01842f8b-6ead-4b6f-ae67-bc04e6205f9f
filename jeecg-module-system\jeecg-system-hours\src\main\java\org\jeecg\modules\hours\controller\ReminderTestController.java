package org.jeecg.modules.hours.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.hours.entity.FsSysReminderConfig;
import org.jeecg.modules.hours.service.IFsSysReminderConfigService;
import org.jeecg.modules.hours.service.IReminderScheduleService;
import org.jeecg.modules.hours.service.IWorkHourReminderService;
import org.jeecg.modules.hours.util.BusinessDateCalculator;
import org.jeecg.modules.hours.vo.UnsubmittedWorkHourVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 提醒系统测试控制器
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Api(tags = "提醒系统测试")
@RestController
@RequestMapping("/hours/reminder/test")
@Slf4j
public class ReminderTestController {

    @Autowired
    private IFsSysReminderConfigService reminderConfigService;
    
    @Autowired
    private IReminderScheduleService reminderScheduleService;
    
    @Autowired
    private IWorkHourReminderService workHourReminderService;

    @ApiOperation(value = "测试定时任务启动", notes = "手动启动指定配置的定时任务")
    @PostMapping("/start-job/{configId}")
    public Result<String> startJob(@PathVariable Long configId) {
        log.info("测试启动定时任务，配置ID: {}", configId);
        
        try {
            FsSysReminderConfig config = reminderConfigService.getById(configId);
            if (config == null) {
                return Result.error("未找到提醒配置");
            }
            
            boolean result = reminderScheduleService.startReminderJob(config);
            if (result) {
                return Result.ok("定时任务启动成功");
            } else {
                return Result.error("定时任务启动失败");
            }
        } catch (Exception e) {
            log.error("启动定时任务时发生异常", e);
            return Result.error("启动定时任务异常: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试定时任务停止", notes = "手动停止指定配置的定时任务")
    @PostMapping("/stop-job/{configId}")
    public Result<String> stopJob(@PathVariable Long configId) {
        log.info("测试停止定时任务，配置ID: {}", configId);
        
        try {
            FsSysReminderConfig config = reminderConfigService.getById(configId);
            if (config == null) {
                return Result.error("未找到提醒配置");
            }
            
            boolean result = reminderScheduleService.stopReminderJob(config);
            if (result) {
                return Result.ok("定时任务停止成功");
            } else {
                return Result.error("定时任务停止失败");
            }
        } catch (Exception e) {
            log.error("停止定时任务时发生异常", e);
            return Result.error("停止定时任务异常: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试提醒执行", notes = "手动执行指定配置的提醒逻辑")
    @PostMapping("/execute-reminder/{configId}")
    public Result<String> executeReminder(@PathVariable Long configId) {
        log.info("测试执行提醒逻辑，配置ID: {}", configId);
        
        try {
            FsSysReminderConfig config = reminderConfigService.getById(configId);
            if (config == null) {
                return Result.error("未找到提醒配置");
            }
            
            workHourReminderService.executeReminder(config);
            return Result.ok("提醒执行成功");
        } catch (Exception e) {
            log.error("执行提醒逻辑时发生异常", e);
            return Result.error("执行提醒异常: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试业务日期计算", notes = "测试指定配置的业务日期计算")
    @GetMapping("/calculate-dates/{configId}")
    public Result<List<LocalDate>> calculateBusinessDates(@PathVariable Long configId) {
        log.info("测试业务日期计算，配置ID: {}", configId);
        
        try {
            FsSysReminderConfig config = reminderConfigService.getById(configId);
            if (config == null) {
                return Result.error("未找到提醒配置");
            }
            
            List<LocalDate> businessDates = BusinessDateCalculator.calculateBusinessDates(config);
            return Result.ok(businessDates);
        } catch (Exception e) {
            log.error("计算业务日期时发生异常", e);
            return Result.error("计算业务日期异常: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试未提交工时查询", notes = "查询指定项目和日期范围的未提交工时")
    @PostMapping("/find-unsubmitted")
    public Result<List<UnsubmittedWorkHourVO>> findUnsubmittedWorkHours(
            @RequestParam String projectKey,
            @RequestBody List<LocalDate> businessDates) {
        log.info("测试未提交工时查询，项目: {}, 日期范围: {}", projectKey, businessDates);
        
        try {
            List<UnsubmittedWorkHourVO> result = workHourReminderService.findUnsubmittedWorkHours(projectKey, businessDates);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("查询未提交工时时发生异常", e);
            return Result.error("查询未提交工时异常: " + e.getMessage());
        }
    }

    @ApiOperation(value = "测试Cron表达式生成", notes = "测试指定配置的Cron表达式生成")
    @GetMapping("/generate-cron/{configId}")
    public Result<String> generateCronExpression(@PathVariable Long configId) {
        log.info("测试Cron表达式生成，配置ID: {}", configId);
        
        try {
            FsSysReminderConfig config = reminderConfigService.getById(configId);
            if (config == null) {
                return Result.error("未找到提醒配置");
            }
            
            String cronExpression = reminderScheduleService.generateCronExpression(config);
            return Result.ok(cronExpression);
        } catch (Exception e) {
            log.error("生成Cron表达式时发生异常", e);
            return Result.error("生成Cron表达式异常: " + e.getMessage());
        }
    }

    @ApiOperation(value = "检查定时任务状态", notes = "检查指定配置的定时任务是否存在")
    @GetMapping("/check-job/{configId}")
    public Result<Boolean> checkJobExists(@PathVariable Long configId) {
        log.info("检查定时任务状态，配置ID: {}", configId);
        
        try {
            FsSysReminderConfig config = reminderConfigService.getById(configId);
            if (config == null) {
                return Result.error("未找到提醒配置");
            }
            
            boolean exists = reminderScheduleService.isJobExists(config);
            return Result.ok(exists);
        } catch (Exception e) {
            log.error("检查定时任务状态时发生异常", e);
            return Result.error("检查定时任务状态异常: " + e.getMessage());
        }
    }
}
