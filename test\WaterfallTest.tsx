/**
 * 瀑布流分页测试组件
 * 专门用于测试重构后的WaterfallCascader分页加载效果
 */
import React, { useState } from 'react';
import { Card, Typography, Space, Button, Notification, Tag } from '@douyinfe/semi-ui';
import WaterfallCascader from './WaterfallCascader';
import { CascaderItem } from '../types';

const { Title, Text, Paragraph } = Typography;

const WaterfallTest: React.FC = () => {
  const [value, setValue] = useState<(string | number)[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<CascaderItem[]>([]);
  const [loadHistory, setLoadHistory] = useState<string[]>([]);
  const [debugInfo, setDebugInfo] = useState<any[]>([]);

  // 处理级联选择器值变化
  const handleChange = (newValue: (string | number)[], options: CascaderItem[]) => {
    console.log('🔄 选择值变化:', newValue, options);
    setValue(newValue);
    setSelectedOptions(options);

    // 记录选择历史
    const timestamp = new Date().toLocaleTimeString();
    const pathStr = options.map(opt => opt.label).join(' → ');
    setLoadHistory(prev => [...prev, `${timestamp}: 选择 ${pathStr}`]);

    // 记录调试信息
    setDebugInfo(prev => [...prev, {
      timestamp,
      type: 'CHANGE',
      value: newValue,
      options: options.map(opt => ({
        label: opt.label,
        value: opt.value,
        isLoadMore: String(opt.value).includes('load_more_')
      }))
    }]);
  };

  // 处理统计信息加载
  const handleStatisticsLoad = (workItemKey: string, instanceKey: string, nodeKey: string) => {
    console.log('📊 统计信息加载:', { workItemKey, instanceKey, nodeKey });
    
    const timestamp = new Date().toLocaleTimeString();
    setLoadHistory(prev => [...prev, `${timestamp}: 统计加载 ${workItemKey}→${instanceKey}→${nodeKey}`]);

    Notification.success({
      title: '统计信息加载',
      content: `已触发统计信息加载: ${workItemKey} → ${instanceKey} → ${nodeKey}`,
      duration: 3
    });
  };

  // 清空选择和历史
  const handleClear = () => {
    setValue([]);
    setSelectedOptions([]);
    setLoadHistory([]);
    setDebugInfo([]);
  };

  // 自定义显示格式
  const displayRender = (labels: string[]) => {
    return labels.join(' → ');
  };

  // 获取当前级别状态
  const getCurrentLevelStatus = () => {
    const level = value.length;
    const levelNames = ['未选择', '工作项', '实例', '节点', '任务'];
    return {
      level,
      name: levelNames[level] || '未知',
      isComplete: level === 4
    };
  };

  const status = getCurrentLevelStatus();

  return (
    <div style={{ padding: '24px', maxWidth: '1000px', margin: '0 auto' }}>
      <Title heading={2}>🌊 瀑布流分页测试</Title>
      
      <Paragraph>
        测试重构后的WaterfallCascader组件的瀑布流分页加载效果。
        <br />
        <strong>支持分页的级别：</strong>实例(第2级) 和 任务(第4级)，每页5条数据。
      </Paragraph>

      <Card title="级联选择器测试" style={{ marginBottom: '24px' }}>
        <Space vertical style={{ width: '100%' }}>
          <WaterfallCascader
            value={value}
            onChange={handleChange}
            onStatisticsLoad={handleStatisticsLoad}
            placeholder="请选择工作内容（测试瀑布流分页）"
            displayRender={displayRender}
            style={{ width: '500px' }}
          />
          
          <Space>
            <Button onClick={handleClear} type="secondary">
              清空选择
            </Button>
            <Tag color={status.isComplete ? 'green' : 'blue'}>
              当前级别: {status.level}/4 - {status.name}
            </Tag>
          </Space>
        </Space>
      </Card>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '24px' }}>
        <Card title="选择状态">
          <Space vertical>
            <div>
              <Text strong>当前选择值: </Text>
              <Text code>{JSON.stringify(value)}</Text>
            </div>

            <div>
              <Text strong>选择路径: </Text>
              <Text>
                {selectedOptions.map(opt => opt.label).join(' → ') || '未选择'}
              </Text>
            </div>

            <div>
              <Text strong>选择级别: </Text>
              <Text>{value.length}/4 级</Text>
            </div>

            <div>
              <Text strong>是否完整: </Text>
              <Tag color={status.isComplete ? 'green' : 'orange'}>
                {status.isComplete ? '完整路径' : '未完成'}
              </Tag>
            </div>
          </Space>
        </Card>

        <Card title="操作历史">
          <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
            {loadHistory.length === 0 ? (
              <Text type="tertiary">暂无操作记录</Text>
            ) : (
              <Space vertical style={{ width: '100%' }}>
                {loadHistory.map((item, index) => (
                  <div key={index} style={{
                    padding: '8px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}>
                    {item}
                  </div>
                ))}
              </Space>
            )}
          </div>
        </Card>

        <Card title="调试信息">
          <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
            {debugInfo.length === 0 ? (
              <Text type="tertiary">暂无调试信息</Text>
            ) : (
              <Space vertical style={{ width: '100%' }}>
                {debugInfo.slice(-10).map((info, index) => (
                  <div key={index} style={{
                    padding: '8px',
                    backgroundColor: info.type === 'CHANGE' ? '#e6f7ff' : '#fff7e6',
                    borderRadius: '4px',
                    fontSize: '11px',
                    border: '1px solid #d9d9d9'
                  }}>
                    <div><strong>{info.timestamp}</strong> - {info.type}</div>
                    <div>值: {JSON.stringify(info.value)}</div>
                    {info.options && (
                      <div>选项: {info.options.map(opt =>
                        `${opt.label}${opt.isLoadMore ? ' [加载更多]' : ''}`
                      ).join(', ')}</div>
                    )}
                  </div>
                ))}
              </Space>
            )}
          </div>
        </Card>
      </div>

      <Card title="测试指南" style={{ marginTop: '24px' }}>
        <Space vertical>
          <div>
            <Text strong>1. 选择工作项</Text>
            <Text> - 第一级，一次性加载所有数据</Text>
          </div>
          <div>
            <Text strong>2. 选择实例</Text>
            <Text> - 第二级，支持分页，每页5条，观察"📄 加载更多"按钮</Text>
          </div>
          <div>
            <Text strong>3. 选择节点</Text>
            <Text> - 第三级，一次性加载所有数据</Text>
          </div>
          <div>
            <Text strong>4. 选择任务</Text>
            <Text> - 第四级，支持分页，每页5条，观察"📄 加载更多"按钮</Text>
          </div>
          <div>
            <Text strong>5. 完成选择</Text>
            <Text> - 选择完整四级路径后，自动触发统计信息加载</Text>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default WaterfallTest;
