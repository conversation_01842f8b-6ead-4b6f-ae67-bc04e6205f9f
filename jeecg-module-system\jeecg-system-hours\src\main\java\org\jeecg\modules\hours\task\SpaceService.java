package org.jeecg.modules.hours.task;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.jeecg.modules.hours.context.AuthContext;
import org.jeecg.modules.hours.entity.FsProject;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.service.IFsCacheService;
import org.jeecg.modules.hours.service.IFsProjectService;
import org.jeecg.modules.hours.service.IFsSyncLogService;
import org.jeecg.modules.hours.service.ISysWorkItemConfigService;
import org.jeecg.modules.hours.utils.RedisLockUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.core.utils.Jsons;
import com.lark.project.service.project.builder.GetProjectDetailReq;
import com.lark.project.service.project.builder.GetProjectDetailResp;
import com.lark.project.service.project.model.Project;
import com.lark.project.service.user.builder.QueryUserDetailReq;
import com.lark.project.service.user.builder.QueryUserDetailResp;
import com.lark.project.service.user.model.UserBasicInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 工作空间初始化
 * 
 * @ClassName SpaceTask
 * <AUTHOR>
 * @date 2025年6月7日
 * @version V0.1
 *
 */
@Slf4j
@Component
public class SpaceService {

	// 飞书应用ID，从配置文件中注入
	@Value("${feishu.project.plugin.id}")
	private String pluginId;

	// 飞书应用Secret，从配置文件中注入
	@Value("${feishu.project.plugin.secret}")
	private String pluginSecret;

	// 飞书API基础URL
	@Value("${feishu.api.base-url:https://project.feishu.cn/}")
	private String feishuApiBaseUrl;

//	public FsProject initProject(FsProject project) throws Exception {
//
//		Project space = this.getSpaceDetail(project.getProjectKey());
//
//		project.setProjectKey(space.getSimpleName());
//		
//		List<UserBasicInfo> userList = this.getUserList(space.getAdministrators());
//		
//		
//		
//		return project;
//	}
	
	
	public List<UserBasicInfo> getUserList(List<String> userKeys) throws Exception {
		log.info("使用飞书应用凭证: pluginId={}, ", pluginId );

		Client client = Client.newBuilder(pluginId, pluginSecret).openBaseUrl(feishuApiBaseUrl).build();

		// 从认证上下文获取token和userKey
		AuthContext authContext = AuthContext.getContext();
		if (authContext == null) {
			throw new Exception("无法获取认证上下文，请确保在Web请求上下文中调用");
		}

		String token = authContext.getToken();
		String userKey = authContext.getUserKey();

		// 验证token和userKey是否有效
		if (token == null || token.isEmpty()) {
			throw new Exception("无法获取有效的访问令牌(token)，请检查飞书授权配置");
		}

		if (userKey == null || userKey.isEmpty()) {
			throw new Exception("无法获取有效的用户标识(userKey)，请检查飞书授权配置");
		}

		log.info("调用飞书API: userKey={}, tokenLength={}", userKey, token.length());
		
		QueryUserDetailReq req = QueryUserDetailReq.newBuilder().userKeys(userKeys).build();

		// 构建请求选项
		RequestOptions options = RequestOptions.newBuilder().userKey(userKey).accessToken(token).build();

		// 调用飞书API
		QueryUserDetailResp resp1 = client.getUserService().queryUserDetail(req, options);
		if(null!=resp1.getErr() && resp1.getErr().getCode()!=0) {
			log.error("errCode={},errMsg={}",resp1.getErrCode(),resp1.getErrMsg());
		}

		// 业务数据处理
		log.info("resp={}", Jsons.DEFAULT.toJson(resp1.getData()));

		if (resp1.getData() != null && !CollectionUtils.isEmpty(resp1.getData())) {
			return resp1.getData();
		}

		return null;
	}
	
	
	

	/** 
	 * 初始化空间详情
	 * @param projectKey
	 * @throws Exception
	 * <AUTHOR>
	 * @date 2025年6月7日 
	 * @version V0.1 
	 * 
	 */
	public Project getSpaceDetail(String projectKey) throws Exception {
		log.info("使用飞书应用凭证: pluginId={}, projectKey={}", pluginId, projectKey);

		Client client = Client.newBuilder(pluginId, pluginSecret).openBaseUrl(feishuApiBaseUrl).build();

		// 从认证上下文获取token和userKey
		AuthContext authContext = AuthContext.getContext();
		if (authContext == null) {
			throw new Exception("无法获取认证上下文，请确保在Web请求上下文中调用");
		}

		String token = authContext.getToken();
		String userKey = authContext.getUserKey();

		// 验证token和userKey是否有效
		if (token == null || token.isEmpty()) {
			throw new Exception("无法获取有效的访问令牌(token)，请检查飞书授权配置");
		}

		if (userKey == null || userKey.isEmpty()) {
			throw new Exception("无法获取有效的用户标识(userKey)，请检查飞书授权配置");
		}

		log.info("调用飞书API: userKey={}, tokenLength={}", userKey, token.length());
		GetProjectDetailReq req = GetProjectDetailReq.newBuilder().userKey(userKey)
				.projectKeys(Lists.newArrayList(projectKey)).build();

		// 构建请求选项
		RequestOptions options = RequestOptions.newBuilder().userKey(userKey).accessToken(token).build();

		// 调用飞书API
		GetProjectDetailResp resp1 = client.getProjectService().getProjectDetail(req, options);
		if(null!=resp1.getErr() && resp1.getErr().getCode()!=0) {
			log.error("errCode={},errMsg={}",resp1.getErrCode(),resp1.getErrMsg());
		}

		// 业务数据处理
		log.info("resp={}", Jsons.DEFAULT.toJson(resp1.getData()));

		if (resp1.getData() != null && resp1.getData().containsKey(projectKey)) {
			return resp1.getData().get(projectKey);
		}

		return null;
	}

}