package org.jeecg.modules.hours.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.shiro.util.CollectionUtils;
import org.jeecg.modules.feishu.service.FeiShuUserService;
import org.jeecg.modules.hours.constant.ConfigKeyConstant;
import org.jeecg.modules.hours.entity.FsRole;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.entity.FsUserRole;
import org.jeecg.modules.hours.mapper.FsUserMapper;
import org.jeecg.modules.hours.service.IFsDepartmentUserService;
import org.jeecg.modules.hours.service.IFsRoleService;
import org.jeecg.modules.hours.service.IFsUserRoleService;
import org.jeecg.modules.hours.service.IFsUserService;
import org.jeecg.modules.hours.service.ISysConfigService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.jeecg.modules.hours.utils.UserInfoUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lark.oapi.service.contact.v3.model.User;
import com.lark.project.service.user.model.UserBasicInfo;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.hours.mapper.FsDepartmentUserMapper;
import java.util.Objects;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date: 2024-06-17
 * @Version: V1.0
 */
@Slf4j
@Service
public class FsUserServiceImpl extends ServiceImpl<FsUserMapper, FsUser> implements IFsUserService {

	/**
	 * 默认密码
	 */
	private static final String DEFAULT_PASSWORD = "123456";

	/**
	 * 飞书项目Key的请求头名称
	 */
	private static final String PROJECT_KEY_HEADER = "x-feishu-project-key";

	/**
	 * 管理员角色编码
	 */
	private static final String ADMIN_ROLE_CODE = "admin";

	@Autowired
	private IFsUserRoleService userRoleService;

	@Autowired
	private IFsRoleService roleService;

	@Autowired
	private UserInfoUtils userInfoUtils;

	@Autowired
	private IFsDepartmentUserService fsDepartmentUserService;

	@Lazy
	@Autowired
	private ISysConfigService sysConfigService;

	/**
	 * 获取当前登录用户信息
	 */
	private String getCurrentUserId() {
		try {
			// 使用UserInfoUtils获取系统内部用户ID
			String userId = userInfoUtils.getUserSystemId();
			if (userId != null && !userId.isEmpty()) {
				return userId;
			}
		} catch (Exception e) {
			log.error("获取当前登录用户信息失败", e);
		}
		return "system"; // 默认为系统用户
	}

	@Override
	public List<FsUser> getUsersByDepartmentId(String departmentId) {
		LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsUser::getDeptId, departmentId);
		queryWrapper.eq(FsUser::getStatus, "1"); // 假设状态1为正常状态
		return this.list(queryWrapper);
	}

	@Override
	public List<FsUser> getUsersByProjectId(String projectId) {
		// 此处应该有用户-项目关联表，此处是简化实现
		// 实际应该通过关联表查询
		// 这里暂时返回null，需要根据实际情况补充代码
		return null;
	}

	@Override
	public boolean resetPassword(String userId) {
		FsUser user = this.getById(userId);
		if (user != null) {
			// 使用MD5加密默认密码
//            String encryptedPassword = DigestUtils.md5DigestAsHex((DEFAULT_PASSWORD).getBytes());
//            user.setPassword(encryptedPassword);
			return this.updateById(user);
		}
		return false;
	}

	@Override
	public boolean save(FsUser entity) {
		// 设置创建时间和创建人
		entity.setCreatedAt(new Date());
		entity.setCreateBy(getCurrentUserId());

		// 设置飞书项目Key
		String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
		if (projectKey != null) {
			entity.setProjectKey(projectKey);
		}

		return super.save(entity);
	}

	@Override
	public boolean saveBatch(Collection<FsUser> entityList) {
		// 设置创建时间和创建人
		String username = getCurrentUserId();
		Date now = new Date();

		// 获取飞书项目Key
		String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();

		entityList.forEach(entity -> {
			entity.setCreatedAt(now);
			entity.setCreateBy(username);

			// 设置飞书项目Key
			if (projectKey != null) {
				entity.setProjectKey(projectKey);
			}
		});
		return super.saveBatch(entityList);
	}

	@Override
	public boolean importUsers(MultipartFile file) {
		boolean result = false;
		try {
			// 使用 jeecg 提供的 Excel 导入工具
			ImportParams params = new ImportParams();
			params.setHeadRows(1);
			params.setTitleRows(0);

			// 解析Excel文件
			List<FsUser> users = ExcelImportUtil.importExcel(file.getInputStream(), FsUser.class, params);

			if (users == null || users.isEmpty()) {
				return false;
			}

			// 处理导入数据
			List<FsUser> validUsers = new ArrayList<>();
			String username = getCurrentUserId();
			Date now = new Date();

			// 获取飞书项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();

			for (FsUser user : users) {
				// 设置默认状态
				if (user.getStatus() == null || user.getStatus().isEmpty()) {
					user.setStatus("1"); // 假设状态1为正常状态
				}

				// 设置创建时间和创建人
				user.setCreatedAt(now);
				user.setCreateBy(username);

				// 设置飞书项目Key
				if (projectKey != null) {
					user.setProjectKey(projectKey);
				}

				validUsers.add(user);
			}

			// 批量保存
			if (!validUsers.isEmpty()) {
				result = this.saveBatch(validUsers);
			}

		} catch (Exception e) {
			log.error("导入用户数据失败", e);
			result = false;
		}

		return result;
	}

	@Override
	public List<FsUser> getAdminList() {
		// 声明在方法开始处，确保finally可以访问
		List<FsUser> adminUsers = new ArrayList<>();

		try {
			// 获取当前项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();

			// 获取管理员角色
			FsRole adminRole = roleService.getRoleByCode(ADMIN_ROLE_CODE);
			if (adminRole == null) {
				log.error("管理员角色不存在");
				return adminUsers;
			}

			// 查询所有用户（可以根据项目Key过滤）
			LambdaQueryWrapper<FsUser> userQueryWrapper = new LambdaQueryWrapper<>();
			if (projectKey != null) {
				userQueryWrapper.eq(FsUser::getProjectKey, projectKey);
			}

			List<FsUser> users = this.list(userQueryWrapper);

			// 查询角色-用户关联信息
			List<FsUserRole> userRoles = userRoleService.getUserRolesByRoleId(adminRole.getId());

			// 收集所有创建人ID，确保能查询到所有可能的创建人
			Set<String> allCreatorIds = new HashSet<>();
			for (FsUserRole userRole : userRoles) {
				if (userRole.getCreateBy() != null && !userRole.getCreateBy().isEmpty()) {
					allCreatorIds.add(userRole.getCreateBy());
				}
			}

			// 查询所有创建人的用户信息
			List<FsUser> allCreators = new ArrayList<>();
			if (!allCreatorIds.isEmpty()) {
				LambdaQueryWrapper<FsUser> creatorQueryWrapper = new LambdaQueryWrapper<>();
				creatorQueryWrapper.in(FsUser::getId, allCreatorIds);
				creatorQueryWrapper.eq(FsUser::getProjectKey, projectKey);
				allCreators = this.list(creatorQueryWrapper);

				// 如果找不到某些创建人，再尝试通过userKey查找
				if (allCreators.size() < allCreatorIds.size()) {
					creatorQueryWrapper = new LambdaQueryWrapper<>();
					creatorQueryWrapper.in(FsUser::getUserKey, allCreatorIds);
					creatorQueryWrapper.eq(FsUser::getProjectKey, projectKey);
					allCreators.addAll(this.list(creatorQueryWrapper));
				}
			}

			// 创建多种可能的ID到用户名的映射，确保能覆盖所有情况
			Map<String, String> idToNameMap = new HashMap<>();

			// 1. 先映射所有普通用户
			for (FsUser user : users) {
				// 映射系统ID和userKey到用户名
				idToNameMap.put(user.getId(), user.getName());
				if (user.getUserKey() != null) {
					idToNameMap.put(user.getUserKey(), user.getName());
				}
			}

			// 2. 再映射所有创建人（可能包含不在当前项目中的用户）
			for (FsUser creator : allCreators) {
				idToNameMap.put(creator.getId(), creator.getName());
				if (creator.getUserKey() != null) {
					idToNameMap.put(creator.getUserKey(), creator.getName());
				}
			}

			// 添加日志用于调试
			log.debug("ID映射表大小: {}", idToNameMap.size());

			// 按用户ID分组获取最新的用户角色关系
			Map<String, FsUserRole> latestUserRoles = new HashMap<>();
			for (FsUserRole userRole : userRoles) {
				String userId = userRole.getUserId();
				if (!latestUserRoles.containsKey(userId)
						|| userRole.getCreatedAt().after(latestUserRoles.get(userId).getCreatedAt())) {
					latestUserRoles.put(userId, userRole);
				}
			}

			List<String> unionIds = users.stream().filter(item -> StringUtils.isNotEmpty(item.getUnionId()))
					.map(item -> item.getUnionId()).collect(Collectors.toList());
			Map<String, String> userDeptMap = fsDepartmentUserService.selectDeptNamesByUnionIds(unionIds);

			// 筛选有管理员角色的用户
			for (FsUser user : users) {
				if (latestUserRoles.containsKey(user.getId())) {
					// 使用角色-用户关联表中的创建者信息
					FsUserRole userRole = latestUserRoles.get(user.getId());

					// 设置创建人姓名 - 使用fs_user_role表的create_by
					if (userRole.getCreateBy() != null && !userRole.getCreateBy().isEmpty()) {
						user.setCreateBy(userRole.getCreateBy()); // 更新createBy为用户角色表中的创建者

						// 尝试从映射表中获取用户名，如果没找到则使用ID作为名称
						String creatorName = idToNameMap.get(userRole.getCreateBy());
						if (creatorName != null && !creatorName.isEmpty()) {
							user.setCreateByName(creatorName);
						} else {
							user.setCreateByName(userRole.getCreateBy());
							// 记录日志用于调试
							log.warn("未找到创建人 {} 的用户名", userRole.getCreateBy());
						}
					}

					// 设置创建时间 - 使用fs_user_role表的created_at
					if (userRole.getCreatedAt() != null) {
						user.setCreatedAt(userRole.getCreatedAt());
					}

					// 设置部门名
					String deptName = userDeptMap.get(user.getUnionId());
					user.setDepartmentName(deptName);

					adminUsers.add(user);
				}
			}
		} catch (Exception e) {
			log.error("获取管理员列表失败", e);
		} finally {
			// 添加更多调试日志
			if (log.isDebugEnabled()) {
				log.debug("===== 管理员列表信息 =====");
				int index = 0;
				for (FsUser admin : adminUsers) {
					log.debug("管理员[{}]: ID={}, 名称={}, 创建人ID={}, 创建人名称={}", index++, admin.getId(), admin.getName(),
							admin.getCreateBy(), admin.getCreateByName());
				}
				log.debug("===== 管理员列表信息结束 =====");
			}
		}

		return adminUsers;
	}

	@Override
	public boolean removeAdmin(String userId) {
		try {
			// 查询用户是否存在
			FsUser user = this.getById(userId);
			if (user == null) {
				log.error("用户不存在，无法移除管理员权限: {}", userId);
				return false;
			}

			// 验证是否是当前项目的用户
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			if (projectKey != null && !projectKey.equals(user.getProjectKey())) {
				log.error("无法移除非当前项目的管理员: {}", userId);
				return false;
			}

			// 获取管理员角色
			FsRole adminRole = roleService.getRoleByCode(ADMIN_ROLE_CODE);
			if (adminRole == null) {
				log.error("管理员角色不存在");
				return false;
			}

			// 移除用户的管理员角色
			return userRoleService.removeRole(userId, adminRole.getId());
		} catch (Exception e) {
			log.error("移除管理员权限失败", e);
			return false;
		}
	}

	@Override
	public boolean addAdmin(String id) {
		try {
			// 查询用户是否存在
			FsUser user = this.getById(id);
			if (user == null) {
				log.error("用户不存在，无法添加为管理员: {}", id);
				return false;
			}

			// 设置项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			if (projectKey != null && (user.getProjectKey() == null || user.getProjectKey().isEmpty())) {
				user.setProjectKey(projectKey);
				this.updateById(user);
			}

			// 获取管理员角色
			FsRole adminRole = roleService.getRoleByCode(ADMIN_ROLE_CODE);
			if (adminRole == null) {
				log.error("管理员角色不存在");
				return false;
			}

			// 为用户分配管理员角色
			return userRoleService.assignRole(id, adminRole.getId());
		} catch (Exception e) {
			log.error("添加管理员权限失败", e);
			return false;
		}
	}

	@Override
	public boolean saveAdminList(List<UserBasicInfo> userList, String projectKey) {
		try {
			// 批量查询现有用户
			List<String> userKeys = userList.stream().map(UserBasicInfo::getUserKey).collect(Collectors.toList());
			List<FsUser> fsUserList = this.getUserByProjectAndUserKeys(projectKey, userKeys);
			Map<String, FsUser> existingUsers = new HashMap<String, FsUser>();
			if (!CollectionUtils.isEmpty(fsUserList)) {
				existingUsers = fsUserList.stream().collect(Collectors.toMap(FsUser::getUserKey, Function.identity()));
			}

			// 获取管理员角色
			FsRole adminRole = roleService.getRoleByCode(ADMIN_ROLE_CODE);
			if (adminRole == null) {
				log.error("管理员角色不存在");
				return false;
			}
			
			List<String> unionIds = fsUserList.stream().filter(item->StringUtils.isNotEmpty(item.getUnionId())).map(item->item.getUnionId()).collect(Collectors.toList());
			Map<String,User> openUserMap = this.getFsOpenUser(projectKey, unionIds);

			// 批量处理用户
			List<FsUser> toSaveUsers = new ArrayList<>();
			List<FsUser> toUpdateUsers = new ArrayList<>();
			List<String> toAssignRoleUserIds = new ArrayList<>();

			for (UserBasicInfo userInfo : userList) {
				FsUser user = existingUsers.get(userInfo.getUserKey());
				if (user == null) {
					log.info("\n\nadd>projectKey={}\n", projectKey);
					// 新增用户
					user = new FsUser().setUserKey(userInfo.getUserKey()).setUnionId(userInfo.getOutID())
							.setName(userInfo.getNameCn()).setEmail(userInfo.getEmail()).setProjectKey(projectKey)
							.setSyncTime(Calendar.getInstance().getTime())
							.setCreatedAt(Calendar.getInstance().getTime());
					user.setUserId(userInfo.getUserKey());
					user.setCreateBy("system");
					// 枚举：initialized：初始（未登陆过）；resigned：离职；frozen：冻结；activated：使用中
					if ("resigned".equals(userInfo.getStatus())) {
						user.setStatus("0");
					} else {
						user.setStatus("1");
					}
					String unionId = user.getUnionId();
					if(StringUtils.isNotEmpty(unionId) && openUserMap.containsKey(unionId)) {
						user.setLeaderUserId(openUserMap.get(unionId).getLeaderUserId());
					}
					user.setId(UUID.randomUUID().toString().replaceAll("-", ""));
					toSaveUsers.add(user);
					toAssignRoleUserIds.add(user.getId());
					log.info("id={},size={}", user.getId(), toSaveUsers.size());
				} else {
					// 更新用户
					user.setUnionId(userInfo.getOutID()).setName(userInfo.getNameCn()).setEmail(userInfo.getEmail())
							.setProjectKey(projectKey).setSyncTime(Calendar.getInstance().getTime());
					String unionId = user.getUnionId();
					if(StringUtils.isNotEmpty(unionId) && openUserMap.containsKey(unionId)) {
						user.setLeaderUserId(openUserMap.get(unionId).getLeaderUserId());
					}
					toUpdateUsers.add(user);
				}
			}

			// 批量操作
			if (!toSaveUsers.isEmpty()) {
				super.saveBatch(toSaveUsers);
			}
			if (!toUpdateUsers.isEmpty()) {
				this.updateBatchById(toUpdateUsers);
			}

			// 批量分配角色
			return userRoleService.batchAssignRole(toAssignRoleUserIds, adminRole.getId());
		} catch (Exception e) {
			log.error("批量保存管理员失败", e);
			return false;
		}
	}

	@Override
	public boolean saveNormalList(List<UserBasicInfo> userList, String projectKey) {
		try {
			// 批量查询现有用户
			List<String> userKeys = userList.stream().map(UserBasicInfo::getUserKey).collect(Collectors.toList());
			List<FsUser> fsUserList = this.getUserByProjectAndUserKeys(projectKey, userKeys);

			Map<String, FsUser> existingUsers = new HashMap<String, FsUser>();
			if (!CollectionUtils.isEmpty(fsUserList)) {
				existingUsers = fsUserList.stream().collect(Collectors.toMap(FsUser::getUserKey, Function.identity()));
			}

			List<String> unionIds = fsUserList.stream().filter(item->StringUtils.isNotEmpty(item.getUnionId())).map(item->item.getUnionId()).collect(Collectors.toList());
			Map<String,User> openUserMap = this.getFsOpenUser(projectKey, unionIds);
			
			// 批量处理用户
			List<FsUser> toSaveUsers = new ArrayList<>();
			List<FsUser> toUpdateUsers = new ArrayList<>();
			List<String> toAssignRoleUserIds = new ArrayList<>();

			for (UserBasicInfo userInfo : userList) {
				FsUser user = existingUsers.get(userInfo.getUserKey());
				if (user == null) {
					log.info("\n\nadd>projectKey={}\n", projectKey);
					// 新增用户
					user = new FsUser().setUserKey(userInfo.getUserKey()).setUnionId(userInfo.getOutID())
							.setName(userInfo.getNameCn()).setEmail(userInfo.getEmail()).setProjectKey(projectKey)
							.setSyncTime(Calendar.getInstance().getTime())
							.setCreatedAt(Calendar.getInstance().getTime());
					user.setUserId(userInfo.getUserKey());
					user.setCreateBy("system");
					// 枚举：initialized：初始（未登陆过）；resigned：离职；frozen：冻结；activated：使用中
					if ("resigned".equals(userInfo.getStatus())) {
						user.setStatus("0");
					} else {
						user.setStatus("1");
					}
					String unionId = user.getUnionId();
					if(StringUtils.isNotEmpty(unionId) && openUserMap.containsKey(unionId)) {
						user.setLeaderUserId(openUserMap.get(unionId).getLeaderUserId());
					}
					user.setId(UUID.randomUUID().toString().replaceAll("-", ""));
					toSaveUsers.add(user);
					toAssignRoleUserIds.add(user.getId());
					log.info("id={},size={}", user.getId(), toSaveUsers.size());
				} else {
					// 更新用户
					user.setUnionId(userInfo.getOutID()).setName(userInfo.getNameCn()).setEmail(userInfo.getEmail())
							.setProjectKey(projectKey).setSyncTime(Calendar.getInstance().getTime());
					String unionId = user.getUnionId();
					if(StringUtils.isNotEmpty(unionId) && openUserMap.containsKey(unionId)) {
						user.setLeaderUserId(openUserMap.get(unionId).getLeaderUserId());
					}
					toUpdateUsers.add(user);
				}
			}

			// 批量操作
			if (!toSaveUsers.isEmpty()) {
				super.saveBatch(toSaveUsers);
			}
			if (!toUpdateUsers.isEmpty()) {
				this.updateBatchById(toUpdateUsers);
			}
			return true;
		} catch (Exception e) {
			log.error("批量保存管理员失败", e);
			return false;
		}
	}

	@Override
	public Map<String, User> getFsOpenUser(String projectKey, List<String> unionIds) {
		Map<String, User> userMap = new HashMap<String, User>();
		// 3.获取工作空间的配置appId、appSecret
		String appId = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.BIND_APP_ID);
		String appSecret = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.BIND_APP_SECRET);
		if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(appSecret)) {
			log.warn("项目{}配置无效,appId={},appSecret={}", projectKey, appId, appSecret);
//			exceptionBuilder.append("projectKey="+projectKey+"配置无效,appId="+appId+",appSecret="+appSecret);
//			continue;
			return userMap;
		}
		if (CollectionUtils.isEmpty(unionIds)) {
			return userMap;
		}

		List<User> users;
		try {
			users = FeiShuUserService.instance(appId, appSecret).getUsers(unionIds.toArray(new String[0]));
			if (CollectionUtils.isEmpty(users)) {
				return userMap;
			}

			for (User user : users) {
				userMap.put(user.getUnionId(), user);
			}

		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return userMap;

	}

	@Override
	public List<FsUser> getAllUsersForAdminSelection() {
		try {
			// 获取当前项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			if (projectKey == null || projectKey.isEmpty()) {
				log.error("获取项目Key失败，无法查询可选管理员用户");
				return new ArrayList<>();
			}

			// 使用mapper方法直接查询未分配管理员角色的活跃用户
			return baseMapper.getNonAdminActiveUsersByProject(projectKey, ADMIN_ROLE_CODE);
		} catch (Exception e) {
			log.error("获取可选管理员用户列表失败", e);
			return new ArrayList<>();
		}
	}

	@Override
	public FsUser getAdminUserLimit1(String projectKey) {
		// 获取管理员角色
		FsRole adminRole = roleService.getOne(new LambdaQueryWrapper<FsRole>().eq(FsRole::getCode, ADMIN_ROLE_CODE));

		// 查询用户-角色关联
		return baseMapper.getAdminUserLimit1(projectKey, adminRole.getId());
	}

	@Override
	public List<String> getUnionIds(String projectKey) {

		LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsUser::getProjectKey, projectKey);

		List<FsUser> userList = this.list(queryWrapper);

		return userList.stream().filter(item -> StringUtils.isNotEmpty(item.getUnionId()))
				.map(item -> item.getUnionId()).collect(Collectors.toList());
	}

	@Override
	public boolean isAdmin(String userId) {
		// 获取管理员角色
		FsRole adminRole = roleService.getOne(new LambdaQueryWrapper<FsRole>().eq(FsRole::getCode, ADMIN_ROLE_CODE));

		if (adminRole == null) {
			return false;
		}

		// 查询用户-角色关联
		return userRoleService.count(new LambdaQueryWrapper<FsUserRole>().eq(FsUserRole::getUserId, userId)
				.eq(FsUserRole::getRoleId, adminRole.getId())) > 0;
	}

	@Override
	public int batchAddAdmins(List<String> userIds) {
		if (userIds == null || userIds.isEmpty()) {
			return 0;
		}

		int successCount = 0;

		try {
			// 获取当前项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();

			// 获取管理员角色
			FsRole adminRole = roleService.getRoleByCode(ADMIN_ROLE_CODE);
			if (adminRole == null) {
				log.error("管理员角色不存在，无法添加管理员");
				return 0;
			}

			// 记录要分配角色的用户ID列表
			List<String> validUserIds = new ArrayList<>();

			// 验证用户是否存在并设置项目Key
			for (String userId : userIds) {
				FsUser user = this.getById(userId);
				if (user == null) {
					log.warn("用户不存在，无法添加为管理员: {}", userId);
					continue;
				}

				// 设置项目Key
				if (projectKey != null && (user.getProjectKey() == null || user.getProjectKey().isEmpty())) {
					user.setProjectKey(projectKey);
					this.updateById(user);
				}

				// 检查是否已经是管理员
				if (userRoleService.hasRole(userId, ADMIN_ROLE_CODE)) {
					log.info("用户已经是管理员: {}", userId);
					successCount++; // 已经是管理员也计入成功
					continue;
				}

				validUserIds.add(userId);
			}

			// 批量分配管理员角色
			if (!validUserIds.isEmpty()) {
				for (String userId : validUserIds) {
					if (userRoleService.assignRole(userId, adminRole.getId())) {
						successCount++;
					}
				}
			}

			return successCount;
		} catch (Exception e) {
			log.error("批量添加管理员失败", e);
			return successCount; // 返回成功添加的数量
		}
	}

	@Override
	public List<FsUser> getUserByProjectAndUserKeys(String projectKey, List<String> userKeys) {
		if (projectKey == null || "".equals(projectKey)) {
			return null;
		}
		if (userKeys == null || CollectionUtils.isEmpty(userKeys)) {
			return null;
		}

		// 构建查询条件
		LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsUser::getProjectKey, projectKey).in(FsUser::getUserKey, userKeys);

		return this.list(queryWrapper);
	}

	@Override
	public List<String> getUnionIdsByUserKey(String projectKey, List<String> userKeys) {
		List<FsUser> userList = this.getUserByProjectAndUserKeys(projectKey, userKeys);
		if (CollectionUtils.isEmpty(userList)) {
			return null;
		}
		List<String> unionIds = userList.stream().filter(item -> StringUtils.isNotEmpty(item.getUnionId()))
				.map(item -> item.getUnionId()).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(unionIds)) {
			return null;
		}
		return unionIds;
	}

	@Override
	public FsUser getUserByProjectAndUserKey(String projectKey, String userKey) {
		if (StringUtils.isEmpty(projectKey) || StringUtils.isEmpty(userKey)) {
			return null;
		}

		// 构建查询条件
		LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsUser::getProjectKey, projectKey).eq(FsUser::getUserKey, userKey).last("LIMIT 1"); // 只取第一条记录

		return this.getOne(queryWrapper);
	}
	
	@Override
	public FsUser getUserByProjectAndUnionId(String projectKey, String unionId) {
		if (StringUtils.isEmpty(projectKey) || StringUtils.isEmpty(unionId)) {
			return null;
		}

		// 构建查询条件
		LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(FsUser::getProjectKey, projectKey)
		.eq(FsUser::getUnionId, unionId).last("LIMIT 1"); // 只取第一条记录

		return this.getOne(queryWrapper);
	}

	@Override
	public List<FsUser> getActiveUsersByProject() {
		try {
			// 从请求header中获取项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			if (projectKey == null || projectKey.trim().isEmpty()) {
				log.warn("无法从请求header中获取项目Key");
				return new ArrayList<>();
			}

			log.info("获取项目有效用户列表，项目Key: {}", projectKey);

			// 构建查询条件：项目Key匹配且状态为有效
			LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(FsUser::getProjectKey, projectKey).eq(FsUser::getStatus, "1") // 状态为1表示有效用户
					.orderBy(true, true, FsUser::getName); // 按真实姓名排序

			return this.list(queryWrapper);
		} catch (Exception e) {
			log.error("获取项目有效用户列表失败", e);
			return new ArrayList<>();
		}
	}

	@Override
	public String getUnionIdByUserKey(String userKey) {
		try {
			// 从请求header中获取项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			if (projectKey == null || projectKey.trim().isEmpty()) {
				log.warn("无法从请求header中获取项目Key");
				return null;
			}

			log.info("获取项目有效用户列表，项目Key: {}", projectKey);

			// 构建查询条件：项目Key匹配且状态为有效
			LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(FsUser::getProjectKey, projectKey).eq(FsUser::getUserKey, userKey)
					.eq(FsUser::getStatus, "1") // 状态为1表示有效用户
					.orderBy(true, true, FsUser::getName); // 按真实姓名排序
			List<FsUser> userList = this.list(queryWrapper);
			if (CollectionUtils.isEmpty(userList)) {
				return null;
			}

			return userList.get(0).getUnionId();
		} catch (Exception e) {
			log.error("获取项目有效用户列表失败", e);
//			return new ArrayList<>();
			return null;
		}
	}

}