# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/runtime@^7.0.0", "@babel/runtime@^7.21.0":
  "integrity" "sha512-1x3D2xEk2fRo3PAhwQwu5UubzgiVWSXTBfWpVd2Mx2AzRqJuDJCsgaDVZ7HB5iGzDW1Hl1sWN2mFyKjmR9uAog=="
  "resolved" "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.27.1.tgz"
  "version" "7.27.1"

"@dnd-kit/accessibility@^3.1.1":
  "integrity" "sha512-2P+YgaXF+gRsIihwwY1gCsQSYnu9Zyj2py8kY5fFvUM1qm2WA2u639R6YNVfU4GWr+ZM5mqEsfHZZLoRONbemw=="
  "resolved" "https://registry.npmmirror.com/@dnd-kit/accessibility/-/accessibility-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "tslib" "^2.0.0"

"@dnd-kit/core@^6.0.7", "@dnd-kit/core@^6.0.8":
  "integrity" "sha512-xkGBRQQab4RLwgXxoqETICr6S5JlogafbhNsidmrkVv2YRs5MLwpjoF2qpiGjQt8S9AoxtIV603s0GIUpY5eYQ=="
  "resolved" "https://registry.npmmirror.com/@dnd-kit/core/-/core-6.3.1.tgz"
  "version" "6.3.1"
  dependencies:
    "@dnd-kit/accessibility" "^3.1.1"
    "@dnd-kit/utilities" "^3.2.2"
    "tslib" "^2.0.0"

"@dnd-kit/sortable@^7.0.2":
  "integrity" "sha512-wDkBHHf9iCi1veM834Gbk1429bd4lHX4RpAwT0y2cHLf246GAvU2sVw/oxWNpPKQNQRQaeGXhAVgrOl1IT+iyA=="
  "resolved" "https://registry.npmmirror.com/@dnd-kit/sortable/-/sortable-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "@dnd-kit/utilities" "^3.2.0"
    "tslib" "^2.0.0"

"@dnd-kit/utilities@^3.2.0", "@dnd-kit/utilities@^3.2.1", "@dnd-kit/utilities@^3.2.2":
  "integrity" "sha512-+MKAJEOfaBe5SmV6t34p80MMKhjvUz0vRrvVJbPT0WElzaOJ/1xs+D+KDv+tD/NE5ujfrChEcshd4fLn0wpiqg=="
  "resolved" "https://registry.npmmirror.com/@dnd-kit/utilities/-/utilities-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "tslib" "^2.0.0"

"@douyinfe/semi-animation-react@2.79.0":
  "integrity" "sha512-FwYNVrm24dZL/Cz4YZUDcwoXoqL+NNfGs19YVfZAJ2utbPTWL0gnN4ygMW4hL+6qLPTnAI5ZkRa5DOUbvZPfqw=="
  "resolved" "https://registry.npmmirror.com/@douyinfe/semi-animation-react/-/semi-animation-react-2.79.0.tgz"
  "version" "2.79.0"
  dependencies:
    "@douyinfe/semi-animation" "2.79.0"
    "@douyinfe/semi-animation-styled" "2.79.0"
    "classnames" "^2.2.6"

"@douyinfe/semi-animation-styled@2.79.0":
  "integrity" "sha512-uCCJjWt6WPbFc8UP6VX77CWb5ZXxvJ6Zs8pEihQPFod7RovpFhTBH1GHWo0wjPOtYr0L4advytMLFRVoqEXKTA=="
  "resolved" "https://registry.npmmirror.com/@douyinfe/semi-animation-styled/-/semi-animation-styled-2.79.0.tgz"
  "version" "2.79.0"

"@douyinfe/semi-animation@2.79.0":
  "integrity" "sha512-4CxP5Hyc94BPN/Ld27Ns17kR1BaxX2v4bl8TIKzAkLkEro0alj5dqLN+xSiRmdKHLmqwn4lRUoK/qQ7w8XJZpA=="
  "resolved" "https://registry.npmmirror.com/@douyinfe/semi-animation/-/semi-animation-2.79.0.tgz"
  "version" "2.79.0"
  dependencies:
    "bezier-easing" "^2.1.0"

"@douyinfe/semi-foundation@2.79.0":
  "integrity" "sha512-ms6nRLm3b7rhT3EV1GlRZtxqsP8/Fy02E71Cpt0vY6FcbBvEpaK9D5uJ0DjNlyHychbcl6Bl9MrNfsN7YBrnSg=="
  "resolved" "https://registry.npmmirror.com/@douyinfe/semi-foundation/-/semi-foundation-2.79.0.tgz"
  "version" "2.79.0"
  dependencies:
    "@douyinfe/semi-animation" "2.79.0"
    "@douyinfe/semi-json-viewer-core" "2.79.0"
    "@mdx-js/mdx" "^3.0.1"
    "async-validator" "^3.5.0"
    "classnames" "^2.2.6"
    "date-fns" "^2.29.3"
    "date-fns-tz" "^1.3.8"
    "fast-copy" "^3.0.1 "
    "lodash" "^4.17.21"
    "lottie-web" "^5.12.2"
    "memoize-one" "^5.2.1"
    "prismjs" "^1.29.0"
    "remark-gfm" "^4.0.0"
    "scroll-into-view-if-needed" "^2.2.24"

"@douyinfe/semi-icons@^2.79.0", "@douyinfe/semi-icons@2.79.0":
  "integrity" "sha512-1eryBYnF+ElgLQaFN2qc/e42LyrSax4LaWVmYn9uvdBhu6luZhujU1zysrQ4BykVsxPzHgMyLixrIHA8eesKvA=="
  "resolved" "https://registry.npmmirror.com/@douyinfe/semi-icons/-/semi-icons-2.79.0.tgz"
  "version" "2.79.0"
  dependencies:
    "classnames" "^2.2.6"

"@douyinfe/semi-illustrations@2.79.0":
  "integrity" "sha512-We5xM7Fn3GvILZYAJEr8c7DlYcA8DNSE1WDgwHvWib7Gu5mjeIYatSJGonqXgNqk2wUwqETi7hMaeGXbKNG08Q=="
  "resolved" "https://registry.npmmirror.com/@douyinfe/semi-illustrations/-/semi-illustrations-2.79.0.tgz"
  "version" "2.79.0"

"@douyinfe/semi-json-viewer-core@2.79.0":
  "integrity" "sha512-v3tpK1B9P9wbjX33PQ1qClgCQ3yKzb0RYRAOO4UtSKLcJvdYvBcHMM/QIdNBGjLUWg1oT85AQWx026vDF/pSGw=="
  "resolved" "https://registry.npmmirror.com/@douyinfe/semi-json-viewer-core/-/semi-json-viewer-core-2.79.0.tgz"
  "version" "2.79.0"
  dependencies:
    "jsonc-parser" "^3.3.1"

"@douyinfe/semi-theme-default@2.79.0":
  "integrity" "sha512-XIrE/QId5ljKRcvU5cwM/mmCzexgjfe+aXsn9m/ZWHEYFA0Y5dvz5IWkGoHChXO9DjggwDTVjGB5OUkKvYbmdg=="
  "resolved" "https://registry.npmmirror.com/@douyinfe/semi-theme-default/-/semi-theme-default-2.79.0.tgz"
  "version" "2.79.0"

"@douyinfe/semi-ui@^2.79.0":
  "integrity" "sha512-0gSljETVbc7SYg16XVftdxKX9DtI4m7iSzhfz1P+GkREjbX2MsZ6bmRW+C7XFm634ZIYlA6GbQzB8Z3Wfs+XfA=="
  "resolved" "https://registry.npmmirror.com/@douyinfe/semi-ui/-/semi-ui-2.79.0.tgz"
  "version" "2.79.0"
  dependencies:
    "@dnd-kit/core" "^6.0.8"
    "@dnd-kit/sortable" "^7.0.2"
    "@dnd-kit/utilities" "^3.2.1"
    "@douyinfe/semi-animation" "2.79.0"
    "@douyinfe/semi-animation-react" "2.79.0"
    "@douyinfe/semi-foundation" "2.79.0"
    "@douyinfe/semi-icons" "2.79.0"
    "@douyinfe/semi-illustrations" "2.79.0"
    "@douyinfe/semi-theme-default" "2.79.0"
    "async-validator" "^3.5.0"
    "classnames" "^2.2.6"
    "copy-text-to-clipboard" "^2.1.1"
    "date-fns" "^2.29.3"
    "date-fns-tz" "^1.3.8"
    "fast-copy" "^3.0.1 "
    "jsonc-parser" "^3.3.1"
    "lodash" "^4.17.21"
    "prop-types" "^15.7.2"
    "react-resizable" "^3.0.5"
    "react-window" "^1.8.2"
    "scroll-into-view-if-needed" "^2.2.24"
    "utility-types" "^3.10.0"

"@lark-project/js-sdk@^2.0.5", "@lark-project/js-sdk@>=2.0.4-alpha.0":
  "integrity" "sha512-Tv5nHnjB3VCXBeLm92tHIIyEd/4PCloyxGr/L9wFrWXTuWrHIdEaxdf8Zc8GPQffHrhyUQPSGysWqkEhnAHe7Q=="
  "resolved" "https://registry.npmmirror.com/@lark-project/js-sdk/-/js-sdk-2.0.8.tgz"
  "version" "2.0.8"

"@lark-project/ui-kit-plugin@^0.4.0":
  "integrity" "sha512-IbQNmGoi5EH5GEw9QxEQFI+9IkQAH3l8Fk0esz+NU1ECWURXPWxL911PZljxhkRSrBQoYybaJxNceWpraJOjBA=="
  "resolved" "https://registry.npmmirror.com/@lark-project/ui-kit-plugin/-/ui-kit-plugin-0.4.0.tgz"
  "version" "0.4.0"

"@mdx-js/mdx@^3.0.1":
  "integrity" "sha512-/QxEhPAvGwbQmy1Px8F899L5Uc2KZ6JtXwlCgJmjSTBedwOZkByYcBG4GceIGPXRDsmfxhHazuS+hlOShRLeDw=="
  "resolved" "https://registry.npmmirror.com/@mdx-js/mdx/-/mdx-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "@types/estree" "^1.0.0"
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdx" "^2.0.0"
    "collapse-white-space" "^2.0.0"
    "devlop" "^1.0.0"
    "estree-util-is-identifier-name" "^3.0.0"
    "estree-util-scope" "^1.0.0"
    "estree-walker" "^3.0.0"
    "hast-util-to-jsx-runtime" "^2.0.0"
    "markdown-extensions" "^2.0.0"
    "recma-build-jsx" "^1.0.0"
    "recma-jsx" "^1.0.0"
    "recma-stringify" "^1.0.0"
    "rehype-recma" "^1.0.0"
    "remark-mdx" "^3.0.0"
    "remark-parse" "^11.0.0"
    "remark-rehype" "^11.0.0"
    "source-map" "^0.7.0"
    "unified" "^11.0.0"
    "unist-util-position-from-estree" "^2.0.0"
    "unist-util-stringify-position" "^4.0.0"
    "unist-util-visit" "^5.0.0"
    "vfile" "^6.0.0"

"@reduxjs/toolkit@^2.8.2":
  "integrity" "sha512-MYlOhQ0sLdw4ud48FoC5w0dH9VfWQjtCjreKwYTT3l+r427qYC5Y8PihNutepr8XrNaBUDQo9khWUwQxZaqt5A=="
  "resolved" "https://registry.npmmirror.com/@reduxjs/toolkit/-/toolkit-2.8.2.tgz"
  "version" "2.8.2"
  dependencies:
    "@standard-schema/spec" "^1.0.0"
    "@standard-schema/utils" "^0.3.0"
    "immer" "^10.0.3"
    "redux" "^5.0.1"
    "redux-thunk" "^3.1.0"
    "reselect" "^5.1.0"

"@standard-schema/spec@^1.0.0":
  "integrity" "sha512-m2bOd0f2RT9k8QJx1JN85cZYyH1RqFBdlwtkSlf4tBDYLCiiZnv1fIIwacK6cqwXavOydf0NPToMQgpKq+dVlA=="
  "resolved" "https://registry.npmmirror.com/@standard-schema/spec/-/spec-1.0.0.tgz"
  "version" "1.0.0"

"@standard-schema/utils@^0.3.0":
  "integrity" "sha512-e7Mew686owMaPJVNNLs55PUvgz371nKgwsc4vxE49zsODpJEnxgxRo2y/OKrqueavXgZNMDVj3DdHFlaSAeU8g=="
  "resolved" "https://registry.npmmirror.com/@standard-schema/utils/-/utils-0.3.0.tgz"
  "version" "0.3.0"

"@types/debug@^4.0.0":
  "integrity" "sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ=="
  "resolved" "https://registry.npmmirror.com/@types/debug/-/debug-4.1.12.tgz"
  "version" "4.1.12"
  dependencies:
    "@types/ms" "*"

"@types/estree-jsx@^1.0.0":
  "integrity" "sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg=="
  "resolved" "https://registry.npmmirror.com/@types/estree-jsx/-/estree-jsx-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "@types/estree" "*"

"@types/estree@*", "@types/estree@^1.0.0":
  "integrity" "sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ=="
  "resolved" "https://registry.npmmirror.com/@types/estree/-/estree-1.0.7.tgz"
  "version" "1.0.7"

"@types/hast@^3.0.0":
  "integrity" "sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ=="
  "resolved" "https://registry.npmmirror.com/@types/hast/-/hast-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "@types/unist" "*"

"@types/mdast@^4.0.0":
  "integrity" "sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA=="
  "resolved" "https://registry.npmmirror.com/@types/mdast/-/mdast-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "@types/unist" "*"

"@types/mdx@^2.0.0":
  "integrity" "sha512-+OWZQfAYyio6YkJb3HLxDrvnx6SWWDbC0zVPfBRzUk0/nqoDyf6dNxQi3eArPe8rJ473nobTMQ/8Zk+LxJ+Yuw=="
  "resolved" "https://registry.npmmirror.com/@types/mdx/-/mdx-2.0.13.tgz"
  "version" "2.0.13"

"@types/ms@*":
  "integrity" "sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA=="
  "resolved" "https://registry.npmmirror.com/@types/ms/-/ms-2.1.0.tgz"
  "version" "2.1.0"

"@types/prop-types@*":
  "integrity" "sha512-gNMvNH49DJ7OJYv+KAKn0Xp45p8PLl6zo2YnvDIbTd4J6MER2BmWN49TG7n9LvkyihINxeKW8+3bfS2yDC9dzQ=="
  "resolved" "https://registry.npmmirror.com/@types/prop-types/-/prop-types-15.7.14.tgz"
  "version" "15.7.14"

"@types/react-dom@^18.2.0", "@types/react-dom@>=17":
  "integrity" "sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ=="
  "resolved" "https://registry.npmmirror.com/@types/react-dom/-/react-dom-18.3.7.tgz"
  "version" "18.3.7"

"@types/react@^18.0.0", "@types/react@^18.2.0", "@types/react@^18.2.25 || ^19", "@types/react@>=17":
  "integrity" "sha512-gXLBtmlcRJeT09/sI4PxVwyrku6SaNUj/6cMubjE6T6XdY1fDmBL7r0nX0jbSZPU/Xr0KuwLLZh6aOYY5d91Xw=="
  "resolved" "https://registry.npmmirror.com/@types/react/-/react-18.3.21.tgz"
  "version" "18.3.21"
  dependencies:
    "@types/prop-types" "*"
    "csstype" "^3.0.2"

"@types/unist@*", "@types/unist@^3.0.0":
  "integrity" "sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q=="
  "resolved" "https://registry.npmmirror.com/@types/unist/-/unist-3.0.3.tgz"
  "version" "3.0.3"

"@types/unist@^2.0.0":
  "integrity" "sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA=="
  "resolved" "https://registry.npmmirror.com/@types/unist/-/unist-2.0.11.tgz"
  "version" "2.0.11"

"@types/use-sync-external-store@^0.0.6":
  "integrity" "sha512-zFDAD+tlpf2r4asuHEj0XH6pY6i0g5NeAHPn+15wk3BV6JA69eERFXC1gyGThDkVa1zCyKr5jox1+2LbV/AMLg=="
  "resolved" "https://registry.npmmirror.com/@types/use-sync-external-store/-/use-sync-external-store-0.0.6.tgz"
  "version" "0.0.6"

"@ungap/structured-clone@^1.0.0":
  "integrity" "sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g=="
  "resolved" "https://registry.npmmirror.com/@ungap/structured-clone/-/structured-clone-1.3.0.tgz"
  "version" "1.3.0"

"acorn-jsx@^5.0.0":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^8.0.0":
  "integrity" "sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg=="
  "resolved" "https://registry.npmmirror.com/acorn/-/acorn-8.14.1.tgz"
  "version" "8.14.1"

"astring@^1.8.0":
  "integrity" "sha512-LElXdjswlqjWrPpJFg1Fx4wpkOCxj1TDHlSV4PlaRxHGWko024xICaa97ZkMfs6DRKlCguiAI+rbXv5GWwXIkg=="
  "resolved" "https://registry.npmmirror.com/astring/-/astring-1.9.0.tgz"
  "version" "1.9.0"

"async-validator@^3.5.0":
  "integrity" "sha512-8eLCg00W9pIRZSB781UUX/H6Oskmm8xloZfr09lz5bikRpBVDlJ3hRVuxxP1SxcwsEYfJ4IU8Q19Y8/893r3rQ=="
  "resolved" "https://registry.npmmirror.com/async-validator/-/async-validator-3.5.2.tgz"
  "version" "3.5.2"

"bail@^2.0.0":
  "integrity" "sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw=="
  "resolved" "https://registry.npmmirror.com/bail/-/bail-2.0.2.tgz"
  "version" "2.0.2"

"bezier-easing@^2.1.0":
  "integrity" "sha512-gbIqZ/eslnUFC1tjEvtz0sgx+xTK20wDnYMIA27VA04R7w6xxXQPZDbibjA9DTWZRA2CXtwHykkVzlCaAJAZig=="
  "resolved" "https://registry.npmmirror.com/bezier-easing/-/bezier-easing-2.1.0.tgz"
  "version" "2.1.0"

"ccount@^2.0.0":
  "integrity" "sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg=="
  "resolved" "https://registry.npmmirror.com/ccount/-/ccount-2.0.1.tgz"
  "version" "2.0.1"

"character-entities-html4@^2.0.0":
  "integrity" "sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA=="
  "resolved" "https://registry.npmmirror.com/character-entities-html4/-/character-entities-html4-2.1.0.tgz"
  "version" "2.1.0"

"character-entities-legacy@^3.0.0":
  "integrity" "sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ=="
  "resolved" "https://registry.npmmirror.com/character-entities-legacy/-/character-entities-legacy-3.0.0.tgz"
  "version" "3.0.0"

"character-entities@^2.0.0":
  "integrity" "sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ=="
  "resolved" "https://registry.npmmirror.com/character-entities/-/character-entities-2.0.2.tgz"
  "version" "2.0.2"

"character-reference-invalid@^2.0.0":
  "integrity" "sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw=="
  "resolved" "https://registry.npmmirror.com/character-reference-invalid/-/character-reference-invalid-2.0.1.tgz"
  "version" "2.0.1"

"classnames@^2.2.6":
  "integrity" "sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow=="
  "resolved" "https://registry.npmmirror.com/classnames/-/classnames-2.5.1.tgz"
  "version" "2.5.1"

"clsx@^1.1.1":
  "integrity" "sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg=="
  "resolved" "https://registry.npmmirror.com/clsx/-/clsx-1.2.1.tgz"
  "version" "1.2.1"

"collapse-white-space@^2.0.0":
  "integrity" "sha512-loKTxY1zCOuG4j9f6EPnuyyYkf58RnhhWTvRoZEokgB+WbdXehfjFviyOVYkqzEWz1Q5kRiZdBYS5SwxbQYwzw=="
  "resolved" "https://registry.npmmirror.com/collapse-white-space/-/collapse-white-space-2.1.0.tgz"
  "version" "2.1.0"

"comma-separated-tokens@^2.0.0":
  "integrity" "sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg=="
  "resolved" "https://registry.npmmirror.com/comma-separated-tokens/-/comma-separated-tokens-2.0.3.tgz"
  "version" "2.0.3"

"compute-scroll-into-view@^1.0.20":
  "integrity" "sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg=="
  "resolved" "https://registry.npmmirror.com/compute-scroll-into-view/-/compute-scroll-into-view-1.0.20.tgz"
  "version" "1.0.20"

"copy-text-to-clipboard@^2.1.1":
  "integrity" "sha512-WRvoIdnTs1rgPMkgA2pUOa/M4Enh2uzCwdKsOMYNAJiz/4ZvEJgmbF4OmninPmlFdAWisfeh0tH+Cpf7ni3RqQ=="
  "resolved" "https://registry.npmmirror.com/copy-text-to-clipboard/-/copy-text-to-clipboard-2.2.0.tgz"
  "version" "2.2.0"

"csstype@^3.0.2":
  "integrity" "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="
  "resolved" "https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz"
  "version" "3.1.3"

"date-fns-tz@^1.3.8":
  "integrity" "sha512-qwNXUFtMHTTU6CFSFjoJ80W8Fzzp24LntbjFFBgL/faqds4e5mo9mftoRLgr3Vi1trISsg4awSpYVsOQCRnapQ=="
  "resolved" "https://registry.npmmirror.com/date-fns-tz/-/date-fns-tz-1.3.8.tgz"
  "version" "1.3.8"

"date-fns@^2.29.3", "date-fns@>=2.0.0":
  "integrity" "sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw=="
  "resolved" "https://registry.npmmirror.com/date-fns/-/date-fns-2.30.0.tgz"
  "version" "2.30.0"
  dependencies:
    "@babel/runtime" "^7.21.0"

"debug@^4.0.0":
  "integrity" "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA=="
  "resolved" "https://registry.npmmirror.com/debug/-/debug-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "ms" "^2.1.3"

"decode-named-character-reference@^1.0.0":
  "integrity" "sha512-Wy+JTSbFThEOXQIR2L6mxJvEs+veIzpmqD7ynWxMXGpnk3smkHQOp6forLdHsKpAMW9iJpaBBIxz285t1n1C3w=="
  "resolved" "https://registry.npmmirror.com/decode-named-character-reference/-/decode-named-character-reference-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "character-entities" "^2.0.0"

"dequal@^2.0.0":
  "integrity" "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA=="
  "resolved" "https://registry.npmmirror.com/dequal/-/dequal-2.0.3.tgz"
  "version" "2.0.3"

"devlop@^1.0.0", "devlop@^1.1.0":
  "integrity" "sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA=="
  "resolved" "https://registry.npmmirror.com/devlop/-/devlop-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "dequal" "^2.0.0"

"esast-util-from-estree@^2.0.0":
  "integrity" "sha512-4CyanoAudUSBAn5K13H4JhsMH6L9ZP7XbLVe/dKybkxMO7eDyLsT8UHl9TRNrU2Gr9nz+FovfSIjuXWJ81uVwQ=="
  "resolved" "https://registry.npmmirror.com/esast-util-from-estree/-/esast-util-from-estree-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "devlop" "^1.0.0"
    "estree-util-visit" "^2.0.0"
    "unist-util-position-from-estree" "^2.0.0"

"esast-util-from-js@^2.0.0":
  "integrity" "sha512-8Ja+rNJ0Lt56Pcf3TAmpBZjmx8ZcK5Ts4cAzIOjsjevg9oSXJnl6SUQ2EevU8tv3h6ZLWmoKL5H4fgWvdvfETw=="
  "resolved" "https://registry.npmmirror.com/esast-util-from-js/-/esast-util-from-js-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "acorn" "^8.0.0"
    "esast-util-from-estree" "^2.0.0"
    "vfile-message" "^4.0.0"

"escape-string-regexp@^5.0.0":
  "integrity" "sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw=="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz"
  "version" "5.0.0"

"estree-util-attach-comments@^3.0.0":
  "integrity" "sha512-cKUwm/HUcTDsYh/9FgnuFqpfquUbwIqwKM26BVCGDPVgvaCl/nDCCjUfiLlx6lsEZ3Z4RFxNbOQ60pkaEwFxGw=="
  "resolved" "https://registry.npmmirror.com/estree-util-attach-comments/-/estree-util-attach-comments-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "@types/estree" "^1.0.0"

"estree-util-build-jsx@^3.0.0":
  "integrity" "sha512-8U5eiL6BTrPxp/CHbs2yMgP8ftMhR5ww1eIKoWRMlqvltHF8fZn5LRDvTKuxD3DUn+shRbLGqXemcP51oFCsGQ=="
  "resolved" "https://registry.npmmirror.com/estree-util-build-jsx/-/estree-util-build-jsx-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "devlop" "^1.0.0"
    "estree-util-is-identifier-name" "^3.0.0"
    "estree-walker" "^3.0.0"

"estree-util-is-identifier-name@^3.0.0":
  "integrity" "sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg=="
  "resolved" "https://registry.npmmirror.com/estree-util-is-identifier-name/-/estree-util-is-identifier-name-3.0.0.tgz"
  "version" "3.0.0"

"estree-util-scope@^1.0.0":
  "integrity" "sha512-2CAASclonf+JFWBNJPndcOpA8EMJwa0Q8LUFJEKqXLW6+qBvbFZuF5gItbQOs/umBUkjviCSDCbBwU2cXbmrhQ=="
  "resolved" "https://registry.npmmirror.com/estree-util-scope/-/estree-util-scope-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "@types/estree" "^1.0.0"
    "devlop" "^1.0.0"

"estree-util-to-js@^2.0.0":
  "integrity" "sha512-WDF+xj5rRWmD5tj6bIqRi6CkLIXbbNQUcxQHzGysQzvHmdYG2G7p/Tf0J0gpxGgkeMZNTIjT/AoSvC9Xehcgdg=="
  "resolved" "https://registry.npmmirror.com/estree-util-to-js/-/estree-util-to-js-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "astring" "^1.8.0"
    "source-map" "^0.7.0"

"estree-util-visit@^2.0.0":
  "integrity" "sha512-m5KgiH85xAhhW8Wta0vShLcUvOsh3LLPI2YVwcbio1l7E09NTLL1EyMZFM1OyWowoH0skScNbhOPl4kcBgzTww=="
  "resolved" "https://registry.npmmirror.com/estree-util-visit/-/estree-util-visit-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/unist" "^3.0.0"

"estree-walker@^3.0.0":
  "integrity" "sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g=="
  "resolved" "https://registry.npmmirror.com/estree-walker/-/estree-walker-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "@types/estree" "^1.0.0"

"extend@^3.0.0":
  "integrity" "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
  "resolved" "https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"fast-copy@^3.0.1 ":
  "integrity" "sha512-dl0O9Vhju8IrcLndv2eU4ldt1ftXMqqfgN4H1cpmGV7P6jeB9FwpN9a2c8DPGE1Ys88rNUJVYDHq73CGAGOPfQ=="
  "resolved" "https://registry.npmmirror.com/fast-copy/-/fast-copy-3.0.2.tgz"
  "version" "3.0.2"

"hast-util-to-estree@^3.0.0":
  "integrity" "sha512-48+B/rJWAp0jamNbAAf9M7Uf//UVqAoMmgXhBdxTDJLGKY+LRnZ99qcG+Qjl5HfMpYNzS5v4EAwVEF34LeAj7w=="
  "resolved" "https://registry.npmmirror.com/hast-util-to-estree/-/hast-util-to-estree-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "@types/estree" "^1.0.0"
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "comma-separated-tokens" "^2.0.0"
    "devlop" "^1.0.0"
    "estree-util-attach-comments" "^3.0.0"
    "estree-util-is-identifier-name" "^3.0.0"
    "hast-util-whitespace" "^3.0.0"
    "mdast-util-mdx-expression" "^2.0.0"
    "mdast-util-mdx-jsx" "^3.0.0"
    "mdast-util-mdxjs-esm" "^2.0.0"
    "property-information" "^7.0.0"
    "space-separated-tokens" "^2.0.0"
    "style-to-js" "^1.0.0"
    "unist-util-position" "^5.0.0"
    "zwitch" "^2.0.0"

"hast-util-to-jsx-runtime@^2.0.0":
  "integrity" "sha512-zl6s8LwNyo1P9uw+XJGvZtdFF1GdAkOg8ujOw+4Pyb76874fLps4ueHXDhXWdk6YHQ6OgUtinliG7RsYvCbbBg=="
  "resolved" "https://registry.npmmirror.com/hast-util-to-jsx-runtime/-/hast-util-to-jsx-runtime-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "@types/estree" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/unist" "^3.0.0"
    "comma-separated-tokens" "^2.0.0"
    "devlop" "^1.0.0"
    "estree-util-is-identifier-name" "^3.0.0"
    "hast-util-whitespace" "^3.0.0"
    "mdast-util-mdx-expression" "^2.0.0"
    "mdast-util-mdx-jsx" "^3.0.0"
    "mdast-util-mdxjs-esm" "^2.0.0"
    "property-information" "^7.0.0"
    "space-separated-tokens" "^2.0.0"
    "style-to-js" "^1.0.0"
    "unist-util-position" "^5.0.0"
    "vfile-message" "^4.0.0"

"hast-util-whitespace@^3.0.0":
  "integrity" "sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw=="
  "resolved" "https://registry.npmmirror.com/hast-util-whitespace/-/hast-util-whitespace-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "@types/hast" "^3.0.0"

"immer@^10.0.3":
  "integrity" "sha512-s2MPrmjovJcoMaHtx6K11Ra7oD05NT97w1IC5zpMkT6Atjr7H8LjaDd81iIxUYpMKSRRNMJE703M1Fhr/TctHw=="
  "resolved" "https://registry.npmmirror.com/immer/-/immer-10.1.1.tgz"
  "version" "10.1.1"

"inline-style-parser@0.2.4":
  "integrity" "sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q=="
  "resolved" "https://registry.npmmirror.com/inline-style-parser/-/inline-style-parser-0.2.4.tgz"
  "version" "0.2.4"

"is-alphabetical@^2.0.0":
  "integrity" "sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ=="
  "resolved" "https://registry.npmmirror.com/is-alphabetical/-/is-alphabetical-2.0.1.tgz"
  "version" "2.0.1"

"is-alphanumerical@^2.0.0":
  "integrity" "sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw=="
  "resolved" "https://registry.npmmirror.com/is-alphanumerical/-/is-alphanumerical-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-alphabetical" "^2.0.0"
    "is-decimal" "^2.0.0"

"is-decimal@^2.0.0":
  "integrity" "sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A=="
  "resolved" "https://registry.npmmirror.com/is-decimal/-/is-decimal-2.0.1.tgz"
  "version" "2.0.1"

"is-hexadecimal@^2.0.0":
  "integrity" "sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg=="
  "resolved" "https://registry.npmmirror.com/is-hexadecimal/-/is-hexadecimal-2.0.1.tgz"
  "version" "2.0.1"

"is-plain-obj@^4.0.0":
  "integrity" "sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg=="
  "resolved" "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-4.1.0.tgz"
  "version" "4.1.0"

"js-tokens@^3.0.0 || ^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"jsonc-parser@^3.3.1":
  "integrity" "sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ=="
  "resolved" "https://registry.npmmirror.com/jsonc-parser/-/jsonc-parser-3.3.1.tgz"
  "version" "3.3.1"

"lodash@^4.17.21":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"longest-streak@^3.0.0":
  "integrity" "sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g=="
  "resolved" "https://registry.npmmirror.com/longest-streak/-/longest-streak-3.1.0.tgz"
  "version" "3.1.0"

"loose-envify@^1.1.0", "loose-envify@^1.4.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lottie-web@^5.12.2":
  "integrity" "sha512-uvhvYPC8kGPjXT3MyKMrL3JitEAmDMp30lVkuq/590Mw9ok6pWcFCwXJveo0t5uqYw1UREQHofD+jVpdjBv8wg=="
  "resolved" "https://registry.npmmirror.com/lottie-web/-/lottie-web-5.12.2.tgz"
  "version" "5.12.2"

"markdown-extensions@^2.0.0":
  "integrity" "sha512-o5vL7aDWatOTX8LzaS1WMoaoxIiLRQJuIKKe2wAw6IeULDHaqbiqiggmx+pKvZDb1Sj+pE46Sn1T7lCqfFtg1Q=="
  "resolved" "https://registry.npmmirror.com/markdown-extensions/-/markdown-extensions-2.0.0.tgz"
  "version" "2.0.0"

"markdown-table@^3.0.0":
  "integrity" "sha512-wiYz4+JrLyb/DqW2hkFJxP7Vd7JuTDm77fvbM8VfEQdmSMqcImWeeRbHwZjBjIFki/VaMK2BhFi7oUUZeM5bqw=="
  "resolved" "https://registry.npmmirror.com/markdown-table/-/markdown-table-3.0.4.tgz"
  "version" "3.0.4"

"mdast-util-find-and-replace@^3.0.0":
  "integrity" "sha512-Tmd1Vg/m3Xz43afeNxDIhWRtFZgM2VLyaf4vSTYwudTyeuTneoL3qtWMA5jeLyz/O1vDJmmV4QuScFCA2tBPwg=="
  "resolved" "https://registry.npmmirror.com/mdast-util-find-and-replace/-/mdast-util-find-and-replace-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "@types/mdast" "^4.0.0"
    "escape-string-regexp" "^5.0.0"
    "unist-util-is" "^6.0.0"
    "unist-util-visit-parents" "^6.0.0"

"mdast-util-from-markdown@^2.0.0":
  "integrity" "sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA=="
  "resolved" "https://registry.npmmirror.com/mdast-util-from-markdown/-/mdast-util-from-markdown-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    "decode-named-character-reference" "^1.0.0"
    "devlop" "^1.0.0"
    "mdast-util-to-string" "^4.0.0"
    "micromark" "^4.0.0"
    "micromark-util-decode-numeric-character-reference" "^2.0.0"
    "micromark-util-decode-string" "^2.0.0"
    "micromark-util-normalize-identifier" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"
    "unist-util-stringify-position" "^4.0.0"

"mdast-util-gfm-autolink-literal@^2.0.0":
  "integrity" "sha512-5HVP2MKaP6L+G6YaxPNjuL0BPrq9orG3TsrZ9YXbA3vDw/ACI4MEsnoDpn6ZNm7GnZgtAcONJyPhOP8tNJQavQ=="
  "resolved" "https://registry.npmmirror.com/mdast-util-gfm-autolink-literal/-/mdast-util-gfm-autolink-literal-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@types/mdast" "^4.0.0"
    "ccount" "^2.0.0"
    "devlop" "^1.0.0"
    "mdast-util-find-and-replace" "^3.0.0"
    "micromark-util-character" "^2.0.0"

"mdast-util-gfm-footnote@^2.0.0":
  "integrity" "sha512-sqpDWlsHn7Ac9GNZQMeUzPQSMzR6Wv0WKRNvQRg0KqHh02fpTz69Qc1QSseNX29bhz1ROIyNyxExfawVKTm1GQ=="
  "resolved" "https://registry.npmmirror.com/mdast-util-gfm-footnote/-/mdast-util-gfm-footnote-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "devlop" "^1.1.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"
    "micromark-util-normalize-identifier" "^2.0.0"

"mdast-util-gfm-strikethrough@^2.0.0":
  "integrity" "sha512-mKKb915TF+OC5ptj5bJ7WFRPdYtuHv0yTRxK2tJvi+BDqbkiG7h7u/9SI89nRAYcmap2xHQL9D+QG/6wSrTtXg=="
  "resolved" "https://registry.npmmirror.com/mdast-util-gfm-strikethrough/-/mdast-util-gfm-strikethrough-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-gfm-table@^2.0.0":
  "integrity" "sha512-78UEvebzz/rJIxLvE7ZtDd/vIQ0RHv+3Mh5DR96p7cS7HsBhYIICDBCu8csTNWNO6tBWfqXPWekRuj2FNOGOZg=="
  "resolved" "https://registry.npmmirror.com/mdast-util-gfm-table/-/mdast-util-gfm-table-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "devlop" "^1.0.0"
    "markdown-table" "^3.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-gfm-task-list-item@^2.0.0":
  "integrity" "sha512-IrtvNvjxC1o06taBAVJznEnkiHxLFTzgonUdy8hzFVeDun0uTjxxrRGVaNFqkU1wJR3RBPEfsxmU6jDWPofrTQ=="
  "resolved" "https://registry.npmmirror.com/mdast-util-gfm-task-list-item/-/mdast-util-gfm-task-list-item-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "devlop" "^1.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-gfm@^3.0.0":
  "integrity" "sha512-0ulfdQOM3ysHhCJ1p06l0b0VKlhU0wuQs3thxZQagjcjPrlFRqY215uZGHHJan9GEAXd9MbfPjFJz+qMkVR6zQ=="
  "resolved" "https://registry.npmmirror.com/mdast-util-gfm/-/mdast-util-gfm-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-gfm-autolink-literal" "^2.0.0"
    "mdast-util-gfm-footnote" "^2.0.0"
    "mdast-util-gfm-strikethrough" "^2.0.0"
    "mdast-util-gfm-table" "^2.0.0"
    "mdast-util-gfm-task-list-item" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-mdx-expression@^2.0.0":
  "integrity" "sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ=="
  "resolved" "https://registry.npmmirror.com/mdast-util-mdx-expression/-/mdast-util-mdx-expression-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "devlop" "^1.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-mdx-jsx@^3.0.0":
  "integrity" "sha512-lj/z8v0r6ZtsN/cGNNtemmmfoLAFZnjMbNyLzBafjzikOM+glrjNHPlf6lQDOTccj9n5b0PPihEBbhneMyGs1Q=="
  "resolved" "https://registry.npmmirror.com/mdast-util-mdx-jsx/-/mdast-util-mdx-jsx-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    "ccount" "^2.0.0"
    "devlop" "^1.1.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"
    "parse-entities" "^4.0.0"
    "stringify-entities" "^4.0.0"
    "unist-util-stringify-position" "^4.0.0"
    "vfile-message" "^4.0.0"

"mdast-util-mdx@^3.0.0":
  "integrity" "sha512-JfbYLAW7XnYTTbUsmpu0kdBUVe+yKVJZBItEjwyYJiDJuZ9w4eeaqks4HQO+R7objWgS2ymV60GYpI14Ug554w=="
  "resolved" "https://registry.npmmirror.com/mdast-util-mdx/-/mdast-util-mdx-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-mdx-expression" "^2.0.0"
    "mdast-util-mdx-jsx" "^3.0.0"
    "mdast-util-mdxjs-esm" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-mdxjs-esm@^2.0.0":
  "integrity" "sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg=="
  "resolved" "https://registry.npmmirror.com/mdast-util-mdxjs-esm/-/mdast-util-mdxjs-esm-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "devlop" "^1.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-phrasing@^4.0.0":
  "integrity" "sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w=="
  "resolved" "https://registry.npmmirror.com/mdast-util-phrasing/-/mdast-util-phrasing-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "unist-util-is" "^6.0.0"

"mdast-util-to-hast@^13.0.0":
  "integrity" "sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA=="
  "resolved" "https://registry.npmmirror.com/mdast-util-to-hast/-/mdast-util-to-hast-13.2.0.tgz"
  "version" "13.2.0"
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@ungap/structured-clone" "^1.0.0"
    "devlop" "^1.0.0"
    "micromark-util-sanitize-uri" "^2.0.0"
    "trim-lines" "^3.0.0"
    "unist-util-position" "^5.0.0"
    "unist-util-visit" "^5.0.0"
    "vfile" "^6.0.0"

"mdast-util-to-markdown@^2.0.0":
  "integrity" "sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA=="
  "resolved" "https://registry.npmmirror.com/mdast-util-to-markdown/-/mdast-util-to-markdown-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    "longest-streak" "^3.0.0"
    "mdast-util-phrasing" "^4.0.0"
    "mdast-util-to-string" "^4.0.0"
    "micromark-util-classify-character" "^2.0.0"
    "micromark-util-decode-string" "^2.0.0"
    "unist-util-visit" "^5.0.0"
    "zwitch" "^2.0.0"

"mdast-util-to-string@^4.0.0":
  "integrity" "sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg=="
  "resolved" "https://registry.npmmirror.com/mdast-util-to-string/-/mdast-util-to-string-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"

"memoize-one@^5.2.1", "memoize-one@>=3.1.1 <6":
  "integrity" "sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q=="
  "resolved" "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.2.1.tgz"
  "version" "5.2.1"

"micromark-core-commonmark@^2.0.0":
  "integrity" "sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg=="
  "resolved" "https://registry.npmmirror.com/micromark-core-commonmark/-/micromark-core-commonmark-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "decode-named-character-reference" "^1.0.0"
    "devlop" "^1.0.0"
    "micromark-factory-destination" "^2.0.0"
    "micromark-factory-label" "^2.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-factory-title" "^2.0.0"
    "micromark-factory-whitespace" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-chunked" "^2.0.0"
    "micromark-util-classify-character" "^2.0.0"
    "micromark-util-html-tag-name" "^2.0.0"
    "micromark-util-normalize-identifier" "^2.0.0"
    "micromark-util-resolve-all" "^2.0.0"
    "micromark-util-subtokenize" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-autolink-literal@^2.0.0":
  "integrity" "sha512-oOg7knzhicgQ3t4QCjCWgTmfNhvQbDDnJeVu9v81r7NltNCVmhPy1fJRX27pISafdjL+SVc4d3l48Gb6pbRypw=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-gfm-autolink-literal/-/micromark-extension-gfm-autolink-literal-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "micromark-util-character" "^2.0.0"
    "micromark-util-sanitize-uri" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-footnote@^2.0.0":
  "integrity" "sha512-/yPhxI1ntnDNsiHtzLKYnE3vf9JZ6cAisqVDauhp4CEHxlb4uoOTxOCJ+9s51bIB8U1N1FJ1RXOKTIlD5B/gqw=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-gfm-footnote/-/micromark-extension-gfm-footnote-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-core-commonmark" "^2.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-normalize-identifier" "^2.0.0"
    "micromark-util-sanitize-uri" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-strikethrough@^2.0.0":
  "integrity" "sha512-ADVjpOOkjz1hhkZLlBiYA9cR2Anf8F4HqZUO6e5eDcPQd0Txw5fxLzzxnEkSkfnD0wziSGiv7sYhk/ktvbf1uw=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-gfm-strikethrough/-/micromark-extension-gfm-strikethrough-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-util-chunked" "^2.0.0"
    "micromark-util-classify-character" "^2.0.0"
    "micromark-util-resolve-all" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-table@^2.0.0":
  "integrity" "sha512-t2OU/dXXioARrC6yWfJ4hqB7rct14e8f7m0cbI5hUmDyyIlwv5vEtooptH8INkbLzOatzKuVbQmAYcbWoyz6Dg=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-gfm-table/-/micromark-extension-gfm-table-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-tagfilter@^2.0.0":
  "integrity" "sha512-xHlTOmuCSotIA8TW1mDIM6X2O1SiX5P9IuDtqGonFhEK0qgRI4yeC6vMxEV2dgyr2TiD+2PQ10o+cOhdVAcwfg=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-gfm-tagfilter/-/micromark-extension-gfm-tagfilter-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-task-list-item@^2.0.0":
  "integrity" "sha512-qIBZhqxqI6fjLDYFTBIa4eivDMnP+OZqsNwmQ3xNLE4Cxwc+zfQEfbs6tzAo2Hjq+bh6q5F+Z8/cksrLFYWQQw=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-gfm-task-list-item/-/micromark-extension-gfm-task-list-item-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm@^3.0.0":
  "integrity" "sha512-vsKArQsicm7t0z2GugkCKtZehqUm31oeGBV/KVSorWSy8ZlNAv7ytjFhvaryUiCUJYqs+NoE6AFhpQvBTM6Q4w=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-gfm/-/micromark-extension-gfm-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "micromark-extension-gfm-autolink-literal" "^2.0.0"
    "micromark-extension-gfm-footnote" "^2.0.0"
    "micromark-extension-gfm-strikethrough" "^2.0.0"
    "micromark-extension-gfm-table" "^2.0.0"
    "micromark-extension-gfm-tagfilter" "^2.0.0"
    "micromark-extension-gfm-task-list-item" "^2.0.0"
    "micromark-util-combine-extensions" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-mdx-expression@^3.0.0":
  "integrity" "sha512-dD/ADLJ1AeMvSAKBwO22zG22N4ybhe7kFIZ3LsDI0GlsNr2A3KYxb0LdC1u5rj4Nw+CHKY0RVdnHX8vj8ejm4Q=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-mdx-expression/-/micromark-extension-mdx-expression-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@types/estree" "^1.0.0"
    "devlop" "^1.0.0"
    "micromark-factory-mdx-expression" "^2.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-events-to-acorn" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-mdx-jsx@^3.0.0":
  "integrity" "sha512-e5+q1DjMh62LZAJOnDraSSbDMvGJ8x3cbjygy2qFEi7HCeUT4BDKCvMozPozcD6WmOt6sVvYDNBKhFSz3kjOVQ=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-mdx-jsx/-/micromark-extension-mdx-jsx-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "@types/estree" "^1.0.0"
    "devlop" "^1.0.0"
    "estree-util-is-identifier-name" "^3.0.0"
    "micromark-factory-mdx-expression" "^2.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-events-to-acorn" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"
    "vfile-message" "^4.0.0"

"micromark-extension-mdx-md@^2.0.0":
  "integrity" "sha512-EpAiszsB3blw4Rpba7xTOUptcFeBFi+6PY8VnJ2hhimH+vCQDirWgsMpz7w1XcZE7LVrSAUGb9VJpG9ghlYvYQ=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-mdx-md/-/micromark-extension-mdx-md-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromark-util-types" "^2.0.0"

"micromark-extension-mdxjs-esm@^3.0.0":
  "integrity" "sha512-DJFl4ZqkErRpq/dAPyeWp15tGrcrrJho1hKK5uBS70BCtfrIFg81sqcTVu3Ta+KD1Tk5vAtBNElWxtAa+m8K9A=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-mdxjs-esm/-/micromark-extension-mdxjs-esm-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "@types/estree" "^1.0.0"
    "devlop" "^1.0.0"
    "micromark-core-commonmark" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-events-to-acorn" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"
    "unist-util-position-from-estree" "^2.0.0"
    "vfile-message" "^4.0.0"

"micromark-extension-mdxjs@^3.0.0":
  "integrity" "sha512-A873fJfhnJ2siZyUrJ31l34Uqwy4xIFmvPY1oj+Ean5PHcPBYzEsvqvWGaWcfEIr11O5Dlw3p2y0tZWpKHDejQ=="
  "resolved" "https://registry.npmmirror.com/micromark-extension-mdxjs/-/micromark-extension-mdxjs-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "acorn" "^8.0.0"
    "acorn-jsx" "^5.0.0"
    "micromark-extension-mdx-expression" "^3.0.0"
    "micromark-extension-mdx-jsx" "^3.0.0"
    "micromark-extension-mdx-md" "^2.0.0"
    "micromark-extension-mdxjs-esm" "^3.0.0"
    "micromark-util-combine-extensions" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-factory-destination@^2.0.0":
  "integrity" "sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA=="
  "resolved" "https://registry.npmmirror.com/micromark-factory-destination/-/micromark-factory-destination-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-factory-label@^2.0.0":
  "integrity" "sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg=="
  "resolved" "https://registry.npmmirror.com/micromark-factory-label/-/micromark-factory-label-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-factory-mdx-expression@^2.0.0":
  "integrity" "sha512-kQnEtA3vzucU2BkrIa8/VaSAsP+EJ3CKOvhMuJgOEGg9KDC6OAY6nSnNDVRiVNRqj7Y4SlSzcStaH/5jge8JdQ=="
  "resolved" "https://registry.npmmirror.com/micromark-factory-mdx-expression/-/micromark-factory-mdx-expression-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "@types/estree" "^1.0.0"
    "devlop" "^1.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-events-to-acorn" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"
    "unist-util-position-from-estree" "^2.0.0"
    "vfile-message" "^4.0.0"

"micromark-factory-space@^2.0.0":
  "integrity" "sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg=="
  "resolved" "https://registry.npmmirror.com/micromark-factory-space/-/micromark-factory-space-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-character" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-factory-title@^2.0.0":
  "integrity" "sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw=="
  "resolved" "https://registry.npmmirror.com/micromark-factory-title/-/micromark-factory-title-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-factory-whitespace@^2.0.0":
  "integrity" "sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ=="
  "resolved" "https://registry.npmmirror.com/micromark-factory-whitespace/-/micromark-factory-whitespace-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-util-character@^2.0.0":
  "integrity" "sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q=="
  "resolved" "https://registry.npmmirror.com/micromark-util-character/-/micromark-util-character-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-util-chunked@^2.0.0":
  "integrity" "sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA=="
  "resolved" "https://registry.npmmirror.com/micromark-util-chunked/-/micromark-util-chunked-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-symbol" "^2.0.0"

"micromark-util-classify-character@^2.0.0":
  "integrity" "sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q=="
  "resolved" "https://registry.npmmirror.com/micromark-util-classify-character/-/micromark-util-classify-character-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-util-combine-extensions@^2.0.0":
  "integrity" "sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg=="
  "resolved" "https://registry.npmmirror.com/micromark-util-combine-extensions/-/micromark-util-combine-extensions-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-chunked" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-util-decode-numeric-character-reference@^2.0.0":
  "integrity" "sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw=="
  "resolved" "https://registry.npmmirror.com/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "micromark-util-symbol" "^2.0.0"

"micromark-util-decode-string@^2.0.0":
  "integrity" "sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ=="
  "resolved" "https://registry.npmmirror.com/micromark-util-decode-string/-/micromark-util-decode-string-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "decode-named-character-reference" "^1.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-decode-numeric-character-reference" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"

"micromark-util-encode@^2.0.0":
  "integrity" "sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw=="
  "resolved" "https://registry.npmmirror.com/micromark-util-encode/-/micromark-util-encode-2.0.1.tgz"
  "version" "2.0.1"

"micromark-util-events-to-acorn@^2.0.0":
  "integrity" "sha512-jmsiEIiZ1n7X1Rr5k8wVExBQCg5jy4UXVADItHmNk1zkwEVhBuIUKRu3fqv+hs4nxLISi2DQGlqIOGiFxgbfHg=="
  "resolved" "https://registry.npmmirror.com/micromark-util-events-to-acorn/-/micromark-util-events-to-acorn-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "@types/estree" "^1.0.0"
    "@types/unist" "^3.0.0"
    "devlop" "^1.0.0"
    "estree-util-visit" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"
    "vfile-message" "^4.0.0"

"micromark-util-html-tag-name@^2.0.0":
  "integrity" "sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA=="
  "resolved" "https://registry.npmmirror.com/micromark-util-html-tag-name/-/micromark-util-html-tag-name-2.0.1.tgz"
  "version" "2.0.1"

"micromark-util-normalize-identifier@^2.0.0":
  "integrity" "sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q=="
  "resolved" "https://registry.npmmirror.com/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-symbol" "^2.0.0"

"micromark-util-resolve-all@^2.0.0":
  "integrity" "sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg=="
  "resolved" "https://registry.npmmirror.com/micromark-util-resolve-all/-/micromark-util-resolve-all-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-types" "^2.0.0"

"micromark-util-sanitize-uri@^2.0.0":
  "integrity" "sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ=="
  "resolved" "https://registry.npmmirror.com/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-character" "^2.0.0"
    "micromark-util-encode" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"

"micromark-util-subtokenize@^2.0.0":
  "integrity" "sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA=="
  "resolved" "https://registry.npmmirror.com/micromark-util-subtokenize/-/micromark-util-subtokenize-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-util-chunked" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-util-symbol@^2.0.0":
  "integrity" "sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q=="
  "resolved" "https://registry.npmmirror.com/micromark-util-symbol/-/micromark-util-symbol-2.0.1.tgz"
  "version" "2.0.1"

"micromark-util-types@^2.0.0":
  "integrity" "sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA=="
  "resolved" "https://registry.npmmirror.com/micromark-util-types/-/micromark-util-types-2.0.2.tgz"
  "version" "2.0.2"

"micromark@^4.0.0":
  "integrity" "sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA=="
  "resolved" "https://registry.npmmirror.com/micromark/-/micromark-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "@types/debug" "^4.0.0"
    "debug" "^4.0.0"
    "decode-named-character-reference" "^1.0.0"
    "devlop" "^1.0.0"
    "micromark-core-commonmark" "^2.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-chunked" "^2.0.0"
    "micromark-util-combine-extensions" "^2.0.0"
    "micromark-util-decode-numeric-character-reference" "^2.0.0"
    "micromark-util-encode" "^2.0.0"
    "micromark-util-normalize-identifier" "^2.0.0"
    "micromark-util-resolve-all" "^2.0.0"
    "micromark-util-sanitize-uri" "^2.0.0"
    "micromark-util-subtokenize" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"ms@^2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"object-assign@^4.1.1":
  "integrity" "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="
  "resolved" "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"parse-entities@^4.0.0":
  "integrity" "sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw=="
  "resolved" "https://registry.npmmirror.com/parse-entities/-/parse-entities-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "@types/unist" "^2.0.0"
    "character-entities-legacy" "^3.0.0"
    "character-reference-invalid" "^2.0.0"
    "decode-named-character-reference" "^1.0.0"
    "is-alphanumerical" "^2.0.0"
    "is-decimal" "^2.0.0"
    "is-hexadecimal" "^2.0.0"

"prismjs@^1.29.0":
  "integrity" "sha512-DEvV2ZF2r2/63V+tK8hQvrR2ZGn10srHbXviTlcv7Kpzw8jWiNTqbVgjO3IY8RxrrOUF8VPMQQFysYYYv0YZxw=="
  "resolved" "https://registry.npmmirror.com/prismjs/-/prismjs-1.30.0.tgz"
  "version" "1.30.0"

"prop-types@^15.7.2", "prop-types@^15.8.1", "prop-types@15.x":
  "integrity" "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg=="
  "resolved" "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz"
  "version" "15.8.1"
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"
    "react-is" "^16.13.1"

"property-information@^7.0.0":
  "integrity" "sha512-TwEZ+X+yCJmYfL7TPUOcvBZ4QfoT5YenQiJuX//0th53DE6w0xxLEtfK3iyryQFddXuvkIk51EEgrJQ0WJkOmQ=="
  "resolved" "https://registry.npmmirror.com/property-information/-/property-information-7.1.0.tgz"
  "version" "7.1.0"

"react-dom@^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^18.2.0", "react-dom@>= 16.3.0", "react-dom@>=16.0.0", "react-dom@>=16.8.0":
  "integrity" "sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw=="
  "resolved" "https://registry.npmmirror.com/react-dom/-/react-dom-18.3.1.tgz"
  "version" "18.3.1"
  dependencies:
    "loose-envify" "^1.1.0"
    "scheduler" "^0.23.2"

"react-draggable@^4.0.3":
  "integrity" "sha512-LtY5Xw1zTPqHkVmtM3X8MUOxNDOUhv/khTgBgrUvwaS064bwVvxT+q5El0uUFNx5IEPKXuRejr7UqLwBIg5pdw=="
  "resolved" "https://registry.npmmirror.com/react-draggable/-/react-draggable-4.4.6.tgz"
  "version" "4.4.6"
  dependencies:
    "clsx" "^1.1.1"
    "prop-types" "^15.8.1"

"react-is@^16.13.1":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-redux@^7.2.1 || ^8.1.3 || ^9.0.0", "react-redux@^9.2.0":
  "integrity" "sha512-ROY9fvHhwOD9ySfrF0wmvu//bKCQ6AeZZq1nJNtbDC+kk5DuSuNX/n6YWYF/SYy7bSba4D4FSz8DJeKY/S/r+g=="
  "resolved" "https://registry.npmmirror.com/react-redux/-/react-redux-9.2.0.tgz"
  "version" "9.2.0"
  dependencies:
    "@types/use-sync-external-store" "^0.0.6"
    "use-sync-external-store" "^1.4.0"

"react-resizable@^3.0.5":
  "integrity" "sha512-vKpeHhI5OZvYn82kXOs1bC8aOXktGU5AmKAgaZS4F5JPburCtbmDPqE7Pzp+1kN4+Wb81LlF33VpGwWwtXem+w=="
  "resolved" "https://registry.npmmirror.com/react-resizable/-/react-resizable-3.0.5.tgz"
  "version" "3.0.5"
  dependencies:
    "prop-types" "15.x"
    "react-draggable" "^4.0.3"

"react-window@^1.8.2":
  "integrity" "sha512-+SRbUVT2scadgFSWx+R1P754xHPEqvcfSfVX10QYg6POOz+WNgkN48pS+BtZNIMGiL1HYrSEiCkwsMS15QogEQ=="
  "resolved" "https://registry.npmmirror.com/react-window/-/react-window-1.8.11.tgz"
  "version" "1.8.11"
  dependencies:
    "@babel/runtime" "^7.0.0"
    "memoize-one" ">=3.1.1 <6"

"react@^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.9.0 || ^17.0.0 || ^18 || ^19", "react@^18.0 || ^19", "react@^18.2.0", "react@^18.3.1", "react@>= 16.3", "react@>= 16.3.0", "react@>=16.0.0", "react@>=16.8.0":
  "integrity" "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ=="
  "resolved" "https://registry.npmmirror.com/react/-/react-18.3.1.tgz"
  "version" "18.3.1"
  dependencies:
    "loose-envify" "^1.1.0"

"recma-build-jsx@^1.0.0":
  "integrity" "sha512-8GtdyqaBcDfva+GUKDr3nev3VpKAhup1+RvkMvUxURHpW7QyIvk9F5wz7Vzo06CEMSilw6uArgRqhpiUcWp8ew=="
  "resolved" "https://registry.npmmirror.com/recma-build-jsx/-/recma-build-jsx-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "@types/estree" "^1.0.0"
    "estree-util-build-jsx" "^3.0.0"
    "vfile" "^6.0.0"

"recma-jsx@^1.0.0":
  "integrity" "sha512-5vwkv65qWwYxg+Atz95acp8DMu1JDSqdGkA2Of1j6rCreyFUE/gp15fC8MnGEuG1W68UKjM6x6+YTWIh7hZM/Q=="
  "resolved" "https://registry.npmmirror.com/recma-jsx/-/recma-jsx-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "acorn-jsx" "^5.0.0"
    "estree-util-to-js" "^2.0.0"
    "recma-parse" "^1.0.0"
    "recma-stringify" "^1.0.0"
    "unified" "^11.0.0"

"recma-parse@^1.0.0":
  "integrity" "sha512-OYLsIGBB5Y5wjnSnQW6t3Xg7q3fQ7FWbw/vcXtORTnyaSFscOtABg+7Pnz6YZ6c27fG1/aN8CjfwoUEUIdwqWQ=="
  "resolved" "https://registry.npmmirror.com/recma-parse/-/recma-parse-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "@types/estree" "^1.0.0"
    "esast-util-from-js" "^2.0.0"
    "unified" "^11.0.0"
    "vfile" "^6.0.0"

"recma-stringify@^1.0.0":
  "integrity" "sha512-cjwII1MdIIVloKvC9ErQ+OgAtwHBmcZ0Bg4ciz78FtbT8In39aAYbaA7zvxQ61xVMSPE8WxhLwLbhif4Js2C+g=="
  "resolved" "https://registry.npmmirror.com/recma-stringify/-/recma-stringify-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "@types/estree" "^1.0.0"
    "estree-util-to-js" "^2.0.0"
    "unified" "^11.0.0"
    "vfile" "^6.0.0"

"redux-thunk@^3.1.0":
  "integrity" "sha512-NW2r5T6ksUKXCabzhL9z+h206HQw/NJkcLm1GPImRQ8IzfXwRGqjVhKJGauHirT0DAuyy6hjdnMZaRoAcy0Klw=="
  "resolved" "https://registry.npmmirror.com/redux-thunk/-/redux-thunk-3.1.0.tgz"
  "version" "3.1.0"

"redux@^5.0.0", "redux@^5.0.1":
  "integrity" "sha512-M9/ELqF6fy8FwmkpnF0S3YKOqMyoWJ4+CS5Efg2ct3oY9daQvd/Pc71FpGZsVsbl3Cpb+IIcjBDUnnyBdQbq4w=="
  "resolved" "https://registry.npmmirror.com/redux/-/redux-5.0.1.tgz"
  "version" "5.0.1"

"rehype-recma@^1.0.0":
  "integrity" "sha512-lqA4rGUf1JmacCNWWZx0Wv1dHqMwxzsDWYMTowuplHF3xH0N/MmrZ/G3BDZnzAkRmxDadujCjaKM2hqYdCBOGw=="
  "resolved" "https://registry.npmmirror.com/rehype-recma/-/rehype-recma-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "@types/estree" "^1.0.0"
    "@types/hast" "^3.0.0"
    "hast-util-to-estree" "^3.0.0"

"remark-gfm@^4.0.0":
  "integrity" "sha512-1quofZ2RQ9EWdeN34S79+KExV1764+wCUGop5CPL1WGdD0ocPpu91lzPGbwWMECpEpd42kJGQwzRfyov9j4yNg=="
  "resolved" "https://registry.npmmirror.com/remark-gfm/-/remark-gfm-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "@types/mdast" "^4.0.0"
    "mdast-util-gfm" "^3.0.0"
    "micromark-extension-gfm" "^3.0.0"
    "remark-parse" "^11.0.0"
    "remark-stringify" "^11.0.0"
    "unified" "^11.0.0"

"remark-mdx@^3.0.0":
  "integrity" "sha512-Ngl/H3YXyBV9RcRNdlYsZujAmhsxwzxpDzpDEhFBVAGthS4GDgnctpDjgFl/ULx5UEDzqtW1cyBSNKqYYrqLBA=="
  "resolved" "https://registry.npmmirror.com/remark-mdx/-/remark-mdx-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "mdast-util-mdx" "^3.0.0"
    "micromark-extension-mdxjs" "^3.0.0"

"remark-parse@^11.0.0":
  "integrity" "sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA=="
  "resolved" "https://registry.npmmirror.com/remark-parse/-/remark-parse-11.0.0.tgz"
  "version" "11.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "micromark-util-types" "^2.0.0"
    "unified" "^11.0.0"

"remark-rehype@^11.0.0":
  "integrity" "sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw=="
  "resolved" "https://registry.npmmirror.com/remark-rehype/-/remark-rehype-11.1.2.tgz"
  "version" "11.1.2"
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "mdast-util-to-hast" "^13.0.0"
    "unified" "^11.0.0"
    "vfile" "^6.0.0"

"remark-stringify@^11.0.0":
  "integrity" "sha512-1OSmLd3awB/t8qdoEOMazZkNsfVTeY4fTsgzcQFdXNq8ToTN4ZGwrMnlda4K6smTFKD+GRV6O48i6Z4iKgPPpw=="
  "resolved" "https://registry.npmmirror.com/remark-stringify/-/remark-stringify-11.0.0.tgz"
  "version" "11.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "mdast-util-to-markdown" "^2.0.0"
    "unified" "^11.0.0"

"reselect@^5.1.0":
  "integrity" "sha512-K/BG6eIky/SBpzfHZv/dd+9JBFiS4SWV7FIujVyJRux6e45+73RaUHXLmIR1f7WOMaQ0U1km6qwklRQxpJJY0w=="
  "resolved" "https://registry.npmmirror.com/reselect/-/reselect-5.1.1.tgz"
  "version" "5.1.1"

"scheduler@^0.23.2":
  "integrity" "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ=="
  "resolved" "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.2.tgz"
  "version" "0.23.2"
  dependencies:
    "loose-envify" "^1.1.0"

"scroll-into-view-if-needed@^2.2.24":
  "integrity" "sha512-dGCXy99wZQivjmjIqihaBQNjryrz5rueJY7eHfTdyWEiR4ttYpsajb14rn9s5d4DY4EcY6+4+U/maARBXJedkA=="
  "resolved" "https://registry.npmmirror.com/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.31.tgz"
  "version" "2.2.31"
  dependencies:
    "compute-scroll-into-view" "^1.0.20"

"source-map@^0.7.0":
  "integrity" "sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA=="
  "resolved" "https://registry.npmmirror.com/source-map/-/source-map-0.7.4.tgz"
  "version" "0.7.4"

"space-separated-tokens@^2.0.0":
  "integrity" "sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q=="
  "resolved" "https://registry.npmmirror.com/space-separated-tokens/-/space-separated-tokens-2.0.2.tgz"
  "version" "2.0.2"

"stringify-entities@^4.0.0":
  "integrity" "sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg=="
  "resolved" "https://registry.npmmirror.com/stringify-entities/-/stringify-entities-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "character-entities-html4" "^2.0.0"
    "character-entities-legacy" "^3.0.0"

"style-to-js@^1.0.0":
  "integrity" "sha512-/Q6ld50hKYPH3d/r6nr117TZkHR0w0kGGIVfpG9N6D8NymRPM9RqCUv4pRpJ62E5DqOYx2AFpbZMyCPnjQCnOw=="
  "resolved" "https://registry.npmmirror.com/style-to-js/-/style-to-js-1.1.16.tgz"
  "version" "1.1.16"
  dependencies:
    "style-to-object" "1.0.8"

"style-to-object@1.0.8":
  "integrity" "sha512-xT47I/Eo0rwJmaXC4oilDGDWLohVhR6o/xAQcPQN8q6QBuZVL8qMYL85kLmST5cPjAorwvqIA4qXTRQoYHaL6g=="
  "resolved" "https://registry.npmmirror.com/style-to-object/-/style-to-object-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "inline-style-parser" "0.2.4"

"trim-lines@^3.0.0":
  "integrity" "sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg=="
  "resolved" "https://registry.npmmirror.com/trim-lines/-/trim-lines-3.0.1.tgz"
  "version" "3.0.1"

"trough@^2.0.0":
  "integrity" "sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw=="
  "resolved" "https://registry.npmmirror.com/trough/-/trough-2.2.0.tgz"
  "version" "2.2.0"

"tslib@^2.0.0":
  "integrity" "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="
  "resolved" "https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz"
  "version" "2.8.1"

"unified@^11.0.0":
  "integrity" "sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA=="
  "resolved" "https://registry.npmmirror.com/unified/-/unified-11.0.5.tgz"
  "version" "11.0.5"
  dependencies:
    "@types/unist" "^3.0.0"
    "bail" "^2.0.0"
    "devlop" "^1.0.0"
    "extend" "^3.0.0"
    "is-plain-obj" "^4.0.0"
    "trough" "^2.0.0"
    "vfile" "^6.0.0"

"unist-util-is@^6.0.0":
  "integrity" "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw=="
  "resolved" "https://registry.npmmirror.com/unist-util-is/-/unist-util-is-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@types/unist" "^3.0.0"

"unist-util-position-from-estree@^2.0.0":
  "integrity" "sha512-KaFVRjoqLyF6YXCbVLNad/eS4+OfPQQn2yOd7zF/h5T/CSL2v8NpN6a5TPvtbXthAGw5nG+PuTtq+DdIZr+cRQ=="
  "resolved" "https://registry.npmmirror.com/unist-util-position-from-estree/-/unist-util-position-from-estree-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@types/unist" "^3.0.0"

"unist-util-position@^5.0.0":
  "integrity" "sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA=="
  "resolved" "https://registry.npmmirror.com/unist-util-position/-/unist-util-position-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@types/unist" "^3.0.0"

"unist-util-stringify-position@^4.0.0":
  "integrity" "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ=="
  "resolved" "https://registry.npmmirror.com/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@types/unist" "^3.0.0"

"unist-util-visit-parents@^6.0.0":
  "integrity" "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw=="
  "resolved" "https://registry.npmmirror.com/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "@types/unist" "^3.0.0"
    "unist-util-is" "^6.0.0"

"unist-util-visit@^5.0.0":
  "integrity" "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg=="
  "resolved" "https://registry.npmmirror.com/unist-util-visit/-/unist-util-visit-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@types/unist" "^3.0.0"
    "unist-util-is" "^6.0.0"
    "unist-util-visit-parents" "^6.0.0"

"use-sync-external-store@^1.4.0":
  "integrity" "sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A=="
  "resolved" "https://registry.npmmirror.com/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz"
  "version" "1.5.0"

"utility-types@^3.10.0":
  "integrity" "sha512-6Z7Ma2aVEWisaL6TvBCy7P8rm2LQoPv6dJ7ecIaIixHcwfbJ0x7mWdbcwlIM5IGQxPZSFYeqRCqlOOeKoJYMkw=="
  "resolved" "https://registry.npmmirror.com/utility-types/-/utility-types-3.11.0.tgz"
  "version" "3.11.0"

"vfile-message@^4.0.0":
  "integrity" "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw=="
  "resolved" "https://registry.npmmirror.com/vfile-message/-/vfile-message-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "@types/unist" "^3.0.0"
    "unist-util-stringify-position" "^4.0.0"

"vfile@^6.0.0":
  "integrity" "sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q=="
  "resolved" "https://registry.npmmirror.com/vfile/-/vfile-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "@types/unist" "^3.0.0"
    "vfile-message" "^4.0.0"

"zwitch@^2.0.0":
  "integrity" "sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A=="
  "resolved" "https://registry.npmmirror.com/zwitch/-/zwitch-2.0.4.tgz"
  "version" "2.0.4"
