package org.jeecg.hours;

import java.util.ArrayList;
import java.util.List;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.hours.context.AuthContext;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.entity.HourRecordApprover;
import org.jeecg.modules.hours.mapper.FsUserMapper;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.service.impl.FeishuAuthServiceImpl;
import org.jeecg.modules.hours.task.AttendanceStatJob;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.plugin.builder.GetPluginTokenReq;
import com.lark.project.service.plugin.builder.GetPluginTokenResp;
import com.lark.project.service.plugin.builder.GetUserPluginTokenReq;
import com.lark.project.service.plugin.builder.GetUserPluginTokenResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 消息推送测试
 * @Author: lsq
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class FsUserTest2 {

	@Autowired
	private FsUserMapper fsUserMapper;

	@org.junit.jupiter.api.Test
	public void testname() throws Exception {

		List<FsUser> leaderList = fsUserMapper.getLeadersByUnionId("on_03144b4670493fe27269925cf7e9cdbf");
		for (FsUser leader : leaderList) {
			String approverId = leader.getUserKey();
			// 若已存在（如既是pm又是leader），优先保留pm
			log.info("userKey={},name={}", leader.getUserKey(), leader.getName());
		}

	}

}
