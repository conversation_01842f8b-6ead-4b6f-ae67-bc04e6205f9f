package org.jeecg.hours;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.jeecg.modules.hours.dto.WorkPeriodDTO;
import org.jeecg.modules.hours.vo.WorkPeriodVO;

import lombok.extern.slf4j.Slf4j;


@Slf4j
public class WeekTest {
	
	
	public static void main(String[] args) {
        String month = "2025-01";


        List<WorkPeriodDTO> workPeriodsDTO = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");

        // 解析月份
        YearMonth yearMonth = YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyy-MM"));
        LocalDate firstDay = yearMonth.atDay(1);
        LocalDate lastDay = yearMonth.atEndOfMonth();

        // 生成周期选项
        // 1. 整月
//        WorkPeriodDTO monthPeriod = new WorkPeriodDTO();
//        monthPeriod.setLabel("整月 (" + firstDay.format(formatter) + " - " + lastDay.format(formatter) + ")");
//        monthPeriod.setValue("month");
//        monthPeriod.setStartDate(firstDay.format(formatter));
//        monthPeriod.setEndDate(lastDay.format(formatter));
//        workPeriodsDTO.add(monthPeriod);

        // 2. 每周（支持跨月截断）
        LocalDate weekStart = firstDay;
        int weekCount = 1;
        while (!weekStart.isAfter(lastDay)) {
            // 第一周特殊处理：如果weekStart是1号且不是周一，则第一周只包含1号
            // 处理跨月周：找到当月第一个自然周的起始日
            LocalDate weekStartInMonth = weekStart;
            if (weekStart.getMonthValue() != yearMonth.getMonthValue()) {
                weekStartInMonth = yearMonth.atDay(1);
            }
            
            // 计算本周的结束日（自然周的周日）
            LocalDate weekEnd = weekStartInMonth.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
            
            // 如果周日不在当月，则截断到月末
            if (weekEnd.getMonthValue() != yearMonth.getMonthValue()) {
                weekEnd = yearMonth.atEndOfMonth();
            }
            
            // 处理月末单日情况
            if (weekStartInMonth.equals(weekEnd)) {
                weekEnd = weekStartInMonth;
            }
            
            WorkPeriodDTO weekPeriod = new WorkPeriodDTO();
            weekPeriod.setLabel(
                weekStartInMonth.format(formatter) + "-" + weekEnd.format(formatter));
            weekPeriod.setValue("week-" + weekCount);
            weekPeriod.setStartDate(weekStartInMonth.format(formatter));
            weekPeriod.setEndDate(weekEnd.format(formatter));
            workPeriodsDTO.add(weekPeriod);
            
            // 下一周的开始日期
            weekStart = weekEnd.plusDays(1);
            weekCount++;
            
            // 确保下一周从周一开始
            weekStart = weekStart.with(TemporalAdjusters.nextOrSame(DayOfWeek.MONDAY));
        }


        // 转换为VO
        workPeriodsDTO.stream().map(dto -> {
            WorkPeriodVO vo = new WorkPeriodVO();
            
            log.info("start={},end={}",dto.getStartDate(),dto.getEndDate());
            
//            vo.setLabel(dto.getLabel());
//            vo.setValue(dto.getValue());
//            vo.setStartDate(dto.getStartDate());
//            vo.setEndDate(dto.getEndDate());
        	
        	
            return vo;
        }).collect(Collectors.toList());
		
	}

}
