package org.jeecg.modules.hours.service;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.HourRecordApply;
import org.jeecg.modules.hours.vo.HourRecordApplyVO;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

public interface IHourRecordApplyService extends IService<HourRecordApply> {

	/**
	 * 生成批次号 格式：SP + 日期(yyMMdd) + 6位数字 例如：SP25071100001
	 * 
	 * @return 生成的批次号
	 */
	String generateBatchNo();

	IPage<HourRecordApplyVO> queryApprovalRecords(Page<HourRecordApplyVO> page, String startDate, String endDate,
			List<String> employeeIdList, String tab, String currentUserId, String status);
	

	// 分页查询待交接审批数据
	IPage<HourRecordApplyVO> queryHandoverApproverRecords(Page<HourRecordApplyVO> page, String startDate,
			String endDate, List<String> employeeIdList,
			String tab, String status,
			String projectId);

}