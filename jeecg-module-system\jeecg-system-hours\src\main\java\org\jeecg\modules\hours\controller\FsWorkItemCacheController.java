package org.jeecg.modules.hours.controller;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.hours.entity.FsWorkItemCache;
import org.jeecg.modules.hours.service.IFsWorkItemCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.servlet.http.HttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;

/**
 * @Description: 工作内容项缓存接口（用于系统设置-工作内容范围下拉）
 * @Author: AI助手
 * @Date: 2024-07-04
 */
@RestController
@RequestMapping("/hapi/hours/workItemCache")
@Slf4j
public class FsWorkItemCacheController {

    @Autowired
    private IFsWorkItemCacheService fsWorkItemCacheService;

    /**
     * 飞书空间ID的请求头名称
     */
    private static final String SPACE_ID_HEADER = RequestHeaderUtil.SPACE_ID_HEADER;

    /**
     * 获取所有工作内容项（仅返回id、name、itemKey、itemType、status）
     */
    @GetMapping("/list")
    public Result<List<FsWorkItemCache>> list() {
        // 从请求头中获取spaceId
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String spaceId = request.getHeader(SPACE_ID_HEADER);
        // 使用spaceId作为过滤条件
        LambdaQueryWrapper<FsWorkItemCache> queryWrapper = new LambdaQueryWrapper<>();
        if (spaceId != null && !spaceId.isEmpty()) {
            queryWrapper.eq(FsWorkItemCache::getProjectKey, spaceId);
        }
        List<FsWorkItemCache> list = fsWorkItemCacheService.list(queryWrapper);
        return Result.OK(list);
    }

    @GetMapping("/workItems")
    public Result<?> getWorkItems(HttpServletRequest request) {
        // 获取飞书空间ID头信息
        String spaceId = request.getHeader(SPACE_ID_HEADER);
        log.info("获取工作项列表，当前空间ID: {}", spaceId);
        
        // ... existing code ...
        return Result.OK();
    }
} 