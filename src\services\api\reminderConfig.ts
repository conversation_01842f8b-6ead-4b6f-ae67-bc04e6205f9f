import { get, post, put, del } from '../../utils/request';
import { 
  ReminderConfig, 
  ReminderConfigParams, 
  ReminderConfigFormData,
  ApiResponse,
  PaginationResponse 
} from '../../types/api';

const API_PREFIX = '/hours/fsSysReminderConfig';

/**
 * 获取登记提醒配置列表
 */
export async function getReminderConfigList(params?: ReminderConfigParams): Promise<ApiResponse<PaginationResponse<ReminderConfig>>> {
  return get(`${API_PREFIX}/list`, params);
}

/**
 * 根据ID获取登记提醒配置
 */
export async function getReminderConfigById(id: number): Promise<ApiResponse<ReminderConfig>> {
  return get(`${API_PREFIX}/queryById`, { id });
}

/**
 * 添加登记提醒配置
 */
export async function addReminderConfig(data: ReminderConfigFormData): Promise<ApiResponse<ReminderConfig>> {
  return post(`${API_PREFIX}/add`, data);
}

/**
 * 更新登记提醒配置
 */
export async function updateReminderConfig(data: ReminderConfig): Promise<ApiResponse<ReminderConfig>> {
  return put(`${API_PREFIX}/edit`, data);
}

/**
 * 删除登记提醒配置
 */
export async function deleteReminderConfig(id: number): Promise<ApiResponse<void>> {
  return del(`${API_PREFIX}/delete?id=${id}`);
}

/**
 * 批量删除登记提醒配置
 */
export async function batchDeleteReminderConfig(ids: number[]): Promise<ApiResponse<void>> {
  return del(`${API_PREFIX}/deleteBatch?ids=${ids.join(',')}`);
}

/**
 * 启用/禁用登记提醒配置
 */
export async function toggleReminderConfigStatus(id: number, status: '启用' | '暂停'): Promise<ApiResponse<void>> {
  return put(`${API_PREFIX}/toggleStatus`, { id, status });
}

/**
 * 获取提醒类型选项
 */
export function getReminderTypeOptions() {
  return [
    { label: '每天', value: 'daily' },
    { label: '每周', value: 'weekly' },
    { label: '每月', value: 'monthly' }
  ];
}

/**
 * 获取重复方式选项
 */
export function getRepeatModeOptions(reminderType: string) {
  const options = {
    daily: [
      { label: '当天', value: 'current_day' },
      { label: '次日', value: 'next_day' }
    ],
    weekly: [
      { label: '本周', value: 'current_week' },
      { label: '次周', value: 'next_week' }
    ],
    monthly: [
      { label: '本月', value: 'current_month' },
      { label: '次月', value: 'next_month' }
    ]
  };
  
  return options[reminderType] || [];
}

/**
 * 获取提醒日期选项
 */
export function getReminderDayOptions(reminderType: string) {
  if (reminderType === 'weekly') {
    return [
      { label: '周一', value: 'monday' },
      { label: '周二', value: 'tuesday' },
      { label: '周三', value: 'wednesday' },
      { label: '周四', value: 'thursday' },
      { label: '周五', value: 'friday' },
      { label: '周六', value: 'saturday' },
      { label: '周日', value: 'sunday' }
    ];
  }
  
  if (reminderType === 'monthly') {
    const options: Array<{ label: string; value: string }> = [];
    for (let i = 1; i <= 31; i++) {
      options.push({ label: `${i}号`, value: i.toString() });
    }
    options.push({ label: '月末', value: 'last_day' });
    return options;
  }
  
  return [];
}

/**
 * 验证提醒配置表单数据
 */
export function validateReminderConfig(data: ReminderConfigFormData): string[] {
  const errors: string[] = [];
  
  if (!data.reminderType) {
    errors.push('请选择提醒类型');
  }
  
  if (!data.repeatMode) {
    errors.push('请选择重复方式');
  }
  
  if ((data.reminderType === 'weekly' || data.reminderType === 'monthly') && !data.reminderDay) {
    errors.push('请选择提醒日期');
  }
  
  if (!data.reminderTime) {
    errors.push('请选择提醒时间');
  } else {
    // 验证时间格式 HH:MM
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(data.reminderTime)) {
      errors.push('提醒时间格式不正确，请使用HH:MM格式');
    }
  }
  
  return errors;
}

/**
 * 格式化提醒配置显示文本
 */
export function formatReminderConfigText(config: ReminderConfig): string {
  const typeMap = {
    daily: '每天',
    weekly: '每周',
    monthly: '每月'
  };
  
  const repeatModeMap = {
    current_day: '当天',
    next_day: '次日',
    current_week: '本周',
    next_week: '次周',
    current_month: '本月',
    next_month: '次月'
  };
  
  const dayMap = {
    monday: '周一',
    tuesday: '周二',
    wednesday: '周三',
    thursday: '周四',
    friday: '周五',
    saturday: '周六',
    sunday: '周日',
    last_day: '月末'
  };
  
  let text = `${typeMap[config.reminderType]}提醒，${repeatModeMap[config.repeatMode]}`;
  
  if (config.reminderDay) {
    if (config.reminderType === 'weekly') {
      text += `${dayMap[config.reminderDay]}`;
    } else if (config.reminderType === 'monthly') {
      text += config.reminderDay === 'last_day' ? '月末' : `${config.reminderDay}号`;
    }
  }
  
  text += ` ${config.reminderTime}`;
  
  return text;
}
