# 工时审批需求文档

## 一、业务背景
工时审批模块用于对员工提交的工时进行多级、多角色审批，确保工时数据的真实性和合规性。主要用户包括普通员工（提交工时）、审批人（如直接领导、项目经理）、管理员（管理流程和数据）。

## 二、主要业务需求

### 1. 工时登记
- 员工按周期（如周/月）登记工时，提交后进入审批流。

### 2. 审批流程
- 审批人为“直属领导（+1）”和“对应项目经理”，两者为并行会签（不分先后）。
- 若周期数据涉及多个项目，需所有相关项目经理和直属领导全部审批通过。
- 审批人去重：如同一人既是直属上级又是多个项目的项目经理，仅需该人审批一次。
- 只有所有审批人都同意，工时单才算“通过”；有一人拒绝即“驳回”，审批流程立即结束。
- 拒绝人可在“已办”中查看审批信息，其他未审批人员无需再审批。
- 每个审批人可单独操作（通过/拒绝/备注），审批意见需记录。
- 审批流需可追溯，展示每个审批人的操作、时间、意见。

### 3. 状态流转
- 工时单状态需能反映“审批中/已通过/已驳回/部分审批”等。
- 需能区分“部分审批人已审批，部分未审批”的中间态。

### 4. 典型页面与交互
- 工时审批列表页：展示所有待办、已办、全部工时单，支持筛选、分页、批量操作。
- 工时审批详情页：展示工时单基本信息、工时明细、审批流、操作按钮。
- 审批流转记录：展示每个审批人的审批节点、时间、结果、意见。

### 5. 消息提醒机制
- 数据提交后，所有审批人收到待审批消息通知。
- 审批完成后（无论通过或拒绝），发起人收到审批结果通知。
- 会签过程中，每位审批人审批时，均会触发消息提醒。

### 6. 移交功能
- 当项目经理离职或被裁，需支持将其名下待审批数据一键移交给其他人员。
- 仅管理员可操作移交功能，选择原审批人和新审批人，将原审批人的待审批数据批量移交，项目信息保持不变。

### 7. 数据查看与隔离
- 项目经理仅能查看自己负责项目的数据。
- 当前阶段暂不实现数据隔离，若客户验收时有要求再行调整。

## 三、现有表结构分析

### 1. hour_record（工时登记记录表）
- 记录工时单的基本信息、状态、周期、总工时等。
- status 字段仅能表达单一状态，无法细化到“部分审批人已审批”。

### 2. hour_record_approval（审批记录表）
- 记录每次审批操作（审批人、动作、时间、备注）。
- 通过 record_id 关联工时单。
- 未区分“审批人角色/类型”，也未明确“审批人清单”。

## 四、表结构存在的不足

1. 无法表达“所有审批人都通过才算通过”的会签机制。
2. 审批人角色不明确，难以配置和展示。
3. 审批人清单缺失，难以追踪每个审批人的审批状态。

## 五、表结构调整建议

### 1. hour_record（工时登记记录表）
- 保持现有主表结构，建议增加字段：
  - `current_status`：如“审批中/部分通过/已通过/已驳回”
  - `required_approvers`（可选，JSON）：记录本单需审批人及角色（便于追溯和展示）

### 2. hour_record_approver（工时单审批人清单表，强烈建议新增）
- 记录每个工时单的所有审批人、角色、审批状态，支持会签、去重、移交。
- 字段设计：
  - `id`：主键
  - `record_id`：工时单ID，外键
  - `approver_id`：审批人ID
  - `approver_name`：审批人姓名
  - `role`：审批人角色（如 leader/pm/other）
  - `status`：0:待审批, 1:已通过, 2:已拒绝, 3:已移交
  - `action_time`：审批时间
  - `remarks`：审批备注
  - `transferred_from`：如为移交产生，记录原审批人ID
  - `transferred_time`：移交时间

```sql
CREATE TABLE `hour_record_approver` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `record_id` varchar(36) NOT NULL COMMENT '工时记录ID',
  `approver_id` varchar(64) NOT NULL COMMENT '审批人ID',
  `approver_name` varchar(50) NOT NULL COMMENT '审批人姓名',
  `role` varchar(20) NOT NULL COMMENT '审批人角色',
  `status` tinyint(1) DEFAULT 0 COMMENT '审批状态(0:待审批,1:已通过,2:已拒绝,3:已移交)',
  `action_time` datetime DEFAULT NULL COMMENT '审批时间',
  `remarks` varchar(200) DEFAULT NULL COMMENT '审批备注',
  `transferred_from` varchar(64) DEFAULT NULL COMMENT '移交前审批人ID',
  `transferred_time` datetime DEFAULT NULL COMMENT '移交时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工时单审批人清单表';
```

### 3. hour_record_approval（工时审批操作日志表，建议完善）
- 记录每一次审批动作（包括通过、拒绝、移交等），便于审批流展示和追溯。
- 字段设计：
  - `id`：主键
  - `record_id`：工时单ID
  - `approver_id`：审批人ID
  - `approver_name`：审批人姓名
  - `role`：审批人角色
  - `action`：approve/reject/transfer/remark等
  - `action_time`：操作时间
  - `remarks`：审批备注
  - `created_at`：记录创建时间

```sql
CREATE TABLE `hour_record_approval` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `record_id` varchar(36) NOT NULL COMMENT '工时记录ID',
  `approver_id` varchar(64) NOT NULL COMMENT '审批人ID',
  `approver_name` varchar(50) NOT NULL COMMENT '审批人姓名',
  `role` varchar(20) NOT NULL COMMENT '审批人角色',
  `action` varchar(20) NOT NULL COMMENT '操作：approve-通过，reject-拒绝，transfer-移交，remark-备注',
  `action_time` datetime NOT NULL COMMENT '操作时间',
  `remarks` varchar(200) DEFAULT NULL COMMENT '审批备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工时审批操作日志表';
```

### 4. 典型数据流说明
1. 工时单提交时，生成 hour_record，并批量插入 hour_record_approver（去重后所有审批人）。
2. 审批人审批时，更新 hour_record_approver 的 status/action_time/remarks，并写入 hour_record_approval。
3. 系统定时/触发检查 hour_record_approver，全部 status=已通过 → hour_record.status=已通过；有一条=已拒绝 → hour_record.status=已驳回。
4. 移交时，管理员操作 hour_record_approver，将待审批数据的 approver_id 替换为新审批人，并记录 transferred_from、transferred_time，同时写入 hour_record_approval（action=transfer）。
5. 审批流展示时，聚合 hour_record_approval + hour_record_approver。

### 5. 设计理由
- 通过 approver 表可灵活支持会签、去重、移交、审批状态追踪。
- approval 表可完整记录所有审批动作，便于审批流展示和追溯。
- 若后续有更复杂的审批流（如多级、条件分支），可在 approver 表增加审批节点类型等字段。

## 六、典型审批流

1. 员工提交工时单，生成 hour_record，插入 hour_record_approver（多条，分别为直属领导、各项目经理，去重）。
2. 审批人各自审批，更新 hour_record_approver.status。
3. 每次审批操作写入 hour_record_approval。
4. 系统定时/触发检查 hour_record_approver，所有 status=已通过 → hour_record.status=已通过；有一条=已拒绝 → hour_record.status=已驳回。
5. 审批流展示时，聚合 hour_record_approval + hour_record_approver。
6. 若审批人发生变更，管理员可通过移交功能批量转移待审批数据。

## 七、需求要点与注意事项
- 审批操作需有权限校验。
- 批量操作需保证原子性和一致性。
- 审批流可配置，支持多级、条件分支。
- 工时明细展示需清晰、易读，支持多种统计维度。
- 审批意见、备注需完整保存。
- 需有完善的错误处理和日志记录。
- UI需兼容主流浏览器和移动端。
- 消息提醒需及时、准确，保证审批流畅。
- 移交操作需有日志记录，确保可追溯。

---

（如需接口设计、前端展示建议等可进一步补充）

// 下面为原有表结构参考

-- 工时登记记录
CREATE TABLE `hour_record` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户姓名',
  `work_month` varchar(7) NOT NULL COMMENT '工作月份(格式：yyyy-MM)',
  `work_period_start` date NOT NULL COMMENT '工作周期开始日期',
  `work_period_end` date NOT NULL COMMENT '工作周期结束日期',
  `hours` decimal(5,2) NOT NULL COMMENT '总工时(小时)',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态(0:待提交, 1:审批中, 2:已通过, 3:已驳回)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `description` varchar(1000) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_user_period` (`user_id`,`work_period_start`,`work_period_end`),
  KEY `idx_work_month` (`work_month`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工时记录表';

-- 审批记录
CREATE TABLE `hour_record_approval` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `record_id` bigint(20) NOT NULL COMMENT '工时记录ID',
  `approver_id` varchar(64) NOT NULL COMMENT '审批人ID',
  `approver_name` varchar(50) NOT NULL COMMENT '审批人姓名',
  `action` varchar(20) NOT NULL COMMENT '操作：approve-通过，reject-拒绝，modify-修改',
  `action_time` datetime NOT NULL COMMENT '操作时间',
  `remarks` varchar(200) DEFAULT NULL COMMENT '审批备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工时审批记录表';
