package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 项目列信息VO（用于动态表头）
 * @Author: jeecg-boot
 * @Date: 2025-07-07
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectColumnVO", description = "项目列信息VO")
public class ProjectColumnVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    /**
     * 项目总工时（列小计）
     */
    @ApiModelProperty(value = "项目总工时")
    private BigDecimal totalHours;

    /**
     * 排序序号
     */
    @ApiModelProperty(value = "排序序号")
    private Integer sortOrder;

    /**
     * 列标识（用于前端表格列的key）
     */
    @ApiModelProperty(value = "列标识")
    public String getColumnKey() {
        return "project_" + this.projectId;
    }
}