package org.jeecg.modules.hours.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/12 下午8:16
 * @version 1.0
 */
@Data
public class SysUserAttendanceStatVo {
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 用户姓名
     */
    private String userName;
    //开始日期
    private String startDate;
    //结束日期
    private String endDate;
    ///工作日期
    private String workDate;
    ///登记状态
    private String statusName;

}
