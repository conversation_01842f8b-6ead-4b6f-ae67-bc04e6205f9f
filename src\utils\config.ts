/**
 * 应用配置文件
 */

// API服务器相关配置
export const API_CONFIG = {
  // 服务器基础URL
  SERVER_BASE_URL: 'http://localhost:8080',
  // 测试环境
  // SERVER_BASE_URL: 'https://test.hoheng.cn:52311',
  // 客户生产环境
  // SERVER_BASE_URL: 'https://project.leekrchassis.com:18080',
  
  // API基础路径
  API_BASE_PATH: '/jeecg-boot/hapi',
  
  // 完整的API基础URL
  get BASE_URL() {
    return this.SERVER_BASE_URL + this.API_BASE_PATH;
  },
  
  // 特定API路径
  API_PATHS: {
    // 授权API
    AUTH: '/plugin/auth/login',
    // 验证token
    VERIFY_TOKEN: '/plugin/auth/verify',
    // 注销
    LOGOUT: '/plugin/auth/logout',
    // 初始化配置
    INIT_CONFIG: '/plugin/auth/initConfig',
    
    // 工时相关API
    WORK_HOURS: {
    
      BASE: '/hours',
      WORK_ITEMS: '/hours/workItemCascade/workItems',
      LIST: '/hours/list',
      ADD: '/hours/add',
      EDIT: '/hours/edit',
      DELETE: '/hours/delete',
      BATCH: '/hours/batch',
    },
    
    // 系统配置相关API
    SYS_CONFIG: {
      BASE: '/sys/config',
      LIST: '/sys/config/list',
      GET_BY_ID: '/sys/config/queryById',
      GET_BY_KEY: '/sys/config/getByKey',
      GET_BY_GROUP: '/sys/config/getByGroup',
      ADD: '/sys/config/add',
      EDIT: '/sys/config/edit',
      DELETE: '/sys/config/delete',
      DELETE_BATCH: '/sys/config/deleteBatch',
      GROUP_STATS: '/sys/config/groupStats',
      HEALTH: '/sys/config/health'
    },
    
    // 其他API可以在此添加...
  },
  
  // 构建完整API URL的辅助函数
  getFullUrl(path: string, useBasePath = true): string {
    if (path.startsWith('http')) {
      return path;
    }
    
    if (useBasePath) {
      return this.BASE_URL + path;
    } else {
      return this.SERVER_BASE_URL + path;
    }
  },
  
  // 获取授权API的完整URL
  getAuthUrl(code: string): string {
    return this.getFullUrl(this.API_PATHS.AUTH) + `?code=${code}`;
  }
};

// HTTP Header常量
export const HTTP_HEADERS = {
  // 飞书空间ID请求头
  SPACE_ID: 'X-FEISHU-SPACE-ID',
  HOUR_TOKEN: 'x-hour-token',
  AUTHORIZATION: 'Authorization',
  CONTENT_TYPE: 'Content-Type'
};

// 本地存储相关配置
export const STORAGE_KEYS = {
  // 用户token
  TOKEN: 'feishu_user_token',
  
  // 用户ID
  USER_ID: 'feishu_user_key',
  
  // 租户ID
  TENANT_ID: 'feishu_tenant_key',
  
  // 用户信息
  USER_INFO: 'feishu_user_info',
  
  // 飞书空间ID
  SPACE_ID: 'feishu_space_id'
};

// 默认请求配置
export const DEFAULT_REQUEST_CONFIG = {
  // 超时时间（毫秒）
  TIMEOUT: 10000,
  
  // 默认是否携带token
  WITH_TOKEN: true,
  
  // 默认请求头
  HEADERS: {
    [HTTP_HEADERS.CONTENT_TYPE]: 'application/json'
  }
}; 