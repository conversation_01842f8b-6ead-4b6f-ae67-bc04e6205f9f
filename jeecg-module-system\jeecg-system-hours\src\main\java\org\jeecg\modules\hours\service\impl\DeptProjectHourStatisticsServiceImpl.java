package org.jeecg.modules.hours.service.impl;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.hours.entity.DeptProjectHourStatistics;
import org.jeecg.modules.hours.entity.HourRecord;
import org.jeecg.modules.hours.mapper.DeptProjectHourStatisticsMapper;
import org.jeecg.modules.hours.mapper.HourRecordMapper;
import org.jeecg.modules.hours.service.IDeptProjectHourStatisticsService;
import org.jeecg.modules.hours.vo.DeptProjectHourReportResult;
import org.jeecg.modules.hours.vo.DeptProjectHourReportVO;
import org.jeecg.modules.hours.vo.ProjectColumnVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;


/**
 * @Description: 部门项目工时统计表
 * @Author: jeecg-boot
 * @Date: 2025-07-07
 * @Version: V1.0
 */
@Service
@Slf4j
public class DeptProjectHourStatisticsServiceImpl extends ServiceImpl<DeptProjectHourStatisticsMapper, DeptProjectHourStatistics> 
    implements IDeptProjectHourStatisticsService {

    @Autowired
    private DeptProjectHourStatisticsMapper deptProjectHourStatisticsMapper;
    
    @Autowired
    private HourRecordMapper hourRecordMapper;

    @Override
    public DeptProjectHourReportResult getDeptProjectHourReport(Date startDate, Date endDate, String statPeriod,
                                                              List<String> departmentIds, List<String> projectIds, Integer isHoliday) {
        log.info("获取部门项目工时统计报表, startDate={}, endDate={}, statPeriod={}", startDate, endDate, statPeriod);
        
        // 1. 获取动态项目列信息
        List<ProjectColumnVO> projectColumns = deptProjectHourStatisticsMapper.getProjectColumns(
            startDate, endDate, statPeriod, departmentIds, projectIds, isHoliday);
        
        // 添加排序序号（按工时总数降序）
        for (int i = 0; i < projectColumns.size(); i++) {
            projectColumns.get(i).setSortOrder(i + 1);
        }
        
        // 2. 获取原始统计数据
        List<DeptProjectHourReportVO> rawData = deptProjectHourStatisticsMapper.getDeptProjectHourReport(
            startDate, endDate, statPeriod, departmentIds, projectIds, isHoliday);
        
        // 3. 转换数据结构，构建部门×项目矩阵
        Map<String, DeptProjectHourReportVO> deptDataMap = new LinkedHashMap<>();
        
        for (DeptProjectHourReportVO data : rawData) {
            String deptKey = data.getDepartmentId();
            DeptProjectHourReportVO deptRow = deptDataMap.computeIfAbsent(deptKey, k -> {
                DeptProjectHourReportVO row = new DeptProjectHourReportVO();
                row.setDepartmentId(data.getDepartmentId());
                row.setDepartmentName(data.getDepartmentName());
                row.setProjectHours(new HashMap<>());
                row.setProjectUserCounts(new HashMap<>());
                row.setTotalHours(BigDecimal.ZERO);
                return row;
            });
            
            // 填充项目工时数据
            if (data.getProjectId() != null) {
                String projectKey = "project_" + data.getProjectId();
                BigDecimal projectHours = data.getTotalHours();
                if (projectHours == null) {
                    projectHours = BigDecimal.ZERO;
                }
                BigDecimal oldProjectHours = Objects.isNull(deptRow.getProjectHours().get(projectKey))?BigDecimal.ZERO:deptRow.getProjectHours().get(projectKey);
                deptRow.getProjectHours().put(projectKey, projectHours.add(oldProjectHours));
                deptRow.getProjectUserCounts().put(projectKey, data.getProjectUserCount());
                
                // 确保 totalHours 不为 null
                BigDecimal currentTotal = deptRow.getTotalHours();
                if (currentTotal == null) {
                    currentTotal = BigDecimal.ZERO;
                }
                deptRow.setTotalHours(currentTotal.add(projectHours));
            }
        }
        
        // 4. 确保所有部门都有完整的项目列数据（填充0值）
        for (DeptProjectHourReportVO deptRow : deptDataMap.values()) {
            for (ProjectColumnVO projectColumn : projectColumns) {
                String projectKey = projectColumn.getColumnKey();
                deptRow.getProjectHours().putIfAbsent(projectKey, BigDecimal.ZERO);
                deptRow.getProjectUserCounts().putIfAbsent(projectKey, 0);
            }
        }
        
        // 5. 构建总计行数据
        DeptProjectHourReportVO totalRow = new DeptProjectHourReportVO();
        totalRow.setDepartmentId("total");
        totalRow.setDepartmentName("总计");
        totalRow.setProjectHours(new HashMap<>());
        totalRow.setProjectUserCounts(new HashMap<>());
        totalRow.setTotalHours(BigDecimal.ZERO);
        
        // 获取总计行的原始数据
        List<DeptProjectHourReportVO> totalRawData = deptProjectHourStatisticsMapper.getTotalRow(
            startDate, endDate, statPeriod, departmentIds, projectIds, isHoliday);
        
        // 处理总计行数据
        for (DeptProjectHourReportVO data : totalRawData) {
            if (data.getProjectId() != null) {
                String projectKey = "project_" + data.getProjectId();
                BigDecimal projectHours = data.getSingleProjectHours();
                if (projectHours == null) {
                    projectHours = BigDecimal.ZERO;
                }
                totalRow.getProjectHours().put(projectKey, projectHours);
                totalRow.getProjectUserCounts().put(projectKey, data.getProjectUserCount());
                totalRow.setTotalHours(totalRow.getTotalHours().add(projectHours));
            }
        }
        
        // 确保总计行有完整的项目列数据
        for (ProjectColumnVO projectColumn : projectColumns) {
            String projectKey = projectColumn.getColumnKey();
            totalRow.getProjectHours().putIfAbsent(projectKey, BigDecimal.ZERO);
            totalRow.getProjectUserCounts().putIfAbsent(projectKey, 0);
        }
        
        // 6. 获取汇总统计信息 (临时注释，避免编译错误)
        // DeptProjectHourReportResult.ReportSummaryVO summary = deptProjectHourStatisticsMapper.getSummaryData(
        //     startDate, endDate, statPeriod, departmentIds, projectIds);
        DeptProjectHourReportResult.ReportSummaryVO summary = new DeptProjectHourReportResult.ReportSummaryVO()
            .setDateRange(String.format("%s 至 %s", 
                DateUtils.date2Str(startDate, DateUtils.date_sdf.get()), DateUtils.date2Str(endDate, DateUtils.date_sdf.get())))
            .setTotalHours(BigDecimal.ZERO)
            .setDeptCount(deptDataMap.size())
            .setProjectCount(projectColumns.size())
            .setUserCount(0);
        
        // 7. 构建返回结果
        DeptProjectHourReportResult result = new DeptProjectHourReportResult();
        result.setProjectColumns(projectColumns);
        result.setDeptHourData(new ArrayList<>(deptDataMap.values()));
        result.setTotalRow(totalRow);
        result.setSummary(summary);
        
        log.info("部门项目工时统计报表获取完成, 部门数={}, 项目数={}", deptDataMap.size(), projectColumns.size());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int calculateHourStatistics(Date statDate, String statPeriod) {
        log.info("开始计算工时统计数据, statDate={}, statPeriod={}", statDate, statPeriod);
        
        try {
            // 1. 删除当天的旧统计数据
            deptProjectHourStatisticsMapper.deleteByStatDate(statDate, statPeriod);
            
            // 2. 从明细表查询当天的工时记录数据
            List<Map<String, Object>> hourDetails = hourRecordMapper.getHourRecordDetailsForDeptStatistics(statDate);
            
            if (hourDetails.isEmpty()) {
                log.info("统计日期 {} 没有工时记录数据", statDate);
                return 0;
            }
            
            // 3. 按部门和项目分组统计
            Map<String, List<Map<String, Object>>> groupedData = hourDetails.stream()
                .filter(detail -> detail.get("department_id") != null && detail.get("project_id") != null)
                .collect(Collectors.groupingBy(detail -> 
                    detail.get("department_id") + "_" + detail.get("project_id")));
            
            // 4. 构建统计数据
            List<DeptProjectHourStatistics> statisticsList = new ArrayList<>();
            
            for (Map.Entry<String, List<Map<String, Object>>> entry : groupedData.entrySet()) {
                List<Map<String, Object>> details = entry.getValue();
                Map<String, Object> firstDetail = details.get(0);
                
                DeptProjectHourStatistics statistics = new DeptProjectHourStatistics();
                statistics.setStatDate(statDate);
                statistics.setStatPeriod(statPeriod);
                statistics.setDepartmentId((String) firstDetail.get("department_id"));
                statistics.setDepartmentName((String) firstDetail.get("department_name"));
                statistics.setProjectId((String) firstDetail.get("project_id"));
                statistics.setProjectName((String) firstDetail.get("project_name"));
                statistics.setProjectCode((String) firstDetail.get("project_code"));
                statistics.setIsHoliday((Integer) firstDetail.get("is_holiday"));
                // 统计工时总数
                BigDecimal totalHours = details.stream()
                    .map(detail -> (BigDecimal) detail.get("actual_hours"))
                    .filter(hours -> hours != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                statistics.setTotalHours(totalHours);
                
                // 统计参与人数
                long userCount = details.stream()
                    .map(detail -> (String) detail.get("user_id"))
                    .filter(userId -> userId != null)
                    .distinct()
                    .count();
                statistics.setUserCount((int) userCount);
                
                statistics.setRecordCount(details.size());
                statistics.setCreateTime(new Date());
                statistics.setUpdateTime(new Date());
                
                statisticsList.add(statistics);
            }
            
            // 5. 批量插入统计数据
            if (!statisticsList.isEmpty()) {
                deptProjectHourStatisticsMapper.batchInsertOrUpdate(statisticsList);
            }
            
            log.info("工时统计数据计算完成, statDate={}, 处理记录数={}", statDate, statisticsList.size());
            return statisticsList.size();
            
        } catch (Exception e) {
            log.error("计算工时统计数据失败, statDate={}, statPeriod={}", statDate, statPeriod, e);
            throw new RuntimeException("计算工时统计数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCalculateHourStatistics(Date startDate, Date endDate, String statPeriod) {
        log.info("开始批量计算工时统计数据, startDate={}, endDate={}, statPeriod={}", startDate, endDate, statPeriod);
        
        int totalProcessed = 0;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        
        while (!calendar.getTime().after(endDate)) {
            Date currentDate = calendar.getTime();
            int processed = calculateHourStatistics(currentDate, statPeriod);
            totalProcessed += processed;
            
            // 移动到下一天
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        
        log.info("批量计算工时统计数据完成, 总处理记录数={}", totalProcessed);
        return totalProcessed;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int recalculateHourStatistics(Date statDate, String statPeriod) {
        log.info("重新计算工时统计数据, statDate={}, statPeriod={}", statDate, statPeriod);
        return calculateHourStatistics(statDate, statPeriod);
    }

    @Override
    public List<String> getStatisticsJobStatus(Date startDate, Date endDate) {
        // TODO: 实现任务状态查询逻辑
        List<String> statusList = new ArrayList<>();
        statusList.add("统计任务状态查询功能待实现");
        return statusList;
    }
}