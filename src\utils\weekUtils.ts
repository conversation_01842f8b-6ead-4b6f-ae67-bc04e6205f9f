/**
 * 周工具函数
 * 提供与周相关的日期计算功能
 */

export interface WeekRange {
  start: Date;
  end: Date;
}

/**
 * 获取指定日期所在周的范围
 * @param date 任意日期
 * @returns 包含周开始和结束日期的对象
 */
export function getWeekRange(date: Date): WeekRange {
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? -6 : 1); // 调整周日的情况
  
  const start = new Date(date);
  start.setDate(diff);
  start.setHours(0, 0, 0, 0);
  
  const end = new Date(start);
  end.setDate(start.getDate() + 6);
  end.setHours(23, 59, 59, 999);
  
  return { start, end };
}

/**
 * 生成一个月的周选项
 * @param month 月份
 * @returns 该月所有周的日期范围数组
 */
export function generateWeekOptions(month: Date): WeekRange[] {
  const weeks: WeekRange[] = [];
  const firstDay = new Date(month.getFullYear(), month.getMonth(), 1);
  const lastDay = new Date(month.getFullYear(), month.getMonth() + 1, 0);
  
  let currentDate = new Date(firstDay);
  
  while (currentDate <= lastDay) {
    const week = getWeekRange(currentDate);
    weeks.push(week);
    
    // 跳到下周
    currentDate = new Date(week.end);
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return weeks;
}

/**
 * 格式化日期为YYYY-MM-DD
 * @param date 日期对象
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}