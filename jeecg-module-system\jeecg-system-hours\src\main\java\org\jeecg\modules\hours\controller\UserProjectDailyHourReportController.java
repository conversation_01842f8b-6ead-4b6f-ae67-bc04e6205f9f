package org.jeecg.modules.hours.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.hours.dto.UserProjectDailyHourReportDTO;
import org.jeecg.modules.hours.service.IUserProjectDailyHourStatisticsService;
import org.jeecg.modules.hours.service.IUserProjectDailyHourExportService;
import org.jeecg.modules.hours.job.DeptProjectDailyHourStatisticsJob;
import org.jeecg.modules.hours.vo.UserProjectDailyHourReportResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * @Description: 人员项目每日工时分布报表
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Api(tags = "人员项目每日工时分布报表")
@RestController
@RequestMapping("/hapi/hours/report/user-project-daily")
@Slf4j
public class UserProjectDailyHourReportController {

    @Autowired
    private IUserProjectDailyHourStatisticsService userProjectDailyHourStatisticsService;
    
    @Autowired
    private IUserProjectDailyHourExportService userProjectDailyHourExportService;
    
    @Autowired
    private DeptProjectDailyHourStatisticsJob deptProjectDailyHourStatisticsJob;

    /**
     * 获取人员项目每日工时分布报表
     *
     * @param reportDTO 查询参数
     * @return 报表数据
     */
    @AutoLog(value = "人员项目每日工时分布报表-查询")
    @ApiOperation(value = "获取人员项目每日工时分布报表", notes = "获取人员项目每日工时分布报表")
    @GetMapping(value = "/query")
    public Result<UserProjectDailyHourReportResult> query(
            @ApiParam(value = "查询参数") @Valid UserProjectDailyHourReportDTO reportDTO) {
        try {
            log.info("获取人员项目每日工时分布报表，参数: {}", reportDTO);
            UserProjectDailyHourReportResult result = userProjectDailyHourStatisticsService.getUserProjectDailyHourReport(reportDTO);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取人员项目每日工时分布报表失败", e);
            return Result.error("获取报表数据失败: " + e.getMessage());
        }
    }

    /**
     * 导出人员项目每日工时分布报表
     *
     * @param reportDTO 查询参数
     * @param request   请求对象
     * @param response  响应对象
     */
    @AutoLog(value = "人员项目每日工时分布报表-导出")
    @ApiOperation(value = "导出人员项目每日工时分布报表", notes = "导出Excel格式的人员项目每日工时分布报表")
    @GetMapping(value = "/export")
    public void export(
            @ApiParam(value = "查询参数") @Valid UserProjectDailyHourReportDTO reportDTO,
            HttpServletRequest request,
            HttpServletResponse response) {
        try {
            log.info("导出人员项目每日工时分布报表，参数: {}", reportDTO);
            userProjectDailyHourExportService.exportUserProjectDailyHourReport(reportDTO, response);
        } catch (Exception e) {
            log.error("导出人员项目每日工时分布报表失败", e);
            throw new RuntimeException("导出报表失败: " + e.getMessage());
        }
    }

    /**
     * 手动执行人员项目每日工时分布统计任务（月度）
     *
     * @param yearMonth 年月，格式：2025-07。如果不传则默认为上个月
     * @return 执行结果
     */
    @AutoLog(value = "人员项目每日工时分布报表-手动执行月度统计任务")
    @ApiOperation(value = "手动执行月度统计任务", notes = "手动执行人员项目每日工时分布月度统计任务")
    @PostMapping(value = "/executeMonthlyJob")
    public Result<String> executeMonthlyJob(
            @ApiParam(value = "年月，格式：2025-07，不传则默认为上个月") @RequestParam(required = false) String yearMonth) {
        try {
            log.info("手动执行人员项目每日工时分布月度统计任务，年月: {}", yearMonth);
            
            String targetMonth = yearMonth;
            if (targetMonth == null || targetMonth.trim().isEmpty()) {
                // 默认为上个月
                java.util.Calendar calendar = java.util.Calendar.getInstance();
                calendar.add(java.util.Calendar.MONTH, -1);
                targetMonth = String.format("%d-%02d", 
                    calendar.get(java.util.Calendar.YEAR), 
                    calendar.get(java.util.Calendar.MONTH) + 1);
            }
            
            // 执行人员统计任务
            int userCount1 = userProjectDailyHourStatisticsService.generateMonthlyStatistics(targetMonth, 0);
            int userCount2 = userProjectDailyHourStatisticsService.generateMonthlyStatistics(targetMonth, 1);
            
            String message = String.format("月度统计任务执行完成，月份=%s，人员统计(不含假日/含假日)=%d/%d", 
                targetMonth, userCount1, userCount2);
            log.info(message);
            
            return Result.OK(message);
        } catch (Exception e) {
            log.error("手动执行人员项目每日工时分布月度统计任务失败", e);
            return Result.error("执行月度统计任务失败: " + e.getMessage());
        }
    }

    /**
     * 手动执行人员项目每日工时分布统计任务（每日更新）
     *
     * @param yearMonth 年月，格式：2025-07。如果不传则默认为当前月
     * @return 执行结果
     */
    @AutoLog(value = "人员项目每日工时分布报表-手动执行每日更新任务")
    @ApiOperation(value = "手动执行每日更新任务", notes = "手动执行人员项目每日工时分布每日更新任务")
    @PostMapping(value = "/executeDailyJob")
    public Result<String> executeDailyJob(
            @ApiParam(value = "年月，格式：2025-07，不传则默认为当前月") @RequestParam(required = false) String yearMonth) {
        try {
            log.info("手动执行人员项目每日工时分布每日更新任务，年月: {}", yearMonth);
            
            String targetMonth = yearMonth;
            if (targetMonth == null || targetMonth.trim().isEmpty()) {
                // 默认为当前月
                java.util.Calendar calendar = java.util.Calendar.getInstance();
                targetMonth = String.format("%d-%02d", 
                    calendar.get(java.util.Calendar.YEAR), 
                    calendar.get(java.util.Calendar.MONTH) + 1);
            }
            
            // 执行人员统计任务
            int userCount1 = userProjectDailyHourStatisticsService.generateMonthlyStatistics(targetMonth, 0);
            int userCount2 = userProjectDailyHourStatisticsService.generateMonthlyStatistics(targetMonth, 1);
            
            String message = String.format("每日更新任务执行完成，月份=%s，人员统计(不含假日/含假日)=%d/%d", 
                targetMonth, userCount1, userCount2);
            log.info(message);
            
            return Result.OK(message);
        } catch (Exception e) {
            log.error("手动执行人员项目每日工时分布每日更新任务失败", e);
            return Result.error("执行每日更新任务失败: " + e.getMessage());
        }
    }
}