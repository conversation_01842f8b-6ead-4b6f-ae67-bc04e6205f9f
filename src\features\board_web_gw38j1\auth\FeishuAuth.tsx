import React, { useState } from 'react';
import { Button, Toast } from '@douyinfe/semi-ui';
import { authorize } from '../../../utils/auth';

interface FeishuAuthProps {
  onSuccess?: () => void;
  buttonText?: string;
  buttonType?: 'primary' | 'secondary' | 'tertiary' | 'warning' | 'danger';
  size?: 'default' | 'small' | 'large';
  className?: string;
}

/**
 * 飞书项目授权按钮组件
 */
const FeishuAuth: React.FC<FeishuAuthProps> = ({
  onSuccess,
  buttonText = '飞书项目授权登录',
  buttonType = 'primary',
  size = 'default',
  className
}) => {
  const [loading, setLoading] = useState(false);

  // 处理授权流程
  const handleAuth = async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      // 使用 authorize() 函数进行授权
      const result = await authorize();
      
      if (result) {
        // 授权成功
        Toast.success('授权成功');
        
        // 执行成功回调
        if (onSuccess) {
          onSuccess();
        }
      } else {
        // 授权失败
        Toast.error('授权失败');
      }
    } catch (error) {
      // 显示错误信息
      const errorMsg = error instanceof Error ? error.message : '未知错误';
      console.error('授权失败:', errorMsg);
      Toast.error('授权失败: ' + errorMsg);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      type={buttonType}
      size={size}
      loading={loading}
      onClick={handleAuth}
      className={className}
    >
      {buttonText}
    </Button>
  );
};

export default FeishuAuth; 