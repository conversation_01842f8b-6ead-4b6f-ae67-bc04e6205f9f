package org.jeecg.modules.hapi.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import javax.servlet.http.HttpServletRequest;

import org.jeecg.common.api.CommonAPI;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.hapi.vo.FeishuLoginUserVO;
import org.jeecg.modules.hapi.vo.MeegoTokenVO;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.entity.FsProject;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.service.IFsUserService;
import org.jeecg.modules.hours.service.IFsProjectService;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.jeecg.modules.hours.utils.UserInfoUtils;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.core.utils.Jsons;
import com.lark.project.core.utils.Lists;
import com.lark.project.service.plugin.builder.GetUserPluginTokenReq;
import com.lark.project.service.plugin.builder.GetUserPluginTokenResp;
import com.lark.project.service.plugin.builder.RefreshTokenReq;
import com.lark.project.service.plugin.builder.RefreshTokenResp;
import com.lark.project.service.user.builder.QueryUserDetailReq;
import com.lark.project.service.user.builder.QueryUserDetailResp;
import com.lark.project.service.user.model.UserBasicInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api(tags = "数据字典")
@RestController
@RequestMapping("/hapi/dict/")
public class ApiDictController  {
	
	@Autowired
	private CommonAPI commonAPI;


	@AutoLog(value = "数据字典")
	@ApiOperation(value = "数据字典", notes = "数据字典")
	@GetMapping(value = "/get")
	public Result<List<DictModel>> initConfig(@RequestParam("dictCode") String dictCode,HttpServletRequest req) {
		try {
			return Result.OK(commonAPI.queryDictItemsByCode(dictCode));
		} catch (Exception e) {
			log.error("初始化配置异常", e);
			return Result.error("初始化配置失败: " + e.getMessage());
		}
	}
	
}