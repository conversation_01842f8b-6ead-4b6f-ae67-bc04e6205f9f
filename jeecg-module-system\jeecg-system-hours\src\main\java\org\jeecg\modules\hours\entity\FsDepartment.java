package org.jeecg.modules.hours.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 飞书部门表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
@Data
@TableName("fs_department")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="fs_department对象", description="飞书部门表")
public class FsDepartment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**
     * 飞书部门ID
     */
    @Excel(name = "飞书部门ID", width = 15)
    @ApiModelProperty(value = "飞书部门ID")
    private String deptId;
    
    /**
     * 部门名称
     */
    @Excel(name = "部门名称", width = 20)
    @ApiModelProperty(value = "部门名称")
    private String name;
    
    /**
     * 父部门ID
     */
    @Excel(name = "父部门ID", width = 15)
    @ApiModelProperty(value = "父部门ID")
    private String parentId;
    
    /**
     * 部门负责人ID
     */
    @Excel(name = "部门负责人ID", width = 15)
    @ApiModelProperty(value = "部门负责人ID")
    private String leaderUserId;
    
    /**
     * 同步时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "同步时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "同步时间")
    private Date syncTime;
    
    /**
     * 是否参与工时登记：1-是；0-否
     */
    @Excel(name = "是否参与工时登记", width = 10, replace = {"是_1", "否_0"})
    @ApiModelProperty(value = "是否参与工时登记：1-是；0-否")
    private Integer isHour;
    
    /**
     * 子部门列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "子部门列表")
    private List<FsDepartment> children;
} 