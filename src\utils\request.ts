import { Notification } from '@douyinfe/semi-ui';
import { clearAuth,getToken, authorize, getFeishuProjectKey, getFeishuSpaceId } from './auth';
import { API_CONFIG, DEFAULT_REQUEST_CONFIG, HTTP_HEADERS } from './config';

// 请求配置接口
export interface RequestConfig {
  skipErrorHandler?: boolean;
  withToken?: boolean;
  timeout?: number;
  headers?: Record<string, string>;
  skipAuthRetry?: boolean; // 是否跳过授权重试
  authRetryCount?: number; // 授权重试次数
  _originalMethod?: string;
  _originalUrl?: string;
  _originalData?: any;
}

// 响应数据接口
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
  success?: boolean;
  result?: any;
}

// 默认请求配置
const defaultConfig: RequestConfig = {
  skipErrorHandler: false,
  withToken: DEFAULT_REQUEST_CONFIG.WITH_TOKEN,
  timeout: DEFAULT_REQUEST_CONFIG.TIMEOUT,
  skipAuthRetry: false,
  authRetryCount: 0 // 初始授权重试次数为0
};

// 最大授权重试次数
const MAX_AUTH_RETRY = 3;

/**
 * 统一处理401自动授权重试逻辑
 */
async function handleAuthRetry<T>(
  config: RequestConfig,
  method: string,
  url: string,
  data?: any
): Promise<T> {
  console.log('[handleAuthRetry] 触发401自动授权重试', { method, url, data, config });
  clearAuth();
  const retryCount = config.authRetryCount || 0;
  if (retryCount < MAX_AUTH_RETRY) {
    console.log(`[handleAuthRetry] 第${retryCount + 1}次尝试自动授权`);
    const authorized = await authorize();
    console.log('[handleAuthRetry] 授权结果:', authorized);
    if (authorized) {
      const newConfig = { ...config, skipAuthRetry: false, authRetryCount: retryCount + 1 };
      switch (method) {
        case 'GET': return await get<T>(url, data, newConfig);
        case 'POST': return await post<T>(url, data, newConfig);
        case 'PUT': return await put<T>(url, data, newConfig);
        case 'DELETE': return await del<T>(url, newConfig);
      }
    }
  }
  console.warn('[handleAuthRetry] 超过重试次数或授权失败');
  handleErrorMessage('登录已过期，请重新登录', config);
  throw new Error('登录已过期，请重新登录');
}

/**
 * 处理错误消息
 * @param message 错误信息
 * @param config 请求配置
 */
const handleErrorMessage = (message: string, config?: RequestConfig) => {
  if (config?.skipErrorHandler) return;
  
  Notification.error({
    title: '请求错误',
    content: message,
    duration: 3,
  });
};

/**
 * 检查响应状态
 * @param response API响应
 * @param config 请求配置
 */
const checkStatus = async <T>(response: ApiResponse<T>, config?: RequestConfig): Promise<T> => {
  // 处理token失效的情况 (状态码401)
  if (response.code === 401) {
    console.warn('[checkStatus] 检测到JSON code: 401，自动授权重试');
    return await handleAuthRetry<T>(
      config!,
      config?._originalMethod || 'GET',
      config?._originalUrl || '',
      config?._originalData
    );
  }
  
  // 处理其他错误
  if (response.code !== 200 && response.success !== true) {
    const errorMessage = response.message || '服务器异常';
    handleErrorMessage(errorMessage, config);
    throw new Error(errorMessage);
  }
  
  // 直接返回完整响应，让客户端自行处理不同的数据结构
  // 注意：客户端需要自行处理响应中的数据结构，如 response.result, response.data 或 response.result.records 等
  // console.log('返回完整响应，客户端需自行处理数据结构');
  return response as unknown as T;
};

/**
 * 通用请求函数
 * @param method 请求方法
 * @param url 请求地址
 * @param data 请求数据
 * @param config 请求配置
 */
export const request = async <T>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  url: string,
  data?: any,
  config?: RequestConfig
): Promise<T> => {
  const mergedConfig = { ...defaultConfig, ...config };
  
  // 保存原始请求信息，用于token失效时重试
  mergedConfig['_originalMethod'] = method;
  mergedConfig['_originalUrl'] = url;
  mergedConfig['_originalData'] = data;
  
  try {
    // 构建完整URL，使用配置中的API_CONFIG.getFullUrl
    const fullUrl = API_CONFIG.getFullUrl(url);
    
    // 构建请求头
    const headers: Record<string, string> = {
      ...DEFAULT_REQUEST_CONFIG.HEADERS,
      ...mergedConfig.headers || {}
    };
    
    // 添加授权头
    if (mergedConfig.withToken) {
      // console.log('withToken 为 true，将添加认证头');
      const token = getToken();
      if (token) {
        // 使用更规范的头部设置方式
        headers['X-HOUR-TOKEN'] = token;
        // 设置常见的Authorization头
        headers['Authorization'] = `Bearer ${token}`;
        
        // 添加调试信息
        // console.log('已添加认证头，X-HOUR-TOKEN:', token);
      } else {
        console.log('没有token，不添加认证头');
      }
    } else {
      console.log('withToken 为 false，不添加认证头');
    }
    
    // 添加项目key到请求头
    const spaceId = getFeishuSpaceId();
    if (spaceId) {
      headers[HTTP_HEADERS.SPACE_ID] = spaceId;
    }
    
    // console.log('请求头:', JSON.stringify(headers, null, 2));
    
    // 构建请求参数
    let requestUrl = fullUrl;
    const options: RequestInit = {
      method,
      headers,
      // 关键设置：确保凭据被发送，即使是跨域请求
      credentials: 'include',
      mode: 'cors'
    };
    
    // 处理GET请求的参数
    if (method === 'GET' && data) {
      const queryParams = new URLSearchParams();
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value));
        }
      });
      
      const queryString = queryParams.toString();
      if (queryString) {
        requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
      }
    } else if (data) {
      // 非GET请求添加请求体
      options.body = JSON.stringify(data);
    }
    
    // 发送请求
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), mergedConfig.timeout);
    });
    
    // console.log('===== 发送请求信息 =====');
    console.log('请求URL:', requestUrl);
    // console.log('请求方法:', method);
    // console.log('请求头:', JSON.stringify(headers, null, 2));
    // console.log('请求参数:', data ? JSON.stringify(data, null, 2) : '无');
    // console.log('======================');
    
    const fetchPromise = fetch(requestUrl, options);
    console.log('fetchPromise:', fetchPromise);
    const response = await Promise.race([fetchPromise, timeoutPromise]);
    
    console.log('===== 响应信息 =====');
    console.log('响应状态:', response.status, response.statusText);
    console.log('响应URL:', response.url);
    // 打印响应头
    const responseHeaders: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value;
    });
    // console.log('响应头:', JSON.stringify(responseHeaders, null, 2));
    // console.log('===================');
    
    if (!response.ok) {
      // 复用：处理HTTP 401自动授权
      if (response.status === 401) {
        console.warn('[request] 检测到HTTP 401，自动授权重试');
        return await handleAuthRetry<T>(
          mergedConfig,
          mergedConfig._originalMethod || 'GET',
          mergedConfig._originalUrl || '',
          mergedConfig._originalData
        );
      }
      throw new Error(`HTTP错误: ${response.status}`);
    }
    
    const responseData = await response.json();
    // console.log('响应数据:', JSON.stringify(responseData, null, 2));
    return await checkStatus(responseData, mergedConfig);
  } catch (error) {
    // 新增：捕获401异常时的自动授权重试
    if (error instanceof Error && error.message && error.message.includes('401')) {
      console.warn('[catch] 捕获到HTTP 401异常，自动授权重试');
      return await handleAuthRetry<T>(
        mergedConfig,
        mergedConfig._originalMethod || 'GET',
        mergedConfig._originalUrl || '',
        mergedConfig._originalData
      );
    }
    console.error('请求错误:', error);
    throw error;
  }
};

/**
 * GET请求
 * @param url 请求地址
 * @param params 请求参数
 * @param config 请求配置
 */
export const get = <T>(url: string, params?: any, config?: RequestConfig): Promise<T> => {
  return request<T>('GET', url, params, config);
};

/**
 * POST请求
 * @param url 请求地址
 * @param data 请求数据
 * @param config 请求配置
 */
export const post = <T>(url: string, data?: any, config?: RequestConfig): Promise<T> => {
  return request<T>('POST', url, data, config);
};

/**
 * PUT请求
 * @param url 请求地址
 * @param data 请求数据
 * @param config 请求配置
 */
export const put = <T>(url: string, data?: any, config?: RequestConfig): Promise<T> => {
  return request<T>('PUT', url, data, config);
};

/**
 * DELETE请求
 * @param url 请求地址
 * @param config 请求配置
 */
export const del = <T>(url: string, config?: RequestConfig): Promise<T> => {
  return request<T>('DELETE', url, undefined, config);
};

export const getAvailableApprovers = (params: { keyword?: string; excludeUserId?: string }) =>
  get('/hours/hourRecordApprover/availableApprovers', params);

export const batchTransferApprover = (params: { applyIds: string; newApproverId: string }) =>
  post('/hours/hourRecordApprover/batchTransfer', params);

export default {
  get,
  post,
  put,
  delete: del
}; 