import React, { useState, useEffect } from 'react';
import { Modal, Table, Typography, Spin, Toast } from '@douyinfe/semi-ui';
import { get } from '../../../utils/request';
import { fmtDate } from '../../../utils/date';

const { Text } = Typography;

interface WorkHourRecord {
  id: string | number;
  userId: string;
  userName: string;
  workDate: string;
  hours: number;
  description: string;
  status: number;
  createdAt: string;
  updatedAt: string;
}

interface WorkHourDetailData {
  workContent: string;
  totalHours: number;
  totalDays: number;
  records: WorkHourRecord[];
}

interface ApiResponse<T = any> {
  success: boolean;
  result?: T;
  message?: string;
}

interface WorkHourDetailModalProps {
  visible: boolean;
  onClose: () => void;
  workItemId?: string | number;
  instanceId?: string | number;
  nodeId?: string | number;
  taskId?: string | number;
}

const WorkHourDetailModal: React.FC<WorkHourDetailModalProps> = ({
  visible,
  onClose,
  workItemId,
  instanceId,
  nodeId,
  taskId
}) => {
  const [loading, setLoading] = useState(false);
  const [detailData, setDetailData] = useState<WorkHourDetailData | null>(null);

  // 加载工时明细数据
  useEffect(() => {
    if (visible && workItemId) {
      loadWorkHourDetail();
    }
  }, [visible, workItemId, instanceId, nodeId, taskId]);

  const loadWorkHourDetail = async () => {
    try {
      setLoading(true);
      
      // 构建查询参数
      const params = new URLSearchParams();
      if (workItemId) params.append('workItemId', String(workItemId));
      if (instanceId) params.append('instanceId', String(instanceId));
      if (nodeId) params.append('nodeId', String(nodeId));
      if (taskId) params.append('taskId', String(taskId));
      
      const url = `/hours/hourRecord/detail?${params.toString()}`;
      console.log('加载工时明细，请求URL:', url);
      
      const response = await get<ApiResponse<WorkHourDetailData>>(url);
      
      if (response && response.success) {
        setDetailData(response.result || null);
        console.log('工时明细加载成功:', response.result);
      } else {
        console.error('加载工时明细失败:', response?.message);
        Toast.error('加载工时明细失败');
        setDetailData(null);
      }
    } catch (error) {
      console.error('加载工时明细出错:', error);
      Toast.error('加载工时明细失败');
      setDetailData(null);
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      render: (_: any, __: any, index: number) => index + 1
    },
    {
      title: '工作人员',
      dataIndex: 'userName',
      width: 100
    },
    {
      title: '登记工时(H)',
      dataIndex: 'hours',
      width: 100,
      render: (hours: number) => hours?.toFixed(1) || '0.0'
    },
    {
      title: '工作描述',
      dataIndex: 'description',
      width: 120,
      render: (description: string) => description || '—'
    },
    {
      title: '工作日期',
      dataIndex: 'workDate',
      width: 100
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      render: (status: number) => {
        const statusMap: { [key: number]: string } = {
          0: '待提交',
          1: '审批中',
          2: '已通过',
          3: '已驳回'
        };
        return statusMap[status] || '未知';
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      width: 140,
      render: (updateTime: string) => (
        <span style={{ color: 'var(--semi-color-warning)' }}>
          {updateTime}
        </span>
      )
    }
  ];

  return (
    <Modal
      title="工时明细"
      visible={visible}
      onCancel={onClose}
      footer={null}
      width={900}
      bodyStyle={{ padding: '24px' }}
    >
      {loading ? (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '200px'
        }}>
          <Spin />
        </div>
      ) : detailData ? (
        <>
          {/* 工作内容和统计信息 */}
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: '1fr 1fr', 
            gap: '24px', 
            marginBottom: '24px',
            padding: '16px',
            backgroundColor: 'var(--semi-color-fill-0)',
            borderRadius: '6px'
          }}>
            <div>
              <Text strong style={{ color: 'var(--semi-color-text-2)' }}>工作内容</Text>
              <div style={{ marginTop: '4px' }}>
                <Text>{detailData.workContent}</Text>
              </div>
            </div>
            <div>
              <Text strong style={{ color: 'var(--semi-color-text-2)' }}>实际登记工时</Text>
              <div style={{ marginTop: '4px' }}>
                <Text style={{ color: 'var(--semi-color-primary)', fontWeight: 600 }}>
                  {detailData.totalHours.toFixed(2)}小时({detailData.totalDays.toFixed(2)}人天)
                </Text>
              </div>
            </div>
          </div>

          {/* 工时记录表格 */}
          <Table
            columns={columns}
            dataSource={detailData.records}
            pagination={false}
            size="small"
            bordered
            rowKey="id"
            style={{ marginTop: '16px' }}
          />
        </>
      ) : (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '200px',
          color: 'var(--semi-color-text-2)'
        }}>
          <Text>暂无工时明细数据</Text>
        </div>
      )}
    </Modal>
  );
};

export default WorkHourDetailModal;
