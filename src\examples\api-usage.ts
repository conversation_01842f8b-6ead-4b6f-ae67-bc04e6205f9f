import { workHoursApi, approvalApi, settingsApi, userApi } from '../services/api';
import { WorkHoursRecord, ApprovalRecord, PaginationParams } from '../types/api';

// 工时列表示例
async function fetchWorkHoursList() {
  try {
    const params: PaginationParams = {
      page: 1,
      pageSize: 10,
      status: 'pending',
    };
    
    const response = await workHoursApi.getWorkHoursList(params);
    console.log('工时列表:', response);
  } catch (error) {
    console.error('获取工时列表失败:', error);
  }
}

// 创建工时记录示例
async function createWorkHoursRecord() {
  try {
    const data: Partial<WorkHoursRecord> = {
      workDate: '2024-03-20',
      worker: '张三',
      registeredHours: 8,
      availableHours: 8,
      type: '开发',
      typeInfo: '前端开发',
      businessLevel1: '项目A',
      businessLevel2: '模块1',
      hours: 8,
      description: '完成首页开发',
    };
    
    const response = await workHoursApi.createWorkHours(data);
    console.log('创建工时记录成功:', response);
  } catch (error) {
    console.error('创建工时记录失败:', error);
  }
}

// 审批操作示例
async function approveWorkHours() {
  try {
    const data: Partial<ApprovalRecord> = {
      comment: '工时记录合理，同意',
    };
    
    const response = await approvalApi.approve('work-hours-id', data);
    console.log('审批成功:', response);
  } catch (error) {
    console.error('审批失败:', error);
  }
}

// 获取系统配置示例
async function fetchSystemConfig() {
  try {
    const basicConfig = await settingsApi.getBasicConfig();
    const feishuConfig = await settingsApi.getFeishuConfig();
    
    console.log('基础配置:', basicConfig);
    console.log('飞书配置:', feishuConfig);
  } catch (error) {
    console.error('获取系统配置失败:', error);
  }
}

// 获取用户信息示例
async function fetchUserInfo() {
  try {
    const userInfo = await userApi.getUserInfo();
    const permissions = await userApi.getUserPermissions();
    
    console.log('用户信息:', userInfo);
    console.log('用户权限:', permissions);
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
}

// 使用示例
export async function runExamples() {
  await fetchWorkHoursList();
  await createWorkHoursRecord();
  await approveWorkHours();
  await fetchSystemConfig();
  await fetchUserInfo();
} 