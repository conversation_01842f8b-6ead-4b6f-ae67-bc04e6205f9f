<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.SysUserAttendanceStatMapper">
    <resultMap id="BaseResultMap" type="org.jeecg.modules.hours.entity.SysUserAttendanceStat">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="union_id" property="unionId" />
        <result column="stat_date" property="statDate" />
        <result column="work_hours" property="workHours" />
        <result column="over_work_hours" property="overWorkHours" />
        <result column="shift" property="shift" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>
    <select id="getPendingRegisteredHoursList" resultType="org.jeecg.modules.hours.vo.SysUserAttendanceStatVo">
        SELECT sas.stat_date as work_date,fs_user.user_key as user_id,fs_user.name as user_name,'待登记' as status_name
        FROM sys_user_attendance_stat sas
                 LEFT JOIN fs_user on fs_user.union_id = sas.union_id
        <where>
            sas.required_attend_hours > 0
            AND sas.real_attend_hours > 0
            AND fs_user.user_key is not null
            AND sas.stat_date is not null
            <if test="sysUserAttendanceStatVo.startDate!=null and sysUserAttendanceStatVo.startDate!=''">
                AND sas.stat_date BETWEEN #{sysUserAttendanceStatVo.startDate} and #{sysUserAttendanceStatVo.endDate}
            </if>
            <if test="employeeIdList != null and employeeIdList.size()>0">
                AND fs_user.user_key IN
                <foreach collection="employeeIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            AND NOT EXISTS (SELECT 1
            FROM hour_record_detail
            LEFT JOIN hour_record ON hour_record.id = hour_record_detail.record_id
            WHERE user_id = fs_user.user_key
            AND work_date = sas.stat_date)
        </where>
    </select>
</mapper>