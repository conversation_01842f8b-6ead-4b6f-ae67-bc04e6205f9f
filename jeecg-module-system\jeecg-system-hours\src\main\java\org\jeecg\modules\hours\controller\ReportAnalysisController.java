package org.jeecg.modules.hours.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.hours.entity.HourRecord;
import org.jeecg.modules.hours.service.IHourRecordService;
import org.jeecg.modules.hours.service.IReportAnalysisService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.jeecg.modules.hours.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 工时报表分析控制器
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Api(tags = "工时报表分析")
@RestController
@RequestMapping("/hapi/api/reports")
@Slf4j
public class ReportAnalysisController extends JeecgController<HourRecord, IHourRecordService> {

    @Autowired
    private IReportAnalysisService reportAnalysisService;

    /**
     * 实例工时周期汇总报表
     */
    @AutoLog(value = "实例工时周期汇总报表")
    @ApiOperation(value = "实例工时周期汇总报表", notes = "实例工时周期汇总报表")
    @GetMapping("/instance-hours")
    public Result<InstanceHoursReportResponse> getInstanceHoursReport(ReportQueryParams params) {
        try {
        	// 获取工作空间标识
            String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
            if (StringUtils.isBlank(projectKey)) {
                return Result.error("未提供有效的工作空间标识");
            }
            params.setProjectKey(projectKey);
            
            InstanceHoursReportResponse response = reportAnalysisService.getInstanceHoursReport(params);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("获取实例工时周期汇总报表失败", e);
            return Result.error("获取报表数据失败: " + e.getMessage());
        }
    }

    /**
     * 员工工时周期汇总报表
     */
    @AutoLog(value = "员工工时周期汇总报表")
    @ApiOperation(value = "员工工时周期汇总报表", notes = "员工工时周期汇总报表")
    @GetMapping("/employee-hours")
    public Result<EmployeeHoursReportResponse> getEmployeeHoursReport(ReportQueryParams params) {
        try {
        	// 获取工作空间标识
            String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
            if (StringUtils.isBlank(projectKey)) {
                return Result.error("未提供有效的工作空间标识");
            }
            params.setProjectKey(projectKey);
            
            EmployeeHoursReportResponse response = reportAnalysisService.getEmployeeHoursReport(params);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("获取员工工时周期汇总报表失败", e);
            return Result.error("获取报表数据失败: " + e.getMessage());
        }
    }

    /**
     * 员工工时实例分布报表
     */
    @AutoLog(value = "员工工时实例分布报表")
    @ApiOperation(value = "员工工时实例分布报表", notes = "员工工时实例分布报表")
    @GetMapping("/employee-instance-distribution")
    public Result<EmployeeInstanceDistributionResponse> getEmployeeInstanceDistributionReport(ReportQueryParams params) {
        try {
        	// 获取工作空间标识
            String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
            if (StringUtils.isBlank(projectKey)) {
                return Result.error("未提供有效的工作空间标识");
            }
            params.setProjectKey(projectKey);
            
            EmployeeInstanceDistributionResponse response = reportAnalysisService.getEmployeeInstanceDistributionReport(params);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("获取员工工时实例分布报表失败", e);
            return Result.error("获取报表数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目下的用户列表
     */
    @AutoLog(value = "获取项目用户列表")
    @ApiOperation(value = "获取项目用户列表", notes = "获取项目用户列表")
    @GetMapping("/users")
    public Result<List<UserVO>> getProjectUsers() {
        try {
        	// 获取工作空间标识
            String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
            if (StringUtils.isBlank(projectKey)) {
                return Result.error("未提供有效的工作空间标识");
            }
            List<UserVO> users = reportAnalysisService.getProjectUsers(projectKey);
            return Result.ok(users);
        } catch (Exception e) {
            log.error("获取项目用户列表失败", e);
            return Result.error("获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目下的实例列表
     */
    @AutoLog(value = "获取项目实例列表")
    @ApiOperation(value = "获取项目实例列表", notes = "获取项目实例列表")
    @GetMapping("/instances")
    public Result<List<InstanceVO>> getProjectInstances() {
        try {
        	// 获取工作空间标识
            String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
            if (StringUtils.isBlank(projectKey)) {
                return Result.error("未提供有效的工作空间标识");
            }
            List<InstanceVO> instances = reportAnalysisService.getProjectInstances(projectKey);
            return Result.ok(instances);
        } catch (Exception e) {
            log.error("获取项目实例列表失败", e);
            return Result.error("获取实例列表失败: " + e.getMessage());
        }
    }

    /**
     * 导出实例工时周期汇总报表
     */
    @AutoLog(value = "导出实例工时周期汇总报表")
    @ApiOperation(value = "导出实例工时周期汇总报表", notes = "导出实例工时周期汇总报表")
    @GetMapping("/instance-hours/exportXls")
    public ModelAndView exportInstanceHoursReport(HttpServletRequest request, HttpServletResponse response, ReportQueryParams params) {
    	// 获取工作空间标识
        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
        params.setProjectKey(projectKey);
        
        return reportAnalysisService.exportInstanceHoursReport(request, response, params);
    }

    /**
     * 导出员工工时周期汇总报表
     */
    @AutoLog(value = "导出员工工时周期汇总报表")
    @ApiOperation(value = "导出员工工时周期汇总报表", notes = "导出员工工时周期汇总报表")
    @GetMapping("/employee-hours/exportXls")
    public ModelAndView exportEmployeeHoursReport(HttpServletRequest request, HttpServletResponse response, ReportQueryParams params) {
    	// 获取工作空间标识
        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
        params.setProjectKey(projectKey);
        
        return reportAnalysisService.exportEmployeeHoursReport(request, response, params);
    }

    /**
     * 导出员工工时实例分布报表
     */
    @AutoLog(value = "导出员工工时实例分布报表")
    @ApiOperation(value = "导出员工工时实例分布报表", notes = "导出员工工时实例分布报表")
    @GetMapping("/employee-instance-distribution/exportXls")
    public ModelAndView exportEmployeeInstanceDistributionReport(HttpServletRequest request, HttpServletResponse response, ReportQueryParams params) {
    	// 获取工作空间标识
        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
        params.setProjectKey(projectKey);
        
    	return reportAnalysisService.exportEmployeeInstanceDistributionReport(request, response, params);
    }
}
