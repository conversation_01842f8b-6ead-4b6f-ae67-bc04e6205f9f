# 飞书工时管理系统产品需求文档

## 1. 产品概述

### 1.1 产品定位

飞书工时管理系统是一个基于飞书插件开发的企业工时管理平台，用于员工工时记录、项目工时统计、审批管理和数据分析。系统作为飞书插件运行，与飞书项目系统深度集成，实现工时填报、审批、统计和分析等完整功能。

### 1.2 产品目标

- 提供简单高效的工时填报体验，支持单条和批量填报
- 实现完整的工时审批流程，确保数据准确性
- 提供多维度的工时统计分析，辅助管理决策
- 与飞书生态深度集成，实现无缝协作
- 支持项目工时数据的导出和共享

### 1.3 用户群体

- 项目成员：填报工时
- 项目经理：审核并可修改自己负责项目工时
- 直属领导：审核但不可修改工时
- 管理员：查看和操作全部内容
- 人事/财务：查看和导出全部数据

## 2. 功能需求

### 2.1 工时管理模块

**功能定位**：实现工时的单条和批量登记、校验、考勤对接、消息提醒等一体化管理。

**主要功能点：**
- **单个工时登记**：
  - 选择工作日期、工时、工作描述
  - 选择工作项、实例/迭代、节点、任务等层级信息
  - 支持工作内容级联选择
  - 保存或提交操作

- **批量工时登记**：
  - 表格式填写界面，按天/项目维度填写工时
  - 支持批量操作、暂存
  - 自动拉取用户参与的项目
  - 支持按日期区间筛选

- **工时状态管理**：
  - 工时填报状态颜色区分（待提交、审批中、审批通过、审批驳回）
  - 支持工时记录的编辑、删除、提交操作

- **校验规则**：
  - 禁止填写未来数据，工时不得超过考勤工时
  - 工时填写不得超过每日考勤工时，前端+后端双重校验
  - 缺卡时禁止填写工时（或考勤工时为0），缺卡日期自动置灰

- **考勤集成**：
  - 实时集成飞书打卡API，获取考勤数据
  - 显示当日可登记工时、已登记工时和剩余可登记工时

- **消息提醒**：
  - 次日9:30飞书消息提醒
  - 每月最后三天对本月漏填内容推送提醒
  - 消息内容支持跳转填写页面

### 2.2 审批管理模块

**功能定位**：实现灵活高效的工时审批与数据隔离。

**主要功能点：**
- **审批流程**：
  - 基本流程：提交人→直属领导+项目经理（会签）
  - 支持配置审批流程：第一审批人、第二审批人
  - 审批方式选择：依次审批或会签审批
  - 审批人缺失处理：转交配置好的人且去重

- **审批人配置**：
  - 直属上级：对接飞书通讯录"所属上级"扩展字段
  - 部门负责人：取部门的部门负责人字段
  - 项目负责人：飞书项目实例负责人
  - 指定人员：从公司通讯录选择特定人员

- **审批操作**：
  - 项目经理权限：可修改工时、填写意见、审批（通过即为最终版本）
  - 直属领导权限：可整单审批并填写意见，不可修改数据
  - 支持批量审批、审批退回、重新填写

- **数据隔离**：
  - 项目经理仅能查看和审批自己负责项目的数据
  - 基于角色的审批权限管理

- **消息通知**：
  - 审批人收到待审批通知
  - 审批结果通知相关人员
  - 支持飞书消息推送

### 2.3 报表分析模块

**功能定位**：多维度统计分析工时数据，辅助管理决策。

**主要功能点：**
- **实例工时周期汇总报表**：
  - 按月/周/日维度统计实例工时
  - 展示各实例工时占比及总计
  - 支持导出Excel报表

- **员工工时周期汇总报表**：
  - 按月/周/日维度统计员工工时
  - 展示员工工时分布及总计
  - 计算工时天数
  - 支持导出Excel报表

- **员工工时实例分布报表**：
  - 统计员工在各实例上的工时分布
  - 展示工时总计和工作天数
  - 支持导出Excel报表

- **考勤对比报表**：
  - 按月统计考勤与实际工时差异
  - 自动生成对比报表

- **日历数据展示**：
  - 按部门展示成员每日项目工时
  - 按项目展示每日成员工时

### 2.4 系统设置模块

**功能定位**：提供系统配置和管理功能。

**主要功能点：**
- **登记审批设置**：
  - 启用/禁用工时登记审批流程
  - 审批流程设置（第一审批人、第二审批人）
  - 审批方式设置（依次审批或会签）

- **审批对接设置**：
  - 启用飞书请假审批校验
  - 启用飞书加班审批校验
  - 启用飞书工时审批

- **席位管理**：
  - 添加席位管理，指定有席位的用户
  - 同步空间成员添加到席位
  - 非空间成员无法进入插件

- **工时规则设置**：
  - 配置工时填写规则
  - 设置工时校验规则
  - 自定义工时提醒规则

## 3. 系统集成

### 3.1 飞书集成

**功能定位**：与飞书生态系统深度集成，实现数据互通和流程协同。

**主要功能点：**
- **飞书组织架构**：
  - 自动获取用户信息
  - 获取用户直属上级
  - 获取部门负责人

- **飞书项目系统**：
  - 获取项目信息
  - 获取项目负责人
  - 获取项目成员

- **飞书考勤系统**：
  - 获取用户考勤数据
  - 校验工时与考勤时间

- **飞书消息通知**：
  - 工时填报提醒
  - 审批通知
  - 结果反馈


## 4. 非功能需求

### 4.1 性能需求

- 页面加载时间不超过3秒
- 批量操作响应时间不超过5秒
- 支持100人以上同时在线操作

### 4.2 安全需求

- 基于角色的权限控制
- 数据隔离，确保用户只能访问有权限的数据
- 操作日志记录，支持审计追踪

### 4.3 可用性需求

- 界面简洁直观，易于操作
- 提供操作引导和帮助文档
- 支持错误提示和操作反馈

### 4.4 兼容性需求

- 支持主流浏览器（Chrome、Firefox、Safari、Edge）
- 支持飞书移动端访问
- 响应式设计，适应不同屏幕尺寸

## 5. 数据模型

### 5.1 工时记录

- **基本信息**：
  - 用户ID/用户名
  - 工作日期
  - 工时数量
  - 工作描述
  - 创建时间
  - 提交时间

- **项目信息**：
  - 工作项ID/名称
  - 实例/迭代ID/名称
  - 节点ID/名称
  - 任务ID/名称

- **状态信息**：
  - 状态（待提交、审批中、已完成、审批驳回）
  - 审批人
  - 驳回原因

### 5.2 审批记录

- 审批ID
- 工时记录ID
- 审批人ID/名称
- 审批状态
- 审批意见
- 审批时间

### 5.3 系统配置

- 审批流程配置
- 工时规则配置
- 提醒规则配置
- 席位管理配置

## 7. 用户界面

### 7.1 界面风格

- 遵循飞书设计规范
- 使用Semi Design UI组件库
- 简洁明了的操作流程
- 清晰的数据可视化展示

### 7.2 主要页面

- 工时登记页面（单条/批量）
- 工时记录详情页面
- 审批管理页面
- 报表分析页面
- 系统设置页面

## 8. 技术架构

### 8.1 前端架构

- **技术栈**：
  - React 18
  - TypeScript
  - Semi Design UI (@douyinfe/semi-ui)
  - 飞书SDK (@lark-project/js-sdk)
  - Vite5 构建工具

### 8.2 后端架构

- **业务逻辑层**：
  - jeecg-boot 框架
  - Spring Boot
  - MyBatis-Plus
  - JWT + Shiro 权限控制

- **数据服务层**：
  - jeecg-boot 低代码开发平台 (3.7.3)
  - Spring Boot 2.7.18
  - Spring Cloud Alibaba 2021.0.1.0
  - MyBatis-Plus *******
  - Shiro + JWT 安全框架
  - MySQL 数据库

## 9. 开发与部署

### 9.1 开发环境

- 前端开发环境
- 后端开发环境
- 数据库环境
- 测试环境

### 9.2 部署方案

- 飞书应用市场部署
- 私有化部署方案
- 数据迁移方案

## 10. 项目规划

### 10.1 里程碑

- 需求分析与设计：[时间]
- 核心功能开发：[时间]
- 测试与优化：[时间]
- 上线与推广：[时间]

### 10.2 迭代计划

- 第一阶段：基础工时填报与审批功能
- 第二阶段：报表分析与数据导出功能
- 第三阶段：系统集成与优化
- 第四阶段：移动端适配与功能扩展 