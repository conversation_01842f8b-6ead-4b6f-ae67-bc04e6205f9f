<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.HourRecordMapper">
	
	<select id="queryPageList" resultType="org.jeecg.modules.hours.entity.HourRecord">
		select hr.* ,t.apply_id
		from hour_record hr
		left join (
			select record_id,max(id) as apply_id from hour_record_apply group by record_id
		) t on hr.id =t.record_id
		where 1=1
		<if test="startDate != null and startDate != ''">
            AND hr.work_period_start &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND hr.work_period_end &lt;= #{endDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND hr.work_period_end &lt;= #{endDate}
        </if>
        <if test="status != null and status != ''">
            AND hr.status = #{status}
        </if>
        <choose>
        	<when test="isAdmin">
        		and 1=1        	
        	</when>
        	<otherwise>
        		<if test="userKey != null and userKey != ''">
		            AND hr.user_id = #{userKey}
		        </if>	
        	</otherwise>
        </choose>
        <if test="null!=deptIdList and deptIdList.size()>0">
        	and exists(
				select 0
				from fs_user fu 
				left join fs_department_user fdu on fdu.union_id =fu.union_id
				where fu.project_key =#{projectKey} and hr.user_id =fu.user_key
				<foreach collection="deptIdList" item="deptId" open=" and fdu.dept_id in (" close=")" separator=",">
		            #{deptId}
		        </foreach>
			)
        </if>
		order by hr.work_period_start desc,hr.work_period_end desc,hr.create_time desc
    </select>
    

    <!-- 根据用户ID和日期区间查询工时记录 -->
    <select id="queryByUserAndDateRange" resultType="org.jeecg.modules.hours.entity.HourRecord">
        SELECT *
        FROM hour_record
        WHERE user_id = #{userId}
        <if test="startDate != null and startDate != ''">
            AND work_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND work_date &lt;= #{endDate}
        </if>
        ORDER BY work_date DESC, created_at DESC
    </select>
    
    <!-- 根据项目ID查询工时统计 -->
    <select id="queryHoursSummaryByProject" resultType="java.util.Map">
        SELECT 
            user_id,
            user_name,
            work_item_name,
            SUM(hours) as total_hours
        FROM hour_record
        WHERE project_id = #{projectId}
          AND status = 2
        GROUP BY user_id, user_name, work_item_name
        ORDER BY total_hours DESC
    </select>
    
    <!-- 根据用户ID获取最近工时记录 -->
    <select id="queryRecentRecordsByUser" resultType="org.jeecg.modules.hours.entity.HourRecord">
        SELECT *
        FROM hour_record
        WHERE user_id = #{userId}
        ORDER BY work_date DESC, created_at DESC
        LIMIT #{limit}
    </select>

    <!-- 从考勤统计表获取可登记工时 -->
    <select id="getScheduledHoursFromAttendance" resultType="java.math.BigDecimal">
        SELECT SUM(work_hours + over_work_hours) as total_hours
        FROM sys_user_attendance_stat
        WHERE union_id IN
        <foreach collection="unionIds" item="unionId" open="(" separator="," close=")">
            #{unionId}
        </foreach>
        AND stat_date >= #{startDate}
        AND stat_date &lt;= #{endDate}
    </select>

    <!-- 获取人员项目工时统计的项目列信息 -->
    <select id="getUserProjectColumns" resultType="org.jeecg.modules.hours.vo.ProjectColumnVO">
        SELECT 
            h.project_id,
            h.project_name,
            h.project_code,
            SUM(h.hours) as total_hours
        FROM hour_record h
        WHERE h.work_date BETWEEN #{startDate} AND #{endDate}
          AND h.status IN ( 2)
        <if test="userIds != null and userIds.size() > 0">
            AND h.user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            AND h.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        GROUP BY h.project_id, h.project_name, h.project_code
        ORDER BY total_hours DESC
    </select>

    <!-- 获取人员项目工时统计报表数据 -->
    <select id="getUserProjectHourReport" resultType="org.jeecg.modules.hours.vo.UserProjectHourReportVO">
        SELECT 
            h.user_id,
            h.user_name,
            u.username as user_code,
            h.department_id,
            h.department_name,
            SUM(h.hours) as total_hours,
            CONCAT('project_', h.project_id) as project_key,
            h.project_id,
            h.hours as project_hours2
        FROM hour_record h
        LEFT JOIN sys_user u ON h.user_id = u.id
        WHERE h.work_date BETWEEN #{startDate} AND #{endDate}
          AND h.status IN ( 2)
        <if test="userIds != null and userIds.size() > 0">
            AND h.user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            AND h.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        GROUP BY h.user_id, h.user_name, u.username, h.department_id, h.department_name, h.project_id, h.hours
        ORDER BY h.user_name, h.project_id
    </select>

    <!-- 获取人员项目工时统计总计行数据 -->
    <select id="getUserProjectTotalRow" resultType="org.jeecg.modules.hours.vo.UserProjectHourReportVO">
        SELECT 
            'total' as user_id,
            '总计' as user_name,
            '' as user_code,
            '' as department_id,
            '' as department_name,
            SUM(h.hours) as total_hours,
            CONCAT('project_', h.project_id) as project_key,
            h.project_id,
            SUM(h.hours) as project_hours2
        FROM hour_record h
        WHERE h.work_date BETWEEN #{startDate} AND #{endDate}
          AND h.status IN ( 2)
        <if test="userIds != null and userIds.size() > 0">
            AND h.user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            AND h.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        GROUP BY h.project_id
    </select>

    <!-- 从明细表查询指定日期的工时记录用于统计 -->
    <select id="getHourRecordDetailsForStatistics" resultType="java.util.Map">
        SELECT 
            hrd.work_date,
            hrd.project_id,
            hrd.project_name,
            hrd.actual_hours,
            hrd.is_rest as is_holiday,
            hr.user_id,
            hr.user_name,
            hr.status,
            fu.user_key as user_code,
            fd.id as department_id,
            fd.name as department_name,
            p.project_code
        FROM hour_record_detail hrd
        INNER JOIN hour_record hr ON hrd.record_id = hr.id
        LEFT JOIN fs_user fu ON hr.user_id = fu.user_key
        LEFT JOIN fs_department_user fdu ON fu.union_id = fdu.union_id
        LEFT JOIN fs_department fd ON fdu.dept_id = fd.dept_id
        LEFT JOIN plm_project p ON hrd.project_id = p.id
        WHERE hrd.work_date = DATE(#{statDate})
          AND hr.status IN (2)
        ORDER BY hr.user_id, hrd.project_id
    </select>

    <!-- 从明细表查询指定日期的工时记录用于部门统计 -->
    <select id="getHourRecordDetailsForDeptStatistics" resultType="java.util.Map">
        SELECT 
            hrd.work_date,
            hrd.project_id,
            hrd.project_name,
            hrd.actual_hours,
            hrd.is_rest as is_holiday,
            hr.user_id,
            hr.status,
            fu.user_key as user_code,
            fd.id as department_id,
            fd.name as department_name,
            p.project_code
        FROM hour_record_detail hrd
        INNER JOIN hour_record hr ON hrd.record_id = hr.id
        LEFT JOIN fs_user fu ON hr.user_id = fu.user_key
        LEFT JOIN fs_department_user fdu ON fu.union_id = fdu.union_id
        LEFT JOIN fs_department fd ON fdu.dept_id = fd.dept_id
        LEFT JOIN plm_project p ON hrd.project_id = p.id
        WHERE hrd.work_date = DATE(#{statDate})
          AND hr.status IN (2)
        ORDER BY fd.id, hrd.project_id
    </select>

</mapper>