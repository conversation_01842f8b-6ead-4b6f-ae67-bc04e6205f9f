.Workbenches {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  .semi-row {
    .semi-card {
      padding: 10px;
      height: 250px;
    }
  }
  .semi-card-body{
    height: 100%;
  }
  .worTopView {
    height: 100%;
    flex-wrap: nowrap;
  }

  .userBaseView {
    flex-wrap: nowrap;
    overflow-x: auto;
    display: flex;
    flex-shrink: 0;

    height: 100%;
    justify-content: space-between;
    .hoView{
      height: 100%;
      position: relative;
      width: 47%;

    }
    .tongjiView {
      margin-top: 25px;
      .block {
        display: block;
        margin-bottom: 15px;
      }
    }

    .btnView {
      position: absolute;
      bottom:80px;
      width: 100%;
      .btn {
        margin-right:15px;
        width:85px;
      }
    }
    .review{
      position: absolute;
      bottom:10px;
      .guize{
        margin-bottom: 0;
        a{
          margin-left:5px;
        }
      }
    }

    .hoView {

      padding-left: 15px;
      flex-shrink: 0;
      .nameView{
        margin-top: 30px;
      }
    }

    .flex-shrink-0 {
      flex-shrink: 0;
      .workbenchesPic {
        width: 250px;
        position: relative;
        top: 50%;
        transform: translate(0, -50%);
      }
    }

  }

  .boxView{

    position: relative;
    height: 100%;
    //height: 400px;
    background: #fff;
    .headerView{
      width: 100%;
      height: 40px;
      background: #fff;
      .boxtitle{
        float: left;
        line-height: 40px;
        font-size: 16px;
        font-weight: 600;
        color: var( --semi-color-primary);
        height: 100%;
      }
      .rightView{
        float: right;
        height: 100%;
        line-height: 40px;
        font-size: 16px;
        font-weight: 600;
      }
      .icon{
        position: relative;
        top: 50%;
        float: right;
        transform: translate(0, -50%);
      }

    }
    .SelectDep{
      width: fit-content;
    }


  }

  .operation{
    display:flex;
   gap: 8px;
    a{
      color: var( --semi-color-primary);
      font-weight: bold;
    }
    .reject{
      color: var( --semi-color-danger );
      font-weight: bold;
    }
  }
.WorkListCard.semi-card{
  height:inherit;
}


}