package org.jeecg.modules.hours.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;

import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 登记提醒配置表
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Data
@TableName("fs_sys_reminder_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="登记提醒配置对象", description="登记提醒配置表")
public class FsSysReminderConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 工作空间标识 */
    @Excel(name = "工作空间标识", width = 15)
    @ApiModelProperty(value = "工作空间标识")
    private String projectKey;

    /** 提醒类型(每天/每周/每月) */
    @Excel(name = "提醒类型", width = 15)
    @ApiModelProperty(value = "提醒类型：daily-每天，weekly-每周，monthly-每月")
    private String reminderType;

    /** 重复方式(当天/次日/本周/次周/本月/次月) */
    @Excel(name = "重复方式", width = 15)
    @ApiModelProperty(value = "重复方式：current_day-当天，next_day-次日，current_week-本周，next_week-次周，current_month-本月，next_month-次月")
    private String repeatMode;

    /** 提醒日期(周几或月份日期) */
    @Excel(name = "提醒日期", width = 15)
    @ApiModelProperty(value = "提醒日期：周几(monday-sunday)或月份日期(1-31或last_day)")
    private String reminderDay;

    /** 提醒时间(HH:MM) */
    @Excel(name = "提醒时间", width = 15)
    @ApiModelProperty(value = "提醒时间，格式：HH:MM")
    private String reminderTime;

    /** 备注 */
    @Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注信息")
    private String remark;

    /** 状态(启用/暂停) */
    @Excel(name = "状态", width = 10)
    @ApiModelProperty(value = "状态：enabled-启用，disabled-禁用")
    private String status;

    /** 创建人 */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    @Dict(dictTable = "fs_user",dicText = "name",dicCode = "id")
    private String createBy;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 更新人 */
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    @Dict(dictTable = "fs_user",dicText = "name",dicCode = "id")
    private String updateBy;

    /** 更新时间 */
    @Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 逻辑删除标志 */
    @TableLogic
    @ApiModelProperty(value = "删除标志：0-正常，1-删除")
    private Integer delFlag;
}
