package org.jeecg.modules.hours.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.number.BigDecimalUtils;
import org.jeecg.modules.feishu.service.FeiShuMessageService;
import org.jeecg.modules.hours.constant.ConfigKeyConstant;
import org.jeecg.modules.hours.dto.HourRecordDTO;
import org.jeecg.modules.hours.dto.WorkPeriodDTO;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.entity.HourRecord;
import org.jeecg.modules.hours.entity.HourRecordApply;
import org.jeecg.modules.hours.entity.HourRecordDetail;
import org.jeecg.modules.hours.entity.SysUserAttendanceStat;
import org.jeecg.modules.hours.mapper.HourRecordDetailMapper;
import org.jeecg.modules.hours.mapper.HourRecordMapper;
import org.jeecg.modules.hours.service.IFsUserRoleService;
import org.jeecg.modules.hours.service.IFsUserService;
import org.jeecg.modules.hours.service.IHourRecordApplyService;
import org.jeecg.modules.hours.service.IHourRecordApproverService;
import org.jeecg.modules.hours.service.IHourRecordDetailService;
import org.jeecg.modules.hours.service.IHourRecordService;
import org.jeecg.modules.hours.service.ISysConfigService;
import org.jeecg.modules.hours.service.ISysUserAttendanceStatService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.jeecg.modules.hours.utils.UserInfoUtils;
import org.jeecg.modules.hours.vo.AvailableDateVO;
import org.jeecg.modules.hours.vo.HourRecordBatchSaveVO;
import org.jeecg.modules.hours.vo.WorkPeriodVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 工时记录表
 * @Author: jeecg-boot
 * @Date: 2023-07-01
 * @Version: V1.0
 */
@Service
@Slf4j
public class HourRecordServiceImpl extends ServiceImpl<HourRecordMapper, HourRecord> implements IHourRecordService {

	// 硬编码配置 - 标准工时（8小时）
	private static final BigDecimal STANDARD_HOURS = new BigDecimal("8.00");

	// 硬编码配置 - 每日最大可登记工时（24小时）
	private static final BigDecimal MAX_DAILY_HOURS = new BigDecimal("24.00");

	// 硬编码配置 - 最小工时单位（0.5小时）
	private static final BigDecimal MIN_HOUR_UNIT = new BigDecimal("0.5");

	// 硬编码配置 - 上月工时登记截止日(15日)
	private static final int SUBMISSION_DEADLINE = 15;

	// 硬编码配置 - 是否允许登记未来工时(false)
	private static final boolean ALLOW_FUTURE_ENTRY = false;

	// 硬编码配置 - 最大可追溯登记天数(60天)
	private static final int MAX_PAST_DAYS = 60;

	// 硬编码配置 - 是否启用审批流程(true)
	private static final boolean ENABLE_APPROVAL = true;

	@Autowired
	private HourRecordDetailMapper hourRecordDetailMapper;
	@Autowired
	private ISysUserAttendanceStatService sysUserAttendanceStatService;
	@Autowired
	private IFsUserService fsUserService;
	@Autowired
	private UserInfoUtils userInfoUtils;
	@Autowired
	private IHourRecordApproverService hourRecordApproverService;
	@Autowired
	private ISysConfigService sysConfigService;
	@Autowired
	private IHourRecordApplyService hourRecordApplyService;
	@Autowired
	private IHourRecordDetailService hourRecordDetailService;
	@Value("${feishu.project.plugin.button.url}")
	private String buttonUrl;
	@Autowired
	private IFsUserRoleService fsUserRoleService;

	@Override
	public IPage<HourRecord> queryByUserAndDateRange(Page<HourRecord> page, String userId, String startDate,
			String endDate) {
		LambdaQueryWrapper<HourRecord> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(HourRecord::getUserId, userId).ge(HourRecord::getWorkPeriodStart, startDate)
				.le(HourRecord::getWorkPeriodEnd, endDate).orderByDesc(HourRecord::getCreateTime);
		return this.page(page, queryWrapper);
	}

	@Override
	public List<Map<String, Object>> queryHoursSummaryByProject(String projectId) {
		return baseMapper.queryHoursSummaryByProject(projectId);
	}

	@Override
	public List<HourRecord> queryRecentRecordsByUser(String userId, int limit) {
		LambdaQueryWrapper<HourRecord> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(HourRecord::getUserId, userId).orderByDesc(HourRecord::getCreateTime).last("LIMIT " + limit);
		return this.list(queryWrapper);
	}

	@Override
	public boolean batchSubmitHourRecords(List<Long> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return false;
		}

		LambdaUpdateWrapper<HourRecord> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.in(HourRecord::getId, ids).set(HourRecord::getStatus, 1).set(HourRecord::getUpdateTime,
				new Date());

		return this.update(updateWrapper);
	}

	@Override
	public boolean approveHourRecord(Long id, String approverId, String approverName, boolean approve,
			String rejectReason) {
		HourRecord record = this.getById(id);
		if (record == null) {
			return false;
		}

		record.setStatus(approve ? 2 : 3); // 2-已完成 3-审批驳回
		if (!approve) {
			// 这里假设HourRecord类有rejectReason, approverId, approverName, approveTime等字段
			// 如果没有这些字段，需要根据实际情况调整
		}
		record.setUpdateTime(new Date());

		return this.updateById(record);
	}

	@Override
	public boolean checkHoursExceedLimit(String userId, String workDate, BigDecimal hours, Long recordId) {
		LambdaQueryWrapper<HourRecord> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(HourRecord::getUserId, userId).eq(HourRecord::getWorkPeriodStart, workDate);

		if (recordId != null) {
			queryWrapper.ne(HourRecord::getId, recordId);
		}

		// 获取当天已登记的工时总和
		BigDecimal totalHours = this.list(queryWrapper).stream().map(HourRecord::getHours).reduce(BigDecimal.ZERO,
				BigDecimal::add);

		// 加上本次要登记的工时
		totalHours = totalHours.add(hours);

		// 判断是否超过24小时
		return totalHours.compareTo(new BigDecimal("24")) > 0;
	}

//    @Override
//    public BigDecimal getRecordedHours(String projectKey, String workItemKey, String instanceKey, String nodeKey, String taskKey) {
//        LambdaQueryWrapper<HourRecord> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper
////        			.eq("project_id", projectKey)
//                   .eq(workItemKey != null, "work_item_id", workItemKey)
//                   .eq(instanceKey != null, "instance_id", instanceKey)
//                   .eq(nodeKey != null, "node_id", nodeKey)
//                   .eq(taskKey != null, "task_id", taskKey)
//                   .in("status", 0,1, 2); // 只统计审批中和已完成的工时
//        
//        return this.list(queryWrapper)
//            .stream()
//            .map(HourRecord::getHours)
//            .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }

//    @Override
//    public Map<String, BigDecimal> getRecordedHoursBatch(String projectKey, List<String> workItemKeys, 
//            List<String> instanceKeys, List<String> nodeKeys, List<String> taskKeys) {
//        // 参数校验
//        if (projectKey == null || (CollectionUtils.isEmpty(workItemKeys) && 
//            CollectionUtils.isEmpty(instanceKeys) && CollectionUtils.isEmpty(nodeKeys) && 
//            CollectionUtils.isEmpty(taskKeys))) {
//            return new HashMap<>();
//        }
//        
//        // 构建查询条件
//        LambdaQueryWrapper<HourRecord> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper
////        .eq("project_id", projectKey)
//                   .in(!CollectionUtils.isEmpty(workItemKeys), "work_item_id", workItemKeys)
//                   .in(!CollectionUtils.isEmpty(instanceKeys), "instance_id", instanceKeys)
//                   .in(!CollectionUtils.isEmpty(nodeKeys), "node_id", nodeKeys)
//                   .in(!CollectionUtils.isEmpty(taskKeys), "task_id", taskKeys)
//                   .in("status", 0,1, 2); // 只统计审批中和已完成的工时
//        
//        // 查询所有相关记录
//        List<HourRecord> records = this.list(queryWrapper);
//        
//        // 初始化结果Map
//        Map<String, BigDecimal> result = new HashMap<>();
//        
//        // 按工作项分组统计
//        if (!CollectionUtils.isEmpty(workItemKeys)) {
//            Map<String, BigDecimal> workItemHours = records.stream()
//                .filter(r -> r.getWorkItemId() != null)
//                .collect(Collectors.groupingBy(
//                    HourRecord::getWorkItemId,
//                    Collectors.reducing(
//                        BigDecimal.ZERO,
//                        HourRecord::getHours,
//                        BigDecimal::add
//                    )
//                ));
//            result.putAll(workItemHours);
//        }
//        
//        // 按实例分组统计
//        if (!CollectionUtils.isEmpty(instanceKeys)) {
//            Map<String, BigDecimal> instanceHours = records.stream()
//                .filter(r -> r.getInstanceId() != null)
//                .collect(Collectors.groupingBy(
//                    HourRecord::getInstanceId,
//                    Collectors.reducing(
//                        BigDecimal.ZERO,
//                        HourRecord::getHours,
//                        BigDecimal::add
//                    )
//                ));
//            result.putAll(instanceHours);
//        }
//        
//        // 按节点分组统计
//        if (!CollectionUtils.isEmpty(nodeKeys)) {
//            Map<String, BigDecimal> nodeHours = records.stream()
//                .filter(r -> r.getNodeId() != null)
//                .collect(Collectors.groupingBy(
//                    HourRecord::getNodeId,
//                    Collectors.reducing(
//                        BigDecimal.ZERO,
//                        HourRecord::getHours,
//                        BigDecimal::add
//                    )
//                ));
//            result.putAll(nodeHours);
//        }
//        
//        // 按任务分组统计
//        if (!CollectionUtils.isEmpty(taskKeys)) {
//            Map<String, BigDecimal> taskHours = new HashMap<>();
//            for (HourRecord record : records) {
//                if (record.getTaskId() != null) {
//                    taskHours.merge(
//                        record.getTaskId(),
//                        record.getHours(),
//                        BigDecimal::add
//                    );
//                }
//            }
//            result.putAll(taskHours);
//        }
//        
//        return result;
//    }

	@Override
	public BigDecimal getScheduledHoursFromAttendance(List<String> unionIds, String startDate, String endDate) {
		if (CollectionUtils.isEmpty(unionIds)) {
			return BigDecimal.ZERO;
		}

		try {
			// 调用mapper方法查询考勤统计数据
			return baseMapper.getScheduledHoursFromAttendance(unionIds, startDate, endDate);
		} catch (Exception e) {
			log.error("从考勤统计表获取可登记工时失败", e);
			return BigDecimal.ZERO;
		}
	}

	@Override
	public BigDecimal getDailyRecordedHours(String projectKey, String userId, String workDate) {
		if (StringUtils.isBlank(projectKey) || StringUtils.isBlank(userId) || StringUtils.isBlank(workDate)) {
			return BigDecimal.ZERO;
		}

		try {
			QueryWrapper<HourRecord> queryWrapper = new QueryWrapper<>();
			queryWrapper
//            .eq("project_id", projectKey)
					.eq("user_id", userId).eq("work_period_start", workDate);

			// 查询所有符合条件的工时记录
			List<HourRecord> records = this.list(queryWrapper);

			// 计算总工时
			return records.stream().map(HourRecord::getHours).reduce(BigDecimal.ZERO, BigDecimal::add);

		} catch (Exception e) {
			log.error("获取当日已登记工时失败", e);
			return BigDecimal.ZERO;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchSave(HourRecordDTO hourRecordDTO) {
		// 获取当前登录用户
		FsUser loginUser = userInfoUtils.getLoginUser();
		if (loginUser == null) {
			throw new JeecgBootException("用户未登录");
		}
		// 获取工作空间标识
		String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
		if (StringUtils.isBlank(projectKey)) {
			throw new JeecgBootException("未提供有效的工作空间标识");
		}

		// 解析日期
		Date startDate;
		Date endDate;
		try {
			java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
			startDate = sdf.parse(hourRecordDTO.getStartDate());
			endDate = sdf.parse(hourRecordDTO.getEndDate());
		} catch (java.text.ParseException e) {
			throw new JeecgBootException("日期格式错误");
		}

		// 检查项目列表
		if (CollectionUtils.isEmpty(hourRecordDTO.getProjects())) {
			throw new JeecgBootException("项目列表不能为空");
		}

		// 获取用户unionId
		String unionId = fsUserService.getUnionIdByUserKey(loginUser.getUserKey());
		if (StringUtils.isBlank(unionId)) {
			throw new JeecgBootException("用户unionId获取失败");
		}

		// 考勤数据
		Map<String, SysUserAttendanceStat> statMap = new HashMap<String, SysUserAttendanceStat>();
		List<SysUserAttendanceStat> statList = this.sysUserAttendanceStatService.getByUserDate(unionId,
				hourRecordDTO.getStartDate(), hourRecordDTO.getEndDate());
		if (CollectionUtils.isNotEmpty(statList)) {
			for (SysUserAttendanceStat stat : statList) {
				String statDate = DateUtils.formatDate(stat.getStatDate());
				statMap.put(statDate, stat);
			}
		}

		// 实际工时项目
		Set<String> realProjectIdList = new HashSet<String>();
		// 计算总工时
		BigDecimal totalHours = BigDecimal.ZERO;
		Map<String, Map<String, BigDecimal>> projectDateHours = new HashMap<>();
		// 遍历项目，计算每个项目的工时
		for (HourRecordDTO.ProjectDTO projectDTO : hourRecordDTO.getProjects()) {
			if (CollectionUtils.isEmpty(projectDTO.getDetails())) {
				continue;
			}

			Map<String, BigDecimal> dateHours = new HashMap<>();
			for (HourRecordDTO.DatePercentageDTO datePercentage : projectDTO.getDetails()) {
				if (null == datePercentage.getPercentage() || datePercentage.getPercentage() <= 0) {
					continue;
				}

				// TODO: 非工作日校验逻辑
				// 从考勤系统获取实际考勤工时
				SysUserAttendanceStat attendanceStat = statMap
						.get(this.getWeekDate(hourRecordDTO.getWorkMonth(), datePercentage.getDate()));
				BigDecimal attendanceHours = BigDecimal.ZERO;
				if (attendanceStat != null) {
					// 实际出勤时长
					BigDecimal workHours = attendanceStat.getRealAttendHours();
					if (workHours != null)
						attendanceHours = attendanceHours.add(workHours);
//                    if (overWorkHours != null) attendanceHours = attendanceHours.add(new BigDecimal(overWorkHours));

					// 是否休息：0-否；1-是
					if (attendanceStat != null && attendanceStat.getRequiredAttendHours() != null
							&& BigDecimalUtils.greaterThan(attendanceStat.getRequiredAttendHours(), BigDecimal.ZERO)) {
						datePercentage.setIsRest(0);
					} else {
						datePercentage.setIsRest(1);
					}

				}
				if (attendanceHours == null) {
					attendanceHours = new BigDecimal(0);
				}

				// 如果考勤工时小于等于0，则跳过
				if (attendanceHours.compareTo(BigDecimal.ZERO) <= 0) {
					continue;
				}

				// 根据实际考勤工时和百分比计算实际工时
				BigDecimal actualHours = attendanceHours.multiply(BigDecimal.valueOf(datePercentage.getPercentage()))
						.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
				realProjectIdList.add(projectDTO.getProjectId());
				dateHours.put(datePercentage.getDate(), actualHours);
				totalHours = totalHours.add(actualHours);
			}

			if (!dateHours.isEmpty()) {
				projectDateHours.put(projectDTO.getProjectId(), dateHours);
			}
		}

		if (projectDateHours.isEmpty()) {
			throw new JeecgBootException("没有有效的工时记录");
		}

		HourRecord hourRecord;
		boolean isUpdate = StringUtils.isNotBlank(hourRecordDTO.getId());
		if (isUpdate) {
			// 修改：查找原记录并更新
			hourRecord = this.getById(hourRecordDTO.getId());
			if (hourRecord == null) {
				throw new JeecgBootException("未找到对应的工时记录");
			}
			// 只更新允许变更的字段
			hourRecord.setWorkMonth(hourRecordDTO.getWorkMonth());
			hourRecord.setWorkPeriodStart(startDate);
			hourRecord.setWorkPeriodEnd(endDate);
			hourRecord.setHours(totalHours);
			hourRecord.setStatus(hourRecordDTO.getStatus());
			hourRecord.setUpdateTime(new Date());
			hourRecord.setUpdateBy(loginUser.getUserKey());
			hourRecord.setDescription(hourRecordDTO.getDescription());
			this.updateById(hourRecord);
			// 删除原有明细
			LambdaQueryWrapper<HourRecordDetail> wrapper = new LambdaQueryWrapper<>();
			wrapper.eq(HourRecordDetail::getRecordId, hourRecord.getId());
			hourRecordDetailMapper.delete(wrapper);
		} else {
			// 新增
			hourRecord = new HourRecord();
			hourRecord.setUserId(loginUser.getUserKey());
			hourRecord.setUserName(loginUser.getName());
			hourRecord.setWorkMonth(hourRecordDTO.getWorkMonth());
			hourRecord.setWorkPeriodStart(startDate);
			hourRecord.setWorkPeriodEnd(endDate);
			hourRecord.setHours(totalHours);
			hourRecord.setStatus(hourRecordDTO.getStatus()); // 待提交
			hourRecord.setCreateTime(new Date());
			hourRecord.setUpdateTime(new Date());
			hourRecord.setCreateBy(loginUser.getUserKey());
			hourRecord.setUpdateBy(loginUser.getUserKey());
			hourRecord.setDescription(hourRecordDTO.getDescription());
			this.save(hourRecord);
		}

		// 1. 统计本周工作日各项目登记百分比（归一化）
		Map<String, BigDecimal> projectWeekPercentNorm = calculateProjectWeekPercent(hourRecordDTO.getProjects(),
				statMap, hourRecordDTO.getWorkMonth());
		Map<String, String> projectMap = new HashMap<String, String>();

		// 保存工时记录明细
		List<HourRecordDetail> details = new ArrayList<>();
		for (HourRecordDTO.ProjectDTO projectDTO : hourRecordDTO.getProjects()) {
			Map<String, BigDecimal> dateHours = projectDateHours.get(projectDTO.getProjectId());
			if (dateHours == null || dateHours.isEmpty()) {
				continue;
			}

			for (HourRecordDTO.DatePercentageDTO datePercentage : projectDTO.getDetails()) {
				if (datePercentage.getPercentage() <= 0) {
					continue;
				}

				BigDecimal actualHours = dateHours.get(datePercentage.getDate());
				if (actualHours == null || actualHours.compareTo(BigDecimal.ZERO) <= 0) {
					continue;
				}

				try {
					String workDate = getWeekDate(hourRecordDTO.getWorkMonth(), datePercentage.getDate());
					java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
					SysUserAttendanceStat stat = statMap.get(workDate);
					BigDecimal requiredAttendHours = stat != null ? stat.getRequiredAttendHours() : null;
					boolean isWorkday = requiredAttendHours != null && BigDecimalUtils.greaterThan(requiredAttendHours, BigDecimal.ZERO);

					HourRecordDetail detail = new HourRecordDetail();
					detail.setRecordId(hourRecord.getId());
					detail.setWorkDate(sdf.parse(workDate));
					detail.setProjectId(projectDTO.getProjectId());
					detail.setProjectName(projectDTO.getProjectName());
					detail.setDescription(projectDTO.getDescription());
					detail.setCreateTime(new Date());
					detail.setUpdateTime(new Date());
					detail.setIsRest(datePercentage.getIsRest());
					// detail.setApplyId(applyId); // 新增 apply_id 字段
					projectMap.put(projectDTO.getProjectId(), projectDTO.getProjectName());

					if (isWorkday) {
						detail.setPercentage(BigDecimal.valueOf(datePercentage.getPercentage()));
						detail.setActualHours(actualHours);
						details.add(detail);
					} else {
						// 休息日
//                        if(stat.getOverWorkHours()>0 && (null!=stat.getRealAttendHours() && stat.getRealAttendHours()>0)) {
//                        	BigDecimal percent = projectWeekPercentNorm.getOrDefault(projectDTO.getProjectId(), BigDecimal.ZERO);
//                        	detail.setPercentage(percent);
//                        	BigDecimal actualHours1 = BigDecimal.valueOf(stat.getRealAttendHours()).multiply(percent).setScale(2, BigDecimal.ROUND_HALF_UP);
//                            detail.setActualHours(actualHours1);
//                        	details.add(detail);
//                        }
					}
				} catch (java.text.ParseException e) {
					log.error("日期解析错误", e);
				}
			}
		}
		// 休息日--前端不会传,故只能通过考勤进行记录
		for (SysUserAttendanceStat stat : statList) {
			if (null == stat.getRequiredAttendHours() || BigDecimalUtils.greaterThan(stat.getRequiredAttendHours(), BigDecimal.ZERO)) {
				// 工作日
				continue;
			}
			// 休息日
			if ((null != stat.getOverWorkHours() && BigDecimalUtils.greaterThan(stat.getOverWorkHours(), BigDecimal.ZERO))
					&& (null != stat.getRealAttendHours() && BigDecimalUtils.greaterThan(stat.getRealAttendHours(), BigDecimal.ZERO))) {
				for (String projectId : projectWeekPercentNorm.keySet()) {
					HourRecordDetail detail = new HourRecordDetail();
					detail.setRecordId(hourRecord.getId());
					detail.setWorkDate(stat.getStatDate());
					detail.setProjectId(projectId);
					detail.setProjectName(projectMap.get(projectId));
					detail.setDescription(null);
					detail.setCreateTime(new Date());
					detail.setUpdateTime(new Date());
					detail.setIsRest(1);
					// detail.setApplyId(applyId); // 新增 apply_id 字段

					BigDecimal percent = projectWeekPercentNorm.getOrDefault(projectId, BigDecimal.ZERO);
					detail.setPercentage(percent.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
					BigDecimal actualHours1 = stat.getRealAttendHours().multiply(percent)
							.setScale(2, BigDecimal.ROUND_HALF_UP);
					detail.setActualHours(actualHours1);
					details.add(detail);
				}
			}
		}

		if (!details.isEmpty()) {
			hourRecordDetailMapper.insertBatch(details);
		}

		// 自动生成审批人清单（最小化改动）
		if (hourRecordDTO.getStatus() != null && hourRecordDTO.getStatus() == 1 && hourRecord.getId() != null) {

			// 1. 新增 hour_record_apply 批次
			String batchNo = hourRecordApplyService.generateBatchNo();
			HourRecordApply apply = new HourRecordApply();
			apply.setRecordId(hourRecord.getId());
			apply.setBatchNo(batchNo);
			apply.setApplicantId(loginUser.getUserKey());
			apply.setApplicantName(loginUser.getName());
			apply.setApplyTime(new Date());
			apply.setDescription(hourRecordDTO.getDescription());
			apply.setStatus(0);
			apply.setCreateTime(new Date());
			apply.setUpdateTime(new Date());
			hourRecordApplyService.save(apply);
			Long applyId = apply.getId();

			long count = hourRecordApproverService.lambdaQuery()
					.eq(org.jeecg.modules.hours.entity.HourRecordApprover::getRecordId, hourRecord.getId())
					.eq(org.jeecg.modules.hours.entity.HourRecordApprover::getApplyId, applyId).count();
			if (count == 0) {
//                List<String> projectIds = hourRecordDTO.getProjects().stream()
//                    .map(HourRecordDTO.ProjectDTO::getProjectId)
//                    .distinct()
//                    .collect(Collectors.toList());
				List<String> projectIds = new ArrayList<String>(realProjectIdList);
				List<org.jeecg.modules.hours.entity.HourRecordApprover> approverList = hourRecordApproverService
						.buildApproverList(hourRecord.getId(), projectIds, projectKey, applyId);
				hourRecordApproverService.saveBatch(approverList);

				List<String> userKeys = approverList.stream().map(item -> item.getApproverId())
						.collect(Collectors.toList());
				List<String> unionIds = fsUserService.getUnionIdsByUserKey(projectKey, userKeys);
				// 发送通知
				this.sendNotification(hourRecord, unionIds);
			}
		}
	}

	private void sendNotification(HourRecord record, List<String> unionIds) {
		try {
			// 从请求头获取项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			if (projectKey == null) {
				throw new JeecgBootException("未提供有效的项目Key");
			}

			// 获取飞书应用配置
			String appId = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.BIND_APP_ID);
			String appSecret = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.BIND_APP_SECRET);

			if (StringUtils.isBlank(appId) || StringUtils.isBlank(appSecret)) {
				log.warn("飞书应用配置不完整，无法发送通知");
				return;
			}

			// 构建消息内容
			String title, content, buttonText;

			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
			String formattedDate = dateFormat.format(record.getWorkPeriodStart()) + "-"
					+ dateFormat.format(record.getWorkPeriodEnd());

			// 审批通过
			title = "审批待办提醒";
			content = "【" + record.getUserName() + "】提交了工时数据待您审批。<br>" + "待审批周期：" + formattedDate + "";
			buttonText = "查看详情";

			// 发送消息
			log.info("审批待办提醒通知，用户: {}", unionIds);
			FeiShuMessageService.instance(appId, appSecret).sendMessage(unionIds, title, content, buttonText,
					buttonUrl);

		} catch (Exception e) {
			log.error("发送审批结果通知失败", e);
		}
	}

	/**
	 * 统计本周工作日各项目实际工时占比（归一化）
	 * 
	 * @param projects  项目列表
	 * @param statMap   日期->考勤
	 * @param workMonth 当前工作月
	 * @return 归一化百分比Map<projectId, percent>
	 */
	private Map<String, BigDecimal> calculateProjectWeekPercent(List<HourRecordDTO.ProjectDTO> projects,
			Map<String, SysUserAttendanceStat> statMap, String workMonth) {
		Map<String, BigDecimal> projectWorkHours = new HashMap<>();
		BigDecimal weekTotalHours = BigDecimal.ZERO;
		for (HourRecordDTO.ProjectDTO projectDTO : projects) {
			for (HourRecordDTO.DatePercentageDTO datePercentage : projectDTO.getDetails()) {
				String workDate = getWeekDate(workMonth, datePercentage.getDate());
				SysUserAttendanceStat stat = statMap.get(workDate);
				BigDecimal requiredAttendHours = stat != null ? stat.getRequiredAttendHours() : null;
				boolean isWorkday = requiredAttendHours != null && BigDecimalUtils.greaterThan(requiredAttendHours, BigDecimal.ZERO);
				if (isWorkday) {
					BigDecimal realAttendHours = stat != null ? stat.getRealAttendHours() : null;
					BigDecimal attendanceHours = (realAttendHours != null) ? realAttendHours : BigDecimal.ZERO;
					if (attendanceHours.compareTo(BigDecimal.ZERO) <= 0 || datePercentage.getPercentage() <= 0) {
						continue;
					}
					// 实际工时 = 出勤时长 * 百分比
					BigDecimal actualHours = attendanceHours
							.multiply(BigDecimal.valueOf(datePercentage.getPercentage()))
							.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
					projectWorkHours.merge(projectDTO.getProjectId(), actualHours, BigDecimal::add);
					weekTotalHours = weekTotalHours.add(actualHours);
				}
			}
		}
		// 归一化
		Map<String, BigDecimal> projectWeekPercentNorm = new HashMap<>();
		if (weekTotalHours.compareTo(BigDecimal.ZERO) > 0) {
			for (Map.Entry<String, BigDecimal> entry : projectWorkHours.entrySet()) {
				projectWeekPercentNorm.put(entry.getKey(),
						entry.getValue().divide(weekTotalHours, 4, BigDecimal.ROUND_HALF_UP));
			}
		}
		return projectWeekPercentNorm;
	}

	private String getWeekDate(String month, String date) {
		if (StringUtils.isEmpty(date)) {
			return null;
		}
		// 1. 2025-06-01 直接返回
		if (date.matches("\\d{4}-\\d{2}-\\d{2}")) {
			return date;
		}
		// 2. 06-01 或 06.01
		String dayPart = date.replace('.', '-');
		if (dayPart.matches("\\d{2}-\\d{2}")) {
			return month + "-" + dayPart.split("-")[1];
		}
		// 3. 06-01（无年份）
		if (dayPart.matches("\\d{2}-\\d{2}")) {
			return month + "-" + dayPart;
		}
		// 4. 2025.06.01
		if (date.matches("\\d{4}\\.\\d{2}\\.\\d{2}")) {
			return date.replace('.', '-');
		}
		// 5. 06.01（无年份）
		if (date.matches("\\d{2}\\.\\d{2}")) {
			return month + "-" + date.replace('.', '-');
		}
		// 6. 20250601
		if (date.matches("\\d{8}")) {
			return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8);
		}
		// 7. 0601
		if (date.matches("\\d{4}")) {
			return month + "-" + date.substring(0, 2) + "-" + date.substring(2, 4);
		}
		// fallback
		return month + "-" + dayPart;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchSubmit(List<String> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return;
		}

		// 获取当前登录用户
		FsUser loginUser = userInfoUtils.getLoginUser();
		if (loginUser == null) {
			throw new JeecgBootException("用户未登录");
		}

		// 查询工时记录
		List<HourRecord> records = this.listByIds(ids);
		if (CollectionUtils.isEmpty(records)) {
			return;
		}

		// 过滤出当前用户的待提交记录
		List<HourRecord> submitRecords = records.stream()
				.filter(record -> record.getUserId().equals(loginUser.getId()) && record.getStatus() == 0)
				.collect(Collectors.toList());

		if (CollectionUtils.isEmpty(submitRecords)) {
			return;
		}

		// 一次查出所有明细
		List<String> submitRecordIds = submitRecords.stream().map(HourRecord::getId).collect(Collectors.toList());
		List<HourRecordDetail> allDetails = hourRecordDetailService
				.list(new QueryWrapper<HourRecordDetail>().in("record_id", submitRecordIds));
		// 收集所有涉及的日期
		Set<String> allDates = allDetails.stream().filter(d -> d.getWorkDate() != null)
				.map(d -> DateUtils.formatDate(d.getWorkDate())) // yyyy-MM-dd
				.collect(Collectors.toSet());
		// 查出所有考勤（只查当前用户）
		String unionId = fsUserService.getUnionIdByUserKey(loginUser.getUserKey());
		String minDate = allDates.stream().min(String::compareTo).orElse(null);
		String maxDate = allDates.stream().max(String::compareTo).orElse(null);
		List<SysUserAttendanceStat> allStats = sysUserAttendanceStatService.getByUserDate(unionId, minDate, maxDate);
		Map<String, Boolean> dateIsWorkday = allStats.stream()
				.collect(Collectors.toMap(s -> DateUtils.formatDate(s.getStatDate()),
						s -> s.getRequiredAttendHours() != null && BigDecimalUtils.greaterThan(s.getRequiredAttendHours(), BigDecimal.ZERO)));
		// 校验每一天的工时百分比总和=100（只校验工作日）
		List<String> errorMsgList = new ArrayList<>();
		for (HourRecord record : submitRecords) {
			// 按日期分组统计百分比
			Map<String, BigDecimal> dateSumMap = new HashMap<>();
			for (HourRecordDetail detail : allDetails) {
				if (!record.getId().equals(detail.getRecordId()))
					continue;
				if (detail.getWorkDate() == null || detail.getPercentage() == null)
					continue;
				String dateStr = DateUtils.formatDate(detail.getWorkDate());
				if (!dateIsWorkday.getOrDefault(dateStr, false))
					continue; // 非工作日跳过
				dateSumMap.put(dateStr, dateSumMap.getOrDefault(dateStr, BigDecimal.ZERO).add(detail.getPercentage()));
			}
			for (Map.Entry<String, BigDecimal> entry : dateSumMap.entrySet()) {
				BigDecimal sum = entry.getValue();
				if (sum.compareTo(new BigDecimal("100")) != 0) {
					String mmdd = entry.getKey().substring(5).replace("-", ".");
					errorMsgList.add(
							String.format("日期 %s 的工时百分比总和为%s，必须等于100", mmdd, sum.stripTrailingZeros().toPlainString()));
				}
			}
		}
		if (!errorMsgList.isEmpty()) {
			throw new JeecgBootException(String.join("; ", errorMsgList));
		}

		// 更新状态为审批中
		for (HourRecord record : submitRecords) {
			record.setStatus(1); // 审批中
			record.setUpdateTime(new Date());
			record.setUpdateBy(loginUser.getUserKey());
		}

		this.updateBatchById(submitRecords);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteHourRecord(String id) {
		if (StringUtils.isBlank(id)) {
			return;
		}

		// 获取当前登录用户
		FsUser loginUser = userInfoUtils.getLoginUser();
		if (loginUser == null) {
			throw new JeecgBootException("用户未登录");
		}

		// 查询工时记录
		HourRecord record = this.getById(id);
		if (record == null) {
			return;
		}

		// 验证是否为当前用户的记录且状态为待提交
		if (!record.getUserId().equals(loginUser.getId()) || record.getStatus() != 0) {
			throw new JeecgBootException("只能删除自己的待提交记录");
		}

		// 删除工时记录明细
		LambdaQueryWrapper<HourRecordDetail> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(HourRecordDetail::getRecordId, id);
		hourRecordDetailMapper.delete(wrapper);

		// 删除工时记录
		this.removeById(id);
	}

	@Override
	public List<AvailableDateVO> getAvailableDates(String month, String startDate, String endDate, String userKey) {
		// 解析月份
		LocalDate monthStart = LocalDate.parse(month + "-01");
		LocalDate monthEnd = monthStart.withDayOfMonth(monthStart.lengthOfMonth());

		// 应用日期范围过滤
		if (StringUtils.isNotBlank(startDate)) {
			LocalDate filterStart = LocalDate.parse(startDate);
			monthStart = filterStart.isAfter(monthStart) ? filterStart : monthStart;
		}
		if (StringUtils.isNotBlank(endDate)) {
			LocalDate filterEnd = LocalDate.parse(endDate);
			monthEnd = filterEnd.isBefore(monthEnd) ? filterEnd : monthEnd;
		}
		// 获取工作空间标识
		String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
		if (StringUtils.isBlank(projectKey)) {
			throw new JeecgBootException("未提供有效的工作空间标识");
		}
		FsUser userInfo = this.fsUserService.getUserByProjectAndUserKey(projectKey, userKey);
		String unionId = userInfo.getUnionId();
		Map<String, SysUserAttendanceStat> statMap = new HashMap<String, SysUserAttendanceStat>();
		List<SysUserAttendanceStat> statList = this.sysUserAttendanceStatService.getByUserDate(unionId, startDate,
				endDate);
		if (CollectionUtils.isNotEmpty(statList)) {
			for (SysUserAttendanceStat stat : statList) {
				String statDate = DateUtils.formatDate(stat.getStatDate());
				statMap.put(statDate, stat);
			}
		}

		// 原有逻辑处理过滤后的日期范围
		List<AvailableDateVO> result = new ArrayList<>();
		for (LocalDate date = monthStart; !date.isAfter(monthEnd); date = date.plusDays(1)) {
//            if (isWorkday(date)) {
			AvailableDateVO vo = new AvailableDateVO();
			vo.setDate(date.format(DateTimeFormatter.ofPattern("MM.dd")));
			vo.setWeek(getChineseWeekDay(date.getDayOfWeek()));

			SysUserAttendanceStat stat = statMap.get(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			if (null != stat) {
				// 来源于考勤的【实际出勤时长】
				if (null != stat.getRealAttendHours()) {
					vo.setAttendanceHours(stat.getRealAttendHours());
				} else {
					vo.setAttendanceHours(new BigDecimal(0));
				}
				// 来源于考勤的【应出勤时长】
//                	vo.setIsWorkday(StringUtils.isNotEmpty(stat.getShift()) && !"-".equals(stat.getShift()));
				vo.setIsWorkday(BigDecimalUtils.greaterThan(stat.getRequiredAttendHours(), BigDecimal.ZERO));
			} else {
				vo.setAttendanceHours(new BigDecimal(0));
				vo.setIsWorkday(false);
			}
			result.add(vo);
//            }
		}
		return result;
	}

	@Override
	public List<WorkPeriodVO> getWorkPeriods(String month) {
		List<WorkPeriodDTO> workPeriodsDTO = new ArrayList<>();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");

		// 解析月份
		YearMonth yearMonth = YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyy-MM"));
		LocalDate firstDay = yearMonth.atDay(1);
		LocalDate lastDay = yearMonth.atEndOfMonth();

		// 生成周期选项
		// 1. 整月
//        WorkPeriodDTO monthPeriod = new WorkPeriodDTO();
//        monthPeriod.setLabel("整月 (" + firstDay.format(formatter) + " - " + lastDay.format(formatter) + ")");
//        monthPeriod.setValue("month");
//        monthPeriod.setStartDate(firstDay.format(formatter));
//        monthPeriod.setEndDate(lastDay.format(formatter));
//        workPeriodsDTO.add(monthPeriod);

		// 2. 每周（支持跨月截断）
		LocalDate weekStart = firstDay;
		int weekCount = 1;
		while (!weekStart.isAfter(lastDay)) {
			// 第一周特殊处理：如果weekStart是1号且不是周一，则第一周只包含1号
			// 处理跨月周：找到当月第一个自然周的起始日
			LocalDate weekStartInMonth = weekStart;
			if (weekStart.getMonthValue() != yearMonth.getMonthValue()) {
				weekStartInMonth = yearMonth.atDay(1);
			}

			// 计算本周的结束日（自然周的周日）
			LocalDate weekEnd = weekStartInMonth.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

			// 如果周日不在当月，则截断到月末
			if (weekEnd.getMonthValue() != yearMonth.getMonthValue()) {
				weekEnd = yearMonth.atEndOfMonth();
			}

			// 处理月末单日情况
			if (weekStartInMonth.equals(weekEnd)) {
				weekEnd = weekStartInMonth;
			}

			WorkPeriodDTO weekPeriod = new WorkPeriodDTO();
			weekPeriod.setLabel(weekStartInMonth.format(formatter) + "-" + weekEnd.format(formatter));
			weekPeriod.setValue("week-" + weekCount);
			weekPeriod.setStartDate(weekStartInMonth.format(formatter));
			weekPeriod.setEndDate(weekEnd.format(formatter));
			workPeriodsDTO.add(weekPeriod);

			// 下一周的开始日期
			weekStart = weekEnd.plusDays(1);
			weekCount++;

			// 确保下一周从周一开始
			weekStart = weekStart.with(TemporalAdjusters.nextOrSame(DayOfWeek.MONDAY));
		}

		// 转换为VO
		return workPeriodsDTO.stream().map(dto -> {
			WorkPeriodVO vo = new WorkPeriodVO();
			vo.setLabel(dto.getLabel());
			vo.setValue(dto.getValue());
			vo.setStartDate(dto.getStartDate());
			vo.setEndDate(dto.getEndDate());
			return vo;
		}).collect(Collectors.toList());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchSave(HourRecordBatchSaveVO batchSaveVO) {
		// 转换为DTO并调用已有的batchSave方法
		HourRecordDTO hourRecordDTO = new HourRecordDTO();
		hourRecordDTO.setWorkMonth(batchSaveVO.getWorkMonth());
		hourRecordDTO.setStartDate(batchSaveVO.getStartDate());
		hourRecordDTO.setEndDate(batchSaveVO.getEndDate());
		hourRecordDTO.setDescription(batchSaveVO.getDescription());
		hourRecordDTO.setId(batchSaveVO.getId());
		// 转换项目列表
		List<HourRecordDTO.ProjectDTO> projects = batchSaveVO.getProjects().stream().map(projectVO -> {
			HourRecordDTO.ProjectDTO projectDTO = new HourRecordDTO.ProjectDTO();
			projectDTO.setProjectId(projectVO.getProjectId());
			projectDTO.setProjectName(projectVO.getProjectName());
			projectDTO.setDescription(projectVO.getDescription());

			// 转换明细列表
			List<HourRecordDTO.DatePercentageDTO> details = projectVO.getDetails().stream()
					.filter(item -> null != item.getPercentage()).map(detailVO -> {
						HourRecordDTO.DatePercentageDTO detailDTO = new HourRecordDTO.DatePercentageDTO();
						detailDTO.setDate(detailVO.getDate());
						detailDTO.setPercentage(detailVO.getPercentage().doubleValue());
						return detailDTO;
					}).collect(Collectors.toList());

			projectDTO.setDetails(details);
			return projectDTO;
		}).collect(Collectors.toList());

		hourRecordDTO.setProjects(projects);

		// 调用已有的batchSave方法
		hourRecordDTO.setStatus(batchSaveVO.getStatus());
		this.batchSave(hourRecordDTO);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchSaveSubmit(HourRecordBatchSaveVO batchSaveVO) {
		// 直接复用batchSave逻辑，或如有特殊提交逻辑可在此扩展
		this.batchSave(batchSaveVO);
		// 如需额外处理提交状态，可在此补充
	}

	@Override
	public void batchSubmit(String ids) {
		// 获取工作空间标识
		String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
		if (StringUtils.isBlank(projectKey)) {
			throw new JeecgBootException("未提供有效的工作空间标识");
		}
		// 获取当前登录用户
		FsUser loginUser = userInfoUtils.getLoginUser();
		if (loginUser == null) {
			throw new JeecgBootException("用户未登录");
		}

		List<String> idList = Arrays.asList(ids.split(","));
		List<HourRecord> recordList = super.listByIds(idList);

		// 一次查出所有明细
//      List<String> submitRecordIds = submitRecords.stream().map(HourRecord::getId).collect(Collectors.toList());
		List<HourRecordDetail> allDetails = hourRecordDetailService
				.list(new QueryWrapper<HourRecordDetail>().in("record_id", idList));
		// 收集所有涉及的日期
		Set<String> allDates = allDetails.stream().filter(d -> d.getWorkDate() != null)
				.map(d -> DateUtils.formatDate(d.getWorkDate())) // yyyy-MM-dd
				.collect(Collectors.toSet());
		// 查出所有考勤（只查当前用户）
		String unionId = fsUserService.getUnionIdByUserKey(loginUser.getUserKey());
		String minDate = allDates.stream().min(String::compareTo).orElse(null);
		String maxDate = allDates.stream().max(String::compareTo).orElse(null);
		List<SysUserAttendanceStat> allStats = sysUserAttendanceStatService.getByUserDate(unionId, minDate, maxDate);
		Map<String, Boolean> dateIsWorkday = allStats.stream()
				.collect(Collectors.toMap(s -> DateUtils.formatDate(s.getStatDate()),
						s -> s.getRequiredAttendHours() != null && BigDecimalUtils.greaterThan(s.getRequiredAttendHours(), BigDecimal.ZERO)));
		// 校验每一天的工时百分比总和=100（只校验工作日）
		List<String> errorMsgList = new ArrayList<>();
		for (HourRecord record : recordList) {
			// 按日期分组统计百分比
			Map<String, BigDecimal> dateSumMap = new HashMap<>();
			for (HourRecordDetail detail : allDetails) {
				if (!record.getId().equals(detail.getRecordId()))
					continue;
				if (detail.getWorkDate() == null || detail.getPercentage() == null)
					continue;
				String dateStr = DateUtils.formatDate(detail.getWorkDate());
				if (!dateIsWorkday.getOrDefault(dateStr, false))
					continue; // 非工作日跳过
				dateSumMap.put(dateStr, dateSumMap.getOrDefault(dateStr, BigDecimal.ZERO).add(detail.getPercentage()));
			}
			for (Map.Entry<String, BigDecimal> entry : dateSumMap.entrySet()) {
				BigDecimal sum = entry.getValue();
				if (sum.compareTo(new BigDecimal("100")) != 0) {
					String mmdd = entry.getKey().substring(5).replace("-", ".");
					errorMsgList.add(
							String.format("日期 %s 的工时百分比总和为%s，必须等于100", mmdd, sum.stripTrailingZeros().toPlainString()));
				}
			}
		}
		if (!errorMsgList.isEmpty()) {
			throw new JeecgBootException(String.join("; ", errorMsgList));
		}

		// TODO 这里是否需要补充提交人?
		for (HourRecord hourRecord : recordList) {
			hourRecord.setStatus(1);
		}
		super.updateBatchById(recordList);

		// 自动生成审批人清单（最小化改动）
		for (HourRecord hourRecord : recordList) {
			// 1. 新增 hour_record_apply 批次
			String batchNo = hourRecordApplyService.generateBatchNo();
			HourRecordApply apply = new HourRecordApply();
			apply.setRecordId(hourRecord.getId());
			apply.setBatchNo(batchNo);
			apply.setApplicantId(loginUser.getUserKey());
			apply.setApplicantName(loginUser.getName());
			apply.setApplyTime(new Date());
			apply.setDescription(hourRecord.getDescription());
			apply.setStatus(0);// 待审批
			apply.setCreateTime(new Date());
			apply.setUpdateTime(new Date());
			hourRecordApplyService.save(apply);
			Long applyId = apply.getId();

			long count = hourRecordApproverService.lambdaQuery()
					.eq(org.jeecg.modules.hours.entity.HourRecordApprover::getRecordId, hourRecord.getId())
					.eq(org.jeecg.modules.hours.entity.HourRecordApprover::getApplyId, applyId).count();
			if (count == 0) {
				QueryWrapper<HourRecordDetail> queryWrapper = new QueryWrapper<HourRecordDetail>();
				queryWrapper.eq("record_id", hourRecord.getId());
				List<HourRecordDetail> projectList = hourRecordDetailMapper.selectList(queryWrapper);

				List<String> projectIds = projectList.stream().map(HourRecordDetail::getProjectId).distinct()
						.collect(Collectors.toList());
				List<org.jeecg.modules.hours.entity.HourRecordApprover> approverList = hourRecordApproverService
						.buildApproverList(hourRecord.getId(), projectIds, projectKey, applyId);
				hourRecordApproverService.saveBatch(approverList);

				List<String> userKeys = approverList.stream().map(item -> item.getApproverId())
						.collect(Collectors.toList());
				List<String> unionIds = fsUserService.getUnionIdsByUserKey(projectKey, userKeys);
				// 发送通知
				this.sendNotification(hourRecord, unionIds);
			}
		}

	}

	private String getChineseWeekDay(DayOfWeek dayOfWeek) {
		switch (dayOfWeek) {
		case MONDAY:
			return "周一";
		case TUESDAY:
			return "周二";
		case WEDNESDAY:
			return "周三";
		case THURSDAY:
			return "周四";
		case FRIDAY:
			return "周五";
		case SATURDAY:
			return "周六";
		case SUNDAY:
			return "周日";
		default:
			return "";
		}
	}

	@Override
	public IPage<HourRecord> queryPageList(Page<HourRecord> page, String startDate, String endDate, String deptIds,
			String status) {
		// 获取工作空间标识
		String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
		if (StringUtils.isBlank(projectKey)) {
			throw new JeecgBootException("未提供有效的工作空间标识");
		}
		// 使用UserInfoUtils获取当前用户ID
		FsUser user = userInfoUtils.getLoginUser();
		boolean isAdmin = fsUserRoleService.hasRole(user.getId(), "admin");
		List<String> deptIdList = new ArrayList<String>();
		if (StringUtils.isNotEmpty(deptIds)) {
			deptIdList.addAll(Arrays.asList(deptIds.split(",")));
		}

		return baseMapper.queryPageList(page, projectKey, startDate, endDate, user.getUserKey(), isAdmin, status,
				deptIdList);
	}

}
