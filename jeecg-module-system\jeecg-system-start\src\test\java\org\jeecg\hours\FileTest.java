package org.jeecg.hours;

import java.io.File;
import java.nio.charset.StandardCharsets;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.task.AttendanceStatJob;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.attendance.v1.model.ChildItem;
import com.lark.oapi.service.attendance.v1.model.Item;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UserStatsView;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 消息推送测试
 * @Author: lsq
 */
@Slf4j
//@ActiveProfiles("dev")
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class FileTest {

	@org.junit.jupiter.api.Test
	public void testname() throws Exception {

		File file = new File("D:\\workspace_sgm\\hh\\feishu\\doc\\未命名2.txt");

		// 检查文件是否存在
		if (!file.exists()) {
			System.out.println("文件不存在: " + file.getAbsolutePath());
			return;
		}

		// 获取文件大小（字节）
		long fileSizeBytes = file.length();

		// 输出文件信息
		System.out.println("=== 文件大小信息 ===");
		System.out.println("文件路径: " + file.getAbsolutePath());
		System.out.println("文件大小: " + fileSizeBytes + " 字节");

		// 可选：输出其他单位的大小
		System.out.println("文件大小: " + String.format("%.2f", fileSizeBytes / 1024.0) + " KB");
		System.out.println("文件大小: " + String.format("%.6f", fileSizeBytes / (1024.0 * 1024.0)) + " MB");

		// 断言验证（可选）
		assert fileSizeBytes > 0 : "文件大小应该大于0";

		System.out.println("文件大小获取成功！");
	}
	
}
