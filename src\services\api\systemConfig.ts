import { get, post, put, del } from '../../utils/request';

const API_PREFIX = '/hours/sys/config';

// API响应类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  result?: T;
  message?: string;
  code?: number;
}

/**
 * 获取配置列表
 */
export async function getConfigList(params: any) {
  return get(`${API_PREFIX}/list`, params);
}

/**
 * 根据ID获取配置
 */
export async function getConfigById(id: string) {
  return get(`${API_PREFIX}/queryById`, { id });
}

/**
 * 根据配置键获取配置
 */
export async function getConfigByKey(configKey: string) {
  return get(`${API_PREFIX}/getByKey/${configKey}`);
}

/**
 * 根据分组获取配置列表
 */
export async function getConfigsByGroup(configGroup: string) {
  return get(`${API_PREFIX}/getByGroup/${configGroup}`);
}

/**
 * 添加配置
 */
export async function addConfig(data: any) {
  return post(`${API_PREFIX}/add`, data);
}

/**
 * 更新配置
 */
export async function updateConfig(data: any) {
  return put(`${API_PREFIX}/edit`, data);
}

/**
 * 删除配置
 */
export async function deleteConfig(id: string) {
  return del(`${API_PREFIX}/delete?id=${id}`);
}

/**
 * 批量删除配置
 */
export async function batchDeleteConfig(ids: string) {
  return del(`${API_PREFIX}/deleteBatch?ids=${ids}`);
}

/**
 * 获取配置分组统计
 */
export async function getConfigGroupStats() {
  return get(`${API_PREFIX}/groupStats`);
}

/**
 * 获取工时基础设置
 */
export async function getWorkHourSettings() {
  return get('/hours/sys/config/work_hour/settings');
}

/**
 * 保存工时基础设置
 * @param data 工时设置数据
 */
export async function saveWorkHourSettings(data: Record<string, string>) {
  return post('/hours/sys/config/work_hour/save', data);
}

/**
 * 获取工时规则说明
 */
export async function getWorkHourRules() {
  return get('/hours/sys/config/rules/richtext');
}

/**
 * 保存工时规则说明
 * @param content 规则内容
 */
export async function saveWorkHourRules(content: string) {
  return post('/hours/sys/config/rules/richtext', { content });
}

/**
 * 获取工作内容范围
 */
export async function getWorkContentRange() {
  return get('/hours/sys/config/work_content/range');
}

/**
 * 保存工作内容范围
 * @param range 工作内容范围
 */
export async function saveWorkContentRange(range: string[]) {
  return post('/hours/sys/config/work_content/range/save', { range });
} 