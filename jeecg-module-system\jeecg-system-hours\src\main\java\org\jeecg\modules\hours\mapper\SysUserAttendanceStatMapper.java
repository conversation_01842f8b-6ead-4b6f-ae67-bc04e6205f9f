package org.jeecg.modules.hours.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.SysUserAttendanceStat;
import org.jeecg.modules.hours.vo.SysUserAttendanceStatVo;

import java.util.List;

/**
 * @Description: 用户考勤统计
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface SysUserAttendanceStatMapper extends BaseMapper<SysUserAttendanceStat> {

    IPage<SysUserAttendanceStatVo> getPendingRegisteredHoursList(Page<SysUserAttendanceStatVo> page,
                                                                 @Param("sysUserAttendanceStatVo") SysUserAttendanceStatVo sysUserAttendanceStatVo,
                                                                 @Param("employeeIdList") List<String> employeeIdList
    );
}