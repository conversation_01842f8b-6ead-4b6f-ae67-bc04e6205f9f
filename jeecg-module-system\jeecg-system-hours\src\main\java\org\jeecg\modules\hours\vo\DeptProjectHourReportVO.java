package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description: 部门项目工时统计报表VO
 * @Author: jeecg-boot
 * @Date: 2025-07-07
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DeptProjectHourReportVO", description = "部门项目工时统计报表VO")
public class DeptProjectHourReportVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private String departmentId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 部门总工时（行小计）
     */
    @ApiModelProperty(value = "部门总工时")
    private BigDecimal totalHours;

    /**
     * 项目工时分布 Map<projectId, hours>
     * key: project_${projectId}
     * value: 工时数
     */
    @ApiModelProperty(value = "项目工时分布")
    private Map<String, BigDecimal> projectHours;

    /**
     * 项目参与人数分布 Map<projectId, userCount>
     */
    @ApiModelProperty(value = "项目参与人数分布")
    private Map<String, Integer> projectUserCounts;

    // 以下字段用于查询结果映射，不是最终输出字段
    /**
     * 项目ID（查询结果字段）
     */
    @ApiModelProperty(value = "项目ID", hidden = true)
    private String projectId;

    /**
     * 单个项目的工时（查询结果字段）
     */
    @ApiModelProperty(value = "单个项目工时", hidden = true)
    private BigDecimal singleProjectHours;

    /**
     * 单个项目的参与人数（查询结果字段）
     */
    @ApiModelProperty(value = "单个项目参与人数", hidden = true)
    private Integer projectUserCount;
}