<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.plm.mapper.PlmProjectMemberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.jeecg.modules.plm.entity.PlmProjectMember">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="project_code" property="projectCode"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, project_code, user_id, user_name, create_time, update_time
    </sql>

    <!-- 根据项目ID查询项目成员 -->
    <select id="queryByProjectId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plm_project_member
        WHERE project_id = #{projectId}
        ORDER BY create_time ASC
    </select>
    
    <!-- 根据项目ID列表批量查询项目成员 -->
    <select id="queryByProjectIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plm_project_member
        WHERE project_id IN
        <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        ORDER BY project_id, create_time ASC
    </select>

    <!-- 根据用户ID查询参与的项目 -->
    <select id="queryByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plm_project_member
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
    </select>

    <!-- 批量新增项目成员 -->
    <insert id="batchInsert">
        INSERT INTO plm_project_member
        (id, project_id, project_code, user_id, user_name, create_time)
        VALUES
        <foreach collection="memberList" item="item" separator=",">
            (#{item.id}, #{item.projectId}, #{item.projectCode}, #{item.userId}, 
             #{item.userName}, now())
        </foreach>
    </insert>

    <!-- 根据项目ID删除项目成员 -->
    <delete id="deleteByProjectId">
        DELETE FROM plm_project_member
        WHERE project_id = #{projectId}
    </delete>
</mapper> 