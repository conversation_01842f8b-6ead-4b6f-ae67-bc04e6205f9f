import React, { useState, useEffect, useCallback } from 'react';
import { Button, Form, Input, InputNumber, Typography, Table, DatePicker, Select, SideSheet, Radio, Space, TextArea, Cascader, Notification } from '@douyinfe/semi-ui';
import { IconPlus, IconDelete, IconChevronDown, IconChevronUp } from '@douyinfe/semi-icons';
import { get, post, put } from '../../utils/request';
import WorkHourRulesModal from './components/WorkHourRulesModal';

const { Title, Text } = Typography;

// 工时明细记录接口
export interface WorkDetail {
  key: string | number;
  type: string;
  typeInfo: string;
  businessLevel1: string;
  businessLevel2: string;
  status: string;
  hours: number;
  description: string;
}

// 工作内容级联选择数据接口
export interface CascaderItem {
  label: string;
  value: string | number;
  isLeaf?: boolean;
  children?: CascaderItem[];
  loading?: boolean;
  [key: string]: any; // 添加索引签名以允许其他属性
}

// 从Semi UI的Cascader组件推导出的类型
type CascaderValue = string | number | (string | number)[];
type CascaderData = {
  label: React.ReactNode;
  value: string | number;
  isLeaf?: boolean;
  children?: CascaderData[];
  disabled?: boolean;
  [key: string]: any;
}

// API响应接口
interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
  success: boolean;
  result?: any; // 添加result字段，可能是数组或包含records的分页对象
}

// 工作项接口
interface WorkItemData {
  value: string | number;  // itemKey
  label: string;          // name
  status?: string;
  itemType?: string;
  [key: string]: any;
}

// 工作实例接口
interface InstanceData {
  value: string | number;  // instanceKey
  label: string;           // name
  status?: string;
  [key: string]: any;
}

// 节点接口
interface NodeData {
  value: string | number;  // nodeKey
  label: string;           // nodeName
  status?: string;
  workflowId?: string;
  [key: string]: any;
}

// 任务接口
interface TaskData {
  value: string | number;  // taskKey
  label: string;           // taskName
  status?: string;
  assignee?: string;
  assigneeName?: string;
  estimateHours?: number;
  [key: string]: any;
}

// 分页结果接口
interface PagedResult<T> {
  records: T[];
  total: number;
  pageNo: number;
  pageSize: number;
}

// 工时统计信息接口
export interface WorkItemStatistics {
  estimateHours?: number;
  estimateDays?: number;
  recordedHours?: number;
  recordedDays?: number;
}

// 当日工时信息接口
export interface DailyHourInfo {
  dailyScheduledHours: number;
  dailyRecordedHours: number;
  remainingHours: number;
}

// 工时登记表单属性接口
export interface HourRecordDetailProps {
  visible: boolean;
  onCancel: () => void;
  recordData: any; // 添加记录数据属性
  onSave?: (data: any) => void;
  onSubmit?: (data: any) => void;
  recordMode?: 'single' | 'batch';
  initialData?: {
    workDate?: Date;
    worker?: string;
    workDetails?: WorkDetail[];
    workContent?: (string | number)[];
    projectKey?: string;
    recordId?: string | number;
    projectName?: string;
    instanceName?: string;
    nodeName?: string;
    taskName?: string;
  };
  isEdit?: boolean;
}



// mock 工时明细数据
const initialWorkDetails: WorkDetail[] = [
  {
    key: '1',
    type: '机种',
    typeInfo: 'GEEK童画设计',
    businessLevel1: '立项',
    businessLevel2: '',
    status: '正常',
    hours: 5,
    description: ''
  }
];

const HourRecordDetail: React.FC<HourRecordDetailProps> = ({
  visible,
  onCancel,
  onSave,
  onSubmit,
  recordMode: initialRecordMode = 'single', // 设置默认值为'single'
  initialData = {},
  isEdit = false
}) => {
  // 状态定义
  const [workDate, setWorkDate] = useState<Date>(initialData.workDate || new Date());
  const [worker, setWorker] = useState<string>(initialData.worker || '');
  const [registeredHours, setRegisteredHours] = useState(3); // 已登记工时，实际应从API获取
  const [availableHours, setAvailableHours] = useState(5); // 可登记工时，实际应从API获取
  const [workDetails, setWorkDetails] = useState<WorkDetail[]>(
    initialData.workDetails || initialWorkDetails
  );
  const [recordMode, setRecordMode] = useState<'single' | 'batch'>(initialRecordMode); // 使用传入的初始记录模式
  const [expandedRow, setExpandedRow] = useState<string | number | null>(null); // 展开的行
  const [workContent, setWorkContent] = useState<(string | number)[]>(initialData.workContent || []); // 工作内容
  const [selectedLabels, setSelectedLabels] = useState<string[]>([]); // 选中项的标签文本
  const [workContentOptions, setWorkContentOptions] = useState<CascaderItem[]>([]); // 工作内容选项
  const [loading, setLoading] = useState<boolean>(false); // 添加loading状态
  const [statistics, setStatistics] = useState<WorkItemStatistics | null>(null); // 工时统计信息
  const [dailyInfo, setDailyInfo] = useState<DailyHourInfo | null>(null); // 当日工时信息
  const [loadingStats, setLoadingStats] = useState(false); // 统计信息加载状态
  const [loadingDaily, setLoadingDaily] = useState(false); // 当日信息加载状态
  const [workHourPerDay, setWorkHourPerDay] = useState<number>(8); // 每天标准工时

  // 项目Key - 通常应该从上下文、全局状态或props获取
  const [projectKey, setProjectKey] = useState<string>(initialData.projectKey || '');
  const [recordId] = useState<string | number | undefined>(initialData.recordId); // 记录ID，用于编辑时

  // 工时登记规则说明模态窗状态
  const [rulesModalVisible, setRulesModalVisible] = useState(false);

  // 组件加载时输出初始化信息
  useEffect(() => {
    console.log('======= 组件初始化信息 =======');
    console.log('isEdit:', isEdit);
    console.log('initialData.recordId:', initialData.recordId);
    console.log('recordId 状态:', recordId);
    console.log('===============================');
  }, []);

  // 工作内容变化时更新标签
  useEffect(() => {
    if (workContent && Array.isArray(workContent) && workContent.length > 0) {
      console.log("工作内容发生变化, 新值:", workContent);
      
      // 查找选项的标签路径
      const findLabelsPath = (values: (string | number)[]): string[] => {
        const result: string[] = [];
        let currentOptions = workContentOptions;
        
        for (let i = 0; i < values.length; i++) {
          const currentValue = values[i];
          const foundOption = currentOptions?.find(opt => opt.value === currentValue);
          
          if (foundOption) {
            result.push(foundOption.label || '未知');
            currentOptions = foundOption.children || [];
          } else {
            break;
          }
        }
        
        return result;
      };
      
      // 更新标签
      const labels = findLabelsPath(workContent);
      if (labels.length > 0) {
        console.log("找到标签路径:", labels);
        setSelectedLabels(labels);
      }
    } else {
      setSelectedLabels([]);
    }
  }, [workContent, workContentOptions]);
  
  // 初始化时获取工作内容的标签名称
  const getWorkContentLabels = useCallback(async () => {
    // 如果不是编辑模式或没有当前记录，直接返回
    if (!isEdit || !initialData || !initialData.workContent || initialData.workContent.length === 0) {
      return;
    }
    
    try {
      console.log('尝试从记录中获取标签信息');
      
      // 从初始数据直接获取各级名称，构建标签数组
      const labels: string[] = [];
      
      // 收集所有非空的标签数据
      if (initialData.workItemName) labels.push(initialData.workItemName);
      if (initialData.instanceName) labels.push(initialData.instanceName);
      if (initialData.nodeName) labels.push(initialData.nodeName);
      if (initialData.taskName) labels.push(initialData.taskName);
      
      console.log('从记录中获取到标签:', labels);
      console.log('工作内容ID:', initialData.workContent);
      
      // 如果获取到有效标签，设置标签
      if (labels.length > 0) {
        setSelectedLabels(labels);
      }
    } catch (error) {
      console.error('从记录中获取标签信息失败:', error);
    }
  }, [isEdit, initialData]);
  
  // 在组件挂载时尝试获取标签
  useEffect(() => {
    getWorkContentLabels();
  }, [getWorkContentLabels]);

  // 初始化工作内容数据
  useEffect(() => {
    // 仅在编辑模式下且有工作内容数据时初始化
    if (isEdit && initialData.workContent && initialData.workContent.length > 0) {
      console.log('编辑模式，初始化工作内容:', initialData.workContent);
      
      // 设置工作内容值
      setWorkContent(initialData.workContent);
      
      // 立即设置标签数据，确保页面首次渲染就有内容
      const labels: string[] = [];
      if (initialData.workItemName) labels.push(initialData.workItemName);
      if (initialData.instanceName) labels.push(initialData.instanceName);
      if (initialData.nodeName) labels.push(initialData.nodeName);
      if (initialData.taskName) labels.push(initialData.taskName);
      
      console.log('立即设置工作内容标签:', labels);
      if (labels.length > 0) {
        setSelectedLabels(labels);
      }
      
      // 额外调用一次 getWorkContentLabels 以确保数据完整
      setTimeout(() => {
        getWorkContentLabels();
      }, 0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEdit, JSON.stringify(initialData.workContent)]);

  // 初始化加载第1级工作项 - 修改为条件加载
  useEffect(() => {
    // 如果是编辑模式且有工作内容，由 getWorkContentLabels 处理
    if (isEdit && initialData.workContent && initialData.workContent.length > 0) {
      console.log("编辑模式，跳过常规的第一级数据加载");
      return;
    }
    
    // 否则正常加载第一级数据
    console.log("初始化组件，开始加载第一级数据");
    fetchWorkItems();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 调试用：监听工作内容选项变化
  useEffect(() => {
    console.log("工作内容选项已更新:", workContentOptions);
  }, [workContentOptions]);

  // 修改初始化函数，仅用于非编辑模式的级联加载
  const initSelectedPath = async (selectedPath: (string | number)[]) => {
    // 仅在非编辑模式下执行级联加载
    if (isEdit) {
      console.log('编辑模式，跳过级联加载');
      return;
    }
    
    console.log('初始化选中路径:', selectedPath);
    if (!selectedPath || selectedPath.length === 0) return;
    
    setLoading(true);
    try {
      // 依次加载各级数据
      let currentOptions = [...workContentOptions];
      // 用于收集路径标签
      const pathLabels: string[] = [];
      
      // 加载第一级数据（如果还没有加载）
      if (currentOptions.length === 0) {
        await fetchWorkItems();
        currentOptions = [...workContentOptions];
      }
      
      // 依次加载每一级数据
      for (let i = 0; i < selectedPath.length; i++) {
        const currentValue = selectedPath[i];
        console.log(`加载第${i+1}级数据，当前值:`, currentValue);
        
        // 找到当前级别中匹配的选项
        const targetOption = findOptionByValue(currentOptions, currentValue);
        if (!targetOption) {
          console.error(`未找到值为${currentValue}的选项`);
          // 使用占位符标签
          pathLabels.push(`级别${i+1}`);
          break;
        }
        
        // 收集标签
        pathLabels.push(targetOption.label as string);
        console.log(`找到第${i+1}级标签:`, targetOption.label);
        
        // 如果是最后一级，或已经有子数据，跳过加载
        if (i === selectedPath.length - 1 || 
            (targetOption.children && targetOption.children.length > 0)) {
          if (i < selectedPath.length - 1) {
            currentOptions = targetOption.children || [];
          }
          continue;
        }
        
        // 加载下一级数据
        let apiUrl = '';
        if (i === 0) { // 加载实例
          apiUrl = `/hours/workItemCascade/instances?workItemKey=${currentValue}&pageNo=1&pageSize=50`;
        } else if (i === 1) { // 加载节点
          apiUrl = `/hours/workItemCascade/nodes?instanceKey=${currentValue}`;
        } else if (i === 2) { // 加载任务
          apiUrl = `/hours/workItemCascade/tasks?nodeKey=${currentValue}&pageNo=1&pageSize=50`;
        }
        
        if (apiUrl) {
          console.log(`请求API: ${apiUrl}`);
          const response = await get<ApiResponse<any>>(apiUrl);
          
          // 解析返回的数据
          let childrenData: any[] = [];
          if (response?.success && response.result) {
            if (response.result.records && Array.isArray(response.result.records)) {
              childrenData = response.result.records;
            } else if (Array.isArray(response.result)) {
              childrenData = response.result;
            }
          }
          
          // 格式化子节点数据
          const formattedChildren = childrenData.map(item => ({
            label: item.label || '未命名',
            value: item.value || `unknown_${Date.now()}_${Math.random()}`,
            isLeaf: !!item.isLeaf,
            status: item.status || '0',
            estimateHours: item.estimateHours || 0,
            assignee: item.assignee || '',
            assigneeName: item.assigneeName || ''
          }));
          
          // 更新当前选项的子数据
          targetOption.children = formattedChildren;
          
          // 更新级联数据
          setWorkContentOptions(prev => updateTreeData(prev, currentValue, formattedChildren));
          
          // 更新循环中使用的选项列表
          currentOptions = formattedChildren;
        }
      }
      
      // 如果通过常规方式获取到了标签，更新标签
      if (pathLabels.length > 0) {
        console.log('通过级联加载获取到标签:', pathLabels);
        setSelectedLabels(pathLabels);
      }
      
      // 保持工作内容值不变
      setWorkContent(selectedPath);
    } catch (error) {
      console.error('初始化选中路径失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取工作项（第一级）
  const fetchWorkItems = async () => {
    console.log("正在加载工作项数据...");
    setLoading(true);
    try {
      // 不再需要传入projectKey参数，它会从请求头中获取
      const response = await get<ApiResponse<any>>('/hours/workItemCascade/workItems');
      console.log("工作项接口返回:", response);
      
      // 检查响应状态
      if (response && response.success) {
        let data;
        
        try {
          // 检查是否为分页结构，如果是则获取records数组
          if (response.result && response.result.records) {
            // 处理分页结构响应
            data = response.result.records;
            console.log("工作项数据(分页格式)加载成功:", data);
          } else if (Array.isArray(response.result)) {
            // 直接是数组
            data = response.result;
            console.log("工作项数据(数组格式)加载成功:", data);
          } else {
            console.error('加载工作项失败: API返回数据格式不符合预期', response.result);
            setWorkContentOptions([]);
            setLoading(false);
            return;
          }
          
          // 验证数据是否有效
          if (!Array.isArray(data)) {
            console.error('加载工作项失败: 返回数据不是数组', data);
            setWorkContentOptions([]);
            setLoading(false);
            return;
          }
          
          // 数据转换为Cascader需要的格式
          const formattedData = data.map((item: WorkItemData) => {
            // 使用后端返回的isLeaf字段
            return {
              label: item.label || '未命名项目',
              value: item.value || `unknown_${Date.now()}_${Math.random()}`,
              isLeaf: !!item.isLeaf, // 确保转换为布尔值
              status: item.status || '0'
            };
          });
          
          console.log("格式化后的工作项数据:", formattedData);
          setWorkContentOptions(formattedData);
        } catch (formattingError) {
          console.error('格式化工作项数据时出错:', formattingError);
          setWorkContentOptions([]);
        }
      } else {
        console.error('加载工作项失败: API返回错误', response);
        setWorkContentOptions([]);
      }
    } catch (error) {
      console.error('加载工作项失败:', error);
      // 加载失败时，使用模拟数据以便测试UI交互
      console.log("使用模拟数据作为回退");
      const mockData = [
        { label: '项目A', value: 'project_a', isLeaf: false },
        { label: '项目B', value: 'project_b', isLeaf: false },
        { label: '需求', value: 'requirement', isLeaf: false },
        { label: 'Bug', value: 'bug', isLeaf: false }
      ];
      setWorkContentOptions(mockData);
    } finally {
      setLoading(false);
    }
  };

  // 更新级联数据的帮助函数
  const updateTreeData = (list: CascaderItem[], value: string | number, children: CascaderItem[]): CascaderItem[] => {
    return list.map(node => {
      if (node.value === value) {
        return { ...node, children };
      }
      if (node.children) {
        return { ...node, children: updateTreeData(node.children, value, children) };
      }
      return node;
    });
  };

  // 级联加载数据处理
  const onLoadData = (selectedOpt: CascaderItem[]) => {
    // 防护措施
    if (!selectedOpt || !Array.isArray(selectedOpt) || selectedOpt.length === 0) {
      console.error("无效的选项数组");
      return Promise.resolve();
    }

    // 获取当前选中的选项
    const targetOpt = selectedOpt[selectedOpt.length - 1];
    
    // 防护措施
    if (!targetOpt) {
      console.error("无法获取目标选项");
      return Promise.resolve();
    }
    
    console.log(`开始加载级联数据: 级别=${selectedOpt.length}, 选中项=`, targetOpt);
    
    // 如果已经有children，不需要再加载
    if (targetOpt.children && targetOpt.children.length > 0) {
      console.log("该节点已有子项，无需加载");
      return Promise.resolve();
    }
    
    // 如果是叶子节点，不需要加载
    if (targetOpt.isLeaf) {
      console.log("该节点是叶子节点，无需加载");
      return Promise.resolve();
    }
    
    // 获取当前级别和父级ID
    const level = selectedOpt.length;
    const parentId = targetOpt.value;
    
    // 构建当前级别API URL
    let apiUrl = '';
    switch (level) {
      case 1: // 第一级选择后，加载实例
        apiUrl = `/hours/workItemCascade/instances?workItemKey=${parentId}&pageNo=1&pageSize=50`;
        break;
      case 2: // 第二级选择后，加载节点
        apiUrl = `/hours/workItemCascade/nodes?instanceKey=${parentId}`;
        break;
      case 3: // 第三级选择后，加载任务
        apiUrl = `/hours/workItemCascade/tasks?nodeKey=${parentId}&pageNo=1&pageSize=50`;
        break;
      default:
        // 已达到最大层级
        return Promise.resolve();
    }
    
    // 设置加载状态
    return new Promise<void>((resolve) => {
      console.log(`请求API: ${apiUrl}`);
      
      get<ApiResponse<any>>(apiUrl)
        .then(response => {
          console.log(`API返回数据:`, response);
          
          // 解析返回的数据
          let childrenData: any[] = [];
          if (response?.success && response.result) {
            if (response.result.records && Array.isArray(response.result.records)) {
              // 分页数据结构
              childrenData = response.result.records;
            } else if (Array.isArray(response.result)) {
              // 数组结构
              childrenData = response.result;
            }
          }
          
          console.log(`加载到${level}级数据:`, childrenData);
          
          // 处理数据为空的情况
          if (!childrenData || !Array.isArray(childrenData) || childrenData.length === 0) {
            console.log(`${level}级数据为空，设置当前节点为叶子节点`);
            
            // 将当前节点标记为叶子节点
            targetOpt.isLeaf = true;
            // 更新级联数据，设置为叶子节点后不会显示箭头
            setWorkContentOptions([...workContentOptions]);
            
            resolve();
            return;
          }
          
          // 格式化子节点数据
          const formattedChildren = childrenData.map(item => ({
            label: item.label || '未命名',
            value: item.value || `unknown_${Date.now()}_${Math.random()}`,
            isLeaf: !!item.isLeaf, // 使用后端返回的isLeaf字段，确保转换为布尔值
            status: item.status || '0',
            estimateHours: item.estimateHours || 0,
            assignee: item.assignee || '',
            assigneeName: item.assigneeName || ''
          }));
          
          // 更新级联数据
          setWorkContentOptions(prev => updateTreeData(prev, parentId, formattedChildren));
          
          resolve();
        })
        .catch(error => {
          console.error('加载数据失败:', error);
          
          // 将当前节点标记为叶子节点，不再显示箭头
          targetOpt.isLeaf = true;
          setWorkContentOptions([...workContentOptions]);
          resolve();
        });
    });
  };

  // 工时明细表格列定义
  const detailColumns = [
    { 
      title: '工作内容', 
      dataIndex: 'typeInfo', 
      width: 150,
      render: (value: string, record: WorkDetail, index: number) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Space>
            {expandedRow === record.key ? (
              <IconChevronUp 
                style={{ cursor: 'pointer' }} 
                onClick={() => setExpandedRow(null)} 
              />
            ) : (
              <IconChevronDown 
                style={{ cursor: 'pointer' }} 
                onClick={() => setExpandedRow(record.key)} 
              />
            )}
            {value}
          </Space>
        </div>
      )
    },
    { title: '成型阶段', dataIndex: 'businessLevel1', width: 100 },
    { title: '预估工时', dataIndex: 'status', width: 80, render: () => '-' },
    { title: '已登记工时', dataIndex: 'hours', width: 100, render: (hours: number) => hours || 0 },
    { title: '剩余工时', dataIndex: 'status', width: 80, render: () => '-' },
    { 
      title: '本次登记工时', 
      dataIndex: 'hours', 
      width: 120,
      render: (hours: number, record: WorkDetail, index: number) => (
        <InputNumber 
          style={{ width: '80px' }} 
          min={0} 
          max={24} 
          defaultValue={hours}
          onChange={(value) => {
            const newDetails = [...workDetails];
            newDetails[index].hours = value as number;
            setWorkDetails(newDetails);
          }}
        />
      )
    },
    { 
      title: '工作描述', 
      dataIndex: 'description', 
      width: 150,
      render: (description: string, record: WorkDetail, index: number) => (
        <Input 
          style={{ width: '100%' }} 
          placeholder="请输入"
          defaultValue={description}
          onChange={(e) => {
            const newDetails = [...workDetails];
            newDetails[index].description = e;
            setWorkDetails(newDetails);
          }}
        />
      )
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 80,
      render: (_: any, record: WorkDetail, index: number) => (
        <Button
          type="danger"
          theme="borderless"
          size="small"
          icon={<IconDelete />}
          onClick={() => handleDeleteDetail(record.key)}
        >
          明细
        </Button>
      )
    }
  ];

  // 处理添加工时明细
  const handleAddDetail = () => {
    const newItem: WorkDetail = {
      key: Date.now(),
      type: '机种',
      typeInfo: 'GEEK童画设计',
      businessLevel1: '立项',
      businessLevel2: '',
      status: '正常',
      hours: 0,
      description: ''
    };
    setWorkDetails([...workDetails, newItem]);
  };

  // 处理删除工时明细
  const handleDeleteDetail = (key: string | number) => {
    setWorkDetails(workDetails.filter(item => item.key !== key));
  };

  // 收集表单数据
  const collectFormData = () => {
    return {
      workDate,
      worker,
      registeredHours,
      availableHours,
      workDetails,
      recordMode,
      workContent,
      projectKey,
      recordId
    };
  };

  // 调用API保存工时记录
  const saveToServer = async (data: any, status: number) => {
    try {
      // 检查必填字段
      if (!workContent || workContent.length === 0) {
        console.error('请选择工作内容');
        Notification.error({
          title: '操作失败',
          content: '请选择工作内容',
          duration: 3
        });
        return false;
      }
      
      if (!workDate) {
        console.error('请选择工作日期');
        Notification.error({
          title: '操作失败',
          content: '请选择工作日期',
          duration: 3
        });
        return false;
      }
      
      if (!worker) {
        console.error('请选择工作人员');
        Notification.error({
          title: '操作失败',
          content: '请选择工作人员',
          duration: 3
        });
        return false;
      }
      
      // 获取工时和描述信息
      const hourValue = recordMode === 'single' 
        ? data.workDetails[0]?.hours || 0
        : data.workDetails.reduce((sum: number, detail: WorkDetail) => sum + detail.hours, 0);
        
      const description = recordMode === 'single'
        ? data.workDetails[0]?.description || ''
        : data.workDetails.map((detail: WorkDetail) => `${detail.typeInfo}: ${detail.description}`).join('; ');
      
      // 添加调试日志，输出记录ID和编辑模式状态
      console.log('===== 调试信息 =====');
      console.log('isEdit:', isEdit);
      console.log('recordId:', recordId);
      console.log('recordId类型:', typeof recordId);
      console.log('initialData:', initialData);
      console.log('initialData.recordId:', initialData.recordId);
      console.log('initialData.recordId类型:', typeof initialData.recordId);
      console.log('===================');
      
      // 构建API请求参数 - 修改字段名称以匹配后端接口
      const requestData = {
        id: isEdit ? initialData.recordId : undefined, // 编辑模式下从initialData中获取ID
        workDate: data.workDate,
        // 不需要设置userId，后端会自动获取当前登录用户
        projectId: workContent.length > 0 ? workContent[0] : '',  // 使用project_id替代projectKey
        projectName: selectedLabels[0] || '',
        instanceId: workContent.length > 1 ? workContent[1] : '', // 使用instance_id替代instanceKey
        instanceName: selectedLabels.length > 1 ? selectedLabels[1] : '',
        nodeId: workContent.length > 2 ? workContent[2] : '',     // 使用node_id替代nodeKey
        nodeName: selectedLabels.length > 2 ? selectedLabels[2] : '',
        taskId: workContent.length > 3 ? workContent[3] : '',     // 使用task_id替代taskKey
        taskName: selectedLabels.length > 3 ? selectedLabels[3] : '',
        hours: hourValue,
        description: description,
        status: status // 直接设置状态: 0-待提交, 1-审批中, 2-已完成, 3-审批驳回
      };

      console.log('正在保存工时记录:', requestData);
      console.log('请求数据中的ID值:', requestData.id);
      
      // 根据是否是编辑模式决定使用哪个接口和请求方法
      let url, response;
      
      if (isEdit) {
        // 编辑模式，使用PUT方法调用edit接口
        url = '/hours/hourRecord/edit';
        console.log(`使用PUT方法调用编辑接口: ${url}`);
        response = await put<ApiResponse>(url, requestData);
      } else {
        // 新增模式，使用POST方法调用add接口
        url = '/hours/hourRecord/add';
        console.log(`使用POST方法调用新增接口: ${url}`);
        response = await post<ApiResponse>(url, requestData);
      }
      
      if (response && response.success === true) {
        console.log('保存成功:', response);
        
        // 根据状态显示不同的成功提示
        if (status === 0) {
          Notification.success({
            title: isEdit ? '编辑成功' : '保存成功',
            content: '工时记录已保存为草稿状态',
            duration: 3
          });
        } else if (status === 1) {
          Notification.success({
            title: '提交成功',
            content: '工时记录已提交审批',
            duration: 3
          });
        }
        
        return true;
      } else {
        console.error('保存失败:', response && response.message ? response.message : '未知错误');
        Notification.error({
          title: '操作失败',
          content: response && response.message ? response.message : '保存工时记录失败，请稍后重试',
          duration: 3
        });
        return false;
      }
    } catch (error) {
      console.error('API调用出错:', error);
      Notification.error({
        title: '操作失败',
        content: '系统出现异常，请稍后重试',
        duration: 3
      });
      return false;
    }
  };

  // 处理保存（标记为待提交）
  const handleSave = async () => {
    try {
      const data = collectFormData();
      await saveToServer(data, 0);
      if (onSave) {
        onSave(data);
      }
    } catch (error) {
      console.error('保存工时记录失败:', error);
    }
  };

  // 处理提交（标记为审核中）
  const handleSubmit = async () => {
    try {
      const data = collectFormData();
      await saveToServer(data, 1);
      if (onSubmit) {
        onSubmit(data);
      }
    } catch (error) {
      console.error('提交工时记录失败:', error);
    }
  };

  // 处理日期选择变化
  const handleDateChange = (value: any) => {
    if (value instanceof Date) {
      setWorkDate(value);
    }
  };

  // 处理工作人员选择变化
  const handleWorkerChange = (value: string) => {
    setWorker(value);
  };

  // 处理工作内容选择变化
  const handleWorkContentChange = (value: (string | number)[]) => {
    console.log("工作内容选择变化:", value);
    
    // 安全地保存选中的值
    setWorkContent(Array.isArray(value) ? value : []);
    
    // 输出调试信息，方便排查值的位置
    if (Array.isArray(value) && value.length > 0) {
      console.log("级联选择器值详情:");
      console.log("project_id (0级):", value[0]);
      if (value.length > 1) console.log("instance_id (1级):", value[1]);
      if (value.length > 2) console.log("node_id (2级):", value[2]);
      if (value.length > 3) console.log("task_id (3级):", value[3]);
    }
  };

  // 调试用：处理下拉框可见性变化
  const handleDropdownVisibleChange = (visible: boolean) => {
    console.log("下拉框可见性变化:", visible);
    if (visible && workContentOptions.length === 0) {
      console.log("下拉框打开且没有选项，重新加载第一级数据");
      fetchWorkItems();
    }
  };

  // 格式化显示工作内容
  const formatWorkContent = (labels: string[]): React.ReactNode => {
    // 始终优先使用selectedLabels（来自编辑记录的标签）
    const displayLabels = selectedLabels.length > 0 ? selectedLabels : (
      Array.isArray(labels) ? labels : []
    );
    
    // 防御性检查
    if (!displayLabels || displayLabels.length === 0) {
      // 如果没有有效的标签，但存在workContent，则显示loading提示
      if (workContent && workContent.length > 0) {
        // 尝试设置默认格式化文本
        if (isEdit && initialData) {
          const tempLabels: string[] = [];
          if (initialData.workItemName) tempLabels.push(initialData.workItemName);
          if (initialData.instanceName) tempLabels.push(initialData.instanceName);
          if (initialData.nodeName) tempLabels.push(initialData.nodeName);
          if (initialData.taskName) tempLabels.push(initialData.taskName);
          
          if (tempLabels.length > 0) {
            // 立即返回格式化文本
            return (
              <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap', overflow: 'hidden' }}>
                {tempLabels.map((label, index) => (
                  <React.Fragment key={index}>
                    {index > 0 && <span style={{ margin: '0 4px', color: '#ccc' }}>/</span>}
                    <span>{label}</span>
                  </React.Fragment>
                ))}
              </div>
            );
          }
        }
        return <Text type="tertiary">正在加载内容...</Text>;
      }
      return '';
    }
    
    // 过滤掉非法值
    const filteredLabels = displayLabels.filter(label => 
      typeof label === 'string' && label.trim() !== ''
    );
    
    if (filteredLabels.length === 0) {
      // 如果过滤后没有有效标签，但有工作内容，则显示loading
      if (workContent && workContent.length > 0) {
        return <Text type="tertiary">正在加载内容...</Text>;
      }
      return '';
    }
    
    // 调试日志，帮助排查显示问题
    console.log('工作内容最终显示标签:', filteredLabels);
    console.log('当前工作内容ID:', workContent);
    
    // 返回格式化的标签，带有正确的分隔符
    return (
      <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap', overflow: 'hidden' }}>
        {filteredLabels.map((label, index) => (
          <React.Fragment key={index}>
            {index > 0 && <span style={{ margin: '0 4px', color: '#ccc' }}>/</span>}
            <span>{label}</span>
          </React.Fragment>
        ))}
      </div>
    );
  };

  // 处理recordMode变化的副作用
  useEffect(() => {
    console.log(`记录模式已切换为: ${recordMode}`);
  }, [recordMode]);

  // 根据值查找级联选项
  const findOptionByValue = (options: CascaderItem[], value: string | number): CascaderItem | null => {
    if (!options || !Array.isArray(options)) return null;
    
    for (const option of options) {
      if (option.value === value) {
        return option;
      }
      
      if (option.children && option.children.length > 0) {
        const found = findOptionByValue(option.children, value);
        if (found) return found;
      }
    }
    
    return null;
  };

  // 获取工时统计信息
  const fetchStatistics = async (workItemKey?: string, instanceKey?: string, nodeKey?: string, taskKey?: string) => {
    if (!workItemKey) return;

    setLoadingStats(true);
    try {
      const response = await get<any>('/hours/workItemCascade/statistics', {
        workItemKey,
        ...(instanceKey && { instanceKey }),
        ...(nodeKey && { nodeKey }),
        ...(taskKey && { taskKey })
      });

      if (response.success) {
        setStatistics(response.result);
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    } finally {
      setLoadingStats(false);
    }
  };

  // 获取当日工时信息
  const fetchDailyInfo = async (workDate: Date) => {
    setLoadingDaily(true);
    try {
      const dateStr = workDate.toISOString().split('T')[0]; // 格式化为YYYY-MM-DD
      console.log('获取当日工时信息，日期:', dateStr);
      const response = await get<any>('/hours/hourRecord/dailyInfo', {
        workDate: dateStr
      });

      console.log('当日工时信息响应:', response);
      if (response.success) {
        setDailyInfo(response.result);
        console.log('设置当日工时信息:', response.result);
      } else {
        console.warn('获取当日工时信息失败:', response.message);
      }
    } catch (error) {
      console.error('获取当日工时信息失败:', error);
    } finally {
      setLoadingDaily(false);
    }
  };

  // 获取每天标准工时配置
  const fetchWorkHourPerDay = async () => {
    try {
      const response = await get<any>('/hours/sys/config/getConfigValue', {
        configKey: 'work_hour_per_day'
      });

      if (response.success && response.result) {
        const value = parseFloat(response.result);
        if (!isNaN(value) && value > 0) {
          setWorkHourPerDay(value);
        }
      }
    } catch (error) {
      console.error('获取每天标准工时配置失败:', error);
    }
  };

  // 格式化工时显示
  const formatHours = (hours?: number, days?: number) => {
    if (hours === undefined || hours === null) {
      return '--';
    }
    const hoursStr = hours.toFixed(2);
    const daysStr = days !== undefined ? days.toFixed(2) : (hours / workHourPerDay).toFixed(2);
    return `${hoursStr}小时(${daysStr}人天)`;
  };

  // 监听工作内容变化，获取统计信息
  useEffect(() => {
    if (workContent && workContent.length > 0) {
      const [workItemKey, instanceKey, nodeKey, taskKey] = workContent;
      fetchStatistics(
        workItemKey?.toString(),
        instanceKey?.toString(),
        nodeKey?.toString(),
        taskKey?.toString()
      );
    }
  }, [workContent]);

  // 监听工作日期变化，获取当日工时信息
  useEffect(() => {
    if (workDate) {
      fetchDailyInfo(workDate);
    }
  }, [workDate]);

  // 初始化时获取每天标准工时配置
  useEffect(() => {
    fetchWorkHourPerDay();
  }, []);

  // 监听记录模式变化，清空表单状态
  useEffect(() => {
    if (!isEdit) { // 只在非编辑模式下清空状态
      console.log('记录模式变化为:', recordMode, '清空表单状态');

      // 清空工作内容相关状态
      setWorkContent([]);
      setSelectedLabels([]);
      setWorkContentOptions([]);

      // 重置工作详情为初始状态
      setWorkDetails(initialWorkDetails);

      // 清空统计信息
      setStatistics(null);

      // 重新加载第一级工作项数据
      fetchWorkItems();
    }
  }, [recordMode]); // 只监听recordMode变化

  // 记录编辑模式的初始化情况
  useEffect(() => {
    if (isEdit && initialData) {
      console.log('编辑模式初始化数据:', {
        recordId: initialData.recordId,
        workContent: initialData.workContent,
        projectName: initialData.projectName,
        workItemName: initialData.workItemName,
        instanceName: initialData.instanceName,
        nodeName: initialData.nodeName,
        taskName: initialData.taskName,
        selectedLabels: selectedLabels  // 添加 selectedLabels 到日志
      });
    }
  }, [isEdit, initialData, selectedLabels]);  // 添加 selectedLabels 作为依赖项

  return (
    <SideSheet
      title={isEdit ? "编辑工时" : (recordMode === 'single' ? "工时登记" : "批量登记工时")}
      visible={visible}
      onCancel={onCancel}
      width={800}
      mask={true}
      placement="right"
      headerStyle={{ padding: '16px 24px' }}
      bodyStyle={{ padding: '0 24px 24px' }}
      
    >
      <div style={{ height: 'calc(100% - 60px)', overflow: 'auto', padding: '20px 0' }}>
        {/* 登记模式选择 */}
        <div style={{ marginBottom: '16px', display: 'flex', gap: '16px' }}>
          <Radio 
            checked={recordMode === 'batch'}
            onChange={() => setRecordMode('batch')}
            disabled={isEdit} // 编辑模式下禁用
          >
            批量登记
          </Radio>
          <Radio 
            checked={recordMode === 'single'}
            onChange={() => setRecordMode('single')}
            disabled={isEdit} // 编辑模式下禁用
          >
            单个登记
          </Radio>
        </div>

        {/* 工时规则提示 */}
        <div style={{
          backgroundColor: 'rgba(var(--semi-blue-0), .2)',
          padding: '12px 16px',
          borderRadius: 6,
          marginBottom: 24,
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <Text size="small">
            {loadingDaily ? '加载当日工时信息中...' :
              dailyInfo ?
                `当前工作日期可登记工时${dailyInfo.dailyScheduledHours.toFixed(2)}小时，剩余可登记工时${dailyInfo.remainingHours.toFixed(2)}小时` :
                '无法获取当日工时信息'
            }
          </Text>
          <Button
            type="tertiary"
            theme="borderless"
            size="small"
            style={{
              padding: '0 4px',
              height: 'auto',
              fontSize: '12px',
              color: 'var(--semi-color-primary)',
              textDecoration: 'underline'
            }}
            onClick={() => setRulesModalVisible(true)}
          >
            工时登记规则
          </Button>
        </div>
        
        {/* 编辑模式提示 */}
        {recordMode === 'single' ? (
          /* 单个登记内容 */
          <>
            {/* 基本信息区域 */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '24px' }}>
              {/* 工作内容 */}
              <div>
                <Form.Label required style={{fontWeight:'normal'}}>工作内容</Form.Label>
                <Cascader
                  placeholder="请选择工作内容"
                  style={{ width: '100%' }}
                  value={workContent}
                  onChange={(value) => {
                    console.log("Cascader onChange被触发，选中值:", value);
                    if (Array.isArray(value)) {
                      // 如果是数组，直接传递
                      handleWorkContentChange(value as (string | number)[]);
                    } else if (value === null || value === undefined) {
                      // 处理清除选择的情况
                      handleWorkContentChange([]);
                    } else {
                      // 将非数组值转换为数组
                      handleWorkContentChange([value as string | number]);
                    }
                  }}
                  onVisibleChange={handleDropdownVisibleChange}
                  loadData={onLoadData}
                  treeData={workContentOptions}
                  loading={loading}
                  showClear
                  filterTreeNode
                  defaultOpen={false}
                  displayRender={(labels, selectedOptions) => {
                    // 在编辑模式下优先使用selectedLabels，这些是从record中获取的友好名称
                    if (isEdit && selectedLabels.length > 0) {
                      return formatWorkContent(selectedLabels);
                    }
                    // 否则使用从级联选择器获取的标签
                    return formatWorkContent(Array.isArray(labels) ? labels : []);
                  }}
                  expandTrigger="click"
                  changeOnSelect
                  disabled={isEdit} // 编辑模式下禁用工作内容选择
                />
              </div>
              
              {/* 排期 */}
              <div>
                <Form.Label style={{fontWeight:'normal'}}>排期</Form.Label>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center',
                  opacity: isEdit ? 0.65 : 1
                }}>
                  --
                </div>
              </div>
            </div>
            
            {/* 工时统计区域 */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '24px' }}>
              {/* 预估工时 */}
              <div>
                <Form.Label style={{fontWeight:'normal'}}>预估工时</Form.Label>
                <Input
                  value={loadingStats ? '加载中...' : formatHours(statistics?.estimateHours, statistics?.estimateDays)}
                  readOnly
                  style={{ width: '100%', opacity: isEdit ? 0.65 : 1 }}
                  disabled={isEdit} // The model should add this code: 编辑模式下禁用
                />
              </div>

              {/* 已登记工时 */}
              <div>
                <Form.Label style={{fontWeight:'normal'}}>已登记工时</Form.Label>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Input
                    value={loadingStats ? '加载中...' : formatHours(statistics?.recordedHours, statistics?.recordedDays)}
                    readOnly
                    style={{ width: '100%', opacity: isEdit ? 0.65 : 1 }}
                    disabled={isEdit} // 编辑模式下禁用
                  />
                  <Button
                    size="small"
                    type="tertiary"
                    style={{ marginLeft: '8px' }}
                    disabled={isEdit} // 编辑模式下禁用
                  >
                    查看明细
                  </Button>
                </div>
              </div>
            </div>
            
            {/* 日期和人员 */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '24px' }}>
              {/* 工作日期 - 编辑模式下可修改 */}
              <div>
                <Form.Label required style={{fontWeight:'normal'}}>工作日期</Form.Label>
                <DatePicker 
                  value={workDate} 
                  onChange={handleDateChange} 
                  style={{ width: '100%' }}
                />
              </div>
              
              {/* 工作人员 */}
              <div>
                <Form.Label required style={{fontWeight:'normal'}}>工作人员</Form.Label>
                <Select
                  placeholder="请选择"
                  value={worker}
                  onChange={handleWorkerChange}
                  style={{ width: '100%' }}
                  disabled={isEdit} // 编辑模式下禁用工作人员选择
                >
                  <Select.Option value="蒋莉芬">蒋莉芬</Select.Option>
                  <Select.Option value="张三">张三</Select.Option>
                  <Select.Option value="李四">李四</Select.Option>
                </Select>
              </div>
            </div>
            
            {/* 本次登记工时 - 编辑模式下可修改 */}
            <div style={{ marginBottom: '24px' }}>
              <Form.Label required style={{fontWeight:'normal'}}>本次登记工时</Form.Label>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <InputNumber
                  min={0}
                  max={24}
                  value={workDetails[0]?.hours || 2}
                  style={{ width: '120px' }}
                  onChange={(value) => {
                    if (workDetails.length > 0) {
                      const newDetails = [...workDetails];
                      newDetails[0].hours = value as number;
                      setWorkDetails(newDetails);
                    }
                  }}
                />
                <Text style={{ margin: '0 8px' }}>小时</Text>
                <Text style={{ marginLeft: '8px' }}>
                  {((workDetails[0]?.hours || 2) / workHourPerDay).toFixed(2)}人天
                </Text>
              </div>
            </div>
            
            {/* 工作描述 - 编辑模式下可修改 */}
            <div style={{ marginBottom: '24px' }}>
              <Form.Label style={{fontWeight:'normal'}} >工作描述</Form.Label>
              <TextArea
                rows={4}
                placeholder="请输入工作描述"
                defaultValue={workDetails[0]?.description || ''}
                style={{ width: '100%' }}
                onChange={(value) => {
                  if (workDetails.length > 0) {
                    const newDetails = [...workDetails];
                    newDetails[0].description = value;
                    setWorkDetails(newDetails);
                  }
                }}
              />
            </div>
          </>
        ) : (
          /* 批量登记内容 */
          <>
            {/* 日期和人员 */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '24px' }}>
              {/* 工作日期 */}
              <div>
                <Form.Label required style={{fontWeight:'normal'}}>工作日期</Form.Label>
                <DatePicker 
                  value={workDate} 
                  onChange={handleDateChange} 
                  style={{ width: '100%' }}
                />
              </div>
              
              {/* 工作人员 */}
              <div>
                <Form.Label required style={{fontWeight:'normal'}}>工作人员</Form.Label>
                <Select
                  placeholder="请选择"
                  value={worker}
                  onChange={handleWorkerChange}
                  style={{ width: '100%' }}
                >
                  <Select.Option value="蒋莉芬">蒋莉芬</Select.Option>
                  <Select.Option value="张三">张三</Select.Option>
                  <Select.Option value="李四">李四</Select.Option>
                </Select>
              </div>
            </div>
            
            {/* 已登记工时 */}
            <div style={{ marginBottom: '24px' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Text>已登记工时</Text>
                <Text style={{ margin: '0 8px' }}>3.00小时(0.38人天)</Text>
                <Button size="small" type="tertiary">
                  查看明细
                </Button>
              </div>
            </div>
            
            {/* 当日工时信息提示 */}
            <div style={{
              backgroundColor: 'rgba(var(--semi-blue-0), .2)',
              padding: '8px 16px',
              borderRadius: 6,
              marginBottom: 16,
              fontSize: '12px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <Text size="small">
                {loadingDaily ? '加载当日工时信息中...' :
                  dailyInfo ?
                    `当前工作日期可登记工时${dailyInfo.dailyScheduledHours.toFixed(2)}小时，剩余可登记工时${dailyInfo.remainingHours.toFixed(2)}小时` :
                    '无法获取当日工时信息'
                }
              </Text>
              <Button
                type="tertiary"
                theme="borderless"
                size="small"
                style={{
                  padding: '0 4px',
                  height: 'auto',
                  fontSize: '12px',
                  color: 'var(--semi-color-primary)',
                  textDecoration: 'underline'
                }}
                onClick={() => setRulesModalVisible(true)}
              >
                工时登记规则
              </Button>
            </div>
            
            {/* 工时登记明细表格 */}
            <div style={{ marginBottom: '24px' }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center', 
                marginBottom: '16px'
              }}>
                <Title heading={6} style={{ margin: 0 }}>工时登记明细</Title>
                <Button icon={<IconPlus />} onClick={handleAddDetail}>添加</Button>
              </div>
              
              <Table
                columns={detailColumns}
                dataSource={workDetails}
                pagination={false}
                size="small"
                bordered
                expandedRowRender={(record: WorkDetail) => {
                  if (expandedRow !== record.key) return null;
                  return (
                    <div style={{ padding: '12px' }}>
                      <div style={{ marginBottom: '8px' }}>
                        <Text strong>业务二级：</Text>
                        <Text>{record.businessLevel2 || '--'}</Text>
                      </div>
                      <div>
                        <Text strong>状态：</Text>
                        <Text>{record.status}</Text>
                      </div>
                    </div>
                  );
                }}
                expandedRowKeys={expandedRow ? [expandedRow] : []}
              />
            </div>
          </>
        )}
      </div>

      {/* 工时登记规则说明模态窗 */}
      <WorkHourRulesModal
        visible={rulesModalVisible}
        onClose={() => setRulesModalVisible(false)}
      />
    </SideSheet>
  );
};

export default HourRecordDetail;