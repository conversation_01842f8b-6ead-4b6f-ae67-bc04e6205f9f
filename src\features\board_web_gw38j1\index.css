#app {
  min-height: 400px;
  width: 100%;
  display: flex;
  align-items: center;
}

a.primary {
  border: 1px solid rgba(28,31,35,.08);
  background: rgba(28,31,35,1);
  color: #fff;
  padding: 6px 12px;
  text-decoration: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

a.primary .icon {
  color: #fff;
}

a.secondary {
  border: 1px solid rgba(28,31,35,.08);
  background: #fff;
  color: rgba(28,31,35,1);
  padding: 6px 12px;
  text-decoration: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

a.secondary .icon {
  color: rgba(28,31,35,1);
}

a.primary:hover {
  background: rgba(28,31,35,.8);
}

a.secondary:hover {
  background: rgba(46,50,56,.09);
}

.icon {
  font-size: 20px;
  margin-right: 4px;
}

.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  line-height: 20px;
}

.title {
  display: flex;
  justify-content: center;
  font-size: 32px;
  margin-bottom: 8px;
  line-height: 45px;
  word-break: break-all;
  white-space: pre-wrap;
  width: 100%;
}

.desc {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
  padding: 6px 16px;
  background: rgba(137,126,246,0.08);
  border-radius: 32px;
  line-height: 20px;
  margin: 8px 0 24px;
  max-width: 100%;
  box-sizing: border-box;
}

.desc-text {
  font-weight: 500;
  margin-right: 10px;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-all;
  max-width: 100%;
}

.desc-text > span {
  margin-right: 10px;
}

.desc-entry {
  margin: 0;
  color: #3250EB;
  white-space: pre-wrap;
  word-break: break-all;
  max-width: 100%;
  text-align: center;
}

.btn-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;
  max-width: 100%;
}

/* 新增整体布局样式 */
.layout-root {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fafbfc;
  overflow: hidden;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 顶部导航栏样式 */
.header {
  width: 100%;
  height: 40px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 0;
  z-index: 10;
}

.nav-container {
  height: 100%;
  display: flex;
  align-items: center;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: #222;
  margin-right: 32px;
}
.header-nav {
  display: flex;
  height: 100%;
}
.nav-item {
  font-size: 14px;
  color: #666;
  cursor: pointer;
  padding: 0 16px;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
  transition: color 0.3s;
  font-weight: bold;
  box-sizing: border-box;
  line-height: 40px;
}
.nav-active {
  color: #3250eb;
  font-weight: bold;
}
.nav-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 16px;
  right: 16px;
  height: 2px;
  background: #3250eb;
}
.nav-item:hover {
  color: #3250eb;
}
.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
.date-picker {
  width: 110px;
  height: 28px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 14px;
  background: #f7f8fa;
  color: #222;
  margin: 0 2px;
}
.date-range-arrow {
  color: #888;
  margin: 0 4px;
}
.team-btn, .dept-btn, .record-btn {
  height: 32px;
  padding: 0 14px;
  border: none;
  border-radius: 4px;
  background: #f7f8fa;
  color: #3250eb;
  font-size: 14px;
  cursor: pointer;
}
.primary.record-btn {
  background: #3250eb;
  color: #fff;
}

/* 全局盒模型设置 */
* {
  box-sizing: border-box;
}

/* 主体区域布局 */
.main-content {
  flex: 1;
  display: flex;
  height: calc(100vh - 40px);
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  justify-content: center;
  align-items: flex-start; /* 确保内容从顶部开始显示 */
}

/* 左侧树状任务栏 */
.sidebar {
  width: 260px;
  background: #fff;
  border-right: 1px solid #f0f0f0;
  padding: 16px 12px;
  overflow-y: auto;
}
.sidebar-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #222;
}
.task-tree {
  list-style: none;
  padding-left: 0;
  font-size: 14px;
  color: #333;
}
.task-tree ul {
  list-style: none;
  padding-left: 18px;
}
.task-tree li {
  margin-bottom: 6px;
}

/* 右侧甘特图区域 */
.gantt-area {
  flex: 1;
  background: #f7f8fa;
  padding: 0 16px;
  overflow: auto;
  position: relative;
}
.gantt-header {
  background: #f7f8fa;
  padding: 12px 0 8px 0;
  border-bottom: 1px solid #e5e6eb;
}
.gantt-header-row {
  display: flex;
  gap: 16px;
}
.gantt-header-cell {
  min-width: 80px;
  text-align: center;
  color: #888;
  font-size: 13px;
}
.gantt-body {
  position: relative;
  min-height: 300px;
  margin-top: 16px;
}
.gantt-bar {
  position: absolute;
  height: 28px;
  background: #3250eb;
  color: #fff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(50,80,235,0.08);
  left: 80px;
  top: 0;
}
.gantt-bar + .gantt-bar {
  top: 40px;
}

/* 工时管理列表页面样式 */
.work-list-root {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  padding: 24px 24px 16px 24px;
  margin: 0px auto;
  max-width: 1200px;
  min-width: 900px;
}

.work-list-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 18px;
  margin-top: 6px;
}
.work-list-toolbar select {
  height: 32px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  background: #fafbfc;
  color: #222;
}
.work-list-toolbar .primary {
  background: #3250eb;
  color: #fff;
  border: none;
  border-radius: 4px;
  height: 32px;
  padding: 0 18px;
  font-size: 14px;
  margin-left: 8px;
  cursor: pointer;
  transition: background 0.2s;
}
.work-list-toolbar .primary:hover {
  background: #2233aa;
}

.work-list-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 18px;
}
.work-list-stat-card {
  background: #f7f8fa;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(50,80,235,0.04);
  padding: 12px 24px;
  min-width: 120px;
  display: flow;
  flex-direction: column;
  align-items: center;
}
.stat-label {
  color: #888;
  font-size: 13px;
  margin-bottom: 4px;
}
.stat-value {
  color: #3250eb;
  font-size: 22px;
  font-weight: bold;
}

.work-list-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  margin-bottom: 12px;
  font-size: 14px;
}
.work-list-table th, .work-list-table td {
  border: 1px solid #f0f0f0;
  padding: 8px 10px;
  text-align: center;
}
.work-list-table th {
  background: #f7f8fa;
  color: #222;
  font-weight: 500;
}
.work-list-table tr:hover {
  background: #f0f6ff;
}
.work-list-table td {
  color: #333;
}
.work-list-table input[type="checkbox"] {
  width: 16px;
  height: 16px;
}
.work-list-table a {
  color: #3250eb;
  text-decoration: none;
  cursor: pointer;
  font-size: 13px;
}
.work-list-table a:hover {
  text-decoration: underline;
}

/* 审批状态彩色标签 */
.work-list-table .status-green {
  color: #52c41a;
  font-weight: bold;
}
.work-list-table .status-red {
  color: #ff4d4f;
  font-weight: bold;
}
.work-list-table .status-orange {
  color: #faad14;
  font-weight: bold;
}

.header-nav-center {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* 内容包装器 */
.content-wrapper {
  width: 100%;
  height: auto;
  min-height: 100%;
  display: flex;
  justify-content: center;
  padding-bottom: 40px;
}

/* 系统设置页面样式 */
.settings-root {
  padding: 20px;
  width: 100%;
  max-width: 1200px; /* 限制最大宽度 */
  box-sizing: border-box;
  overflow-x: hidden; /* 防止水平滚动 */
  margin: 0 auto;
  height: auto;
  min-height: 100%;
  padding-bottom: 50px; /* 为底部留出空间 */
}

.settings-vertical {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden; /* 防止水平滚动 */
  box-sizing: border-box; /* 确保padding不会导致宽度溢出 */
}

.settings-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden; /* 修改为hidden，防止水平滚动 */
  background-color: #fff;
  padding: 0;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  height: auto;
  position: relative;
  z-index: 1;
  margin-bottom: 16px;
}

/* 表单布局 */
.settings-form-section {
  margin-top: 16px;
  margin-bottom: 16px;
  width: 100%;
  overflow: hidden; /* 修改为hidden，防止水平滚动 */
  max-width: 100%; /* 添加最大宽度限制 */
  box-sizing: border-box; /* 确保padding不会导致宽度溢出 */
}

.settings-form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  width: 100%;
  overflow: visible;
}

.settings-form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
}

.settings-form-item-wide {
  width: 100%;
}

.settings-form-help {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.settings-form-actions {
  text-align: right;
  margin-top: 16px;
  margin-bottom: 20px;
  width: 100%;
}

/* 表单元素样式 */
.settings-radio-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.settings-radio {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  margin-bottom: 8px;
}

/* 搜索区域 */
.settings-search {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

/* 标题栏带Switch样式 */
.settings-section-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  padding: 12px 20px;
  box-sizing: border-box;
}

.settings-section-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-section-title h5 {
  font-size: 16px !important;
  font-weight: 600 !important;
}

/* Switch样式调整 */
.settings-section-title .semi-switch {
  margin-top: 1px;
  transform: scale(1.2);
  margin-left: 4px;
}

/* 卡片内分隔线调整 */
.settings-card .semi-divider {
  margin: 0;
}

/* 卡片内容区域样式 */
.settings-card .settings-form-section,
.settings-card .settings-form-actions,
.settings-card .settings-header,
.settings-card .settings-search,
.settings-card .settings-table-wrapper {
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 列表样式 */
.settings-list {
  padding-left: 20px;
  margin: 12px 0;
}

/* 表格样式优化 */
.semi-table {
  margin-top: 8px;
  width: 100%;
  table-layout: auto;
}

/* 响应式表格 */
.semi-table-wrapper {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 16px;
  max-width: 100%;
  padding: 0;
  margin: 0;
}

/* 系统配置页面样式 */
.system-config-root {
  padding: 16px;
  height: 100%;
  overflow: auto;
}

.system-config-search {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.system-config-action {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

/* 表格区域样式 */
.settings-table-wrapper {
  width: 100%;
  overflow-x: auto; /* 允许表格内容横向滚动 */
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

/* 调整表格内部元素样式 */
.semi-table .semi-table-tbody .semi-table-row .semi-table-cell {
  white-space: normal;
  word-break: break-word;
  height: auto;
  min-height: 48px;
  padding: 8px 12px;
}

/* 修复表格内操作按钮布局 */
.semi-table .semi-table-tbody .semi-table-row .semi-table-cell button {
  margin: 2px;
}

/* 标题栏样式 */
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 16px 0;
}

/* 卡片样式覆盖 */
.semi-card-body {
  padding: 0px !important;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 登记提醒设置样式 */
.reminder-settings {
  width: 100%;
  padding: 0;
}

.reminder-settings .settings-form-section {
  padding: 0 20px;
  margin-bottom: 16px;
}

.reminder-settings .settings-table-wrapper {
  padding: 0 20px;
  width: 100%;
  overflow-x: auto;
  margin-bottom: 16px;
}

/* 提醒配置表格样式 */
.reminder-settings .semi-table {
  margin-top: 0;
}

.reminder-settings .semi-table .semi-table-tbody .semi-table-row .semi-table-cell {
  vertical-align: middle;
  padding: 12px 8px;
}

/* 提醒配置弹窗样式 */
.reminder-settings .semi-modal-content {
  max-height: 80vh;
  overflow-y: auto;
}

.reminder-settings .semi-form-field {
  margin-bottom: 16px;
}

.reminder-settings .semi-form-field-label {
  font-weight: 500;
  margin-bottom: 8px;
}

/* 状态标签样式 */
.reminder-settings .status-tag {
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

.reminder-settings .status-enabled {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.reminder-settings .status-disabled {
  background-color: #f5f5f5;
  color: #999;
  border: 1px solid #d9d9d9;
}

/* 操作按钮样式 */
.reminder-settings .action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.reminder-settings .action-buttons .semi-button {
  min-width: 60px;
}

/* 表格行选择样式 */
.reminder-settings .semi-table-row-selected {
  background-color: #e6f7ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reminder-settings .settings-form-section,
  .reminder-settings .settings-table-wrapper {
    padding: 0 12px;
  }

  .reminder-settings .semi-table .semi-table-tbody .semi-table-row .semi-table-cell {
    padding: 8px 4px;
    font-size: 12px;
  }

  .reminder-settings .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .reminder-settings .action-buttons .semi-button {
    width: 100%;
    min-width: auto;
  }
}

/* 表单验证错误样式 */
.reminder-settings .semi-form-field-error .semi-input,
.reminder-settings .semi-form-field-error .semi-select,
.reminder-settings .semi-form-field-error .semi-timepicker {
  border-color: #ff4d4f;
}

.reminder-settings .semi-form-field-error .semi-form-field-error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}