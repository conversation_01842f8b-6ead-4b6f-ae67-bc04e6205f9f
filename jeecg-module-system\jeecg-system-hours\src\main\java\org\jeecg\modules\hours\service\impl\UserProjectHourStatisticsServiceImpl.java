package org.jeecg.modules.hours.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.hours.entity.HourRecord;
import org.jeecg.modules.hours.entity.UserProjectHourStatistics;
import org.jeecg.modules.hours.mapper.HourRecordMapper;
import org.jeecg.modules.hours.mapper.UserProjectHourStatisticsMapper;
import org.jeecg.modules.hours.service.IUserProjectHourStatisticsService;
import org.jeecg.modules.hours.vo.ProjectColumnVO;
import org.jeecg.modules.hours.vo.UserProjectHourReportResult;
import org.jeecg.modules.hours.vo.UserProjectHourReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 人员项目工时统计表
 * @Author: jeecg-boot
 * @Date: 2025-07-08
 * @Version: V1.0
 */
@Service
@Slf4j
public class UserProjectHourStatisticsServiceImpl extends ServiceImpl<UserProjectHourStatisticsMapper, UserProjectHourStatistics> 
    implements IUserProjectHourStatisticsService {

    @Autowired
    private UserProjectHourStatisticsMapper userProjectHourStatisticsMapper;
    
    @Autowired
    private HourRecordMapper hourRecordMapper;

    @Override
    public UserProjectHourReportResult getUserProjectHourReport(Date startDate, Date endDate, String statPeriod,
                                                              List<String> userIds, List<String> projectIds, Integer isHoliday) {
        log.info("获取人员项目工时统计报表, startDate={}, endDate={}, statPeriod={}, isHoliday={}", startDate, endDate, statPeriod, isHoliday);
        
        // 1. 获取动态项目列信息
        List<ProjectColumnVO> projectColumns = userProjectHourStatisticsMapper.getProjectColumns(
            startDate, endDate, statPeriod, userIds, projectIds, isHoliday);
        
        // 添加排序序号（按工时总数降序）
        for (int i = 0; i < projectColumns.size(); i++) {
            projectColumns.get(i).setSortOrder(i + 1);
        }
        
        // 2. 获取原始统计数据
        List<UserProjectHourReportVO> rawData = userProjectHourStatisticsMapper.getUserProjectHourReport(
            startDate, endDate, statPeriod, userIds, projectIds, isHoliday);
        
        // 3. 转换数据结构，构建人员×项目矩阵
        Map<String, UserProjectHourReportVO> userDataMap = new LinkedHashMap<>();
        
        for (UserProjectHourReportVO data : rawData) {
            String userKey = data.getUserId();
            UserProjectHourReportVO userRow = userDataMap.computeIfAbsent(userKey, k -> {
                UserProjectHourReportVO row = new UserProjectHourReportVO();
                row.setUserId(data.getUserId());
                row.setUserName(data.getUserName());
                row.setUserCode(data.getUserCode());
                row.setDepartmentId(data.getDepartmentId());
                row.setDepartmentName(data.getDepartmentName());
                row.setProjectHours(new HashMap<>());
                row.setTotalHours(BigDecimal.ZERO);
                return row;
            });
            
            // 填充项目工时数据
            if (data.getProjectId() != null && data.getTotalHours() != null) {
                String projectKey = "project_" + data.getProjectId();
                BigDecimal oldProjectHours = Objects.isNull(userRow.getProjectHours().get(projectKey))?BigDecimal.ZERO:userRow.getProjectHours().get(projectKey);
                userRow.getProjectHours().put(projectKey, oldProjectHours.add(data.getTotalHours()));
                userRow.setTotalHours(userRow.getTotalHours().add(data.getTotalHours()));
            }
        }
        
        // 4. 确保所有人员都有完整的项目列数据（填充0值）
        for (UserProjectHourReportVO userRow : userDataMap.values()) {
            for (ProjectColumnVO projectColumn : projectColumns) {
                String projectKey = projectColumn.getColumnKey();
                userRow.getProjectHours().putIfAbsent(projectKey, BigDecimal.ZERO);
            }
        }
        
        // 5. 获取总计行数据
        List<UserProjectHourReportVO> totalRowList = userProjectHourStatisticsMapper.getTotalRow(
            startDate, endDate, statPeriod, userIds, projectIds, isHoliday);
        
        // 构建总计行的项目工时数据
        UserProjectHourReportVO totalRow = new UserProjectHourReportVO();
        totalRow.setUserId("total");
        totalRow.setUserName("总计");
        totalRow.setUserCode("");
        totalRow.setDepartmentId("");
        totalRow.setDepartmentName("");
        totalRow.setProjectHours(new HashMap<>());
        totalRow.setTotalHours(BigDecimal.ZERO);
        
        for (UserProjectHourReportVO total : totalRowList) {
            if (total.getProjectId() != null && total.getProjectHours2() != null) {
                String projectKey = "project_" + total.getProjectId();
                totalRow.getProjectHours().put(projectKey, total.getProjectHours2());
                totalRow.setTotalHours(totalRow.getTotalHours().add(total.getProjectHours2()));
            }
        }
        
        // 确保总计行也有完整的项目列数据
        for (ProjectColumnVO projectColumn : projectColumns) {
            String projectKey = projectColumn.getColumnKey();
            totalRow.getProjectHours().putIfAbsent(projectKey, BigDecimal.ZERO);
        }
        
        // 6. 获取汇总统计信息
        UserProjectHourReportResult.ReportSummaryVO summary = userProjectHourStatisticsMapper.getSummaryData(
            startDate, endDate, statPeriod, userIds, projectIds, isHoliday);
        
        if (summary == null) {
            summary = new UserProjectHourReportResult.ReportSummaryVO()
                .setDateRange(String.format("%s 至 %s", 
                    DateUtils.date2Str(startDate, DateUtils.date_sdf.get()), 
                    DateUtils.date2Str(endDate, DateUtils.date_sdf.get())))
                .setTotalHours(BigDecimal.ZERO)
                .setUserCount(userDataMap.size())
                .setProjectCount(projectColumns.size())
                .setDeptCount(0);
        }
        
        // 7. 构建返回结果
        UserProjectHourReportResult result = new UserProjectHourReportResult();
        result.setProjectColumns(projectColumns);
        result.setUserHourData(new ArrayList<>(userDataMap.values()));
        result.setTotalRow(totalRow);
        result.setSummary(summary);
        
        log.info("人员项目工时统计报表获取完成, 人员数={}, 项目数={}", userDataMap.size(), projectColumns.size());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int calculateHourStatistics(Date statDate, String statPeriod) {
        log.info("开始计算人员项目工时统计数据, statDate={}, statPeriod={}", statDate, statPeriod);
        
        try {
            // 1. 删除当天的旧统计数据
            userProjectHourStatisticsMapper.deleteByStatDate(statDate, statPeriod);
            
            // 2. 从明细表查询当天的工时记录数据
            List<Map<String, Object>> hourDetails = hourRecordMapper.getHourRecordDetailsForStatistics(statDate);
            
            if (hourDetails.isEmpty()) {
                log.info("统计日期 {} 没有工时记录数据", statDate);
                return 0;
            }
            
            // 3. 按用户和项目分组统计
            Map<String, List<Map<String, Object>>> groupedData = hourDetails.stream()
                .filter(detail -> detail.get("user_id") != null && detail.get("project_id") != null)
                .collect(Collectors.groupingBy(detail -> 
                    detail.get("user_id") + "_" + detail.get("project_id")));
            
            // 4. 构建统计数据
            List<UserProjectHourStatistics> statisticsList = new ArrayList<>();
            
            for (Map.Entry<String, List<Map<String, Object>>> entry : groupedData.entrySet()) {
                List<Map<String, Object>> details = entry.getValue();
                Map<String, Object> firstDetail = details.get(0);
                
                UserProjectHourStatistics statistics = new UserProjectHourStatistics();
                statistics.setStatDate(statDate);
                statistics.setStatPeriod(statPeriod);
                statistics.setUserId((String) firstDetail.get("user_id"));
                statistics.setUserName((String) firstDetail.get("user_name"));
                statistics.setUserCode((String) firstDetail.get("user_code"));
                statistics.setDepartmentId((String) firstDetail.get("department_id"));
                statistics.setDepartmentName((String) firstDetail.get("department_name"));
                statistics.setProjectId((String) firstDetail.get("project_id"));
                statistics.setProjectName((String) firstDetail.get("project_name"));
                statistics.setProjectCode((String) firstDetail.get("project_code"));
                
                // 统计工时总数
                BigDecimal totalHours = details.stream()
                    .map(detail -> (BigDecimal) detail.get("actual_hours"))
                    .filter(hours -> hours != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                statistics.setTotalHours(totalHours);
                
                statistics.setRecordCount(details.size());
                statistics.setCreateTime(new Date());
                statistics.setUpdateTime(new Date());
                
                statisticsList.add(statistics);
            }
            
            // 5. 批量插入统计数据
            if (!statisticsList.isEmpty()) {
                userProjectHourStatisticsMapper.batchInsertOrUpdate(statisticsList);
            }
            
            log.info("人员项目工时统计数据计算完成, statDate={}, 处理记录数={}", statDate, statisticsList.size());
            return statisticsList.size();
            
        } catch (Exception e) {
            log.error("计算人员项目工时统计数据失败, statDate={}, statPeriod={}", statDate, statPeriod, e);
            throw new RuntimeException("计算人员项目工时统计数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCalculateHourStatistics(Date startDate, Date endDate, String statPeriod) {
        log.info("开始批量计算人员项目工时统计数据, startDate={}, endDate={}, statPeriod={}", startDate, endDate, statPeriod);
        
        int totalProcessed = 0;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        
        while (!calendar.getTime().after(endDate)) {
            Date currentDate = calendar.getTime();
            int processed = calculateHourStatistics(currentDate, statPeriod);
            totalProcessed += processed;
            
            // 移动到下一天
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        
        log.info("批量计算人员项目工时统计数据完成, 总处理记录数={}", totalProcessed);
        return totalProcessed;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int recalculateHourStatistics(Date statDate, String statPeriod) {
        log.info("重新计算人员项目工时统计数据, statDate={}, statPeriod={}", statDate, statPeriod);
        return calculateHourStatistics(statDate, statPeriod);
    }
}