package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.entity.FsTaskCache;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * @Description: 飞书项目任务缓存表服务接口
 * @Author: jeecg-boot
 * @Date: 2023-08-20
 * @Version: V1.0
 */
public interface IFsTaskCacheService extends IService<FsTaskCache> {

    /**
     * 根据项目key和节点key获取任务列表
     * @param projectKey 项目key
     * @param nodeKey 节点key
     * @return 任务列表
     */
    List<FsTaskCache> getTasksByProjectAndNode(String projectKey, String nodeKey);
    
    /**
     * 根据项目key和任务key获取任务详情
     * @param projectKey 项目key
     * @param taskKey 任务key
     * @return 任务详情
     */
    FsTaskCache getTaskDetail(String projectKey, String taskKey);
    
    /**
     * 根据负责人ID获取任务列表
     * @param assignee 负责人ID
     * @return 任务列表
     */
    List<FsTaskCache> getTasksByAssignee(String assignee);
    
    /**
     * 根据状态获取任务列表
     * @param status 状态
     * @return 任务列表
     */
    List<FsTaskCache> getTasksByStatus(String status);
    
    /**
     * 分页查询任务数据
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<FsTaskCache> pageList(Page<FsTaskCache> page, QueryWrapper<FsTaskCache> queryWrapper);
    
    /**
     * 统计任务数据
     * @param projectKey 项目key
     * @return 统计结果
     */
    Map<String, Object> statTasks(String projectKey);
    
    /**
     * 批量保存或更新任务数据
     * @param taskList 任务列表
     * @return 是否成功
     */
    boolean saveOrUpdateBatch(List<FsTaskCache> taskList);
    
    /**
     * 批量保存或更新任务缓存
     * 基于 projectKey + taskKey 的唯一约束进行更新或插入
     * 
     * @param taskList 任务列表
     * @return 是否全部处理成功
     */
    boolean saveOrUpdateBatchByUniqueKey(List<FsTaskCache> taskList);

    /**
     * 批量保存或更新任务缓存，支持指定批次大小
     * 基于 projectKey + taskKey 的唯一约束进行更新或插入
     * 
     * @param taskList 任务列表
     * @param batchSize 每批处理的数据量，默认为1000
     * @return 是否全部处理成功
     */
    boolean saveOrUpdateBatchByUniqueKey(List<FsTaskCache> taskList, int batchSize);

    /**
     * 根据项目key、工作项key、实例key和节点key获取任务列表
     * @param projectKey 项目key
     * @param workItemKey 工作项key
     * @param instanceKey 实例key
     * @param nodeKey 节点key
     * @return 任务列表
     */
    List<FsTaskCache> getTasksByKeys(String projectKey, String workItemKey, String instanceKey, String nodeKey);
} 