package org.jeecg.modules.hours.mapper;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.vo.UnsubmittedWorkHourVO;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 工时提醒数据查询Mapper
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface WorkHourReminderMapper {

    /**
     * 查询有考勤无工时登记的用户（飞书考勤校验模式）
     * 
     * @param projectKey 项目标识
     * @param businessDates 业务日期列表
     * @return 未提交工时的用户信息
     */
    List<UnsubmittedWorkHourVO> findUnsubmittedWithAttendance(
            @Param("projectKey") String projectKey, 
            @Param("businessDates") List<LocalDate> businessDates);

    /**
     * 根据工时上限查询未提交工时的用户（手动校验模式）
     * 
     * @param projectKey 项目标识
     * @param businessDates 业务日期列表
     * @param maxWorkHourPerDay 每日工时上限
     * @return 未提交工时的用户信息
     */
    List<UnsubmittedWorkHourVO> findUnsubmittedByWorkHourLimit(
            @Param("projectKey") String projectKey, 
            @Param("businessDates") List<LocalDate> businessDates,
            @Param("maxWorkHourPerDay") Double maxWorkHourPerDay);
}
