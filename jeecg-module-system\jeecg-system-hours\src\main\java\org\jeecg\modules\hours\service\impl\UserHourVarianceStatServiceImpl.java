package org.jeecg.modules.hours.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.UUIDGenerator;
import org.jeecg.modules.hours.dto.UserVarianceDataDTO;
import org.jeecg.modules.hours.entity.UserHourVarianceStat;
import org.jeecg.modules.hours.mapper.UserHourVarianceStatMapper;
import org.jeecg.modules.hours.service.IUserHourVarianceStatService;
import org.jeecg.modules.hours.vo.UserHourVarianceReportVO;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserHourVarianceStatServiceImpl extends ServiceImpl<UserHourVarianceStatMapper, UserHourVarianceStat> 
        implements IUserHourVarianceStatService {

    @Autowired
    private UserHourVarianceStatMapper userHourVarianceStatMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int calculateMonthlyVarianceStat(int year, int month) {
        log.info("开始计算{}年{}月的人员工时差值统计", year, month);
        
        try {
            // 删除该月已有的统计数据
            LambdaQueryWrapper<UserHourVarianceStat> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(UserHourVarianceStat::getStatYear, year)
                        .eq(UserHourVarianceStat::getStatMonth, month);
            this.remove(deleteWrapper);
            
            // 计算包含假日的统计数据
            List<UserVarianceDataDTO> varianceDataWithHoliday = 
                userHourVarianceStatMapper.calculateMonthlyVarianceData(year, month, true);
            
            // 计算不包含假日的统计数据
            List<UserVarianceDataDTO> varianceDataWithoutHoliday = 
                userHourVarianceStatMapper.calculateMonthlyVarianceData(year, month, false);
            
            List<UserHourVarianceStat> statRecords = new ArrayList<>();
            
            // 处理包含假日的数据
            for (UserVarianceDataDTO data : varianceDataWithHoliday) {
                UserHourVarianceStat stat = createStatRecord(data, year, month, true);
                statRecords.add(stat);
            }
            
            // 处理不包含假日的数据
            for (UserVarianceDataDTO data : varianceDataWithoutHoliday) {
                UserHourVarianceStat stat = createStatRecord(data, year, month, false);
                statRecords.add(stat);
            }
            
            // 批量保存
            if (!statRecords.isEmpty()) {
                this.saveBatch(statRecords);
            }
            
            log.info("完成{}年{}月的人员工时差值统计，共生成{}条记录", year, month, statRecords.size());
            return statRecords.size();
            
        } catch (Exception e) {
            log.error("计算人员工时差值统计失败: {}年{}月", year, month, e);
            throw new RuntimeException("计算人员工时差值统计失败", e);
        }
    }

    private UserHourVarianceStat createStatRecord(UserVarianceDataDTO data, int year, int month, boolean includeHoliday) {
        UserHourVarianceStat stat = new UserHourVarianceStat();
        stat.setId(UUIDGenerator.generate());
        stat.setUserId(data.getUserId());
        stat.setUserName(data.getUserName());
        stat.setUserCode(data.getUserCode());
        stat.setDeptId(data.getDeptId());
        stat.setDeptName(data.getDeptName());
        stat.setStatYear(year);
        stat.setStatMonth(month);
        stat.setIncludeHoliday(includeHoliday);
        
        // 处理数值字段
        stat.setActualHours(data.getActualHours() != null ? data.getActualHours() : BigDecimal.ZERO);
        stat.setRequiredHours(data.getRequiredHours() != null ? data.getRequiredHours() : BigDecimal.ZERO);
        
        // 计算差值 = 可登记工时 - 实际工时
        stat.setVarianceHours(stat.getRequiredHours().subtract(stat.getActualHours()));
        
        return stat;
    }

    @Override
    public List<UserHourVarianceReportVO> getVarianceReport(int year, List<String> userIds, List<String> deptIds, boolean includeHoliday) {
        // 查询指定年份的统计数据
        LambdaQueryWrapper<UserHourVarianceStat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserHourVarianceStat::getStatYear, year)
                   .eq(UserHourVarianceStat::getIncludeHoliday, includeHoliday)
                   .orderByAsc(UserHourVarianceStat::getDeptName)
                   .orderByAsc(UserHourVarianceStat::getUserName)
                   .orderByAsc(UserHourVarianceStat::getStatMonth);
        
        if (userIds != null && !userIds.isEmpty()) {
            queryWrapper.in(UserHourVarianceStat::getUserId, userIds);
        }
        
        if (deptIds != null && !deptIds.isEmpty()) {
            queryWrapper.in(UserHourVarianceStat::getDeptId, deptIds);
        }
        
        List<UserHourVarianceStat> statList = this.list(queryWrapper);
        
        // 按用户分组
        Map<String, List<UserHourVarianceStat>> userGroupMap = statList.stream()
            .collect(Collectors.groupingBy(UserHourVarianceStat::getUserId));
        
        List<UserHourVarianceReportVO> reportList = new ArrayList<>();
        
        for (Map.Entry<String, List<UserHourVarianceStat>> entry : userGroupMap.entrySet()) {
            List<UserHourVarianceStat> userStats = entry.getValue();
            if (userStats.isEmpty()) continue;
            
            UserHourVarianceReportVO reportVO = new UserHourVarianceReportVO();
            UserHourVarianceStat firstStat = userStats.get(0);
            reportVO.setUserId(entry.getKey());
            reportVO.setUserName(firstStat.getUserName());
            reportVO.setUserCode(firstStat.getUserCode());
            reportVO.setDeptId(firstStat.getDeptId());
            reportVO.setDeptName(firstStat.getDeptName());
            reportVO.setStatYear(year);
            
            // 构建月度数据
            Map<String, BigDecimal> monthlyDataMap = new HashMap<>();
            BigDecimal totalActual = BigDecimal.ZERO;
            BigDecimal totalRequired = BigDecimal.ZERO;
            
            for (UserHourVarianceStat stat : userStats) {
                String monthKey = String.valueOf(stat.getStatMonth());
                
                monthlyDataMap.put(monthKey + "月份实际工时", stat.getActualHours());
                monthlyDataMap.put(monthKey + "月份可登记工时", stat.getRequiredHours());
                monthlyDataMap.put(monthKey + "月份差值", stat.getVarianceHours());
                
                totalActual = totalActual.add(stat.getActualHours());
                totalRequired = totalRequired.add(stat.getRequiredHours());
            }
            
            reportVO.setMonthlyData(monthlyDataMap);
            
            // 年度总计
            UserHourVarianceReportVO.YearlyTotal yearlyTotal = new UserHourVarianceReportVO.YearlyTotal();
            yearlyTotal.setTotalActualHours(totalActual);
            yearlyTotal.setTotalRequiredHours(totalRequired);
            yearlyTotal.setTotalVarianceHours(totalRequired.subtract(totalActual));
            reportVO.setYearlyTotal(yearlyTotal);
            
            reportList.add(reportVO);
        }
        
        return reportList;
    }

    @Override
    public void exportVarianceReport(int year, List<String> userIds, List<String> deptIds, boolean includeHoliday, HttpServletResponse response) {
        try {
            // 获取报表数据
            List<UserHourVarianceReportVO> reportData = getVarianceReport(year, userIds, deptIds, includeHoliday);
            
            // 设置响应头
            String holidayText = includeHoliday ? "(含假日)" : "(不含假日)";
            String fileName = year + "年人员工时差值分析报表" + holidayText + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", 
                "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));

            // 导出Excel格式
            exportToExcel(reportData, year, includeHoliday, response.getOutputStream());
            
            log.info("人员工时差值分析报表导出完成: {}", fileName);

        } catch (Exception e) {
            log.error("导出人员工时差值分析报表失败", e);
            throw new RuntimeException("导出报表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出为Excel格式
     */
    private void exportToExcel(List<UserHourVarianceReportVO> reportData, int year, boolean includeHoliday, OutputStream outputStream) 
            throws IOException {
        
        // 构建列定义
        List<ExcelExportEntity> colList = new ArrayList<>();
        
        // 固定列
        colList.add(new ExcelExportEntity("姓名", "userName", 20));
        colList.add(new ExcelExportEntity("工号", "userCode", 15));
        colList.add(new ExcelExportEntity("部门", "deptName", 20));
        
        // 动态添加12个月的列
        for (int month = 1; month <= 12; month++) {
            colList.add(new ExcelExportEntity(month + "月份实际工时", "month" + month + "Actual", 15));
            colList.add(new ExcelExportEntity(month + "月份可登记工时", "month" + month + "Required", 15));
            colList.add(new ExcelExportEntity(month + "月份差值", "month" + month + "Variance", 12));
        }
        
        // 年度总计列
        colList.add(new ExcelExportEntity("年度实际工时总计", "yearTotalActual", 15));
        colList.add(new ExcelExportEntity("年度可登记工时总计", "yearTotalRequired", 15));
        colList.add(new ExcelExportEntity("年度差值总计", "yearTotalVariance", 15));
        
        // 构建数据列表
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        // 添加数据行
        for (UserHourVarianceReportVO item : reportData) {
            Map<String, Object> rowData = new HashMap<>();
            rowData.put("userName", item.getUserName());
            rowData.put("userCode", item.getUserCode());
            rowData.put("deptName", item.getDeptName());
            
            // 设置12个月的数据
            for (int month = 1; month <= 12; month++) {
                BigDecimal actualHours = item.getMonthlyData().get(month + "月份实际工时");
                BigDecimal requiredHours = item.getMonthlyData().get(month + "月份可登记工时");
                BigDecimal varianceHours = item.getMonthlyData().get(month + "月份差值");
                
                rowData.put("month" + month + "Actual", actualHours != null ? actualHours : BigDecimal.ZERO);
                rowData.put("month" + month + "Required", requiredHours != null ? requiredHours : BigDecimal.ZERO);
                rowData.put("month" + month + "Variance", varianceHours != null ? varianceHours : BigDecimal.ZERO);
            }
            
            // 设置年度总计
            if (item.getYearlyTotal() != null) {
                rowData.put("yearTotalActual", item.getYearlyTotal().getTotalActualHours());
                rowData.put("yearTotalRequired", item.getYearlyTotal().getTotalRequiredHours());
                rowData.put("yearTotalVariance", item.getYearlyTotal().getTotalVarianceHours());
            } else {
                rowData.put("yearTotalActual", BigDecimal.ZERO);
                rowData.put("yearTotalRequired", BigDecimal.ZERO);
                rowData.put("yearTotalVariance", BigDecimal.ZERO);
            }
            
            dataList.add(rowData);
        }
        
        // 创建导出参数
        String title = year + "年人员工时差值分析报表" + (includeHoliday ? "(含假日)" : "(不含假日)");
        ExportParams exportParams = new ExportParams(title, "人员差值分析");
        exportParams.setCreateHeadRows(true);
        
        // 生成Excel
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, dataList);
        workbook.write(outputStream);
        workbook.close();
    }
}