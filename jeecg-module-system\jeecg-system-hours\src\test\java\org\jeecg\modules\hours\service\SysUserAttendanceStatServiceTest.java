package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.BaseTest;
import org.jeecg.modules.hours.entity.SysUserAttendanceStat;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 用户考勤统计服务测试类
 */
public class SysUserAttendanceStatServiceTest extends BaseTest {

    @Autowired
    private ISysUserAttendanceStatService sysUserAttendanceStatService;

    @Test
    public void testSaveOrUpdateBatch() {
        // 准备测试数据
        List<SysUserAttendanceStat> stats = new ArrayList<>();
        Date testDate = new Date();

        // 第一条记录 - 新插入
        SysUserAttendanceStat stat1 = new SysUserAttendanceStat();
        stat1.setUnionId("test_union_id_1");
        stat1.setUserId("test_user_id_1");
        stat1.setStatDate(testDate);
        stat1.setWorkHours(new BigDecimal(480)); // 8小时 = 480分钟
        stat1.setOverWorkHours(new BigDecimal(60)); // 1小时加班 = 60分钟
        stat1.setShift("白班");
        stats.add(stat1);

        // 第二条记录 - 新插入
        SysUserAttendanceStat stat2 = new SysUserAttendanceStat();
        stat2.setUnionId("test_union_id_2");
        stat2.setUserId("test_user_id_2");
        stat2.setStatDate(testDate);
        stat2.setWorkHours(new BigDecimal(480));
        stat2.setOverWorkHours(new BigDecimal(0));
        stat2.setShift("白班");
        stats.add(stat2);

        // 执行批量保存
        long startTime = System.currentTimeMillis();
        boolean result = sysUserAttendanceStatService.saveOrUpdateBatch(stats);
        long endTime = System.currentTimeMillis();

        // 验证结果
        assert result : "批量保存应该成功";
        System.out.println("首次批量保存耗时：" + (endTime - startTime) + "ms");

        // 修改第一条记录的数据，再次保存（应该是更新）
        stat1.setWorkHours(new BigDecimal(500)); // 修改工时
        stat1.setOverWorkHours(new BigDecimal(120)); // 修改加班时间

        // 添加一条新记录
        SysUserAttendanceStat stat3 = new SysUserAttendanceStat();
        stat3.setUnionId("test_union_id_3");
        stat3.setUserId("test_user_id_3");
        stat3.setStatDate(testDate);
        stat3.setWorkHours(new BigDecimal(450));
        stat3.setOverWorkHours(new BigDecimal(30));
        stat3.setShift("夜班");

        List<SysUserAttendanceStat> mixedStats = new ArrayList<>();
        mixedStats.add(stat1); // 更新
        mixedStats.add(stat3); // 插入

        // 执行混合操作（插入+更新）
        startTime = System.currentTimeMillis();
        boolean mixedResult = sysUserAttendanceStatService.saveOrUpdateBatch(mixedStats);
        endTime = System.currentTimeMillis();

        // 验证更新结果
        assert mixedResult : "混合操作应该成功";
        System.out.println("混合操作耗时：" + (endTime - startTime) + "ms");

        // 验证数据是否正确更新
        SysUserAttendanceStat updated = sysUserAttendanceStatService.getByUserAndDate(
                stat1.getUnionId(), stat1.getStatDate());

        assert updated != null : "应该能查询到更新后的记录";
        assert updated.getWorkHours().equals(500) : "工时应该已更新为500";
        assert updated.getOverWorkHours().equals(120) : "加班时间应该已更新为120";

        // 验证新插入的记录
        SysUserAttendanceStat inserted = sysUserAttendanceStatService.getByUserAndDate(
                stat3.getUnionId(), stat3.getStatDate());

        assert inserted != null : "应该能查询到新插入的记录";
        assert inserted.getWorkHours().equals(450) : "新记录工时应该为450";
        assert "夜班".equals(inserted.getShift()) : "新记录班次应该为夜班";

        System.out.println("测试通过：优化后的批量保存或更新功能正常，性能显著提升");
    }

    @Test
    public void testPerformanceComparison() {
        // 测试大批量数据的性能
        List<SysUserAttendanceStat> largeStats = new ArrayList<>();
        Date baseDate = new Date();

        // 生成1000条测试数据
        for (int i = 0; i < 1000; i++) {
            SysUserAttendanceStat stat = new SysUserAttendanceStat();
            stat.setUnionId("perf_test_union_" + i);
            stat.setUserId("perf_test_user_" + i);
            stat.setStatDate(new Date(baseDate.getTime() + i * 24 * 60 * 60 * 1000L)); // 每天递增
            stat.setWorkHours(new BigDecimal(480));
            stat.setOverWorkHours(new BigDecimal(i % 3 == 0 ? 60 : 0)); // 每3条记录有加班
            stat.setShift(i % 2 == 0 ? "白班" : "夜班");
            largeStats.add(stat);
        }

        // 执行大批量保存
        long startTime = System.currentTimeMillis();
        boolean result = sysUserAttendanceStatService.saveOrUpdateBatch(largeStats);
        long endTime = System.currentTimeMillis();

        assert result : "大批量保存应该成功";
        System.out.println("1000条记录批量保存耗时：" + (endTime - startTime) + "ms");
        System.out.println("平均每条记录耗时：" + (endTime - startTime) / 1000.0 + "ms");

        // 修改部分数据再次保存（测试更新性能）
        for (int i = 0; i < 500; i++) {
            largeStats.get(i).setWorkHours(new BigDecimal(500 + i % 100)); // 修改工时
        }

        startTime = System.currentTimeMillis();
        boolean updateResult = sysUserAttendanceStatService.saveOrUpdateBatch(largeStats);
        endTime = System.currentTimeMillis();

        assert updateResult : "大批量更新应该成功";
        System.out.println("1000条记录批量更新耗时：" + (endTime - startTime) + "ms");
        System.out.println("优化后的批量操作性能测试完成");
    }
}
