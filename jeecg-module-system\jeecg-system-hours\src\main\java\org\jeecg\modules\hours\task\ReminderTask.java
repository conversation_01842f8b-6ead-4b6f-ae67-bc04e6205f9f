package org.jeecg.modules.hours.task;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import org.jeecg.modules.hours.constant.ReminderConstant;
import org.jeecg.modules.hours.entity.FsSysReminderConfig;
import org.jeecg.modules.hours.service.IFsSysReminderConfigService;
import org.jeecg.modules.hours.utils.ReminderUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 登记提醒定时任务
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Component
public class ReminderTask {

    @Autowired
    private IFsSysReminderConfigService reminderConfigService;

    // 线程池用于异步执行提醒任务
    private final Executor reminderExecutor = Executors.newFixedThreadPool(
        ReminderConstant.Schedule.REMINDER_THREAD_POOL_SIZE
    );

    /**
     * 定时执行提醒任务
     * 每分钟执行一次，检查是否有需要发送的提醒
     */
//    @Scheduled(cron = "0 * * * * ?") // 每分钟的第0秒执行
    public void executeReminderTask() {
        log.debug("开始执行登记提醒任务检查");
        
        try {
            // 查询需要执行的提醒配置
            List<FsSysReminderConfig> configsToExecute = reminderConfigService.queryReminderConfigsToExecute();
            
            if (configsToExecute.isEmpty()) {
                log.debug("当前时间无需要执行的提醒配置");
                return;
            }
            
            log.info("找到 {} 个需要执行的提醒配置", configsToExecute.size());
            
            // 异步执行提醒任务
            for (FsSysReminderConfig config : configsToExecute) {
                CompletableFuture.runAsync(() -> {
                    executeReminder(config);
                }, reminderExecutor);
            }
            
        } catch (Exception e) {
            log.error("执行登记提醒任务时发生异常", e);
        }
    }

    /**
     * 执行单个提醒
     * 
     * @param config 提醒配置
     */
    private void executeReminder(FsSysReminderConfig config) {
        try {
            log.info("执行提醒任务 - 项目: {}, 类型: {}, 时间: {}", 
                    config.getProjectKey(), config.getReminderType(), config.getReminderTime());
            
            // 生成提醒消息
            String message = ReminderUtils.generateReminderMessage(config);
            String title = ReminderConstant.MessageTemplate.REMINDER_TITLE;
            
            // 发送提醒（这里可以根据实际需求实现不同的提醒方式）
            sendReminder(config, title, message);
            
            log.info("提醒任务执行成功 - 项目: {}, 配置ID: {}", 
                    config.getProjectKey(), config.getId());
            
        } catch (Exception e) {
            log.error("执行提醒任务失败 - 项目: {}, 配置ID: {}", 
                    config.getProjectKey(), config.getId(), e);
        }
    }

    /**
     * 发送提醒
     * 
     * @param config 提醒配置
     * @param title 提醒标题
     * @param message 提醒消息
     */
    private void sendReminder(FsSysReminderConfig config, String title, String message) {
        // TODO: 根据实际需求实现提醒发送逻辑
        // 可以实现以下提醒方式：
        // 1. 飞书消息通知
        // 2. 邮件通知
        // 3. 系统内消息
        // 4. 短信通知
        // 5. 企业微信通知
        
        log.info("发送提醒 - 项目: {}, 标题: {}, 消息: {}", 
                config.getProjectKey(), title, message);
        
        // 示例：发送飞书消息通知
        sendFeishuNotification(config, title, message);
        
        // 示例：发送系统内消息
        sendSystemNotification(config, title, message);
    }

    /**
     * 发送飞书消息通知
     * 
     * @param config 提醒配置
     * @param title 提醒标题
     * @param message 提醒消息
     */
    private void sendFeishuNotification(FsSysReminderConfig config, String title, String message) {
        try {
            // TODO: 实现飞书消息通知逻辑
            // 1. 获取项目成员列表
            // 2. 构建飞书消息内容
            // 3. 调用飞书API发送消息
            
            log.debug("发送飞书通知 - 项目: {}, 消息: {}", config.getProjectKey(), message);
            
        } catch (Exception e) {
            log.error("发送飞书通知失败 - 项目: {}", config.getProjectKey(), e);
        }
    }

    /**
     * 发送系统内消息
     * 
     * @param config 提醒配置
     * @param title 提醒标题
     * @param message 提醒消息
     */
    private void sendSystemNotification(FsSysReminderConfig config, String title, String message) {
        try {
            // TODO: 实现系统内消息通知逻辑
            // 1. 获取项目成员列表
            // 2. 创建系统消息记录
            // 3. 推送到前端（WebSocket等）
            
            log.debug("发送系统通知 - 项目: {}, 消息: {}", config.getProjectKey(), message);
            
        } catch (Exception e) {
            log.error("发送系统通知失败 - 项目: {}", config.getProjectKey(), e);
        }
    }

    /**
     * 手动触发提醒任务（用于测试）
     * 
     * @param projectKey 项目标识
     */
    public void manualTriggerReminder(String projectKey) {
        log.info("手动触发提醒任务 - 项目: {}", projectKey);
        
        try {
            List<FsSysReminderConfig> configs = reminderConfigService.queryByProjectKeyAndStatus(
                projectKey, ReminderConstant.Status.ENABLED
            );
            
            for (FsSysReminderConfig config : configs) {
                CompletableFuture.runAsync(() -> {
                    executeReminder(config);
                }, reminderExecutor);
            }
            
            log.info("手动触发提醒任务完成 - 项目: {}, 配置数量: {}", projectKey, configs.size());
            
        } catch (Exception e) {
            log.error("手动触发提醒任务失败 - 项目: {}", projectKey, e);
        }
    }

    /**
     * 测试提醒配置
     * 
     * @param configId 配置ID
     */
    public void testReminderConfig(String configId) {
        log.info("测试提醒配置 - 配置ID: {}", configId);
        
        try {
            FsSysReminderConfig config = reminderConfigService.getById(configId);
            if (config == null) {
                log.warn("提醒配置不存在 - 配置ID: {}", configId);
                return;
            }
            
            if (!ReminderConstant.Status.ENABLED.equals(config.getStatus())) {
                log.warn("提醒配置未启用 - 配置ID: {}", configId);
                return;
            }
            
            executeReminder(config);
            
            log.info("测试提醒配置完成 - 配置ID: {}", configId);
            
        } catch (Exception e) {
            log.error("测试提醒配置失败 - 配置ID: {}", configId, e);
        }
    }

    /**
     * 获取提醒任务统计信息
     * 
     * @return 统计信息
     */
    public String getReminderTaskStatistics() {
        try {
            List<FsSysReminderConfig> allConfigs = reminderConfigService.list();
            long enabledCount = allConfigs.stream()
                .filter(config -> ReminderConstant.Status.ENABLED.equals(config.getStatus()))
                .count();
            
            return String.format("提醒配置总数: %d, 启用数量: %d, 禁用数量: %d", 
                allConfigs.size(), enabledCount, allConfigs.size() - enabledCount);
                
        } catch (Exception e) {
            log.error("获取提醒任务统计信息失败", e);
            return "获取统计信息失败";
        }
    }
}
