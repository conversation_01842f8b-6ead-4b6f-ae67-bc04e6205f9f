package org.jeecg.modules.hours.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 工时记录查询结果视图对象
 * @Author: jeecg-boot
 * @Date: 2023-07-01
 * @Version: V1.0
 */
@Data
public class HourRecordVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /** 主键 */
    private String id;
    
//    /** 登记用户ID */
//    private String userId;
//    
//    /** 登记用户姓名 */
//    private String userName;
//    
//    /** 工作日期 */
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    private Date workDate;
//    
//    /** 登记工时数量 */
//    private BigDecimal hours;
    
    /**
	 * 用户ID
	 */
	@Excel(name = "用户ID", width = 15)
	@ApiModelProperty(value = "用户ID")
	private String userId;

	/**
	 * 用户姓名
	 */
	@Excel(name = "用户姓名", width = 15)
	@ApiModelProperty(value = "用户姓名")
	private String userName;

	/**
	 * 工作月份(格式：yyyy-MM)
	 */
	@Excel(name = "工作月份", width = 15)
	@ApiModelProperty(value = "工作月份(格式：yyyy-MM)")
	private String workMonth;

	/**
	 * 工作周期开始日期
	 */
	@Excel(name = "工作周期开始日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "工作周期开始日期")
	private Date workPeriodStart;

	/**
	 * 工作周期结束日期
	 */
	@Excel(name = "工作周期结束日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "工作周期结束日期")
	private Date workPeriodEnd;

	/**
	 * 总工时(小时)
	 */
	@Excel(name = "总工时", width = 15)
	@ApiModelProperty(value = "总工时(小时)")
	private BigDecimal hours;
    
    /** 工作描述 */
    private String description;
    
//    /** 关联空间ID */
//    private String projectId;
//    
//    /** 工作项ID */
//    private String workItemId;
//    
//    /** 工作项名称 */
//    private String workItemName;
//    
//    /** 实例/迭代ID */
//    private String instanceId;
//    
//    /** 实例/迭代名称 */
//    private String instanceName;
//    
//    /** 节点ID */
//    private String nodeId;
//    
//    /** 节点名称 */
//    private String nodeName;
//    
//    /** 任务ID */
//    private String taskId;
//    
//    /** 任务名称 */
//    private String taskName;
    
    /** 状态：0-待提交 1-审批中 2-已完成 3-审批驳回 */
    private Integer status;
    
    /** 状态名称 */
    private String statusName;
    

	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createBy;

	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	@Dict(dictTable = "fs_user", dicText = "name", dicCode = "user_key")
	private String updateBy;
	
//    /** 创建时间 */
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date createdAt;
    
    /** 提交时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;
    
    /** 审批人姓名 */
    private String approverName;
    
    /** 驳回原因 */
    private String rejectReason;
    
    /** 完整工作路径 */
    private String workPath;
    

    /** 申请人ID */
    @ApiModelProperty(value = "申请人ID")
    private String applicantId;

    /** 申请人姓名 */
    @ApiModelProperty(value = "申请人姓名")
    private String applicantName;
    
    /** 审批批次申请ID */
    @ApiModelProperty(value = "审批批次申请ID")
    private Long applyId;
    
    /** 申请时间 */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;
    
} 