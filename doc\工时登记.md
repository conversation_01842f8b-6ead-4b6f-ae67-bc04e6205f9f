# 工时登记设计文档

## 1. 功能概述

工时登记模块主要用于管理用户的工作时间记录，允许用户按周期批量记录工作项目的工时信息。该模块基于飞书考勤数据，以百分比形式登记工时，系统自动转换为实际工时。用户可以选择所属月份和填写周期（通常是一周），在工时分配表格中直接登记各项目的工时百分比，并与PLM项目系统集成，允许用户选择与自己相关的项目进行工时登记。

## 2. 数据模型

### 2.1 数据表结构

#### `hour_record` 表 - 工时记录表
```sql
CREATE TABLE `hour_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `work_month` varchar(7) NOT NULL COMMENT '所属月份，格式YYYY.MM',
  `work_period_start` date NOT NULL COMMENT '工作周期开始日期',
  `work_period_end` date NOT NULL COMMENT '工作周期结束日期',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-草稿，1-已提交',
  `creator_id` varchar(64) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(64) NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_work_month` (`work_month`),
  KEY `idx_creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工时记录主表';

CREATE TABLE `hour_record_detail` (

### 接口设计

#### 1. 获取工作周期接口
```
GET /hapi/hours/hourRecord/availableDates
请求参数：
- month: String (必填) 格式YYYY.MM，如2025.04

响应结果：
{
  "code": 0,
  "data": [
    {
      "period": "2025.04.01-2025.04.07", // 周区间，周一至周日
      "dates": [
        {
          "date": "2025.04.01", // 日期
          "weekday": "星期二",   // 星期
          "isRegistrable": true // 是否可登记（TODO: 后续对接考勤系统）
        },
        ...
      ]
    },
    ...
  ],
  "message": "success"
}
```

#### 2. 批量保存接口
```
POST /hapi/hours/hourRecord/batchSave
请求参数：
{
  "workMonth": "2025.04",
  "workPeriodStart": "2025.04.01",
  "workPeriodEnd": "2025.04.07",
  "details": [
    {
      "workDate": "2025.04.01",
      "projectId": "project1",
      "percentage": 50.00,
      "description": "开发功能A"
    },
    ...
  ]
}

响应结果：
{
  "code": 0,
  "data": {"success": true},
  "message": "保存成功"
}
```

### 前端实现方案
1. **所属月份选择**
   - 使用Semi Design的DatePicker.MonthPicker组件
   - 格式化为YYYY.MM格式

2. **填写周期选择**
   - 使用Select组件
   - 根据所选月份调用/hapi/hours/hourRecord/availableDates接口获取周区间
   - 选项格式：YYYY.MM.DD-YYYY.MM.DD（如2025.04.01-2025.04.07）
   - 跨月周处理：直接根据接口返回的日期范围显示，无需特殊处理

3. **工时登记明细表格**
   - 表头：动态生成日期列（根据所选周区间）
   - 每行：工作内容（Cascader组件）、各日期百分比输入（InputNumber组件）
   - 校验：单日工作内容总和占比不得超过100%
   - 单元格状态：根据isRegistrable显示/禁用输入框
   - 非工作日：不添加特殊样式标识，直接禁用输入

4. **TODO标记**
   - 考勤工时计算：实际工时 = 考勤工时 × 百分比（待对接考勤系统）
   - 可登记状态：根据考勤结果确定（待对接考勤系统）
   - 非工作日校验：后端保存接口需添加校验逻辑（TODO）
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hour_record_id` bigint(20) NOT NULL COMMENT '关联的hour_record表ID',
  `work_date` date NOT NULL COMMENT '工作日期',
  `project_id` varchar(64) NOT NULL COMMENT '项目ID',
  `project_name` varchar(128) NOT NULL COMMENT '项目名称',
  `percentage` decimal(5,2) NOT NULL COMMENT '工作占比（百分比）',
  `actual_hours` decimal(5,2) DEFAULT NULL COMMENT '实际工时（考勤工时×百分比）',
  `description` varchar(512) DEFAULT NULL COMMENT '工作描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_hour_record_id` (`hour_record_id`),
  KEY `idx_work_date` (`work_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工时记录明细表';
  `id` varchar(36) NOT NULL COMMENT '主键',
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户姓名',
  `work_month` varchar(7) NOT NULL COMMENT '工作月份(格式：yyyy-MM)',
  `work_period_start` date NOT NULL COMMENT '工作周期开始日期',
  `work_period_end` date NOT NULL COMMENT '工作周期结束日期',
  `hours` decimal(5,2) NOT NULL COMMENT '总工时(小时)',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态(0:待提交, 1:审批中, 2:已通过, 3:已驳回)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_user_period` (`user_id`,`work_period_start`,`work_period_end`),
  KEY `idx_work_month` (`work_month`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工时记录表';
```

#### `hour_record_detail` 表 - 工时记录明细表
```sql
CREATE TABLE `hour_record_detail` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `record_id` varchar(36) NOT NULL COMMENT '关联工时记录ID',
  `work_date` date NOT NULL COMMENT '工作日期',
  `project_id` varchar(50) NOT NULL COMMENT '项目ID',
  `project_name` varchar(100) DEFAULT NULL COMMENT '项目名称',
  `description` varchar(500) DEFAULT NULL COMMENT '工作描述',
  `percentage` decimal(5,2) NOT NULL COMMENT '工时百分比',
  `actual_hours` decimal(5,2) NOT NULL COMMENT '实际工时(小时)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_work_date` (`work_date`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工时记录明细表';
```

#### `plm_project` 表 - PLM项目基本信息
该表结构参考项目人员数据维护文档中的定义。

## 3. 功能设计

### 3.1 功能列表

1. **工时登记列表页**：
   - 支持按工作日期范围、项目等条件筛选
   - 分页展示工时记录信息
   - 显示工时记录的基本信息和状态
   - 提供批量提交、编辑、删除功能

2. **批量工时登记**：
   - 支持选择所属月份，可使用semi design提供的控件
   - 支持选择填写周期（通常是一周，周跨月时为本周开始到月末，列表数据从接口获取，根据月份动态生成）
   - 提供工时分配表格（纵向列为可登记工时的日期）
   - 以百分比形式登记工时，每日总和不超过100%
   - 支持批量保存和提交

### 3.2 权限设计

| 功能点 | 普通用户 | 项目经理 | 管理员 |
|-------|---------|---------|-------|
| 查看个人工时记录 | ✓ | ✓ | ✓ |
| 批量登记个人工时 | ✓ | ✓ | ✓ |
| 查看项目成员工时 | ✗ | ✓ | ✓ |
| 审批工时记录 | ✗ | ✓ | ✓ |
| 查看统计报表 | ✗ | ✓ | ✓ |

### 3.3 流程设计

#### 批量工时登记流程
1. 用户进入工时登记列表页
2. 用户点击"工时登记"按钮
3. 系统弹出批量工时登记表单
4. 用户选择所属月份
5. 用户选择填写周期（如一周，或周跨月时选择本周开始到月末）
6. 系统展示该周期内可登记工时的日期（飞书考勤工作日时长>0的日期）
7. 用户选择项目并填写各日期的工时百分比
8. 系统实时验证每日百分比总和是否超过100%
9. 用户点击保存或提交按钮
10. 系统根据考勤工时将百分比转换为实际工时，保存记录并返回列表页

## 4. 接口设计

### 4.1 后端接口

#### 工时记录列表查询接口
- **URL**: `/hapi/hours/hourRecord/list`
- **Method**: GET
- **请求参数**:
  ```json
  {
    "pageNo": 1,
    "pageSize": 10,
    "startDate": "2023-06-01",
    "endDate": "2023-06-07",
    "projectId": "",
    "status": 0
  }
  ```
- **响应结果**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": {
      "records": [
        {
          "id": "1",
          "userId": "user1",
          "userName": "张三",
          "workMonth": "2023-06",
          "workPeriodStart": "2023-06-01",
          "workPeriodEnd": "2023-06-07",
          "hours": 40.0,
          "status": 0,
          "createTime": "2023-06-01 10:00:00",
          "updateTime": "2023-06-01 10:00:00",
          "projects": [
            {
              "projectId": "PRJ001",
              "projectName": "示例项目1",
              "hours": 24.0
            },
            {
              "projectId": "PRJ002",
              "projectName": "示例项目2",
              "hours": 16.0
            }
          ]
        }
      ],
      "total": 100,
      "size": 10,
      "current": 1,
      "pages": 10
    }
  }
  ```

#### 获取可登记工时日期接口
- **URL**: `/hapi/hours/hourRecord/availableDates`
- **Method**: GET
- **请求参数**:
  ```json
  {
    "month": "2023-06",
    "startDate": "2023-06-01",
    "endDate": "2023-06-07"
  }
  ```
- **响应结果**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": [
      {
        "date": "2023-06-01",
        "attendanceHours": 8.0,
        "isWorkday": true
      },
      {
        "date": "2023-06-02",
        "attendanceHours": 8.0,
        "isWorkday": true
      },
      {
        "date": "2023-06-05",
        "attendanceHours": 8.0,
        "isWorkday": true
      },
      {
        "date": "2023-06-06",
        "attendanceHours": 8.0,
        "isWorkday": true
      },
      {
        "date": "2023-06-07",
        "attendanceHours": 8.0,
        "isWorkday": true
      }
    ]
  }
  ```

#### 批量保存工时记录接口
- **URL**: `/hapi/hours/hourRecord/batchSave`
- **Method**: POST
- **请求参数**:
  ```json
  {
    "workMonth": "2023-06",
    "startDate": "2023-06-01",
    "endDate": "2023-06-07",
    "projects": [
      {
        "projectId": "PRJ001",
        "projectName": "示例项目1",
        "description": "开发功能模块",
        "details": [
          {
            "date": "2023-06-01",
            "percentage": 50.0
          },
          {
            "date": "2023-06-02",
            "percentage": 60.0
          },
          {
            "date": "2023-06-05",
            "percentage": 70.0
          }
        ]
      },
      {
        "projectId": "PRJ002",
        "projectName": "示例项目2",
        "description": "测试功能模块",
        "details": [
          {
            "date": "2023-06-01",
            "percentage": 50.0
          },
          {
            "date": "2023-06-02",
            "percentage": 40.0
          },
          {
            "date": "2023-06-05",
            "percentage": 30.0
          }
        ]
      }
    ]
  }
  ```
- **响应结果**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": true
  }
  ```

#### 批量提交工时记录接口
- **URL**: `/hapi/hours/hourRecord/batchSubmit`
- **Method**: POST
- **请求参数**:
  ```json
  ["1", "2", "3"]
  ```
- **响应结果**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": true
  }
  ```

#### 删除工时记录接口
- **URL**: `/hapi/hours/hourRecord/delete`
- **Method**: DELETE
- **请求参数**:
  ```json
  {
    "id": "1"
  }
  ```
- **响应结果**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": true
  }
  ```

#### 获取用户相关项目接口
- **URL**: `/api/project/userProjects`
- **Method**: GET
- **请求参数**:
  ```json
  {}
  ```
- **响应结果**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": [
      {
        "id": "1",
        "projectCode": "PRJ001",
        "projectName": "示例项目1",
        "projectStatus": "AO"
      }
    ]
  }
  ```

### 4.2 前端组件

#### 工时登记列表组件
- 展示工时记录列表
- 支持筛选和分页
- 提供"工时登记"、"编辑"、"删除"、"提交"按钮

#### 批量工时登记组件
- 提供月份选择器
- 提供周期选择器（开始日期和结束日期）
- 展示周期内可登记工时的日期表格
- 提供项目选择器（来自PLM项目和自定义项目）
- 允许填写每日工时百分比，实时验证总和不超过100%
- 批量保存和提交按钮

## 5. 界面设计

### 5.1 工时登记列表页面

主要元素：
- 搜索区域：工作日期范围、项目等筛选条件
- 操作区域：工时登记按钮
- 列表区域：展示工时记录信息
- 操作区域：编辑、删除、提交按钮

### 5.2 批量工时登记表单

主要元素：
- 所属月份选择器
- 填写周期选择器（开始日期和结束日期）
- 项目选择区域（支持添加多个项目行）
- 工时分配表格：
  - 纵向列为可登记工时的日期（工作日时长>0的日期）
  - 横向行为不同项目
  - 单元格填写百分比值
  - 显示每日百分比总和，超过100%时提示错误
- 底部按钮区域：批量保存、批量提交、取消

## 6. 开发计划

### 6.1 开发任务拆分

1. 数据库表设计与创建
   - 创建hour_record表，包含工作月份和周期字段
   - 创建hour_record_detail表，存储项目信息、百分比和实际工时
2. 后端接口开发
   - 工时记录列表查询接口
   - 获取可登记工时日期接口（集成飞书考勤）
   - 批量保存工时记录接口
   - 批量提交工时记录接口
   - 删除工时记录接口
   - 获取用户相关项目接口
3. 前端页面开发
   - 工时登记列表组件
   - 批量工时登记组件
   - 百分比验证和转换逻辑
   - 集成API调用

### 6.2 开发排期

| 任务 | 工作量(人天) | 负责人 |
|-----|------------|-------|
| 数据库表设计与创建 | 0.5 | DBA |
| 后端接口开发 | 3 | 后端开发 |
| 前端页面开发 | 3 | 前端开发 |
| 联调测试 | 1 | 全员 |
| 总计 | 7.5 | - |

## 7. 注意事项

1. **项目来源**：
   - 工时登记时的项目来源于plm_project表中与当前用户相关的项目
   - 同时支持用户自定义添加项目

2. **工时验证**：
   - 每日工时百分比总和不能超过100%
   - 只有飞书考勤中工作日时长>0的日期才可登记工时
   - 系统自动将百分比转换为实际工时

3. **周跨月处理**：
   - 当选择的周期跨月时，系统应自动调整为本周开始日期到本月月末日期
   - 下月的工时需要在下月进行登记

4. **数据一致性**：
   - 确保批量操作的原子性
   - 防止重复提交
   - 确保百分比和实际工时的一致性转换

5. **性能优化**：
   - 为常用查询添加索引
   - 批量操作时注意性能

6. **用户体验**：
   - 提供友好的错误提示
   - 实时验证百分比总和
   - 支持快捷操作和键盘导航
   - 记住用户最近使用的项目和工作内容