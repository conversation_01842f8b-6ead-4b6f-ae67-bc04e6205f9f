package org.jeecg.modules.plm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.plm.entity.PlmProject;
import org.jeecg.modules.plm.mapper.PlmProjectMapper;
import org.jeecg.modules.plm.service.IPlmProjectService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * PLM项目Service实现类
 */
@Service
@Slf4j
public class PlmProjectServiceImpl extends ServiceImpl<PlmProjectMapper, PlmProject> implements IPlmProjectService {

    @Override
    public IPage<PlmProject> queryProjectList(Page<PlmProject> page) {
        return baseMapper.queryProjectList(page);
    }

    @Override
    public PlmProject queryByProjectCode(String projectCode) {
        return baseMapper.queryByProjectCode(projectCode);
    }
    
    @Override
    public PlmProject getByProjectCode(String projectCode) {
        if (projectCode == null || projectCode.isEmpty()) {
            return null;
        }
        
        LambdaQueryWrapper<PlmProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlmProject::getProjectCode, projectCode);
        return getOne(queryWrapper);
    }


    @Override
    public List<Map<String, Object>> statisticsByStatus() {
        return baseMapper.statisticsByStatus();
    }

} 