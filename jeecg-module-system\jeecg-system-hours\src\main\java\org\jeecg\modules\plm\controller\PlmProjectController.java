package org.jeecg.modules.plm.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.service.IFsUserService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.jeecg.modules.hours.utils.UserInfoUtils;
import org.jeecg.modules.plm.entity.PlmProject;
import org.jeecg.modules.plm.entity.PlmProjectMember;
import org.jeecg.modules.plm.entity.PlmSyncLog;
import org.jeecg.modules.plm.job.PlmSyncJob;
import org.jeecg.modules.plm.service.IPlmProjectMemberService;
import org.jeecg.modules.plm.service.IPlmProjectService;
import org.jeecg.modules.plm.service.IPlmSyncLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aliyuncs.kms.model.v20160120.UpdateSecretRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * PLM项目Controller
 */
@Slf4j
@Api(tags = "PLM项目管理")
@RestController
@RequestMapping("/hapi/plm/project")
public class PlmProjectController {

	@Autowired
	private IPlmProjectService plmProjectService;

	@Autowired
	private IPlmProjectMemberService plmProjectMemberService;

	@Autowired
	private IPlmSyncLogService plmSyncLogService;
	@Autowired
	private PlmSyncJob plmSyncJob;
	@Autowired
	private UserInfoUtils userInfoUtils;
	@Autowired
	private IFsUserService fsUserService;

	/**
	 * 分页列表查询
	 */
	@AutoLog(value = "PLM项目-分页列表查询")
	@ApiOperation(value = "PLM项目-分页列表查询", notes = "PLM项目-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PlmProject>> queryPageList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,HttpServletRequest req) {
	     // 获取工作空间标识
        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
        if (StringUtils.isBlank(projectKey)) {
        	throw new JeecgBootException("未提供有效的工作空间标识");
        }
        
		Page<PlmProject> page = new Page<>(pageNo, pageSize);
		IPage<PlmProject> pageList = plmProjectService.queryProjectList(page);
		if(CollectionUtils.isNotEmpty(pageList.getRecords())) {
			// 收集所有项目ID
			List<String> projectIds = pageList.getRecords().stream()
				.map(PlmProject::getId)
				.collect(Collectors.toList());
			
			// 一次性查询所有项目的成员
			List<PlmProjectMember> allMembers = new ArrayList<>();
			if(CollectionUtils.isNotEmpty(projectIds)) {
				// 查询这些项目的所有成员
				allMembers = plmProjectMemberService.queryByProjectIds(projectIds);
			}
			
			// 按项目ID分组成员
			Map<String, List<PlmProjectMember>> projectMembersMap = allMembers.stream()
				.collect(Collectors.groupingBy(PlmProjectMember::getProjectId));
			
			// 为每个项目设置成员名称
			for (PlmProject project : pageList.getRecords()) {
				List<PlmProjectMember> members = projectMembersMap.get(project.getId());
				if (CollectionUtils.isNotEmpty(members)) {
					// 提取成员名称并用逗号分隔
					String memberNames = members.stream()
						.map(PlmProjectMember::getUserName)
						.filter(name -> name != null && !name.isEmpty())
						.collect(Collectors.joining(", "));
					project.setMemberNames(memberNames);
				}
			}
		}
		
		return Result.OK(pageList);
	}
	
	
	@AutoLog(value = "PLM项目列表")
	@ApiOperation(value = "PLM项目列表", notes = "PLM项目列表")
	@GetMapping(value = "/userProjectList")
	public Result<List<PlmProject>> userProjectList(HttpServletRequest req) {
		FsUser loginUser = userInfoUtils.getLoginUser();
		QueryWrapper<PlmProject> queryWrapper = new QueryWrapper<PlmProject>();
		queryWrapper.and(wrapper -> wrapper
			.eq("manager_id", loginUser.getUserKey())
			.or()
			.exists("SELECT 1 FROM plm_project_member WHERE project_id = plm_project.id AND user_id = '" + loginUser.getUserKey() + "'"));
		
		List<PlmProject> pageList = plmProjectService.list(queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 获取项目详情
	 */
	@AutoLog(value = "PLM项目-详情")
	@ApiOperation(value = "PLM项目-详情", notes = "PLM项目-详情")
	@GetMapping(value = "/detail")
	public Result<Map<String, Object>> detail(@RequestParam(name = "id") String id) {
		PlmProject project = plmProjectService.getById(id);
		if (project == null) {
			return Result.error("未找到对应项目");
		}

		List<PlmProjectMember> members = plmProjectMemberService.queryByProjectId(id);

		Map<String, Object> result = new HashMap<>();
		result.put("project", project);
		result.put("members", members);

		return Result.OK(result);
	}

	/**
	 * 同步PLM项目数据
	 */
	@AutoLog(value = "PLM项目-同步项目数据")
	@ApiOperation(value = "PLM项目-同步项目数据", notes = "PLM项目-同步项目数据")
	@GetMapping(value = "/sync")
	public Result<Map<String, Object>> syncProjects() {

		plmSyncJob.initSync();
		return Result.OK("同步成功");

	}

	/**
	 * 增量同步PLM项目数据
	 */
	@AutoLog(value = "PLM项目-增量同步项目数据")
	@ApiOperation(value = "PLM项目-增量同步项目数据", notes = "PLM项目-增量同步项目数据")
	@GetMapping(value = "/syncIncremental")
	public Result<Map<String, Object>> syncIncrementalProjects() {

		plmSyncJob.incrementalSync();

		return Result.OK("同步成功");
	}

	/**
	 * 获取同步日志
	 */
	@AutoLog(value = "PLM项目-获取同步日志")
	@ApiOperation(value = "PLM项目-获取同步日志", notes = "PLM项目-获取同步日志")
	@GetMapping(value = "/syncLogs")
	public Result<IPage<PlmSyncLog>> getSyncLogs(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
		Page<PlmSyncLog> page = new Page<>(pageNo, pageSize);
		IPage<PlmSyncLog> pageList = plmSyncLogService.querySyncLogList(page, null, null);
		return Result.OK(pageList);
	}

	/**
	 * 获取项目统计信息
	 */
	@AutoLog(value = "PLM项目-获取项目统计信息")
	@ApiOperation(value = "PLM项目-获取项目统计信息", notes = "PLM项目-获取项目统计信息")
	@GetMapping(value = "/statistics")
	public Result<Map<String, Object>> getStatistics() {
		Map<String, Object> result = new HashMap<>();

		// 获取项目状态统计
		List<Map<String, Object>> statusStats = plmProjectService.statisticsByStatus();
		result.put("statusStats", statusStats);

		return Result.OK(result);
	}

	/**
	 * 保存项目信息（更新项目经理、成员和备注）
	 */
	@AutoLog(value = "PLM项目-保存项目信息")
	@ApiOperation(value = "PLM项目-保存项目信息", notes = "PLM项目-保存项目信息")
	@PostMapping(value = "/save")
	public Result<Boolean> saveProject(@RequestBody Map<String, Object> params) {
		// 从请求头获取项目Key
        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
        if (projectKey == null) {
            return Result.error("未提供有效的项目Key");
        }
        // 获取当前用户ID
        String currentUserKey = userInfoUtils.getCurrentUserKey();
        if (currentUserKey == null) {
            return Result.error("无法获取当前用户信息");
        }
        
		String projectId = (String) params.get("projectId");
		String managerId = (String) params.get("managerId");
		String memberIds = (String) params.get("memberIds");
		String remark = (String) params.get("remark");

		if (projectId == null || projectId.isEmpty()) {
			return Result.error("项目ID不能为空");
		}
		
		try {
			// 获取项目信息
			PlmProject project = plmProjectService.getById(projectId);
			if (project == null) {
				return Result.error("未找到对应项目");
			}
			
			// 更新项目经理
			if (managerId != null && !managerId.isEmpty()) {
				project.setManagerId(managerId);
				// 可以根据managerId查询用户名，这里简化处理
				FsUser managerUser = fsUserService.getUserByProjectAndUserKey(projectKey,managerId);
				project.setManagerName(managerUser.getName());
			}
			
			// 更新备注
			if (remark != null) {
				project.setRemark(remark);
			}
			
			project.setUpdateTime(new Date());
			project.setUpdateBy(currentUserKey);
			boolean success = plmProjectService.updateById(project);
			
			if (!success) {
				return Result.error("更新项目信息失败");
			}
			
			// 更新项目成员
			if (memberIds != null && !memberIds.isEmpty()) {
				String[] memberIdArray = memberIds.split(",");
				List<PlmProjectMember> members = new ArrayList<>();
				List<FsUser> userList = fsUserService.getUserByProjectAndUserKeys(projectKey, Arrays.asList(memberIdArray));
				Map<String,FsUser> userMap = new HashMap<String, FsUser>();
				for (FsUser fsUser : userList) {
					userMap.put(fsUser.getUserKey(), fsUser);
				}
				
				for (String memberId : memberIdArray) {
					if (memberId != null && !memberId.trim().isEmpty()) {
						PlmProjectMember member = new PlmProjectMember();
						member.setProjectId(projectId);
						member.setProjectCode(project.getProjectCode());
						member.setUserId(memberId.trim());
						// 可以根据userId查询用户名，这里简化处理
						member.setUserName(userMap.get(memberId).getName());
						members.add(member);
					}
				}
				
				success = plmProjectMemberService.updateProjectMembers(projectId, project.getProjectCode(), members);
				if (!success) {
					return Result.error("更新项目成员失败");
				}
			}
			
			return Result.OK(true);
		} catch (Exception e) {
			log.error("保存项目信息失败", e);
			return Result.error("保存项目信息失败: " + e.getMessage());
		}
	}

	/**
	 * 获取可添加的项目列表，排除已存在的项目
	 * @param excludeProjectIds 逗号分隔的项目ID字符串
	 * @return 可添加的项目列表
	 */
	@GetMapping("/availableForAdd")
	public Result<List<PlmProject>> getAvailableForAdd(@RequestParam(required = false) String excludeProjectIds) {
		final List<String> excludeList = new ArrayList<>();
		if (excludeProjectIds != null && !excludeProjectIds.trim().isEmpty()) {
			excludeList.addAll(Arrays.asList(excludeProjectIds.split(",")));
		}
		// 获取所有项目
		List<PlmProject> allProjects = plmProjectService.list();
		// 过滤掉已存在的项目
		List<PlmProject> result = allProjects.stream()
			.filter(p -> !excludeList.contains(p.getId()))
			.collect(Collectors.toList());
		return Result.OK(result);
	}
}