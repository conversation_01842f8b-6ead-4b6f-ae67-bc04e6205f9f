# 批次号生成功能说明

## 功能概述

在 `IHourRecordApplyService` 中新增了 `generateBatchNo()` 方法，用于生成格式化的批次号。

## 批次号格式

- **格式**: `SP + 日期(yyMMdd) + 6位数字`
- **示例**: `SP25071100001`
  - `SP`: 固定前缀
  - `250711`: 日期（2025年07月11日）
  - `000001`: 6位序号（当天第1个批次）

## 使用方法

```java
@Autowired
private IHourRecordApplyService hourRecordApplyService;

// 生成批次号
String batchNo = hourRecordApplyService.generateBatchNo();
// 返回: SP25071100001

// 在创建 HourRecordApply 时使用
HourRecordApply apply = new HourRecordApply();
apply.setBatchNo(batchNo);
// ... 设置其他字段
hourRecordApplyService.save(apply);
```

## 实现特点

1. **自动递增**: 同一天内序号自动递增
2. **日期前缀**: 每天重新开始计数
3. **格式统一**: 固定13位长度（SP + 6位日期 + 6位序号）
4. **线程安全**: 基于数据库查询确保唯一性

## 数据库变更

- `hour_record_apply` 表的 `batch_no` 字段类型从 `INTEGER` 改为 `VARCHAR(20)`

## 向后兼容

提供了 `generateNumericBatchNo(String recordId)` 方法用于处理历史数据中的数字批次号。

## 测试验证

运行测试类 `HourRecordApplyServiceTest` 可以验证批次号生成功能：

```bash
mvn test -Dtest=HourRecordApplyServiceTest#testGenerateBatchNo
``` 