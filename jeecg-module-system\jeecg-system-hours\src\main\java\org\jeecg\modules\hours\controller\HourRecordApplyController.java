package org.jeecg.modules.hours.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.hours.entity.HourRecord;
import org.jeecg.modules.hours.entity.HourRecordApply;
import org.jeecg.modules.hours.entity.HourRecordDetail;
import org.jeecg.modules.hours.service.IHourRecordApplyService;
import org.jeecg.modules.hours.service.IHourRecordApproverService;
import org.jeecg.modules.hours.service.IHourRecordDetailService;
import org.jeecg.modules.hours.service.IHourRecordLogService;
import org.jeecg.modules.hours.service.IHourRecordService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.jeecg.modules.hours.utils.UserInfoUtils;
import org.jeecg.modules.hours.vo.HourRecordApplyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api(tags = "工时审批管理")
@RestController
@RequestMapping("/hapi/hours/hourRecordApply")
public class HourRecordApplyController extends JeecgController<HourRecordApply, IHourRecordApplyService> {

	@Autowired
	private IHourRecordApplyService hourRecordApplyService;
	@Autowired
	private UserInfoUtils userInfoUtils;
	@Autowired
	private IHourRecordLogService hourRecordLogService;
	@Autowired
	private IHourRecordApproverService hourRecordApproverService;
	@Autowired
	private IHourRecordService hourRecordService;
	@Autowired
	private IHourRecordDetailService hourRecordDetailService;

	/**
	 * 获取工时审批列表
	 *
	 * @param queryParams 查询参数
	 * @return 工时审批记录列表
	 */
	@ApiOperation(value = "获取工时审批列表", notes = "根据条件筛选获取工时审批记录")
	@GetMapping(value = "/approvals")
	public Result<IPage<HourRecordApplyVO>> getWorkHourApprovals(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(name = "status", required = false) String status,
			@RequestParam(name = "tab", required = false) String tab,
			@RequestParam(name = "startDate", required = false) String startDate,
			@RequestParam(name = "endDate", required = false) String endDate,
			@RequestParam(name = "employeeId", required = false) String employeeId) {
		try {
			// 从请求头获取项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			if (projectKey == null) {
				return Result.error("未提供有效的项目Key");
			}

			// 获取当前用户ID
			String currentUserId = userInfoUtils.getCurrentUserKey();
			if (StringUtils.isBlank(currentUserId)) {
				return Result.error("未获取到有效的用户信息");
			}
			
			List<String> employeeIdList = new ArrayList<String>();
			if(StringUtils.isNotEmpty(employeeId)) {
				employeeIdList.addAll(Arrays.asList(employeeId.split(",")));
			}

			// 使用Mapper方法进行分页查询
			Page<HourRecordApplyVO> page = new Page<>(pageNo, pageSize);
			IPage<HourRecordApplyVO> pageList = hourRecordApplyService.queryApprovalRecords(
				page, startDate, endDate, employeeIdList, tab, currentUserId,status);

			// 转换为VO对象
			for (HourRecordApplyVO vo : pageList.getRecords()) {
				// 设置状态名称
				switch (vo.getStatus()) {
				case 0:
					vo.setStatusName("待提交");
					break;
				case 1:
					vo.setStatusName("审批中");
					break;
				case 2:
					vo.setStatusName("已完成");
					break;
				case 3:
					vo.setStatusName("审批驳回");
					break;
				default:
					vo.setStatusName("未知");
				}
				// 兜底设置applyStatusName
				if (vo.getApplyStatusName() == null) {
					switch (vo.getApplyStatus() != null ? vo.getApplyStatus() : -1) {
						case 0: vo.setApplyStatusName("审批中"); break;
						case 1: vo.setApplyStatusName("已通过"); break;
						case 2: vo.setApplyStatusName("已驳回"); break;
						default: vo.setApplyStatusName("未知");
					}
				}
				// 兜底设置dealStatusName
				if (vo.getDealStatusName() == null) {
					switch (vo.getDealStatus() != null ? vo.getDealStatus() : -1) {
						case 0: vo.setDealStatusName("待审批"); break;
						case 1: vo.setDealStatusName("已通过"); break;
						case 2: vo.setDealStatusName("已拒绝"); break;
						case 3: vo.setDealStatusName("已移交"); break;
						default: vo.setDealStatusName("未知");
					}
				}
			}
			return Result.OK(page);

		} catch (Exception e) {
			log.error("获取工时审批列表失败", e);
			return Result.error("获取工时审批列表失败: " + e.getMessage());
		}
	}

	/**
	 * 批量审批工时记录
	 *
	 * @param batchApproveRequest 批量审批请求
	 * @return 操作结果
	 */
	@ApiOperation(value = "批量审批工时记录", notes = "批量审批通过或驳回工时记录")
	@PostMapping(value = "/batch-approve")
	public Result<?> batchApproveWorkHours(@RequestParam(name = "applyIds") String applyIds,
			@RequestParam(name = "action") String action,
			@RequestParam(name = "remarks", required = false) String remarks) {

		if (StringUtils.isBlank(applyIds)) {
			return Result.error("未选择需要审批的记录");
		}

		// 将逗号分隔的ID字符串转换为列表
		List<String> applyIdList = Arrays.asList(applyIds.split(","));

		// 验证操作类型
		if (!"approve".equals(action) && !"reject".equals(action)) {
			return Result.error("无效的操作类型，只支持approve或reject");
		}

		// 驳回时必须提供驳回原因
		if ("reject".equals(action) && StringUtils.isBlank(remarks)) {
			return Result.error("驳回时必须提供驳回原因");
		}

		try {
			// 获取当前操作用户作为审批人
			String approverId = userInfoUtils.getCurrentUserKey();
			String approverName = userInfoUtils.getUserName();

			if (StringUtils.isBlank(approverId)) {
				return Result.error("未获取到有效的用户信息");
			}
			
			List<HourRecordApply> applys = hourRecordApplyService.listByIds(applyIdList);
			
			List<String> ids = applys.stream().map(item->item.getRecordId()).collect(Collectors.toList());

			// 查询所有记录，验证状态
			List<HourRecord> records = hourRecordService.listByIds(ids);
			Map<String,HourRecord> recordMap = new HashMap<String, HourRecord>();
			for (HourRecord hourRecord : records) {
				recordMap.put(hourRecord.getId(), hourRecord);
			}
			
			// 验证记录状态，只允许审批"审批中(1)"状态的记录
			List<HourRecord> invalidRecords = records.stream().filter(record -> record.getStatus() != 1)
					.collect(Collectors.toList());
			if (!invalidRecords.isEmpty()) {
				return Result.error("选中记录中包含非法状态记录，只能审批状态为1的记录");
			}
			boolean isApprove = "approve".equals(action);
			for (HourRecordApply apply : applys) {
				HourRecord record = recordMap.get(apply.getRecordId());
				hourRecordApproverService.processApproval(record.getId().toString(), approverId, approverName,
						isApprove, remarks,apply.getId());
				hourRecordLogService.logApprove(record, isApprove, approverId, approverName,
						isApprove ? null : remarks);
			}
			return Result.OK(isApprove ? "批量审批通过成功" : "批量驳回成功");

		} catch (Exception e) {
			log.error("批量审批工时记录失败", e);
			return Result.error("批量审批工时记录失败: " + e.getMessage());
		}
	}
	
	@ApiOperation(value = "工时记录表单详情", notes = "获取工时主表和明细集合")
	@GetMapping("/getById")
	public Result<?> getById(@RequestParam Long applyId) {
		HourRecordApply applyInfo = this.hourRecordApplyService.getById(applyId);
		
		HourRecord hourRecord = hourRecordService.getById(applyInfo.getRecordId());
		if (hourRecord == null) {
			return Result.error("未找到工时记录");
		}
		List<HourRecordDetail> details = hourRecordDetailService
				.list(new QueryWrapper<HourRecordDetail>().eq("record_id", applyInfo.getRecordId()));
		Map<String, Object> result = new HashMap<>();
		result.put("hourRecord", hourRecord);
		result.put("hourRecordDetail", details);
		result.put("applyInfo", applyInfo);
		return Result.OK(result);
	}
	
	/**
	 * 获取工时审批列表
	 *
	 * @param queryParams 查询参数
	 * @return 工时审批记录列表
	 */
	@ApiOperation(value = "分页查询待交接审批数据", notes = "分页查询待交接审批数据")
	@GetMapping(value = "/handovers")
	public Result<IPage<HourRecordApplyVO>> queryHandoverApproverRecords(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
//			@RequestParam(name = "status", required = false) String status,
//			@RequestParam(name = "tab", required = false) String tab,
//			@RequestParam(name = "startDate", required = false) String startDate,
//			@RequestParam(name = "endDate", required = false) String endDate,
			@RequestParam(name = "projectId", required = false) String projectId,
			@RequestParam(name = "employeeId", required = false) String employeeId) {
		try {
			// 从请求头获取项目Key
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			if (projectKey == null) {
				return Result.error("未提供有效的项目Key");
			}

			// 获取当前用户ID
			String currentUserId = userInfoUtils.getCurrentUserKey();
			if (StringUtils.isBlank(currentUserId)) {
				return Result.error("未获取到有效的用户信息");
			}
			
			List<String> employeeIdList = new ArrayList<String>();
			if(StringUtils.isNotEmpty(employeeId)) {
				employeeIdList.addAll(Arrays.asList(employeeId.split(",")));
			}
			String status = null;
			String tab = "pending";
			String startDate = null;
			String endDate = null;

			// 使用Mapper方法进行分页查询
			Page<HourRecordApplyVO> page = new Page<>(pageNo, pageSize);
			IPage<HourRecordApplyVO> pageList = hourRecordApplyService.queryHandoverApproverRecords(page, startDate, endDate, employeeIdList, tab, status, projectId);

			// 转换为VO对象
			for (HourRecordApplyVO vo : pageList.getRecords()) {
				// 设置状态名称
				switch (vo.getStatus()) {
				case 0:
					vo.setStatusName("待提交");
					break;
				case 1:
					vo.setStatusName("审批中");
					break;
				case 2:
					vo.setStatusName("已完成");
					break;
				case 3:
					vo.setStatusName("审批驳回");
					break;
				default:
					vo.setStatusName("未知");
				}
				// 兜底设置applyStatusName
				if (vo.getApplyStatusName() == null) {
					switch (vo.getApplyStatus() != null ? vo.getApplyStatus() : -1) {
						case 0: vo.setApplyStatusName("审批中"); break;
						case 1: vo.setApplyStatusName("已通过"); break;
						case 2: vo.setApplyStatusName("已驳回"); break;
						default: vo.setApplyStatusName("未知");
					}
				}
				// 兜底设置dealStatusName
				if (vo.getDealStatusName() == null) {
					switch (vo.getDealStatus() != null ? vo.getDealStatus() : -1) {
						case 0: vo.setDealStatusName("待审批"); break;
						case 1: vo.setDealStatusName("已通过"); break;
						case 2: vo.setDealStatusName("已拒绝"); break;
						case 3: vo.setDealStatusName("已移交"); break;
						default: vo.setDealStatusName("未知");
					}
				}
			}
			return Result.OK(page);

		} catch (Exception e) {
			log.error("获取工时审批列表失败", e);
			return Result.error("获取工时审批列表失败: " + e.getMessage());
		}
	}

}