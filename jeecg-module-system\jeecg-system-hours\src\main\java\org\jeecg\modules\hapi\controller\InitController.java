package org.jeecg.modules.hapi.controller;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.feishu.service.FeiShuService;
import org.jeecg.modules.hours.entity.FsProject;
import org.jeecg.modules.hours.service.IFsProjectService;
import org.jeecg.modules.hours.task.AttendanceStatJob;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.jeecg.modules.plm.job.PlmSyncJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api(tags = "飞书初始化")
@RestController
@RequestMapping("/hapi/init/")
public class InitController {

	@Value("${feishu.project.plugin.id}")
	private String pluginId;

	@Value("${feishu.project.plugin.secret}")
	private String pluginSecret;

	@Value("${feishu.api.base-url:https://project.feishu.cn/}")
	private String feishuApiBaseUrl;
	@Autowired
	private IFsProjectService fsProjectService;
	@Autowired
	private FsSyncTask fsSyncTask;
	@Autowired
	private PlmSyncJob plmSyncJob;
	@Autowired
	private FeiShuService feiShuService;
	@Autowired
	private AttendanceStatJob attendanceStatJob;

	/**
	 * 初始化配置
	 */
	@AutoLog(value = "PLM项目")
	@ApiOperation(value = "PLM项目", notes = "PLM项目")
	@GetMapping(value = "/initPlm")
	public Result<?> initPlm(HttpServletRequest req) {
		try {
			plmSyncJob.initSync();

			return Result.OK("同步成功!");
		} catch (Exception e) {
			log.error("初始化配置异常", e);
			return Result.error("初始化配置失败: " + e.getMessage());
		}
	}

	/**
	 * 初始化配置
	 */
	@AutoLog(value = "初始化部门用户")
	@ApiOperation(value = "初始化部门用户", notes = "初始化部门用户")
	@GetMapping(value = "/initDeptUser")
	public Result<?> initPlm(@RequestParam("appId") String appId, @RequestParam("appSecret") String appSecret,
			HttpServletRequest req) {
		try {
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();

			feiShuService.syncAllDepartmentsAndUsers(appId, appSecret, projectKey);
			return Result.OK("同步成功!");
		} catch (Exception e) {
			log.error("初始化配置异常", e);
			return Result.error("初始化配置失败: " + e.getMessage());
		}
	}

	/**
	 * 初始化配置
	 */
	@AutoLog(value = "初始化考勤")
	@ApiOperation(value = "初始化考勤", notes = "初始化考勤")
	@GetMapping(value = "/initAttendanceStat")
	public Result<?> initPlm(@RequestParam("start") Integer start, @RequestParam("end") Integer end,
			HttpServletRequest req) {
		try {
			attendanceStatJob.startTest(start, end);
			return Result.OK("同步成功!");
		} catch (Exception e) {
			log.error("初始化配置异常", e);
			return Result.error("初始化配置失败: " + e.getMessage());
		}
	}

	/**
	 * 初始化配置
	 */
	@AutoLog(value = "初始化配置")
	@ApiOperation(value = "初始化配置", notes = "获取系统配置并初始化项目空间数据")
	@GetMapping(value = "/initConfig")
	public Result<?> initConfig(HttpServletRequest req) {
		try {
			// 从请求头中获取项目标识
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();

			// 验证项目是否存在
			FsProject project = fsProjectService.lambdaQuery().eq(FsProject::getProjectKey, projectKey)
					.eq(FsProject::getStatus, "1").one();

			if (project == null) {
				log.warn("项目[{}]不存在或状态异常", projectKey);
				return Result.error("项目不存在或状态异常");
			}

			try {
				fsSyncTask.startInitSpace(projectKey);
			} catch (Exception e) {
				log.error(e.getMessage(), e);
			}

			// 返回配置数据
			Map<String, Object> config = new HashMap<>();
			// TODO: 后续可以在这里添加更多配置数据

			return Result.OK(config);
		} catch (Exception e) {
			log.error("初始化配置异常", e);
			return Result.error("初始化配置失败: " + e.getMessage());
		}
	}

}