# 工时登记提醒系统使用说明

## 系统概述

工时登记提醒系统是一个基于SpringBoot + Quartz的智能提醒系统，能够根据配置的提醒规则自动检测用户未登记的工时，并通过飞书推送个性化提醒消息。

## 核心功能

### 1. 动态定时任务管理
- **自动启动**: 新增提醒配置时，如果状态为"启用"，自动启动对应的定时任务
- **自动更新**: 更新提醒配置时，自动管理定时任务的启动/停止/更新
- **自动停止**: 删除提醒配置时，如果配置处于启用状态，自动停止对应的定时任务
- **状态切换**: 启用/禁用提醒时自动启动/停止对应的定时任务
- **启动恢复**: 应用启动时自动加载所有已启用的提醒配置并启动定时任务

### 2. 智能业务日期计算
- 支持每天、每周、每月三种提醒类型
- 支持当天/次日、本周/次周、本月/次月等重复方式
- 自动计算需要检查的业务日期范围

### 3. 灵活的工时检测规则
- **飞书考勤校验模式**：检查有考勤记录但无工时登记的情况
- **手动校验模式**：根据配置的每日工时上限进行检测

### 4. 个性化消息推送
- 通过飞书机器人发送个性化提醒消息
- 支持不同提醒类型的差异化消息内容
- 包含具体的未提交日期信息

## 系统架构

```
├── controller/
│   ├── FsSysReminderConfigController.java    # 提醒配置管理接口
│   └── ReminderTestController.java           # 测试接口
├── service/
│   ├── IFsSysReminderConfigService.java      # 提醒配置服务接口
│   ├── IReminderScheduleService.java         # 定时任务调度服务接口
│   ├── IWorkHourReminderService.java         # 工时提醒服务接口
│   └── impl/
│       ├── FsSysReminderConfigServiceImpl.java
│       ├── ReminderScheduleServiceImpl.java
│       └── WorkHourReminderServiceImpl.java
├── job/
│   └── WorkHourReminderJob.java              # 工时提醒定时任务
├── mapper/
│   ├── FsSysReminderConfigMapper.java
│   └── WorkHourReminderMapper.java
├── entity/
│   └── FsSysReminderConfig.java              # 提醒配置实体
├── vo/
│   └── UnsubmittedWorkHourVO.java            # 未提交工时VO
├── util/
│   └── BusinessDateCalculator.java           # 业务日期计算工具
└── config/
    └── ReminderJobInitializer.java           # 启动时任务初始化器
```

## 配置说明

### 1. 提醒配置表结构 (fs_sys_reminder_config)

| 字段 | 类型 | 说明 | 示例值 |
|------|------|------|--------|
| id | bigint(20) | 主键ID | 1 |
| project_key | varchar(64) | 工作空间标识 | demo_project_001 |
| reminder_type | varchar(20) | 提醒类型 | daily/weekly/monthly |
| repeat_mode | varchar(20) | 重复方式 | current_day/next_day/current_week/next_week/current_month/next_month |
| reminder_day | varchar(20) | 提醒日期 | monday-sunday/1-31/last_day |
| reminder_time | varchar(10) | 提醒时间 | 17:30 |
| remark | varchar(500) | 备注 | 下班前提醒填写当日工时 |
| status | varchar(10) | 状态 | 启用/暂停 |

### 2. 系统配置项

需要在sys_config表中配置以下参数：

| 配置键 | 说明 | 示例值 |
|--------|------|--------|
| bind_app_id | 飞书应用AppID | cli_xxxxxx |
| bind_app_secret | 飞书应用AppSecret | xxxxxx-xxxx-xxxx |
| feishu_attendance_validate_switch | 飞书考勤校验开关 | 0-关闭，1-开启 |
| max_work_hour_per_day | 每日工时上限 | 8 |

## 使用方法

### 1. 创建提醒配置

```http
POST /hours/reminder-config
Content-Type: application/json

{
    "projectKey": "demo_project_001",
    "reminderType": "daily",
    "repeatMode": "current_day",
    "reminderTime": "17:30",
    "remark": "下班前提醒填写当日工时",
    "status": "启用"  // 设置为"启用"时会自动启动定时任务
}
```

**自动定时任务管理**：
- ✅ 新增配置时，如果状态为"启用"，系统会自动启动对应的定时任务
- ✅ 无需手动管理定时任务，系统全自动处理

### 2. 启用/禁用提醒

```http
PUT /hours/reminder-config/toggle-status/{id}
Content-Type: application/json

{
    "status": "启用"  // 或 "暂停"
}
```

**自动定时任务管理**：
- ✅ 启用时：自动启动对应的定时任务
- ✅ 禁用时：自动停止对应的定时任务

### 3. 更新提醒配置

```http
PUT /hours/reminder-config/{id}
Content-Type: application/json

{
    "reminderType": "weekly",
    "repeatMode": "current_week",
    "reminderDay": "friday",
    "reminderTime": "16:00",
    "status": "启用"
}
```

**自动定时任务管理**：
- ✅ 更新配置时，系统会自动管理定时任务：
  - 如果从禁用变为启用：启动定时任务
  - 如果从启用变为禁用：停止定时任务
  - 如果保持启用状态：更新定时任务配置

### 4. 删除提醒配置

```http
DELETE /hours/reminder-config/{id}
```

**自动定时任务管理**：
- ✅ 删除配置时，如果配置处于启用状态，系统会自动停止对应的定时任务

### 5. 测试功能

系统提供了完整的测试接口：

```http
# 测试定时任务启动
POST /hours/reminder/test/start-job/{configId}

# 测试提醒执行
POST /hours/reminder/test/execute-reminder/{configId}

# 测试业务日期计算
GET /hours/reminder/test/calculate-dates/{configId}

# 测试Cron表达式生成
GET /hours/reminder/test/generate-cron/{configId}
```

## 提醒类型说明

### 1. 每天提醒 (daily)
- **current_day**: 检查当天的工时登记情况
- **next_day**: 检查昨天的工时登记情况

### 2. 每周提醒 (weekly)
- **current_week**: 检查本周的工时登记情况
- **next_week**: 检查上周的工时登记情况
- 需要配置reminder_day指定星期几触发

### 3. 每月提醒 (monthly)
- **current_month**: 检查本月的工时登记情况
- **next_month**: 检查上月的工时登记情况
- 需要配置reminder_day指定几号触发或last_day表示月末

## 消息模板

系统会根据不同的提醒类型生成个性化的消息内容：

- **日提醒**: "您今天/昨日有工时未登记"
- **周提醒**: "您本周/上周有工时未登记，未提交日期：1.15, 1.16"
- **月提醒**: "您本月/上月有工时未登记，未提交日期：1.15, 1.16, 1.17"

## 定时任务自动管理机制

### 🚀 **完全自动化的定时任务管理**

系统实现了完全自动化的定时任务管理，无需手动干预：

#### **1. 新增配置时**
```java
// 重写了 save() 方法
@Override
public boolean save(FsSysReminderConfig entity) {
    // 1. 保存配置到数据库
    boolean saveResult = super.save(entity);

    // 2. 如果状态为"启用"，自动启动定时任务
    if ("启用".equals(entity.getStatus())) {
        reminderScheduleService.startReminderJob(entity);
    }

    return saveResult;
}
```

#### **2. 更新配置时**
```java
// 重写了 updateById() 方法
@Override
public boolean updateById(FsSysReminderConfig entity) {
    // 1. 获取更新前的配置状态
    FsSysReminderConfig oldConfig = this.getById(entity.getId());

    // 2. 更新配置到数据库
    boolean updateResult = super.updateById(entity);

    // 3. 智能管理定时任务
    if ("启用".equals(newStatus)) {
        if (!"启用".equals(oldStatus)) {
            // 从禁用变为启用：启动定时任务
            reminderScheduleService.startReminderJob(entity);
        } else {
            // 已启用状态下的更新：更新定时任务
            reminderScheduleService.updateReminderJob(entity);
        }
    } else if ("暂停".equals(newStatus) && "启用".equals(oldStatus)) {
        // 从启用变为禁用：停止定时任务
        reminderScheduleService.stopReminderJob(entity);
    }

    return updateResult;
}
```

#### **3. 删除配置时**
```java
// 重写了 deleteReminderConfig() 方法
@Override
public boolean deleteReminderConfig(Long id, String deleteBy) {
    // 1. 获取要删除的配置
    FsSysReminderConfig reminderConfig = this.getById(id);

    // 2. 如果配置是启用状态，先停止定时任务
    if ("启用".equals(reminderConfig.getStatus())) {
        reminderScheduleService.stopReminderJob(reminderConfig);
    }

    // 3. 逻辑删除配置
    return this.update(updateWrapper);
}
```

#### **4. 应用启动时**
```java
// ReminderJobInitializer 组件
@Component
public class ReminderJobInitializer implements ApplicationRunner {
    @Override
    public void run(ApplicationArguments args) {
        // 查询所有启用状态的提醒配置
        List<FsSysReminderConfig> enabledConfigs = reminderConfigService.list(...);

        // 为每个启用的配置启动定时任务
        for (FsSysReminderConfig config : enabledConfigs) {
            reminderScheduleService.startReminderJob(config);
        }
    }
}
```

### 📋 **操作与定时任务管理对照表**

| 操作 | 配置状态变化 | 定时任务操作 | 说明 |
|------|-------------|-------------|------|
| 新增配置（启用） | 无 → 启用 | ✅ 启动任务 | 自动启动 |
| 新增配置（禁用） | 无 → 禁用 | ❌ 无操作 | 不启动任务 |
| 启用配置 | 禁用 → 启用 | ✅ 启动任务 | 自动启动 |
| 禁用配置 | 启用 → 禁用 | ⏹️ 停止任务 | 自动停止 |
| 更新配置（保持启用） | 启用 → 启用 | 🔄 更新任务 | 重新配置 |
| 更新配置（保持禁用） | 禁用 → 禁用 | ❌ 无操作 | 无需操作 |
| 删除配置（启用状态） | 启用 → 删除 | ⏹️ 停止任务 | 删除前停止 |
| 删除配置（禁用状态） | 禁用 → 删除 | ❌ 无操作 | 直接删除 |
| 应用启动 | - | ✅ 批量启动 | 恢复所有启用的任务 |

## 注意事项

1. **完全自动化**: 所有定时任务管理都是自动的，无需手动干预
2. **异常处理**: 定时任务操作失败不会影响配置的数据库操作
3. **事务安全**: 使用@Transactional确保数据一致性
4. **并发控制**: 使用@DisallowConcurrentExecution确保同一任务不会并发执行
5. **数据持久化**: 使用@PersistJobDataAfterExecution确保任务数据的持久化
6. **启动恢复**: 应用重启时自动恢复所有已启用的定时任务

## 故障排查

1. **定时任务未执行**: 检查Quartz配置和数据库连接
2. **消息发送失败**: 检查飞书应用配置和网络连接
3. **日期计算错误**: 检查提醒配置的参数设置
4. **数据查询异常**: 检查数据库表结构和数据完整性
