package org.jeecg.modules.hours.service.impl;

import org.jeecg.modules.hours.dto.DeptProjectDailyHourReportDTO;
import org.jeecg.modules.hours.entity.DeptProjectDailyHourStatistics;
import org.jeecg.modules.hours.mapper.DeptProjectDailyHourStatisticsMapper;
import org.jeecg.modules.hours.mapper.HourRecordMapper;
import org.jeecg.modules.hours.service.IDeptProjectDailyHourStatisticsService;
import org.jeecg.modules.hours.vo.DeptProjectDailyHourReportResult;
import org.jeecg.modules.hours.vo.DeptProjectDailyHourReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * @Description: 部门项目每日工时分布统计服务实现
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Service
@Slf4j
public class DeptProjectDailyHourStatisticsServiceImpl implements IDeptProjectDailyHourStatisticsService {

    @Autowired
    private DeptProjectDailyHourStatisticsMapper deptProjectDailyHourStatisticsMapper;
    
    @Autowired
    private HourRecordMapper hourRecordMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public DeptProjectDailyHourReportResult getDeptProjectDailyHourReport(DeptProjectDailyHourReportDTO reportDTO) {
        try {
            // 解析部门ID列表
            List<String> departmentIds = parseDepartmentIds(reportDTO.getDepartmentIds());

            // 1. 从统计表查询数据
            QueryWrapper<DeptProjectDailyHourStatistics> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("stat_month", reportDTO.getYearMonth());
            
            // 假日数据过滤
            if (reportDTO.getIsHoliday() != null) {
                queryWrapper.eq("is_holiday", reportDTO.getIsHoliday());
            }
            
            // 部门过滤
            if (departmentIds != null && !departmentIds.isEmpty()) {
                queryWrapper.in("department_id", departmentIds);
            }
            
            // 项目关键字过滤
            if (StringUtils.hasText(reportDTO.getProjectKeyword())) {
                queryWrapper.and(wrapper -> wrapper
                    .like("project_name", reportDTO.getProjectKeyword())
                    .or()
                    .like("project_code", reportDTO.getProjectKeyword())
                );
            }
            
            queryWrapper.orderByAsc("department_name", "project_name");
            
            List<DeptProjectDailyHourStatistics> statisticsList = deptProjectDailyHourStatisticsMapper.selectList(queryWrapper);

            // 2. 生成日期列
            List<DeptProjectDailyHourReportResult.DayColumnVO> dayColumns = generateDayColumns(reportDTO.getYearMonth());

            // 3. 转换数据格式
            List<DeptProjectDailyHourReportVO> deptProjectHourData = convertToReportData(statisticsList, dayColumns);

            // 4. 计算总计行
            DeptProjectDailyHourReportVO totalRow = calculateTotalRow(deptProjectHourData, dayColumns);

            // 5. 生成统计摘要
            DeptProjectDailyHourReportResult.ReportSummaryVO summary = generateSummary(
                reportDTO.getYearMonth(), deptProjectHourData, dayColumns);

            // 6. 构建结果
            DeptProjectDailyHourReportResult result = new DeptProjectDailyHourReportResult()
                    .setDayColumns(dayColumns)
                    .setDeptProjectHourData(deptProjectHourData)
                    .setTotalRow(totalRow)
                    .setSummary(summary);

            log.info("部门项目每日工时分布报表查询完成，部门项目数: {}, 日期列数: {}", 
                deptProjectHourData.size(), dayColumns.size());

            return result;

        } catch (Exception e) {
            log.error("获取部门项目每日工时分布报表失败", e);
            throw new RuntimeException("获取报表数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int generateMonthlyStatistics(String statMonth, Integer isHoliday) {
        try {
            log.info("开始生成部门项目每日工时分布统计数据，月份: {}, 是否统计假日: {}", statMonth, isHoliday);
            
            // 先删除已有数据（使用QueryWrapper避免XML映射问题）
            QueryWrapper<DeptProjectDailyHourStatistics> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.eq("stat_month", statMonth);
            if (isHoliday != null) {
                deleteWrapper.eq("is_holiday", isHoliday);
            }
            deptProjectDailyHourStatisticsMapper.delete(deleteWrapper);
            
            // 解析月份，获取该月的所有日期
            String[] parts = statMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);
            
            Calendar calendar = Calendar.getInstance();
            calendar.set(year, month - 1, 1);
            int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            
            // 存储每个部门项目的每日工时数据
            Map<String, DeptProjectDailyHourStatistics> statisticsMap = new HashMap<>();
            
            int processedCount = 0;
            
            // 遍历该月的每一天
            for (int day = 1; day <= daysInMonth; day++) {
                calendar.set(year, month - 1, day);
                Date currentDate = calendar.getTime();
                
//                // 检查是否是周末（假日）
//                boolean isWeekend = calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY ||
//                                   calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY;
                
                // 如果设置了不统计假日，且当前是周末，则跳过
                if (isHoliday != null && isHoliday == 0 ) {
                    continue;
                }
                
                // 使用getHourRecordDetailsForDeptStatistics查询该天的工时记录
                List<Map<String, Object>> dayRecords = hourRecordMapper.getHourRecordDetailsForDeptStatistics(currentDate);
                
                for (Map<String, Object> record : dayRecords) {
                    // 如果设置了只统计假日，且当前不是周末，则跳过非假日的工时记录
                    Integer recordIsHoliday = (Integer) record.get("is_holiday");
                    if (isHoliday != null && isHoliday == 1 &&
                        (recordIsHoliday == null )) {
                        continue;
                    }
                    
                    String departmentId = (String) record.get("department_id");
                    String departmentName = (String) record.get("department_name");
                    String projectId = (String) record.get("project_id");
                    String projectName = (String) record.get("project_name");
                    String projectCode = (String) record.get("project_code");
                    BigDecimal actualHours = (BigDecimal) record.get("actual_hours");
                    
                    if (actualHours == null || actualHours.compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    
                    // 创建唯一键
                    String key = departmentId + "_" + projectId + "_" + isHoliday;
                    
                    DeptProjectDailyHourStatistics stats = statisticsMap.get(key);
                    if (stats == null) {
                        stats = new DeptProjectDailyHourStatistics();
                        stats.setStatMonth(statMonth);
                        stats.setDepartmentId(departmentId);
                        stats.setDepartmentName(departmentName);
                        stats.setProjectId(projectId);
                        stats.setProjectName(projectName);
                        stats.setProjectCode(projectCode);
                        stats.setIsHoliday(recordIsHoliday);
                        stats.setTotalHours(BigDecimal.ZERO);
                        stats.setDailyHoursJson("{}");
                        
                        statisticsMap.put(key, stats);
                    }
                    
                    // 更新总工时
                    stats.setTotalHours(stats.getTotalHours().add(actualHours));
                    
                    // 更新每日工时JSON
                    try {
                        Map<String, BigDecimal> dailyHours = objectMapper.readValue(
                            stats.getDailyHoursJson(), 
                            new TypeReference<Map<String, BigDecimal>>() {}
                        );
                        
                        String dayKey = String.valueOf(day);
                        BigDecimal existingHours = dailyHours.getOrDefault(dayKey, BigDecimal.ZERO);
                        dailyHours.put(dayKey, existingHours.add(actualHours));
                        
                        stats.setDailyHoursJson(objectMapper.writeValueAsString(dailyHours));
                    } catch (Exception e) {
                        log.warn("更新每日工时JSON失败: {}", e.getMessage());
                    }
                }
            }
            
            // 批量插入统计数据
            for (DeptProjectDailyHourStatistics stats : statisticsMap.values()) {
                deptProjectDailyHourStatisticsMapper.insert(stats);
                processedCount++;
            }
            
            log.info("部门项目每日工时分布统计数据生成完成，月份: {}, 是否统计假日: {}, 处理记录数: {}", 
                statMonth, isHoliday, processedCount);
            
            return processedCount;
            
        } catch (Exception e) {
            log.error("生成部门项目每日工时分布统计数据失败，月份: {}, 是否统计假日: {}", statMonth, isHoliday, e);
            throw new RuntimeException("生成统计数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析部门ID列表
     */
    private List<String> parseDepartmentIds(String departmentIds) {
        if (!StringUtils.hasText(departmentIds)) {
            return null;
        }
        return Arrays.stream(departmentIds.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
    }

    /**
     * 生成日期列
     */
    private List<DeptProjectDailyHourReportResult.DayColumnVO> generateDayColumns(String yearMonth) {
        List<DeptProjectDailyHourReportResult.DayColumnVO> dayColumns = new ArrayList<>();
        
        try {
            // 解析年月
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);
            
            // 计算该月的天数
            Calendar calendar = Calendar.getInstance();
            calendar.set(year, month - 1, 1);
            int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            
            // 生成日期列
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            for (int day = 1; day <= daysInMonth; day++) {
                calendar.set(Calendar.DAY_OF_MONTH, day);
                
                DeptProjectDailyHourReportResult.DayColumnVO dayColumn = 
                    new DeptProjectDailyHourReportResult.DayColumnVO()
                        .setColumnKey(String.format("day_%02d", day))
                        .setDayName(String.valueOf(day))
                        .setFullDate(dateFormat.format(calendar.getTime()));
//                        .setIsHoliday(isWeekend(calendar));
                        
                dayColumns.add(dayColumn);
            }
            
        } catch (Exception e) {
            log.error("生成日期列失败，年月: {}", yearMonth, e);
        }
        
        return dayColumns;
    }

    /**
     * 判断是否为周末
     */
    private boolean isWeekend(Calendar calendar) {
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        return dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY;
    }

    /**
     * 转换统计数据为报表数据
     */
    private List<DeptProjectDailyHourReportVO> convertToReportData(
            List<DeptProjectDailyHourStatistics> statisticsList,
            List<DeptProjectDailyHourReportResult.DayColumnVO> dayColumns) {
        
        List<DeptProjectDailyHourReportVO> result = new ArrayList<>();
        
        for (DeptProjectDailyHourStatistics statistics : statisticsList) {
            DeptProjectDailyHourReportVO reportData = new DeptProjectDailyHourReportVO()
                    .setDepartmentId(statistics.getDepartmentId())
                    .setDepartmentName(statistics.getDepartmentName())
                    .setProjectId(statistics.getProjectId())
                    .setProjectName(statistics.getProjectName())
                    .setProjectCode(statistics.getProjectCode())
                    .setTotalHours(statistics.getTotalHours())
                    .setIsTotalRow(false);

            // 解析JSON格式的每日工时数据
            Map<String, BigDecimal> dailyHours = parseDailyHoursFromJson(statistics.getDailyHoursJson());
            
            reportData.setDailyHours(dailyHours);
            result.add(reportData);
        }
        
        return result;
    }

    /**
     * 从JSON字符串解析每日工时数据
     */
    private Map<String, BigDecimal> parseDailyHoursFromJson(String dailyHoursJson) {
        Map<String, BigDecimal> dailyHours = new HashMap<>();
        
        if (!StringUtils.hasText(dailyHoursJson)) {
            return dailyHours;
        }
        
        try {
            // 解析JSON: {"1": 8.0, "2": 7.5, ...}
            Map<String, Double> jsonData = objectMapper.readValue(dailyHoursJson, 
                new TypeReference<Map<String, Double>>() {});
            
            // 转换为目标格式: {"day_01": 8.0, "day_02": 7.5, ...}
            for (Map.Entry<String, Double> entry : jsonData.entrySet()) {
                String day = entry.getKey();
                Double hours = entry.getValue();
                
                if (hours != null && hours > 0) {
                    String columnKey = String.format("day_%02d", Integer.parseInt(day));
                    dailyHours.put(columnKey, BigDecimal.valueOf(hours));
                }
            }
            
        } catch (Exception e) {
            log.warn("解析每日工时JSON数据失败: {}", dailyHoursJson, e);
        }
        
        return dailyHours;
    }

    /**
     * 计算总计行
     */
    private DeptProjectDailyHourReportVO calculateTotalRow(
            List<DeptProjectDailyHourReportVO> deptProjectHourData,
            List<DeptProjectDailyHourReportResult.DayColumnVO> dayColumns) {
        
        if (deptProjectHourData == null || deptProjectHourData.isEmpty()) {
            return null;
        }

        Map<String, BigDecimal> totalDailyHours = new HashMap<>();
        BigDecimal grandTotal = BigDecimal.ZERO;

        // 汇总所有行的每日工时
        for (DeptProjectDailyHourReportVO row : deptProjectHourData) {
            if (row.getDailyHours() != null) {
                for (Map.Entry<String, BigDecimal> entry : row.getDailyHours().entrySet()) {
                    String dayKey = entry.getKey();
                    BigDecimal hours = entry.getValue() != null ? entry.getValue() : BigDecimal.ZERO;
                    totalDailyHours.put(dayKey, totalDailyHours.getOrDefault(dayKey, BigDecimal.ZERO).add(hours));
                }
            }
            if (row.getTotalHours() != null) {
                grandTotal = grandTotal.add(row.getTotalHours());
            }
        }

        return new DeptProjectDailyHourReportVO()
                .setDepartmentId("total")
                .setDepartmentName("总计")
                .setProjectId("total")
                .setProjectName("")
                .setProjectCode("")
                .setDailyHours(totalDailyHours)
                .setTotalHours(grandTotal)
                .setIsTotalRow(true);
    }

    /**
     * 生成统计摘要
     */
    private DeptProjectDailyHourReportResult.ReportSummaryVO generateSummary(
            String yearMonth,
            List<DeptProjectDailyHourReportVO> deptProjectHourData,
            List<DeptProjectDailyHourReportResult.DayColumnVO> dayColumns) {
        
        BigDecimal totalHours = BigDecimal.ZERO;
        Set<String> deptIds = new HashSet<>();
        Set<String> projectIds = new HashSet<>();
        
        for (DeptProjectDailyHourReportVO row : deptProjectHourData) {
            if (row.getTotalHours() != null) {
                totalHours = totalHours.add(row.getTotalHours());
            }
            if (StringUtils.hasText(row.getDepartmentId())) {
                deptIds.add(row.getDepartmentId());
            }
            if (StringUtils.hasText(row.getProjectId())) {
                projectIds.add(row.getProjectId());
            }
        }

        return new DeptProjectDailyHourReportResult.ReportSummaryVO()
                .setDateRange(yearMonth)
                .setTotalHours(totalHours)
                .setDeptCount(deptIds.size())
                .setProjectCount(projectIds.size())
                .setUserCount(0) // 需要额外查询
                .setDayCount(dayColumns.size());
    }
}