package org.jeecg.modules.hours.task;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.jeecg.modules.hours.context.AuthContext;
import org.jeecg.modules.hours.entity.FsInstanceCache;
import org.jeecg.modules.hours.entity.FsNodeCache;
import org.jeecg.modules.hours.entity.FsProject;
import org.jeecg.modules.hours.entity.FsWorkItemCache;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.service.IFsCacheService;
import org.jeecg.modules.hours.service.IFsDataAggregateService;
import org.jeecg.modules.hours.service.IFsProjectService;
import org.jeecg.modules.hours.service.IFsSyncLogService;
import org.jeecg.modules.hours.service.IFsUserService;
import org.jeecg.modules.hours.service.ISysWorkItemConfigService;
import org.jeecg.modules.hours.utils.RedisLockUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.lark.project.service.project.model.Project;
import com.lark.project.service.user.group.UserGroupService;
import com.lark.project.service.user.model.UserBasicInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 飞书项目数据同步任务
 * @Author: jeecg-boot
 * @Date: 2023-08-20
 * @Version: V1.0
 */
@Slf4j
@Component
public class FsSyncTask {

	@Autowired
	private IFsCacheService fsCacheService;

	@Autowired
	private IFsSyncLogService fsSyncLogService;

	@Autowired
	private ISysWorkItemConfigService sysWorkItemConfigService;

	@Autowired
	private IFsProjectService fsProjectService;

	@Autowired
	private IFeishuAuthService feishuAuthService;

	@Autowired
	private RedisLockUtil redisLockUtil;
	@Autowired
	private SpaceService spaceService;
	@Autowired
	private IFsUserService fsUserService;

	// 添加数据汇总服务
	@Autowired
	private IFsDataAggregateService fsDataAggregateService;

	// 线程池配置，用于并行处理
	private final ExecutorService executorService = Executors
			.newFixedThreadPool(Math.min(10, Runtime.getRuntime().availableProcessors()));

	/** 锁前缀 */
	private static final String LOCK_PREFIX = "fs_sync:project:";
	/** 默认锁超时时间（分钟） */
	private static final long DEFAULT_LOCK_TIMEOUT = 30L;

	/**
	 * 初始化工作空间-依赖于登录用户
	 * 
	 * @param projectKey 项目标识
	 * @param token      认证token
	 * @param userKey    用户标识
	 */
	@Async
	@Transactional(rollbackFor = Exception.class)
	public void initSpace(String projectKey, String token, String userKey) {
		String lockKey = LOCK_PREFIX + projectKey;
		String requestId = UUID.randomUUID().toString();

		// 尝试获取分布式锁
		if (!redisLockUtil.tryLock(lockKey, requestId, DEFAULT_LOCK_TIMEOUT, TimeUnit.MINUTES)) {
			log.warn("项目[{}]正在被其他线程同步，请稍后重试", projectKey);
			throw new RuntimeException("项目正在被其他线程同步，请稍后重试");
		}

		try {
			log.info("开始初始化项目空间数据, projectKey: {}", projectKey);

			// 1. 检查项目是否存在且未初始化
			FsProject project = fsProjectService.lambdaQuery().eq(FsProject::getProjectKey, projectKey)
					.eq(FsProject::getStatus, "1") // 状态正常
					.one();

			if (project == null) {
				log.warn("项目[{}]不存在或状态异常", projectKey);
				return;
			}

			if (project.getIsInit() != null && project.getIsInit() == 1) {
				log.info("项目[{}]已初始化，无需重复处理", projectKey);
				return;
			}

			// 2. 记录同步开始时间和日志
			String logId = fsSyncLogService.createSyncLog(projectKey, "INIT_SPACE");

			try {
				// 验证认证信息
				if (token == null || token.isEmpty() || userKey == null || userKey.isEmpty()) {
					String errorMsg = "无法获取有效的认证信息";
					fsSyncLogService.updateSyncFailed(logId, errorMsg);
					log.error("项目[{}]空间数据初始化失败: {}", projectKey, errorMsg);
					return;
				}

				// 设置认证上下文
				AuthContext authContext = AuthContext.create(token, userKey);
				AuthContext.setContext(authContext);

				try {
					// 获取空间详情
					Project space = spaceService.getSpaceDetail(projectKey);
					log.info("space==>{}",space);
					if (null != space) {
						project.setSimpleName(space.getSimpleName());
						try {
							// 1. 初始化空间管理员
							log.info("init..workspace..admin");
							List<String> admins = space.getAdministrators();
							if (!CollectionUtils.isEmpty(admins)) {
								List<UserBasicInfo> userList = spaceService.getUserList(admins);
								fsUserService.saveAdminList(userList, projectKey);
							}
							log.info("init..workspace..admins={}",admins);
							// 2. 初始化空间用户
							List<String> memberAll = UserGroupService.getMembers(token, userKey, projectKey,
									"PROJECT_MEMBER");
							log.info("init..workspace..members={}",memberAll);

							// 3. memberAll与admins 做差集(不在admins中的)
							Collection<String> members = CollectionUtils.subtract(memberAll, admins);
							log.info("members:{}",members);

							if (!CollectionUtils.isEmpty(members)) {
								List<UserBasicInfo> userList = spaceService.getUserList(new ArrayList<String>(members));
								fsUserService.saveNormalList(userList, projectKey);
							}
							log.info("空间({})管理员+用户同步完成,admins({}),members({})已剔除管理员!", projectKey,
									CollectionUtils.size(admins), CollectionUtils.size(members));

						} catch (Exception e) {
							log.info("init..workspace..exception!!");
							log.error(e.getMessage(), e);
						}
					}

					// 3. 同步工作空间数据（全量）
//                    boolean syncResult = syncProject(projectKey, true);
					boolean syncResult = true;

					if (syncResult) {
						// 4. 更新项目初始化状态
						project.setIsInit(1);
						project.setUpdatedAt(new Date());
						fsProjectService.updateById(project);

						// 5. 记录同步成功
						fsSyncLogService.updateSyncSuccess(logId, null, null);
						log.info("项目[{}]空间数据初始化成功", projectKey);
					} else {
						fsSyncLogService.updateSyncFailed(logId, "同步工作空间数据失败");
						log.error("项目[{}]空间数据初始化失败: 同步工作空间数据失败", projectKey);
					}
				} finally {
					// 清理认证上下文
					AuthContext.clearContext();
				}
			} catch (Exception e) {
				fsSyncLogService.updateSyncFailed(logId, "初始化异常: " + e.getMessage());
				log.error("项目[{}]空间数据初始化异常", projectKey, e);
				throw e;
			}
		} catch (Exception e) {
			log.error("初始化项目[{}]空间数据异常", projectKey, e);
			throw new RuntimeException("初始化项目空间数据失败", e);
		} finally {
			// 释放分布式锁
			if (!redisLockUtil.releaseLock(lockKey, requestId)) {
				log.warn("释放项目[{}]的分布式锁失败", projectKey);
			}
		}
	}

	/**
	 * 同步工作空间入口方法
	 * 
	 * @param projectKey 项目标识
	 */
	public void startInitSpace(String projectKey) {
		String lockKey = LOCK_PREFIX + projectKey;
		String requestId = UUID.randomUUID().toString();

		// 尝试获取分布式锁
		if (!redisLockUtil.tryLock(lockKey, requestId, DEFAULT_LOCK_TIMEOUT, TimeUnit.MINUTES)) {
			log.warn("项目[{}]正在被其他线程同步，请稍后重试", projectKey);
			throw new RuntimeException("项目正在被其他线程同步，请稍后重试");
		}

		try {
			// 在主线程中获取认证信息
			Map<String, String> authInfo = feishuAuthService.getAuthInfo();
			if (authInfo == null || authInfo.get("token") == null || authInfo.get("userKey") == null) {
				throw new Exception("无法获取有效的认证信息");
			}

			// 4. 异步执行初始化，不等待完成
			CompletableFuture.runAsync(() -> {
				try {
					initSpace(projectKey, authInfo.get("token"), authInfo.get("userKey"));
				} catch (Exception e) {
					log.error("异步初始化失败", e);
				} finally {
					redisLockUtil.releaseLock(lockKey, requestId);
				}
			});

		} catch (Exception e) {
			log.error("启动项目[{}]空间初始化失败: {}", projectKey, e.getMessage());
			throw new RuntimeException("启动项目空间初始化失败", e);
		} finally {
			// 释放分布式锁
			if (!redisLockUtil.releaseLock(lockKey, requestId)) {
				log.warn("释放项目[{}]的分布式锁失败", projectKey);
			}
		}
	}

	/**
	 * 获取项目下的工作项键值列表 从数据库中查询
	 */
	private List<String> getProjectWorkItemKeys(String projectKey) {
		try {
			// 查询项目下有效的工作项
			List<FsWorkItemCache> workItems = fsCacheService.getWorkItems(projectKey);

			List<String> workItemKeys = workItems.stream().map(FsWorkItemCache::getItemKey)
					.collect(Collectors.toList());

			log.info("项目[{}]下获取到{}个工作项", projectKey, workItemKeys.size());
			return workItemKeys;
		} catch (Exception e) {
			log.error("获取项目[{}]工作项列表异常", projectKey, e);
			return new ArrayList<>();
		}
	}

	/**
	 * 判断工作项是否是节点流类型 策略模式的一部分，根据工作项类型判断处理方式
	 */
	private boolean isWorkflowType(String projectKey, String workItemKey) {
		try {
			// 获取工作项详情
			FsWorkItemCache workItem = fsCacheService.getWorkItemDetail(projectKey, workItemKey);
			// 判断flowMode是否为workflow(节点流)
			if (workItem == null) {
				log.warn("工作项[{}]不存在", workItemKey);
				return false;
			}

			// 增强流程模式检查，处理null和空字符串情况
			String flowMode = workItem.getFlowMode();
			if (flowMode == null || flowMode.trim().isEmpty()) {
				log.warn("工作项[{}]的流程模式为空，默认按非节点流处理", workItemKey);
				return false;
			}

			return "workflow".equalsIgnoreCase(flowMode);
		} catch (Exception e) {
			log.error("检查工作项[{}]流程模式异常", workItemKey, e);
			return false;
		}
	}

	/**
	 * 获取工作项-实例映射 采用建造者模式构建复杂数据结构
	 */
	private Map<String, List<String>> getWorkItemInstanceMap(String projectKey) {
		try {
			Map<String, List<String>> workItemInstanceMap = new HashMap<>();

			// 获取项目下所有工作项
			List<FsWorkItemCache> workItems = fsCacheService.getWorkItems(projectKey);

			// 构建工作项-实例映射
			for (FsWorkItemCache workItem : workItems) {
				String workItemKey = workItem.getItemKey();
				List<FsInstanceCache> instances = fsCacheService.getInstances(projectKey, workItemKey);

				List<String> instanceKeys = instances.stream().map(FsInstanceCache::getInstanceKey)
						.collect(Collectors.toList());

				workItemInstanceMap.put(workItemKey, instanceKeys);
			}

			return workItemInstanceMap;
		} catch (Exception e) {
			log.error("获取项目[{}]工作项-实例映射异常", projectKey, e);
			return Collections.emptyMap();
		}
	}

	/**
	 * 获取节点映射 (工作项-实例-节点) 使用组合模式构建树状结构
	 */
	private Map<String, Map<String, List<String>>> getNodeMap(String projectKey) {
		try {
			Map<String, Map<String, List<String>>> nodeMap = new HashMap<>();

			// 获取工作项-实例映射
			Map<String, List<String>> workItemInstanceMap = getWorkItemInstanceMap(projectKey);

			// 构建工作项-实例-节点映射
			for (Map.Entry<String, List<String>> entry : workItemInstanceMap.entrySet()) {
				String workItemKey = entry.getKey();
				List<String> instanceKeys = entry.getValue();

				// 只处理节点流工作项
				if (isWorkflowType(projectKey, workItemKey)) {
					Map<String, List<String>> instanceNodeMap = new HashMap<>();

					for (String instanceKey : instanceKeys) {
						// 获取实例下的节点列表
						List<FsNodeCache> nodes = fsCacheService.getNodes(projectKey, instanceKey);

						List<String> nodeKeys = nodes.stream().map(FsNodeCache::getNodeKey)
								.collect(Collectors.toList());

						instanceNodeMap.put(instanceKey, nodeKeys);
					}

					nodeMap.put(workItemKey, instanceNodeMap);
				}
			}

			return nodeMap;
		} catch (Exception e) {
			log.error("获取项目[{}]节点映射异常", projectKey, e);
			return Collections.emptyMap();
		}
	}

	/**
	 * 获取实例的节点键值列表 辅助方法，支持责任链模式
	 */
	private List<String> getInstanceNodeKeys(String projectKey, String workItemKey, String instanceKey) {
		try {
			// 获取实例下的节点列表
			List<FsNodeCache> nodes = fsCacheService.getNodes(projectKey, instanceKey);

			return nodes.stream().map(FsNodeCache::getNodeKey).collect(Collectors.toList());
		} catch (Exception e) {
			log.error("获取实例[{}]节点列表异常", instanceKey, e);
			return Collections.emptyList();
		}
	}

	/**
	 * 同步工作空间数据（级联） 采用责任链模式处理同步流程
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean syncProject(String projectKey, boolean forceSync) {
		try {
			String logId = fsSyncLogService.createSyncLog(projectKey, "PROJECT_DATA");

			// 2. 级联同步数据
			boolean workItemResult = fsCacheService.syncWorkItems(projectKey);
			boolean instanceResult = true;
			boolean nodeResult = true;
			boolean taskResult = true;

			if (workItemResult) {
				// 同步工作项成功后，同步实例
				List<String> workItemKeys = getProjectWorkItemKeys(projectKey);
				instanceResult = syncInstancesForProject(projectKey, workItemKeys);

				if (instanceResult) {
					// 同步实例成功后，同步节点和任务
					nodeResult = syncNodesAndTasksForProject(projectKey);
				}
			}

			// 3. 记录同步结果
			boolean overallResult = workItemResult && instanceResult && nodeResult && taskResult;
			log.info("overallResult={},workItemResult={},instanceResult={},nodeResult={},taskResult={}", overallResult,
					workItemResult, instanceResult, nodeResult, taskResult);
			if (overallResult) {
				fsSyncLogService.updateSyncSuccess(logId, null, null);
			} else {
				fsSyncLogService.updateSyncFailed(logId, "同步失败，部分数据同步出错");
			}

			return overallResult;
		} catch (Exception e) {
			log.error("同步工作空间[{}]数据异常", projectKey, e);
			// 异常情况记录日志
			String logId = fsSyncLogService.createSyncLog(projectKey, "PROJECT_DATA");
			fsSyncLogService.updateSyncFailed(logId, "同步异常: " + e.getMessage());
			return false;
		}
	}

	/**
	 * 同步工作空间下所有工作项的实例 观察者模式的实现部分
	 */
	private boolean syncInstancesForProject(String projectKey, List<String> workItemKeys) {
		boolean result = true;

		// 使用观察者模式通知实例同步
		for (String workItemKey : workItemKeys) {
			try {
				// 获取工作项详情，包括流程模式

				log.info("同步工作空间[{}]工作项[{}]", projectKey, workItemKey);
				boolean itemResult = fsCacheService.syncInstances(projectKey, workItemKey);
				if (!itemResult) {
					result = false;
				}
			} catch (Exception e) {
				log.error("同步工作空间[{}]工作项[{}]实例异常", projectKey, workItemKey, e);
				result = false;
			}
		}

		return result;
	}

	/**
	 * 同步工作空间下所有实例的节点和任务 责任链模式的实现部分
	 */
	private boolean syncNodesAndTasksForProject(String projectKey) {
		boolean result = true;

		// 获取工作项-实例映射
		Map<String, List<String>> workItemInstanceMap = getWorkItemInstanceMap(projectKey);

		for (Map.Entry<String, List<String>> entry : workItemInstanceMap.entrySet()) {
			String workItemKey = entry.getKey();

			// 只处理节点流工作项
			if (isWorkflowType(projectKey, workItemKey)) {
				log.info("项目[{}]工作项[{}]是节点流类型，继续同步节点和任务", projectKey, workItemKey);
				for (String instanceKey : entry.getValue()) {
					try {
						// 同步节点
						boolean nodeResult = fsCacheService.syncNodes(projectKey, workItemKey, instanceKey);

						if (nodeResult) {
							// 获取节点列表
							// synNodes方法中已经同步了 节点+任务
							result = true;

							// 更新实例中的排期、工时的汇总
							fsDataAggregateService.aggregateInstanceData(projectKey, workItemKey, instanceKey);

						} else {
							result = false;
						}
					} catch (Exception e) {
						log.error("同步工作空间[{}]工作项[{}]实例[{}]节点和任务异常", projectKey, workItemKey, instanceKey, e);
						result = false;
					}
				}
			} else {
				log.info("项目[{}]工作项[{}]不是节点流类型，跳过同步节点和任务", projectKey, workItemKey);
			}
		}

		return result;
	}

	/**
	 * 处理飞书项目事件触发的同步
	 * 
	 * @param requestBody 飞书事件请求体
	 */
	@Transactional(rollbackFor = Exception.class)
	public void doSyncByEvent(Map<String, Object> requestBody) {
		try {
			// 1. 提取数据
			Map<String, Object> dataMap = (Map<String, Object>) requestBody.get("data");
			if (dataMap == null) {
				log.warn("事件数据为空，无法处理");
				return;
			}

			// 2. 获取基本信息
			String projectKey = (String) dataMap.get("project_key");
			String workItemTypeKey = (String) dataMap.get("work_item_type_key");
			Integer eventType = (Integer) dataMap.get("event_type");
			Integer flowType = (Integer) dataMap.get("flow_type");

			log.info("收到飞书事件通知: projectKey={}, eventType={}", projectKey, eventType);

			// 3. 根据事件类型处理
			if (eventType == null) {
				log.warn("事件类型为空，无法处理");
				return;
			}

			// 获取认证上下文
			AuthContext authContext = getAuthContext();
			if (authContext == null) {
				log.error("无法获取有效的认证上下文，同步终止");
				return;
			}

			try {
				AuthContext.setContext(authContext);

				// 根据事件类型分发处理
				switch (eventType) {
				case 1001: // 新增工作项
				case 1009: // 编辑工作项
					handleWorkItemEvent(dataMap, projectKey, workItemTypeKey, eventType);
					break;
				case 1002: // 删除工作项
					handleWorkItemDeleteEvent(dataMap, projectKey, workItemTypeKey);
					break;
				case 1004: // 恢复工作项 --不用处理
				case 1005: // 终止工作项 --不用处理
					log.info("工作项事件类型: {}，无需处理", eventType);
					break;
				case 2005: // 新增任务
					handleTaskCreateEvent(dataMap, projectKey, workItemTypeKey, flowType);
					break;
				case 2006: // 修改节点负责人
					handleNodeUpdateEvent(dataMap, projectKey, workItemTypeKey);
					break;
				case 3001: // 状态变更
					handleStatusChangeEvent(dataMap, projectKey, workItemTypeKey);
					break;
				case 4001: // 新建子任务
					handleTaskCreateEvent(dataMap, projectKey, workItemTypeKey, flowType);
					break;
				case 4002: // 删除子任务
					handleTaskDeleteEvent(dataMap, projectKey, workItemTypeKey);
					break;
				case 4003: // 完成子任务
				case 4004: // 取消完成子任务
					handleTaskStatusEvent(dataMap, projectKey, workItemTypeKey);
					break;
				case 4005: // 修改子任务负责人
				case 4006: // 修改子任务估分
				case 4007: // 修改子任务排期
				case 4009: // 修改子任务字段
					handleTaskUpdateEvent(dataMap, projectKey, workItemTypeKey);
					break;
				default:
					log.info("未处理的事件类型: {}", eventType);
				}
			} finally {
				// 清理认证上下文
				AuthContext.clearContext();
			}
		} catch (Exception e) {
			log.error("处理飞书事件异常", e);
			throw e;
		}
	}

	/**
	 * 处理工作项事件
	 */
	private void handleWorkItemEvent(Map<String, Object> dataMap, String projectKey, String workItemTypeKey,
			Integer eventType) {
		try {
			// 1. 获取工作项信息
			List<Map<String, Object>> workItemInfoList = (List<Map<String, Object>>) dataMap.get("work_item_info");
			if (workItemInfoList == null || workItemInfoList.isEmpty()) {
				log.warn("工作项信息为空，无法处理");
				return;
			}

			Map<String, Object> workItemInfo = workItemInfoList.get(0);
			Long workItemId = Long.valueOf(workItemInfo.get("work_item_id").toString());

			// 2. 同步工作项
			boolean result = fsCacheService.syncWorkItem(projectKey, workItemTypeKey, workItemId);
			log.info("同步工作项结果: {}", result);

			// 3. 同步实例
			if (result) {
				result = fsCacheService.syncInstances(projectKey, workItemTypeKey);
				log.info("同步实例结果: {}", result);

				// 4. 如果是节点流类型，同步节点和任务
				FsWorkItemCache workItem = fsCacheService.getWorkItemDetail(projectKey, workItemTypeKey);
				if (workItem != null && "workflow".equalsIgnoreCase(workItem.getFlowMode())) {
					List<FsInstanceCache> instances = fsCacheService.getInstances(projectKey, workItemTypeKey);
					for (FsInstanceCache instance : instances) {
						syncNodesAndTasks(projectKey, workItemTypeKey, instance.getInstanceKey());
					}
				}
			}
		} catch (Exception e) {
			log.error("处理工作项事件异常", e);
			throw e;
		}
	}

	/**
	 * 处理任务创建事件
	 */
	private void handleTaskCreateEvent(Map<String, Object> dataMap, String projectKey, String workItemTypeKey,
			Integer flowType) {
		try {
			// 1. 获取工作项信息
			List<Map<String, Object>> workItemInfoList = (List<Map<String, Object>>) dataMap.get("work_item_info");
			if (workItemInfoList == null || workItemInfoList.isEmpty()) {
				log.warn("工作项信息为空，无法处理");
				return;
			}

			Map<String, Object> workItemInfo = workItemInfoList.get(0);
			Long workItemId = Long.valueOf(workItemInfo.get("work_item_id").toString());

			// 2. 获取节点信息
			Map<String, Object> nodeInfo = (Map<String, Object>) dataMap.get("node_info");
			if (nodeInfo == null) {
				log.warn("节点信息为空，无法处理");
				return;
			}

			// 3. 处理不同流程类型
			if (flowType != null && flowType == 0) { // 节点流
				// 获取实例信息
				List<FsInstanceCache> instances = fsCacheService.getInstances(projectKey, workItemTypeKey);
				if (instances != null && !instances.isEmpty()) {
					// 直接同步所有实例的节点和任务，或者根据其他条件匹配
					for (FsInstanceCache instance : instances) {
						syncNodesAndTasks(projectKey, workItemTypeKey, instance.getInstanceKey());
					}
				}
			} else { // 状态流
				// 获取任务ID
				List<Map<String, Object>> subTaskInfoList = (List<Map<String, Object>>) dataMap.get("sub_task_info");
				if (subTaskInfoList != null && !subTaskInfoList.isEmpty()) {
					Map<String, Object> subTaskInfo = subTaskInfoList.get(0);
					Long subTaskId = Long.valueOf(subTaskInfo.get("sub_task_id").toString());

					// 直接同步任务
					boolean result = fsCacheService.syncTask(projectKey, workItemTypeKey, subTaskId);
					log.info("同步任务结果: {}", result);
				} else {
					log.warn("任务信息为空，无法同步任务");
				}
			}
		} catch (Exception e) {
			log.error("处理任务创建事件异常", e);
			throw e;
		}
	}

	/**
	 * 处理任务更新事件
	 */
	private void handleTaskUpdateEvent(Map<String, Object> dataMap, String projectKey, String workItemTypeKey) {
		try {
			// 1. 获取任务信息
			List<Map<String, Object>> subTaskInfoList = (List<Map<String, Object>>) dataMap.get("sub_task_info");
			if (subTaskInfoList == null || subTaskInfoList.isEmpty()) {
				log.warn("任务信息为空，无法处理");
				return;
			}

			Map<String, Object> subTaskInfo = subTaskInfoList.get(0);
			Long subTaskId = Long.valueOf(subTaskInfo.get("sub_task_id").toString());
			Long linkWorkItemId = Long.valueOf(subTaskInfo.get("link_work_item_id").toString());
			String nodeKey = (String) subTaskInfo.get("node_key");

			// 2. 同步任务
			boolean result = fsCacheService.syncTask(projectKey, workItemTypeKey, subTaskId);
			log.info("同步任务结果: {}", result);

			// 3. 汇总数据
			if (result) {
				// 获取实例信息
				List<FsInstanceCache> instances = fsCacheService.getInstances(projectKey, workItemTypeKey);
				if (instances != null && !instances.isEmpty()) {
					// 对所有相关实例进行汇总
					for (FsInstanceCache instance : instances) {
						// 汇总节点工时
						fsDataAggregateService.aggregateNodeEstimateHours(projectKey, workItemTypeKey,
								instance.getInstanceKey(), nodeKey);
						// 汇总实例工时
						fsDataAggregateService.aggregateInstanceData(projectKey, workItemTypeKey,
								instance.getInstanceKey());
					}
				}
			}
		} catch (Exception e) {
			log.error("处理任务更新事件异常", e);
			throw e;
		}
	}

	/**
	 * 处理任务状态变更事件
	 */
	private void handleTaskStatusEvent(Map<String, Object> dataMap, String projectKey, String workItemTypeKey) {
		// 与任务更新事件处理逻辑类似
		handleTaskUpdateEvent(dataMap, projectKey, workItemTypeKey);
	}

	/**
	 * 处理任务删除事件
	 */
	private void handleTaskDeleteEvent(Map<String, Object> dataMap, String projectKey, String workItemTypeKey) {
		try {
			// 1. 获取任务信息
			List<Map<String, Object>> subTaskInfoList = (List<Map<String, Object>>) dataMap.get("sub_task_info");
			if (subTaskInfoList == null || subTaskInfoList.isEmpty()) {
				log.warn("任务信息为空，无法处理");
				return;
			}

			Map<String, Object> subTaskInfo = subTaskInfoList.get(0);
			Long subTaskId = Long.valueOf(subTaskInfo.get("sub_task_id").toString());
			Long linkWorkItemId = Long.valueOf(subTaskInfo.get("link_work_item_id").toString());
			String nodeKey = (String) subTaskInfo.get("node_key");

			// 2. 删除任务
			boolean result = fsCacheService.deleteTask(projectKey, subTaskId);
			log.info("删除任务结果: {}", result);

			// 3. 汇总数据
			if (result) {
				// 获取实例信息
				List<FsInstanceCache> instances = fsCacheService.getInstances(projectKey, workItemTypeKey);
				if (instances != null && !instances.isEmpty()) {
					// 对所有相关实例进行汇总
					for (FsInstanceCache instance : instances) {
						// 汇总节点工时
						fsDataAggregateService.aggregateNodeEstimateHours(projectKey, workItemTypeKey,
								instance.getInstanceKey(), nodeKey);
						// 汇总实例工时
						fsDataAggregateService.aggregateInstanceData(projectKey, workItemTypeKey,
								instance.getInstanceKey());
					}
				}
			}
		} catch (Exception e) {
			log.error("处理任务删除事件异常", e);
			throw e;
		}
	}

	/**
	 * 同步节点和任务
	 */
	private void syncNodesAndTasks(String projectKey, String workItemTypeKey, String instanceKey) {
		try {
			// 1. 同步节点
			boolean nodeResult = fsCacheService.syncNodes(projectKey, workItemTypeKey, instanceKey);
			log.info("同步节点结果: {}", nodeResult);

			// 2. 如果节点同步成功，汇总实例数据
			if (nodeResult) {
				fsDataAggregateService.aggregateInstanceData(projectKey, workItemTypeKey, instanceKey);
			}
		} catch (Exception e) {
			log.error("同步节点和任务异常", e);
			throw e;
		}
	}

	/**
	 * 获取认证上下文
	 */
	private AuthContext getAuthContext() {
		try {
			Map<String, String> authInfo = feishuAuthService.getAuthInfo();
			if (authInfo != null && authInfo.get("token") != null && authInfo.get("userKey") != null) {
				return AuthContext.create(authInfo.get("token"), authInfo.get("userKey"));
			}
		} catch (Exception e) {
			log.error("获取认证信息异常", e);
		}
		return null;
	}

	/**
	 * 处理工作项删除事件
	 */
	private void handleWorkItemDeleteEvent(Map<String, Object> dataMap, String projectKey, String workItemTypeKey) {
		try {
			// 1. 获取工作项信息
			List<Map<String, Object>> workItemInfoList = (List<Map<String, Object>>) dataMap.get("work_item_info");
			if (workItemInfoList == null || workItemInfoList.isEmpty()) {
				log.warn("工作项信息为空，无法处理");
				return;
			}

			Map<String, Object> workItemInfo = workItemInfoList.get(0);
			Long workItemId = Long.valueOf(workItemInfo.get("work_item_id").toString());

			// 2. 删除工作项相关数据
			boolean result = fsCacheService.deleteWorkItem(projectKey, workItemTypeKey, workItemId);
			log.info("删除工作项结果: {}", result);
		} catch (Exception e) {
			log.error("处理工作项删除事件异常", e);
			throw e;
		}
	}

	/**
	 * 处理节点更新事件
	 */
	private void handleNodeUpdateEvent(Map<String, Object> dataMap, String projectKey, String workItemTypeKey) {
		try {
			// 1. 获取节点信息
			Map<String, Object> nodeInfo = (Map<String, Object>) dataMap.get("node_info");
			if (nodeInfo == null) {
				log.warn("节点信息为空，无法处理");
				return;
			}

			String nodeKey = (String) nodeInfo.get("node_key");
			String instanceKey = (String) nodeInfo.get("instance_key");

			// 2. 同步节点
			boolean result = fsCacheService.syncNode(projectKey, workItemTypeKey, instanceKey, nodeKey);
			log.info("同步节点结果: {}", result);

			// 3. 汇总实例数据
			if (result) {
				fsDataAggregateService.aggregateInstanceData(projectKey, workItemTypeKey, instanceKey);
			}
		} catch (Exception e) {
			log.error("处理节点更新事件异常", e);
			throw e;
		}
	}

	/**
	 * 处理状态变更事件
	 */
	private void handleStatusChangeEvent(Map<String, Object> dataMap, String projectKey, String workItemTypeKey) {
		try {
			// 1. 获取工作项信息
			List<Map<String, Object>> workItemInfoList = (List<Map<String, Object>>) dataMap.get("work_item_info");
			if (workItemInfoList == null || workItemInfoList.isEmpty()) {
				log.warn("工作项信息为空，无法处理");
				return;
			}

			Map<String, Object> workItemInfo = workItemInfoList.get(0);
			Long workItemId = Long.valueOf(workItemInfo.get("work_item_id").toString());

			// 2. 同步工作项
			boolean result = fsCacheService.syncWorkItem(projectKey, workItemTypeKey, workItemId);
			log.info("同步工作项结果: {}", result);

			// 3. 同步实例
			if (result) {
				result = fsCacheService.syncInstances(projectKey, workItemTypeKey);
				log.info("同步实例结果: {}", result);
			}
		} catch (Exception e) {
			log.error("处理状态变更事件异常", e);
			throw e;
		}
	}
}