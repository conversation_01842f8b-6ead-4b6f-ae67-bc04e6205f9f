/**
 * 瀑布流分页级联选择器组件
 *
 * 功能特性：
 * - 四级级联选择：工作项 → 实例 → 节点 → 任务
 * - 瀑布流分页：混合模式 - Semi Design异步加载 + 分页"加载更多"
 * - 叶子状态修复：节点接口传递工作项ID，正确判断叶子状态
 * - 统计接口调用：选择完整路径后自动调用统计接口
 * - 数据缓存：避免重复请求，提升性能
 * - 错误处理：网络错误和数据异常的友好提示
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Cascader, Notification } from '@douyinfe/semi-ui';
import { get } from '../../../utils/request';
import type { CascaderProps } from '@douyinfe/semi-ui/lib/es/cascader';
import { ApiResponse, CascaderItem } from '../types';
import type { Value } from '@douyinfe/semi-ui/lib/es/cascader';

// CSS样式来隐藏"加载更多"节点的向右箭头
const loadMoreStyles = `
  /* 隐藏"加载更多"按钮的向右箭头 */
  .semi-cascader-option[title*="📄"] .semi-cascader-option-icon,
  .semi-cascader-option[title*="📄"]::after,
  .semi-cascader-option[title*="加载更多"] .semi-cascader-option-icon,
  .semi-cascader-option[title*="加载更多"]::after {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }
`;

// 注入样式
const injectStyles = () => {
  if (typeof document !== 'undefined') {
    const styleId = 'waterfall-cascader-load-more-styles';
    const existingStyle = document.getElementById(styleId);

    if (!existingStyle) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = loadMoreStyles;
      document.head.appendChild(style);
      console.log('WaterfallCascader: "加载更多"样式已注入');
    }
  }
};

// 分页状态接口
interface PaginationState {
  pageNo: number;
  pageSize: number;
  total: number;
  hasMore: boolean;
  loading: boolean;
}

interface StatisticsResult {
  scheduleStartTime?: number;
  scheduleEndTime?: number;
  estimateHours?: number;
  recordedHours?: number;
}

// 组件属性接口
interface WaterfallCascaderProps {
  value?: (string | number)[];
  onChange?: (value: (string | number)[]) => void;
  onDropdownVisibleChange?: (visible: boolean) => void;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
  displayRender?: (labels: string[]) => React.ReactNode;
  loading?: boolean;
  onStatisticsLoad?: (workItemKey: string, instanceKey: string, nodeKey: string) => void;
  onStatisticsChange?: (result: StatisticsResult | null) => void;
}

// 缓存数据结构
interface CacheData {
  [key: string]: {
    data: CascaderItem[];
    pagination: PaginationState;
    allLoaded: boolean;
  };
}

// 工作项数据接口
interface WorkItemData {
  label: string;
  value: string;
  isLeaf: boolean;
  status: string;
  type: string;
}

// 数据处理函数
const processInstanceData = (instance: any) => {
  const result = {
    label: instance.label || instance.name || '未命名实例',
    value: instance.value || instance.instanceKey || `unknown_${Date.now()}_${Math.random()}`,
    isLeaf: !!instance.isLeaf,
    status: instance.status || '0',
    scheduleStartTime: instance.scheduleStartTime,  // 排期开始时间
    scheduleEndTime: instance.scheduleEndTime,      // 排期结束时间
    estimateHours: instance.estimateHours,         // 预估工时
    assignee: instance.assignee,                   // 负责人ID
    assigneeName: instance.assigneeName,           // 负责人姓名
    type: 'instance'                              // 明确设置类型为实例
  };
  return result;
};

const processNodeData = (node: any) => {
  const result = {
    label: node.label || node.nodeName || '未命名节点',
    value: node.value || node.nodeKey || `unknown_${Date.now()}_${Math.random()}`,
    isLeaf: !!node.isLeaf,
    status: node.status || '0',
    scheduleStartTime: node.scheduleStartTime,  // 排期开始时间
    scheduleEndTime: node.scheduleEndTime,      // 排期结束时间
    estimateHours: node.estimateHours,          // 预估工时
    type: 'node'                               // 明确设置类型为节点
  };
  return result;
};

const processTaskData = (task: any) => {
  const result = {
    label: task.label || task.taskName || '未命名任务',
    value: task.value || task.taskKey || `unknown_${Date.now()}_${Math.random()}`,
    isLeaf: true,
    status: task.status || '0',
    scheduleStartTime: task.scheduleStartTime,  // 排期开始时间
    scheduleEndTime: task.scheduleEndTime,      // 排期结束时间
    estimateHours: task.estimateHours,         // 预估工时
    assignee: task.assignee,                   // 负责人ID
    assigneeName: task.assigneeName,           // 负责人姓名
    type: 'task'                              // 明确设置类型为任务
  };
  return result;
};

const processWorkItemData = (workItem: any): WorkItemData => {
  const result = {
    label: workItem.label || workItem.name || '未命名工作项',
    value: workItem.value || workItem.itemKey || `unknown_${Date.now()}_${Math.random()}`,
    isLeaf: !!workItem.isLeaf,
    status: workItem.status || '0',
    type: 'workItem'                          // 明确设置类型为工作项
  };
  return result;
};

const WaterfallCascader: React.FC<WaterfallCascaderProps> = ({
  value = [],
  onChange,
  onDropdownVisibleChange,
  placeholder = "请选择工作内容",
  disabled = false,
  style,
  className,
  displayRender,
  loading: externalLoading = false,
  onStatisticsLoad,
  onStatisticsChange
}) => {
  const [treeData, setTreeData] = useState<CascaderItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [cache, setCache] = useState<CacheData>({});
  const cascaderRef = useRef<any>(null);

  // 获取缓存键
  const getCacheKey = useCallback((level: number, parentId?: string | number): string => {
    switch (level) {
      case 1: return 'workItems';
      case 2: return `instances_${parentId}`;
      case 3: return `nodes_${parentId}`;
      case 4: return `tasks_${parentId}`;
      default: return `level_${level}_${parentId}`;
    }
  }, []);

  // 加载工作项
  const loadWorkItems = async () => {
    const cacheKey = getCacheKey(1);
    if (cache[cacheKey]?.data.length > 0) {
      setTreeData(cache[cacheKey].data);
      return;
    }

    setLoading(true);
    try {
      const response = await get<ApiResponse<any>>('/hours/workItemCascade/workItems');
      if (response?.success) {
        const data = response.result?.records || response.result || [];
        const formattedData = data.map(processWorkItemData);
        setTreeData(formattedData);
        setCache(prev => ({
          ...prev,
          [cacheKey]: {
            data: formattedData,
            pagination: {
              pageNo: 1,
              pageSize: 5,
              total: formattedData.length,
              hasMore: false,
              loading: false
            },
            allLoaded: true
          }
        }));
      }
    } catch (error) {
      console.error('加载工作项失败:', error);
      Notification.error({
        title: '加载失败',
        content: '加载工作项数据失败，请重试'
      });
    } finally {
      setLoading(false);
    }
  };

  // 加载数据
  const loadData = async (
    level: number,
    parentId: string | number,
    selectedPath: CascaderItem[],
    pageNo: number = 1
  ): Promise<CascaderItem[]> => {
    try {
      let result: CascaderItem[] = [];
      const projectKey = selectedPath[0]?.value;

      switch (level) {
        case 1: // 加载工作项
          const workItemsResponse = await get<ApiResponse<any[]>>('/hours/workItemCascade/workItems');
          if (workItemsResponse?.success) {
            result = workItemsResponse.result.map(processWorkItemData);
          }
          break;

        case 2: // 加载实例
          const instancesResponse = await get<ApiResponse<{records: any[]}>>('/hours/workItemCascade/instances', {
            workItemKey: parentId,
            pageNo,
            pageSize: 10
          });
          if (instancesResponse?.success) {
            result = instancesResponse.result.records.map(processInstanceData);
          }
          break;

        case 3: // 加载节点
          const nodesResponse = await get<ApiResponse<any[]>>('/hours/workItemCascade/nodes', {
            workItemKey: selectedPath[0]?.value,
            instanceKey: parentId
          });
          if (nodesResponse?.success) {
            result = nodesResponse.result.map(processNodeData);
          }
          break;

        case 4: // 加载任务
          const tasksResponse = await get<ApiResponse<{records: any[]}>>('/hours/workItemCascade/tasks', {
            workItemKey: selectedPath[0]?.value,
            instanceKey: selectedPath[1]?.value,
            nodeKey: parentId,
            pageNo,
            pageSize: 10
          });
          if (tasksResponse?.success) {
            result = tasksResponse.result.records.map(processTaskData);
          }
          break;
      }

      return result;
    } catch (error) {
      console.error('加载数据失败:', error);
      return [];
    }
  };

  // 更新树形数据
  const updateTreeData = (list: CascaderItem[], targetValue: string | number, children: CascaderItem[]): CascaderItem[] => {
    return list.map(node => {
      if (node.value === targetValue) {
        return { ...node, children };
      }
      if (node.children) {
        return { ...node, children: updateTreeData(node.children, targetValue, children) };
      }
      return node;
    });
  };

  // 级联加载数据处理
  const onLoadData = async (selectedOpt: CascaderItem[]) => {
    if (!selectedOpt || selectedOpt.length === 0) {
      return Promise.resolve();
    }

    const targetOpt = selectedOpt[selectedOpt.length - 1];
    const level = selectedOpt.length;

    if (targetOpt.children && targetOpt.children.length > 0 || targetOpt.isLeaf) {
      return Promise.resolve();
    }

    const children = await loadData(level + 1, targetOpt.value, selectedOpt);
    setTreeData(prev => updateTreeData(prev, targetOpt.value, children));

    return Promise.resolve();
  };

  // 处理选择变更
  const handleChange = (value: any) => {
    console.log('级联选择器值变更:', value);
    
    // 确保 onChange 回调接收到正确的参数
    onChange?.(value);
    
    // 如果有选择项，获取统计信息
    if (value && value.length > 0) {
      try {
        // 根据选择的路径构造统计接口参数
        const level = value.length;
        
        // 构造参数对象
        const params: any = {
          workItemKey: value[0] // 工作项Key是必需的
        };
        
        // 根据级别添加其他参数
        if (level >= 2 && value[1]) {
          params.instanceKey = value[1];
        }
        if (level >= 3 && value[2]) {
          params.nodeKey = value[2];
        }
        if (level >= 4 && value[3]) {
          params.taskKey = value[3];
        }
        
        console.log(`选择级别: ${level}, 统计接口参数:`, params);
        
        get<ApiResponse<StatisticsResult>>('/hours/workItemCascade/statistics', params)
          .then(response => {
            console.log('统计信息响应:', response);
            if (response.success) {
              onStatisticsChange?.(response.result);
            } else {
              console.warn('获取统计信息失败:', response.message);
            }
          })
          .catch(error => {
            console.error('获取统计信息失败:', error);
            Notification.error({
              title: '获取统计信息失败',
              content: '请稍后重试'
            });
          });
      } catch (error) {
        console.error('处理选项时出错:', error);
        Notification.error({
          title: '处理选项失败',
          content: '请稍后重试'
        });
      }
    } else {
      console.log('清空选择，重置统计信息');
      // 清空时也触发统计信息变更
      onStatisticsChange?.(null);
    }
  };

  // 处理下拉框可见性变化
  const handleDropdownVisibleChange = (visible: boolean) => {
    onDropdownVisibleChange?.(visible);
    if (visible && treeData.length === 0) {
      loadWorkItems();
    }
  };

  return (
    <Cascader
      ref={cascaderRef}
      style={style}
      className={className}
      placeholder={placeholder}
      disabled={disabled || externalLoading}
      value={value}
      treeData={treeData}
      loadData={onLoadData}
      onChange={handleChange}
      onDropdownVisibleChange={handleDropdownVisibleChange}
      displayRender={displayRender}
      loading={loading || externalLoading}
      filterTreeNode
      showClear={true}
      onClear={() => {
        console.log('级联选择器清除操作');
        // 传递空数组和空选项
        onChange?.([]);
        // 重置统计信息
        onStatisticsChange?.(null);
      }}
    />
  );
};

export default WaterfallCascader;