import { get, post } from '../../utils/request';

/**
 * 获取管理员列表
 */
export async function getAdminList() {
  return get('/hours/sys/user/admin/list');
}

/**
 * 移除管理员
 * @param id 用户ID
 */
export async function removeAdmin(id: string) {
  return post('/hours/sys/user/admin/remove', { id });
}

/**
 * 添加管理员
 * @param id 用户ID
 */
export async function addAdmin(id: string) {
  return post('/hours/sys/user/admin/add', { id });
}

/**
 * 批量添加管理员
 * @param ids 用户ID列表
 */
export async function batchAddAdmins(ids: string[]) {
  return post('/hours/sys/user/admin/batchAdd', { ids });
}

/**
 * 获取可添加为管理员的用户列表
 */
export async function getAllUsersForAdminSelection() {
  return get('/hours/sys/user/list');
} 