package org.jeecg.modules.hours.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工时记录明细表
 */
@Data
@TableName("hour_record_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="hour_record_detail对象", description="工时记录明细表")
public class HourRecordDetail implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**
     * 关联工时记录ID
     */
    @Excel(name = "关联工时记录ID", width = 15)
    @ApiModelProperty(value = "关联工时记录ID")
    private String recordId;
    
    /**
     * 工作日期
     */
    @Excel(name = "工作日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "工作日期")
    private Date workDate;
    
    /**
     * 项目ID
     */
    @Excel(name = "项目ID", width = 15)
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    
    /**
     * 项目名称
     */
    @Excel(name = "项目名称", width = 15)
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    
    /**
     * 工作描述
     */
    @Excel(name = "工作描述", width = 30)
    @ApiModelProperty(value = "工作描述")
    private String description;
    
    /**
     * 工时百分比
     */
    @Excel(name = "工时百分比", width = 15)
    @ApiModelProperty(value = "工时百分比")
    private BigDecimal percentage;
    
    /**
     * 实际工时(小时)
     */
    @Excel(name = "实际工时", width = 15)
    @ApiModelProperty(value = "实际工时(小时)")
    private BigDecimal actualHours;
    
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /**
     * 是否休息：0-否；1-是
     */
    @Excel(name = "是否休息：0-否；1-是", width = 15)
    @ApiModelProperty(value = "是否休息：0-否；1-是")
    private Integer isRest;

}