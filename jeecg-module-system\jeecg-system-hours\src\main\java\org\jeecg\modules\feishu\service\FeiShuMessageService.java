package org.jeecg.modules.feishu.service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.contact.v3.model.BatchUserReq;
import com.lark.oapi.service.contact.v3.model.BatchUserResp;
import com.lark.oapi.service.contact.v3.model.GetUserReq;
import com.lark.oapi.service.contact.v3.model.GetUserResp;
import com.lark.oapi.service.contact.v3.model.User;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;

import lombok.extern.slf4j.Slf4j;

@Slf4j
//@Service
public class FeiShuMessageService {

	private static Client client;

	private static FeiShuMessageService _instance;

	private FeiShuMessageService() {
	}

	public static FeiShuMessageService instance(String appId, String appSecret) {
		client = Client.newBuilder(appId, appSecret).build();
		if (null == _instance) {
			_instance = new FeiShuMessageService();
		}
		return _instance;
	}

	/**
	 * 发送消息卡片
	 * 
	 * @param receiveId 接收人union_id
	 * @param title     消息标题
	 * @param content   消息内容
	 * @param url       点击跳转链接
	 * @return 发送是否成功
	 */
	private boolean sendCardMessage(String unionId, String title, String content, String buttonText, String buttonUrl) {
		try {
			// 构建卡片JSON，确保所有特殊字符被正确转义
			String cardJson = String.format(
					"{\"config\":{\"wide_screen_mode\":true},\"header\":{\"title\":{\"tag\":\"plain_text\",\"content\":\"%s\"},\"template\":\"blue\"},\"elements\":[{\"tag\":\"markdown\",\"content\":\"%s\"},{\"actions\":[{\"tag\":\"button\",\"text\":{\"tag\":\"plain_text\",\"content\":\"%s\"},\"type\":\"primary\",\"url\":\"%s\"}],\"tag\":\"action\"}]}",
					title.replace("\\", "\\\\").replace("\"", "\\\""),
					content.replace("\\", "\\\\").replace("\"", "\\\""),
					buttonText.replace("\\", "\\\\").replace("\"", "\\\""), buttonUrl);

			// 验证JSON格式
			JSON.parseObject(cardJson);

			CreateMessageReq req = CreateMessageReq.newBuilder().receiveIdType("union_id")
					.createMessageReqBody(CreateMessageReqBody.newBuilder().receiveId(unionId).msgType("interactive")
							.content(cardJson).uuid(UUID.randomUUID().toString()).build())
					.build();

			CreateMessageResp resp = client.im().v1().message().create(req);

			if (!resp.success()) {
				System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
						resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
								.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
				log.error("code:{},msg:{},reqId:{}, resp:{}", resp.getCode(), resp.getMsg(), resp.getRequestId(),
						Jsons.createGSON(true, false).toJson(JsonParser
								.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8))));
				return false;
			}

			System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
			return resp.success();
		} catch (Exception e) {
			log.error("构建卡片消息失败: " + e.getMessage());
			log.error("请检查卡片JSON格式是否正确");
			return false;
		}
	}

	public void sendMessage(String unionId, String title, String content, String buttonText, String buttonUrl) {
		if(StringUtils.isEmpty(unionId)) {
			log.error("暂无需要推送的消息!unionId={},title={},content={},buttonText={},buttonUrl={}",unionId,title, content, buttonText, buttonUrl);
			return;
		}

		this.sendCardMessage(unionId, title, content, buttonText, buttonUrl);

	}
	
	public void sendMessage(List<String> unionIdList, String title, String content, String buttonText, String buttonUrl) {

		if(CollectionUtils.isEmpty(unionIdList)) {
			log.error("暂无需要推送的消息!unionIdList={},title={},content={},buttonText={},buttonUrl={}",unionIdList,title, content, buttonText, buttonUrl);
			return;
		}
		for (String unionId : unionIdList) {
			this.sendCardMessage(unionId, title, content, buttonText, buttonUrl);	
		}
	}

}
