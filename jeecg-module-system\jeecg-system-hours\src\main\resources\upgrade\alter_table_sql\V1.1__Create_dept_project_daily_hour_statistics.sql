-- 部门项目每日工时分布统计表
CREATE TABLE IF NOT EXISTS `dept_project_daily_hour_statistics` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `stat_month` varchar(7) NOT NULL COMMENT '统计月份(格式: 2025-07)',
  `department_id` varchar(32) NOT NULL COMMENT '部门ID',
  `department_name` varchar(100) NOT NULL COMMENT '部门名称',
  `project_id` varchar(32) NOT NULL COMMENT '项目ID',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `project_code` varchar(50) DEFAULT NULL COMMENT '项目编码',
  `total_hours` decimal(10,2) DEFAULT 0.00 COMMENT '月总工时',
  `daily_hours_json` text COMMENT '每日工时JSON数据,格式: {"1": 8.0, "2": 7.5, ...}',
  `is_holiday` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否统计假日：0-否；1-是',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dept_project_month_holiday` (`stat_month`, `department_id`, `project_id`, `is_holiday`),
  KEY `idx_stat_month` (`stat_month`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_is_holiday` (`is_holiday`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部门项目每日工时分布统计表';

-- 添加索引优化查询性能
CREATE INDEX `idx_dept_project_month` ON `dept_project_daily_hour_statistics` (`stat_month`, `department_id`, `project_id`);
CREATE INDEX `idx_month_holiday` ON `dept_project_daily_hour_statistics` (`stat_month`, `is_holiday`);