package org.jeecg.modules.hours.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 飞书项目实例缓存表
 * @Author: jeecg-boot
 * @Date: 2023-08-20
 * @Version: V1.0
 */
@Data
@TableName("fs_instance_cache")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="飞书项目实例缓存对象", description="缓存飞书项目实例数据")
public class FsInstanceCache implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    /** 飞书项目key */
    @ApiModelProperty(value = "飞书项目key")
    private String projectKey;
    
    /** 工作项键值 */
    @ApiModelProperty(value = "工作项键值")
    private String workItemKey;
    
    /** 实例键值(迭代版本ID) */
    @ApiModelProperty(value = "实例键值(迭代版本ID)")
    private String instanceKey;
    
    /** 实例名称 */
    @ApiModelProperty(value = "实例名称")
    private String name;
    
    /** 状态 */
    @ApiModelProperty(value = "状态")
    private String status;
    
    /** 数据更新时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "数据更新时间")
    private Date updateTime;
    
    /** 负责人ID */
    @TableField
    @ApiModelProperty(value = "负责人ID")
    private String assignee;
    
    /** 负责人姓名 */
    @TableField
    @ApiModelProperty(value = "负责人姓名")
    private String assigneeName;
    
    /** 预估总工时 */
    @TableField
    @ApiModelProperty(value = "预估总工时")
    private BigDecimal estimateHours;
    
    /** 计划开始时间 */
    @TableField
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划开始时间")
    private Date scheduleStartTime;
    
    /** 计划结束时间 */
    @TableField
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划结束时间")
    private Date scheduleEndTime;
    
//    /** 临时字段，用于前端显示，不存入数据库 */
//    @TableField(exist = false)
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @ApiModelProperty(value = "排期开始时间")
//    private Date startTime;
//    
//    /** 临时字段，用于前端显示，不存入数据库 */
//    @TableField(exist = false)
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @ApiModelProperty(value = "排期结束时间")
//    private Date endTime;
}