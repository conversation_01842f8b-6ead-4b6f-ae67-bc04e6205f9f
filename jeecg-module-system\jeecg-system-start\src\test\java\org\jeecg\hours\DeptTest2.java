package org.jeecg.hours;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.feishu.service.FeiShuDeptService;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.task.AttendanceStatJob;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.attendance.v1.model.ChildItem;
import com.lark.oapi.service.attendance.v1.model.Item;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UserStatsView;
import com.lark.oapi.service.contact.v3.model.Department;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 消息推送测试
 * @Author: lsq
 */
@Slf4j
//@ActiveProfiles("dev")
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class DeptTest2 {

	private static final String appId = "cli_a7b2f4bbcb38500d";

	private static final String appSecret = "kzen0nGwHB15eEUVsDXkp1wn0YOF4HUK";

	@org.junit.jupiter.api.Test
	public void testname() throws Exception {

		FeiShuDeptService deptService = FeiShuDeptService.instance(appId, appSecret);

		List<Department> allList = new ArrayList<Department>();
		collectAllDepartments(deptService, "0", allList);

		log.info("children.size:{}", CollectionUtils.size(allList));
		for (Department department : allList) {
			log.info("children:{}", Jsons.DEFAULT.toJson(department));
		}

	}
	
    private void collectAllDepartments(FeiShuDeptService deptService, String deptId, List<Department> result) throws Exception {
        Department dept = deptService.getDeptById(deptId);
        if(null == dept) {
        	return;
        }
        result.add(dept);
        List<Department> children = deptService.children(deptId);
        for (Department child : children) {
        	result.add(child);
            collectAllDepartments(deptService, child.getDepartmentId(), result);
        }
    }

}
