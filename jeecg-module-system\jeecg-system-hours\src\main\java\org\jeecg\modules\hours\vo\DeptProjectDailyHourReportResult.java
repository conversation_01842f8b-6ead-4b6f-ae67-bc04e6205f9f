package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 部门项目每日工时分布报表结果VO
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DeptProjectDailyHourReportResult", description = "部门项目每日工时分布报表结果VO")
public class DeptProjectDailyHourReportResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 动态日期列信息
     */
    @ApiModelProperty(value = "动态日期列信息")
    private List<DayColumnVO> dayColumns;

    /**
     * 部门项目工时数据
     */
    @ApiModelProperty(value = "部门项目工时数据")
    private List<DeptProjectDailyHourReportVO> deptProjectHourData;

    /**
     * 总计行数据
     */
    @ApiModelProperty(value = "总计行数据")
    private DeptProjectDailyHourReportVO totalRow;

    /**
     * 统计摘要信息
     */
    @ApiModelProperty(value = "统计摘要信息")
    private ReportSummaryVO summary;

    @Data
    @Accessors(chain = true)
    public static class DayColumnVO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 日期列key
         */
        @ApiModelProperty(value = "日期列key")
        private String columnKey;

        /**
         * 日期显示名称
         */
        @ApiModelProperty(value = "日期显示名称")
        private String dayName;

        /**
         * 完整日期
         */
        @ApiModelProperty(value = "完整日期")
        private String fullDate;

        /**
         * 是否假日
         */
        @ApiModelProperty(value = "是否假日")
        private Boolean isHoliday;
    }

    @Data
    @Accessors(chain = true)
    public static class ReportSummaryVO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 统计日期范围
         */
        @ApiModelProperty(value = "统计日期范围")
        private String dateRange;

        /**
         * 总工时
         */
        @ApiModelProperty(value = "总工时")
        private BigDecimal totalHours;

        /**
         * 部门数量
         */
        @ApiModelProperty(value = "部门数量")
        private Integer deptCount;

        /**
         * 项目数量
         */
        @ApiModelProperty(value = "项目数量")
        private Integer projectCount;

        /**
         * 参与人数
         */
        @ApiModelProperty(value = "参与人数")
        private Integer userCount;

        /**
         * 统计天数
         */
        @ApiModelProperty(value = "统计天数")
        private Integer dayCount;
    }
}