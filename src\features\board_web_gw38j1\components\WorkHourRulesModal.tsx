import React, { useState, useEffect } from 'react';
import { Modal, Toast, Spin, Typography } from '@douyinfe/semi-ui';
import { RichTextEditor } from '@lark-project/ui-kit-plugin';
import { getFeishuSpaceId } from '../../../utils/auth';
import { get } from '../../../utils/request';

const { Text } = Typography;

interface RichTextContent {
  doc: string;
  doc_html: string;
  doc_text: string;
  is_empty: boolean;
}

interface ApiResponse<T = any> {
  success: boolean;
  result?: T;
  message?: string;
}

interface WorkHourRulesModalProps {
  visible: boolean;
  onClose: () => void;
}

const WorkHourRulesModal: React.FC<WorkHourRulesModalProps> = ({
  visible,
  onClose
}) => {
  const [loading, setLoading] = useState(false);
  const [spaceId, setSpaceId] = useState<string | null>(null);
  const [richTextContent, setRichTextContent] = useState<RichTextContent>({
    doc: '',
    doc_html: '',
    doc_text: '',
    is_empty: false
  });

  // 获取飞书空间ID
  useEffect(() => {
    const feishuSpaceId = getFeishuSpaceId();
    setSpaceId(feishuSpaceId);
  }, []);

  // 加载富文本内容
  useEffect(() => {
    if (visible) {
      loadRichTextContent();
    }
  }, [visible]);

  // 加载富文本内容
  const loadRichTextContent = async () => {
    try {
      setLoading(true);
      const response = await get('/hours/sys/config/rules/richtext') as ApiResponse;
      
      if (response && response.success) {
        if (response.result) {
          try {
            const parsedContent = JSON.parse(response.result);
            // const content = JSON.parse(parsedContent.content);
            setRichTextContent(parsedContent);
            console.log('成功加载富文本内容:', parsedContent);
          } catch (e) {
            console.error('解析富文本内容失败:', e);
            // 如果解析失败，说明是旧格式，将其作为doc内容
            setRichTextContent({
              doc: response.result,
              doc_html: '',
              doc_text: '',
              is_empty: false
            });
          }
        } else {
          console.warn('服务器返回空富文本内容，使用默认值');
          setRichTextContent({
            doc: '<p>1. 工时填写需真实反映实际工作情况，禁止弄虚作假。</p><p>2. 每日工时填写不得超过当日考勤工时。</p><p>3. 请在当天或次日完成工时填写，月底务必在规定时间前完成。</p><p>4. 工时需经项目负责人和直属上级审批通过。</p><p>5. 请假当天不得填写工时。</p>',
            doc_html: '',
            doc_text: '',
            is_empty: false
          });
        }
      } else {
        console.error('获取富文本内容失败:', response?.message);
        Toast.error('获取规则说明内容失败');
        // 使用默认内容
        setRichTextContent({
          doc: '<p>1. 工时填写需真实反映实际工作情况，禁止弄虚作假。</p><p>2. 每日工时填写不得超过当日考勤工时。</p><p>3. 请在当天或次日完成工时填写，月底务必在规定时间前完成。</p><p>4. 工时需经项目负责人和直属上级审批通过。</p><p>5. 请假当天不得填写工时。</p>',
          doc_html: '',
          doc_text: '',
          is_empty: false
        });
      }
    } catch (error) {
      console.error('加载富文本内容出错:', error);
      Toast.error('获取规则说明内容失败');
      // 使用默认内容
      setRichTextContent({
        doc: '<p>1. 工时填写需真实反映实际工作情况，禁止弄虚作假。</p><p>2. 每日工时填写不得超过当日考勤工时。</p><p>3. 请在当天或次日完成工时填写，月底务必在规定时间前完成。</p><p>4. 工时需经项目负责人和直属上级审批通过。</p><p>5. 请假当天不得填写工时。</p>',
        doc_html: '',
        doc_text: '',
        is_empty: false
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="工时登记规则说明"
      visible={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      bodyStyle={{ padding: '5px', minHeight: '400px' }}
    >
      <div style={{ position: 'relative' }}>
        {loading && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            zIndex: 1
          }}>
            <Spin />
          </div>
        )}
        
        {!loading && richTextContent && (
          <div style={{ 
            border: '1px solid var(--semi-color-border)',
            borderRadius: '6px',
            padding: '16px',
            minHeight: '300px',
            backgroundColor: 'var(--semi-color-bg-0)'
          }}>
            {spaceId ? (
              <RichTextEditor 
                spaceId={spaceId} 
                editable={false} 
                defaultValue={{ 
                  doc: richTextContent?.doc
                }}
              />
            ) : (
              <div style={{ 
                padding: '16px',
                color: 'var(--semi-color-text-1)',
                lineHeight: '1.6'
              }}>
                <Text>
                  {richTextContent.doc_text || '正在加载规则说明...'}
                </Text>
              </div>
            )}
          </div>
        )}
        
        {!loading && !richTextContent.doc && (
          <div style={{ 
            padding: '32px',
            textAlign: 'center',
            color: 'var(--semi-color-text-2)'
          }}>
            <Text>暂无规则说明内容</Text>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default WorkHourRulesModal;
