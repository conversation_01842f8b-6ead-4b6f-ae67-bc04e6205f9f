package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.entity.FsWorkItemCache;
import org.jeecg.modules.hours.entity.FsInstanceCache;
import org.jeecg.modules.hours.entity.FsNodeCache;
import org.jeecg.modules.hours.entity.FsTaskCache;

import java.util.List;
import java.util.Map;

/**
 * @Description: 飞书数据缓存服务
 * @Author: jeecg-boot
 * @Date: 2023-08-20
 * @Version: V1.0
 */
public interface IFsCacheService {
    
    /**
     * 获取项目的工作项列表
     * @param projectKey 项目键值
     * @return 工作项列表
     */
    List<FsWorkItemCache> getWorkItems(String projectKey);
    
    /**
     * 获取工作项下的实例列表
     * @param projectKey 项目键值
     * @param workItemKey 工作项键值
     * @return 实例列表
     */
    List<FsInstanceCache> getInstances(String projectKey, String workItemKey);
    
    /**
     * 获取实例下的节点列表
     * @param projectKey 项目键值
     * @param instanceKey 实例键值
     * @return 节点列表
     */
    List<FsNodeCache> getNodes(String projectKey, String instanceKey);
    
    /**
     * 获取节点下的任务列表
     * @param projectKey 项目键值
     * @param nodeKey 节点键值
     * @return 任务列表
     */
    List<FsTaskCache> getTasks(String projectKey, String nodeKey);
    
    /**
     * 同步工作空间的工作项数据
     * @param projectKey 项目键值
     * @return 是否成功
     */
    boolean syncWorkItems(String projectKey);
    
    /**
     * 同步工作项的实例数据
     * @param projectKey 项目键值
     * @param workItemKey 工作项键值
     * @return 是否成功
     */
    boolean syncInstances(String projectKey, String workItemKey);
    
    /**
     * 同步实例的节点数据
     * @param projectKey 项目键值
     * @param workItemKey 工作项键值
     * @param instanceKey 实例键值
     * @return 是否成功
     */
    boolean syncNodes(String projectKey, String workItemKey, String instanceKey);
    
    /**
     * 同步节点的任务数据
     * @param projectKey 项目键值
     * @param workItemKey 工作项键值
     * @param instanceKey 实例键值
     * @param nodeKey 节点键值
     * @return 是否成功
     */
//    boolean syncTasks(String projectKey, String workItemKey, String instanceKey, String nodeKey);
    
    /**
     * 获取工作项详情
     * @param projectKey 项目键值
     * @param itemKey 工作项键值
     * @return 工作项详情
     */
    FsWorkItemCache getWorkItemDetail(String projectKey, String itemKey);
    
    /**
     * 获取实例详情
     * @param projectKey 项目键值
     * @param instanceKey 实例键值
     * @return 实例详情
     */
    FsInstanceCache getInstanceDetail(String projectKey, String instanceKey);
    
    /**
     * 获取节点详情
     * @param projectKey 项目Key
     * @param workItemKey 工作项Key
     * @param instanceKey 实例Key
     * @param nodeKey 节点Key
     * @return 节点缓存对象
     */
    FsNodeCache getNodeDetail(String projectKey, String workItemKey, String instanceKey, String nodeKey);
    
    /**
     * 获取任务详情
     * @param projectKey 项目键值
     * @param taskKey 任务键值
     * @return 任务详情
     */
    FsTaskCache getTaskDetail(String projectKey, String taskKey);
    
    /**
     * 获取项目的所有实例列表，不区分工作项
     * @param projectKey 项目键值
     * @return 所有实例列表
     */
    List<FsInstanceCache> getAllInstances(String projectKey);
    
    /**
     * 获取项目的所有节点列表，不区分实例
     * @param projectKey 项目键值
     * @return 所有节点列表
     */
    List<FsNodeCache> getAllNodes(String projectKey);
    
    /**
     * 获取项目的所有任务列表，不区分节点
     * @param projectKey 项目键值
     * @return 所有任务列表
     */
    List<FsTaskCache> getAllTasks(String projectKey);
    
    /**
     * 同步任务数据到 fs_data_task 表
     * @param projectKey 项目键值
     * @param workItemKey 工作项键值
     * @param instanceKey 实例键值
     * @param nodeKey 节点键值
     * @return 是否成功
     */
    boolean syncFsDataTask(String projectKey, String workItemKey, String instanceKey, String nodeKey);
    
    /**
     * 根据项目key、工作项key、实例key和节点key获取任务列表
     * @param projectKey 项目key
     * @param workItemKey 工作项key
     * @param instanceKey 实例key
     * @param nodeKey 节点key
     * @return 任务列表
     */
    List<FsTaskCache> getTasksByKeys(String projectKey, String workItemKey, String instanceKey, String nodeKey);
    
    /**
     * 同步单个工作项
     * @param projectKey 项目标识
     * @param workItemTypeKey 工作项类型标识
     * @param workItemId 工作项ID
     * @return 是否同步成功
     */
    boolean syncWorkItem(String projectKey, String workItemTypeKey, Long workItemId);
    
    /**
     * 同步单个任务
     * @param projectKey 项目标识
     * @param workItemTypeKey 工作项类型标识
     * @param taskId 任务ID
     * @return 是否同步成功
     */
    boolean syncTask(String projectKey, String workItemTypeKey, Long taskId);
    
    /**
     * 删除任务
     * @param projectKey 项目标识
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteTask(String projectKey, Long taskId);
    
    /**
     * 删除工作项
     * @param projectKey 项目标识
     * @param workItemTypeKey 工作项类型标识
     * @param workItemId 工作项ID
     * @return 是否删除成功
     */
    boolean deleteWorkItem(String projectKey, String workItemTypeKey, Long workItemId);
    
    /**
     * 同步单个节点
     * @param projectKey 项目标识
     * @param workItemTypeKey 工作项类型标识
     * @param instanceKey 实例键值
     * @param nodeKey 节点键值
     * @return 是否同步成功
     */
    boolean syncNode(String projectKey, String workItemTypeKey, String instanceKey, String nodeKey);
} 