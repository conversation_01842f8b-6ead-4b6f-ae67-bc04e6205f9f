# 项目管理模块

## 数据字典组件使用说明

本项目中使用了 `DictSelect` 组件来处理数据字典相关的下拉选择功能。该组件会自动从后端获取数据字典项并渲染成下拉选择框。

### 组件位置

```
front/project-hours-front/src/components/DictSelect/index.tsx
```

### 使用方法

#### 1. 导入组件

```tsx
import DictSelect, { preloadDictItems } from '@/components/DictSelect';
```

#### 2. 基本使用

```tsx
<DictSelect dictCode="project_status" />
```

#### 3. 在表单中使用

```tsx
<Form.Slot label="项目状态">
  <DictSelect
    dictCode="project_status"
    value={formData.status}
    onChange={(value) => setFormData({ ...formData, status: value as string })}
  />
</Form.Slot>
```

#### 4. 获取字典项数据

如果需要在组件外部获取字典项数据，可以使用 `onLoadFinish` 回调：

```tsx
const [statusItems, setStatusItems] = useState([]);

// ...

<DictSelect
  dictCode="project_status"
  onLoadFinish={(items) => setStatusItems(items)}
/>
```

#### 5. 使用预加载功能（推荐）

为了避免重复请求和提高性能，建议使用预加载功能：

```tsx
import { preloadDictItems } from '@/components/DictSelect';

// 在组件初始化时预加载
useEffect(() => {
  // 预加载多个字典
  preloadDictItems(['project_status', 'user_role']);
}, []);
```

### 项目状态字典

项目状态字典代码为 `project_status`，包含以下状态：

| 值 | 文本 | 颜色 |
| --- | --- | --- |
| AN | 未启动 | grey |
| AO | 进行中 | blue |
| AD | 冻结 | orange |
| AI | 完成 | green |

### 在 ProjectMemberSettings 中的应用

在项目人员管理模块中，我们使用了 `DictSelect` 组件来处理项目状态的显示和选择：

1. 表格中的状态显示：
   - 在组件初始化时预加载状态字典项
   - 根据状态值显示不同颜色的标签

2. 编辑表单中的状态选择：
   - 使用 `DictSelect` 组件替代原来的硬编码选项
   - 支持禁用状态

### 性能优化

为了解决重复请求问题，我们对 `DictSelect` 组件进行了以下优化：

1. **组件级缓存**：使用全局静态变量缓存字典数据，相同的 `dictCode` 只会请求一次
2. **API 级缓存**：在 API 服务层也实现了缓存，避免重复的网络请求
3. **预加载机制**：通过 `preloadDictItems` 函数预加载字典数据
4. **优化回调处理**：使用 `useRef` 保存回调函数，避免因回调变化导致的重复请求

### 后端接口

数据字典接口由 `ApiDictController` 提供，路径为 `/dict/get`，需要传递 `dictCode` 参数。

```java
@GetMapping(value = "/get")
public Result<List<DictModel>> initConfig(@RequestParam("dictCode") String dictCode, HttpServletRequest req) {
    try {
        return Result.OK(commonAPI.queryDictItemsByCode(dictCode));
    } catch (Exception e) {
        log.error("初始化配置异常", e);
        return Result.error("初始化配置失败: " + e.getMessage());
    }
}
```

### 注意事项

1. 确保后端接口 `/dict/get` 可用且返回格式正确
2. 字典代码 `dictCode` 必须与后端定义的字典代码一致
3. 组件会在挂载时自动获取字典数据，无需手动调用
4. 如果需要在多个地方使用相同的字典数据，建议使用预加载功能
5. 避免在一个页面中多次渲染相同 `dictCode` 的 `DictSelect` 组件，可能会导致重复请求 