import React, {useCallback, useEffect, useMemo, useState} from "react";
import {Button, Form, Input, Modal, Notification, Select, Table, Tag, Tooltip, Typography} from "@douyinfe/semi-ui";
import {get, post} from "../../../utils/request";
import HourRecordAuditDetail from "../HourRecordAuditDetail";
import {getUserInfo} from "../../../utils/auth";
import "./ApprovalList.scss"
const { Text } = Typography;
// 添加可截断文本的展示组件
const TruncateText: React.FC<{ content: string, width?: number }> = ({ content, width }) => {
    if (!content) return <span>-</span>;

    return (
        <Tooltip content={content}>
      <span className="truncate-cell" style={{ width: width ? `${width}px` : '100%' }}>
        {content}
      </span>
        </Tooltip>
    );
};

// 接口返回的工时记录类型
interface HourRecord {
    id: string | number;
    recordId: string | number;
    userId: string;
    userName: string;
    workDate: string;
    hours: number;
    description: string;
    workItemId: string;       // 工作项ID
    workItemName: string;     // 工作项名称
    projectId: string;
    projectName: string;
    instanceId?: string;
    instanceName?: string;
    nodeId?: string;
    nodeName?: string;
    taskId?: string;
    taskName?: string;
    status: number | string;
    statusName: string;
    applyStatus?: number | string;
    applyStatusName?: string;
    dealStatus?: number | string;
    dealStatusName?: string;
    createdAt: string;
    submitTime?: string | null;
    approverName?: string | null;
    rejectReason?: string | null;
    workPath?: string;
    workPeriodStart?: string;
    workPeriodEnd?: string;
    applyId?: number; // 申请批次ID
    approvers?: {
        userId: string;
        userName: string;
        status: number;
        statusName: string;
        updatedAt: string;
    }[];
}

function toFormInitialData(record: HourRecord | null): any {
    if (!record) return undefined;
    return {
        workDate: record.workDate ? new Date(record.workDate) : undefined,
        worker: record.userId,
        workContent: [record.workItemId, record.instanceId, record.nodeId, record.taskId].filter(Boolean),
        projectKey: record.projectId,
        recordId: record.recordId, // 用recordId字段
        projectName: record.projectName,
        instanceName: record.instanceName,
        nodeName: record.nodeName,
        taskName: record.taskName,
        applyId: record.applyId // 添加申请批次ID
    };
}

// API响应接口
interface ApiResponse<T = any> {
    success: boolean;
    message?: string;
    code?: number;
    result: T;
    timestamp?: number;
}

// 分页结果接口
interface PageResult<T> {
    records: T[];
    total: number;
    size: number;
    current: number;
    pages?: number;
}

// 工时记录接口
interface HourRecord {
    id: string | number;
    userId: string;
    userName: string;
    workDate: string;
    hours: number;
    description: string;
    workItemId: string;
    workItemName: string;
    projectId: string;
    projectName: string;
    instanceId?: string;
    instanceName?: string;
    nodeId?: string;
    nodeName?: string;
    taskId?: string;
    taskName?: string;
    status: number | string;
    statusName: string;
    createdAt: string;
    submitTime?: string | null;
    approverName?: string | null;
    rejectReason?: string | null;
    workPath?: string;
    applyId?: number; // 申请批次ID
    approvers?: {
        userId: string;
        userName: string;
        status: number;
        statusName: string;
        updatedAt: string;
    }[];
}

// 分页查询参数接口
interface QueryParams {
    pageNo: number;
    pageSize: number;
    status?: string;
    tab?: string;
    startDate?: string;
    endDate?: string;
    employeeId?: string;
}

const ApprovalList: React.FC = () => {
    // 获取当前登录用户
    const currentUser = getUserInfo();

    const [activeTab, setActiveTab] = useState('pending');
    const [loading, setLoading] = useState(false);
    const [records, setRecords] = useState<HourRecord[]>([]);
    const [total, setTotal] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    // selectedRowKeys类型改为string[]
    const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
    const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
    const [rejectModalVisible, setRejectModalVisible] = useState(false);
    const [rejectReason, setRejectReason] = useState('');
    const [currentRecord, setCurrentRecord] = useState<HourRecord | null>(null);
    const [approveModalVisible, setApproveModalVisible] = useState(false);
    const [approveComment, setApproveComment] = useState('');
    const [batchApproveModalVisible, setBatchApproveModalVisible] = useState(false);
    const [batchRejectModalVisible, setBatchRejectModalVisible] = useState(false);
    const [visible, setVisible] = useState(false);
    const [queryParams, setQueryParams] = useState<QueryParams>({
        pageNo: 1,
        pageSize: 5,
        tab:activeTab,
    });
    const [transferModalVisible, setTransferModalVisible] = useState(false);
    const [transferRecord, setTransferRecord] = useState<HourRecord | null>(null);
    const [oldApproverId, setOldApproverId] = useState<string>('');
    const [newApproverId, setNewApproverId] = useState<string>('');

    // 新增批量移交相关状态
    const [batchTransferModalVisible, setBatchTransferModalVisible] = useState(false);
    const [batchOldApproverId, setBatchOldApproverId] = useState('');
    const [batchNewApproverId, setBatchNewApproverId] = useState('');
    const [batchTransferLoading, setBatchTransferLoading] = useState(false);

    // 获取工时审批列表
    const fetchRecords = useCallback(async (params?: Partial<QueryParams>) => {
        setLoading(true);
        let mergedParams = { ...queryParams, ...params, tab: activeTab };


  /*      // 仅“全部”Tab允许传status，其他Tab不传status
        if (activeTab !== 'all') {
            delete mergedParams.status;
        }
*/
        try {
            // 使用GET请求
            const response = await get<ApiResponse<PageResult<HourRecord>>>('/hours/hourRecordApply/approvals', mergedParams);

            if (response && response.success && response.result) {
                setRecords(response.result.records || []);
                setTotal(response.result.total || 0);
            } else {
                console.error('获取工时审批记录失败:', response);
                setRecords([]);
                setTotal(0);
            }
        } catch (error) {
            console.error('获取工时审批记录失败:', error);
            Notification.error({
                title: '获取工时审批记录失败',
                content: error.message
            });
            setRecords([]);
            setTotal(0);
        } finally {
            setLoading(false);
        }
    }, [queryParams]);

    useEffect(() => {
        fetchRecords();
    }, [activeTab]);


    // 处理审批通过
    const handleApprove = async () => {
        if (!currentRecord) return;

        if (!currentRecord.applyId) {
            Notification.error({
                title: '审批失败',
                content: '当前记录没有有效的申请批次ID'
            });
            return;
        }

        try {
            setLoading(true);
            const response = await post<ApiResponse<boolean>>('/hours/hourRecordApprover/approve', {
                recordId: currentRecord.recordId,
                applyId: currentRecord.applyId,
                action: 'approve',
                remarks: approveComment,
                approverId: currentUser?.userId,
                approverName: currentUser?.name
            });

            if (response && response.success) {
                Notification.success({
                    title: '审批成功',
                    content: '已通过该工时申请'
                });
                setApproveModalVisible(false);
                fetchRecords();
            } else {
                Notification.error({
                    title: '审批失败',
                    content: response?.message || '操作未完成，请重试'
                });
            }
        } catch (error: any) {
            console.error('审批失败:', error);
            Notification.error({
                title: '审批失败',
                content: error.message || '操作未完成，请重试'
            });
        } finally {
            setLoading(false);
        }
    };

    // 打开驳回模态窗
    const openRejectModal = (record: HourRecord) => {
        console.log('test.openRejectModal', record);
        setCurrentRecord(record);
        setRejectReason('');
        setRejectModalVisible(true);
    };

    // 处理审批驳回
    const handleReject = async () => {
        if (!currentRecord) return;

        if (!rejectReason.trim()) {
            Notification.warning({
                title: '请填写驳回原因',
                content: '驳回时必须填写驳回原因'
            });
            return;
        }

        if (!currentRecord.applyId) {
            Notification.error({
                title: '驳回失败',
                content: '当前记录没有有效的申请批次ID'
            });
            return;
        }

        try {
            setLoading(true);
            const response = await post<ApiResponse<boolean>>('/hours/hourRecordApprover/approve', {
                recordId: currentRecord.recordId,
                applyId: currentRecord.applyId,
                action: 'reject',
                remarks: rejectReason,
                approverId: currentUser?.userId,
                approverName: currentUser?.name
            });

            if (response && response.success) {
                Notification.success({
                    title: '驳回成功',
                    content: '已驳回该工时申请'
                });
                setRejectModalVisible(false);
                fetchRecords();
            } else {
                Notification.error({
                    title: '驳回失败',
                    content: response?.message || '操作未完成，请重试'
                });
            }
        } catch (error: any) {
            console.error('驳回失败:', error);
            Notification.error({
                title: '驳回失败',
                content: error.message || '操作未完成，请重试'
            });
        } finally {
            setLoading(false);
        }
    };

    // 打开单条审批通过确认弹窗
    const openApproveModal = (record: HourRecord) => {
        setCurrentRecord(record);
        setApproveComment('');
        setApproveModalVisible(true);
    };

    // 打开批量审批通过确认弹窗
    const openBatchApproveModal = () => {
        if (!selectedRowKeys || selectedRowKeys.length === 0) {
            Notification.warning({
                title: '请选择记录',
                content: '请先选择需要审批的工时记录'
            });
            return;
        }
        setBatchApproveModalVisible(true);
    };

    // 打开批量驳回确认弹窗
    const openBatchRejectModal = () => {
        if (!selectedRowKeys || selectedRowKeys.length === 0) {
            Notification.warning({
                title: '请选择记录',
                content: '请先选择需要驳回的工时记录'
            });
            return;
        }
        setRejectReason('');
        setBatchRejectModalVisible(true);
    };

    // 处理批量审批通过
    const handleBatchApprove = async () => {
        if (!selectedRowKeys || selectedRowKeys.length === 0) {
            Notification.warning({ title: '请选择记录', content: '请先选择需要审批的工时记录' });
            return;
        }
        try {
            setLoading(true);
            // 获取选中记录的applyId
            const selectedRecords = records.filter(record => selectedRowKeys.includes(String(record.id)));
            const applyIds = selectedRecords.map(record => record.applyId).filter(Boolean);

            if (applyIds.length === 0) {
                Notification.error({ title: '批量审批失败', content: '选中的记录中没有有效的申请批次ID' });
                return;
            }

            // 构建URL参数
            const applyIdsString = applyIds.join(',');
            const url = `/hours/hourRecordApply/batch-approve?applyIds=${applyIdsString}&action=approve`;
            const response = await post<ApiResponse<boolean>>(url, null, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            if (response && response.success) {
                Notification.success({
                    title: '批量审批成功',
                    content: `已通过${selectedRowKeys.length}条工时申请`
                });
                setSelectedRowKeys([]);
                setBatchApproveModalVisible(false);
                fetchRecords();
            } else {
                Notification.error({
                    title: '批量审批失败',
                    content: response?.message || '操作未完成，请重试'
                });
            }
        } catch (error: any) {
            console.error('批量审批失败:', error);
            Notification.error({
                title: '批量审批失败',
                content: error.message || '操作未完成，请重试'
            });
        } finally {
            setLoading(false);
        }
    };

    // 处理批量驳回
    const handleBatchReject = async () => {
        if (!selectedRowKeys || selectedRowKeys.length === 0) {
            Notification.warning({ title: '请选择记录', content: '请先选择需要驳回的工时记录' });
            return;
        }
        if (!rejectReason.trim()) {
            Notification.warning({
                title: '请填写驳回原因',
                content: '批量驳回时必须填写驳回原因'
            });
            return;
        }

        try {
            setLoading(true);
            // 获取选中记录的applyId
            const selectedRecords = records.filter(record => selectedRowKeys.includes(String(record.id)));
            const applyIds = selectedRecords.map(record => record.applyId).filter(Boolean);

            if (applyIds.length === 0) {
                Notification.error({ title: '批量驳回失败', content: '选中的记录中没有有效的申请批次ID' });
                return;
            }

            // 构建URL参数
            const applyIdsString = applyIds.join(',');
            const url = `/hours/hourRecordApply/batch-approve?applyIds=${applyIdsString}&action=reject&remarks=${encodeURIComponent(rejectReason)}`;
            const response = await post<ApiResponse<boolean>>(url, null, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            if (response && response.success) {
                Notification.success({
                    title: '批量驳回成功',
                    content: `已驳回${selectedRowKeys.length}条工时申请`
                });
                setSelectedRowKeys([]);
                setBatchRejectModalVisible(false);
                fetchRecords();
            } else {
                Notification.error({
                    title: '批量驳回失败',
                    content: response?.message || '操作未完成，请重试'
                });
            }
        } catch (error: any) {
            console.error('批量驳回失败:', error);
            Notification.error({
                title: '批量驳回失败',
                content: error.message || '操作未完成，请重试'
            });
        } finally {
            setLoading(false);
        }
    };

    // 批量移交弹窗
    const openBatchTransferModal = () => {
        if (!selectedRowKeys || selectedRowKeys.length === 0) {
            Notification.warning({
                title: '请选择记录',
                content: '请先选择需要移交的工时记录'
            });
            return;
        }
        setBatchOldApproverId('');
        setBatchNewApproverId('');
        setBatchTransferModalVisible(true);
    };

    const handleBatchTransfer = async () => {
        if (!batchOldApproverId || !batchNewApproverId) return;
        setBatchTransferLoading(true);
        try {
            await post('/api/work-hours/batch-transfer-approver', {
                recordIds: selectedRowKeys,
                oldApproverId: batchOldApproverId,
                newApproverId: batchNewApproverId
            });
            setBatchTransferModalVisible(false);
            setSelectedRowKeys([]);
            fetchRecords();
            Notification.success({ title: '批量移交成功' });
        } catch (e: any) {
            Notification.error({ title: '批量移交失败', content: e.message });
        } finally {
            setBatchTransferLoading(false);
        }
    };


    const handleTransfer = async () => {
        if (!transferRecord || !oldApproverId || !newApproverId) return;
        await post('/api/work-hours/transfer-approver', {
            recordId: transferRecord.id,
            oldApproverId,
            newApproverId
        });
        setTransferModalVisible(false);
        fetchRecords();
    };


    // 处理打开详情模态窗
    const handleOpenDetailRecord = (record: HourRecord) => {

        // 设置当前编辑的记录
        setCurrentRecord(record);

        // setEditMode('view');
        setVisible(true);
    };


    // 审批进度列
    const columns = [
        {
            title: '审批流水号',
            dataIndex: 'applyId',
            width: 120,

        },

        {
            title: '登记周期',
            dataIndex: 'workDate',
            width: 150,
            render: (_: any, record: any) => {
                const start = record.workPeriodStart ? record.workPeriodStart.replace(/-/g, '.').slice(0, 10) : '';
                const end = record.workPeriodEnd ? record.workPeriodEnd.replace(/-/g, '.').slice(0, 10) : '';
                return <TruncateText content={start && end ? `${start}-${end}` : '-'} />;
            }
        },
 /*       {
            title: '工作人员',
            dataIndex: 'userName',
            width: 90,
            render: (userName: string) => <TruncateText content={userName || '-'} />
        },*/
        {
            title: '状态',
            dataIndex: 'statusName',
            width: 90,
            render: (statusName: string, record: HourRecord) => {
                let type: 'primary' | 'success' | 'warning' | 'danger' | 'default' = 'default';
                let displayName = statusName;
                // 部分审批判断
                if (
                    record.status === 1 &&
                    Array.isArray(record.approvers) &&
                    record.approvers.length > 1 &&
                    record.approvers.some(a => a.status === 1) &&
                    record.approvers.some(a => a.status === 0)
                ) {
                    type = 'warning';
                    displayName = '部分审批';
                } else {
                    switch (record.status) {
                        case 0: // 待提交
                            type = 'default';
                            break;
                        case 1: // 审批中
                            type = 'primary';
                            break;
                        case 2: // 审批通过
                            type = 'success';
                            break;
                        case 3: // 审批退回
                            type = 'danger';
                            break;
                        default:
                            type = 'default';
                    }
                }
                return <Tag type={type as any}>{displayName}</Tag>;
            },
        },

        {
            title: '提交人',
            dataIndex: 'applicantName',
            width: 90,
            render: (approverName: string) => <TruncateText content={approverName || '-'} />
        },
        {
            title: '提交时间',
            dataIndex: 'applyTime',
            width: 160,
            render: (submitTime: string) => <TruncateText content={submitTime || '-'} />
        },
        {
            title: '操作',
            dataIndex: 'operation',
            width: 140,
            fixed: 'right' as const,
            render: (_: any, record: HourRecord) => (
                <div className={'operation'}>
                    <a className={'info'}
                        onClick={() => handleOpenDetailRecord(record)}
                    >
                        详情
                    </a>
                    {record.status === 1 && (
                        <>

                            <a className={'approve'}
                onClick={() => openApproveModal(record)}
              >
                通过
              </a>
              <a className={'reject'}
                onClick={() => openRejectModal(record)}
              >
                驳回
              </a>
                        </>
                    )}
                    {/* {isAdmin && (
            <Button type="tertiary" size="small" onClick={() => openTransferModal(record)}>
              移交
            </Button>
          )} */}
                </div>
            ),
        },
    ];
    const scroll = useMemo(() => ({ y: 140 }), []);
    return (
        <div className={'ApprovalList'}>
            <Table scroll={scroll}
                   className="approvalList"
                columns={columns}
                dataSource={records}
                loading={loading}
                pagination={false}
                size="middle"
            />

            {/* 单条驳回原因模态窗 */}
            <Modal
                title="驳回原因"
                visible={rejectModalVisible}
                onOk={handleReject}
                onCancel={() => setRejectModalVisible(false)}
                maskClosable={false}
                okText="确认驳回"
                cancelText="取消"
            >
                <Form>
                    <Form.TextArea
                        field="rejectReason"
                        label="驳回原因"
                        initValue={rejectReason}
                        onChange={(value: string) => setRejectReason(value)}
                        placeholder="请输入驳回原因"
                        rules={[
                            { required: true, message: '请输入驳回原因' }
                        ]}
                        rows={4}
                    />
                </Form>
            </Modal>

            {/* 单条审批通过确认弹窗 */}
            <Modal
                title="工时审批通过确认"
                visible={approveModalVisible}
                onOk={handleApprove}
                onCancel={() => setApproveModalVisible(false)}
                maskClosable={false}
                okText="确定"
                cancelText="取消"
            >
                <div style={{ padding: '20px 0' }}>
                    <Text>是否审批通过该数据？确认后当前工时立即生效。</Text>
                </div>
                <Form>
                    <Form.TextArea
                        field="approveComment"
                        label="审批意见（非必填）"
                        initValue={approveComment}
                        onChange={(value: string) => setApproveComment(value)}
                        placeholder="请输入审批意见"
                        rows={4}
                    />
                </Form>
            </Modal>

            {/* 批量审批通过确认弹窗 */}
            <Modal
                title="工时审批通过确认"
                visible={batchApproveModalVisible}
                onOk={handleBatchApprove}
                onCancel={() => setBatchApproveModalVisible(false)}
                maskClosable={false}
                okText="确定"
                cancelText="取消"
            >
                <div style={{ padding: '20px 0' }}>
                    <Text>是否审批通过该数据？确认后当前工时立即生效。</Text>
                </div>
                <Form>
                    <Form.TextArea
                        field="approveComment"
                        label="审批意见（非必填）"
                        initValue={approveComment}
                        onChange={(value: string) => setApproveComment(value)}
                        placeholder="请输入审批意见"
                        rows={4}
                    />
                </Form>
            </Modal>

            {/* 批量驳回确认弹窗 */}
            <Modal
                title="工时审批驳回确认"
                visible={batchRejectModalVisible}
                onOk={handleBatchReject}
                onCancel={() => setBatchRejectModalVisible(false)}
                maskClosable={false}
                okText="确定"
                cancelText="取消"
            >
                <div style={{ padding: '20px 0' }}>
                    <Text>是否驳回该数据？驳回后当前工时将被取回给对应工作人员</Text>
                </div>
                <Form>
                    <Form.TextArea
                        field="rejectReason"
                        label="审批意见（必填）"
                        initValue={rejectReason}
                        onChange={(value: string) => setRejectReason(value)}
                        placeholder="请输入驳回原因"
                        rules={[
                            { required: true, message: '请输入驳回原因' }
                        ]}
                        rows={4}
                    />
                </Form>
            </Modal>

            {/* 移交审批人弹窗 */}
            <Modal
                title="移交审批人"
                visible={transferModalVisible}
                onOk={handleTransfer}
                onCancel={() => setTransferModalVisible(false)}
                okText="确认移交"
                cancelText="取消"
            >
                <div style={{ marginBottom: 12 }}>
                    <div>原审批人：</div>
                    <Select
                        style={{ width: 200 }}
                        value={oldApproverId}
                        onChange={setOldApproverId}
                        placeholder="请选择原审批人"
                    >
                        {transferRecord?.approvers?.filter(a => a.status === 0).map(a => (
                            <Select.Option value={a.userId} key={a.userId}>{a.userName}</Select.Option>
                        ))}
                    </Select>
                </div>
                <div>
                    <div>新审批人：</div>
                    <Input
                        style={{ width: 200 }}
                        value={newApproverId}
                        onChange={setNewApproverId}
                        placeholder="请输入新审批人ID"
                    />
                    {/* 实际项目可用用户选择器，这里用输入框占位 */}
                </div>
            </Modal>

            {/* 批量移交弹窗 */}
            <Modal
                title="批量移交审批人"
                visible={batchTransferModalVisible}
                onOk={handleBatchTransfer}
                onCancel={() => setBatchTransferModalVisible(false)}
                okText="确认移交"
                cancelText="取消"
                confirmLoading={batchTransferLoading}
            >
                <div style={{ marginBottom: 12 }}>
                    <div>原审批人：</div>
                    <Select
                        style={{ width: 200 }}
                        value={batchOldApproverId}
                        onChange={setBatchOldApproverId}
                        placeholder="请选择原审批人"
                    >
                        {/* 修正批量移交弹窗中allApprovers的flatMap兼容性和类型 */}
                        {(() => {
                            const allApprovers = records
                                .filter(r => selectedRowKeys.includes(String(r.id)))
                                .map(r => r.approvers || [])
                                .reduce((acc, cur) => acc.concat(cur), [] as Approver[]);
                            const pendingApprovers = allApprovers.filter((a: Approver) => a.status === 0);
                            // 取唯一userId
                            const unique = Array.from(new Map(pendingApprovers.map(a => [a.userId, a])).values());
                            return unique.map(a => (
                                <Select.Option value={a.userId} key={a.userId}>{a.userName}</Select.Option>
                            ));
                        })()}
                    </Select>
                </div>
                <div>
                    <div>新审批人：</div>
                    <Input
                        style={{ width: 200 }}
                        value={batchNewApproverId}
                        onChange={setBatchNewApproverId}
                        placeholder="请输入新审批人ID"
                    />
                    {/* 实际项目可用用户选择器，这里用输入框占位 */}
                </div>
            </Modal>
            <HourRecordAuditDetail
                visible={visible}
                isEdit={false}
                readOnly={true}
                initialData={toFormInitialData(currentRecord)}
                onCancel={() => { setVisible(false); setCurrentRecord(null); }}
                onSave={fetchRecords}
                onSubmit={fetchRecords}
            />
        </div>
    )
}
export default ApprovalList;