package org.jeecg.modules.hours.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.hours.entity.SysConfig;

import java.util.List;
import java.util.Map;

/**
 * 系统配置表 服务接口
 */
public interface ISysConfigService extends IService<SysConfig> {

    /**
     * 获取配置值
     * @param projectKey 项目标识
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String projectKey, String configKey);

    /**
     * 获取配置值（带默认值）
     * @param projectKey 项目标识
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(String projectKey, String configKey, String defaultValue);

    /**
     * 获取分组配置
     * @param projectKey 项目标识
     * @param configGroup 配置组
     * @return 配置列表
     */
    List<SysConfig> getConfigsByGroup(String projectKey, String configGroup);

    /**
     * 获取所有配置组
     * @return 配置组列表
     */
    List<String> getAllGroups();

    /**
     * 保存配置
     * @param config 配置对象
     * @return 是否成功
     */
    boolean saveConfig(SysConfig config);

    /**
     * 批量保存配置
     * @param configs 配置列表
     * @return 是否成功
     */
    boolean batchSaveConfig(List<SysConfig> configs);

    /**
     * 获取工时基础设置
     * @param projectKey 项目标识
     * @return 工时设置Map
     */
    Map<String, Object> getWorkHourSettings(String projectKey);
    
    Map<String, Object> getSysSettings(String projectKey);

    /**
     * 保存工时基础设置
     * @param projectKey 项目标识
     * @param settings 设置Map
     * @return 是否成功
     */
    boolean saveWorkHourSettings(String projectKey, Map<String, Object> settings);

    /**
     * 保存或更新配置，如果当前projectKey下不存在该配置，则尝试从默认配置(projectKey='0')复制
     * @param projectKey 项目标识
     * @param configKey 配置键
     * @param configValue 配置值
     * @param extraParams 额外参数(configName,configGroup,valueType,defaultValue,sortOrder,remark等)
     * @return 是否成功
     */
    boolean saveOrUpdateConfigWithFallback(String projectKey, String configKey, String configValue, Map<String, Object> extraParams);

    /**
     * 复制配置到指定工作空间
     * @param projectKey 项目标识
     * @return 是否成功
     * 
     */
    boolean copyConfigs(String projectKey);
    
    /** 
     * 批量保存
     * @param params
     * @param projectKey
     * <AUTHOR>
     * @date 2025年6月7日 
     * @version V0.1 
     * 
     */
    boolean saveBatchByKeys(List<Map<String,String>> params,String projectKey);
} 