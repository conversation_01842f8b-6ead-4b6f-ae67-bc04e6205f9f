package org.jeecg.modules.hours.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.jeecg.modules.hours.entity.FsSysReminderConfig;

/**
 * @Description: 登记提醒配置表
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface IFsSysReminderConfigService extends IService<FsSysReminderConfig> {

    /**
     * 分页查询提醒配置列表
     * 
     * @param page 分页参数
     * @param reminderConfig 查询条件
     * @param projectKey 工作空间标识
     * @return 分页结果
     */
    IPage<FsSysReminderConfig> queryPageList(Page<FsSysReminderConfig> page, FsSysReminderConfig reminderConfig, String projectKey);

    /**
     * 根据工作空间标识查询提醒配置列表
     * 
     * @param projectKey 工作空间标识
     * @return 提醒配置列表
     */
    List<FsSysReminderConfig> queryByProjectKey(String projectKey);

    /**
     * 根据工作空间标识和状态查询提醒配置列表
     * 
     * @param projectKey 工作空间标识
     * @param status 状态
     * @return 提醒配置列表
     */
    List<FsSysReminderConfig> queryByProjectKeyAndStatus(String projectKey, String status);

    /**
     * 添加提醒配置
     * 
     * @param reminderConfig 提醒配置
     * @param projectKey 工作空间标识
     * @param createBy 创建人
     * @return 是否成功
     */
    boolean addReminderConfig(FsSysReminderConfig reminderConfig, String projectKey, String createBy);

    /**
     * 更新提醒配置
     * 
     * @param reminderConfig 提醒配置
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean updateReminderConfig(FsSysReminderConfig reminderConfig, String updateBy);

    /**
     * 删除提醒配置
     *
     * @param id 配置ID
     * @param deleteBy 删除人
     * @return 是否成功
     */
    boolean deleteReminderConfig(Long id, String deleteBy);

    /**
     * 批量删除提醒配置
     *
     * @param ids ID列表
     * @param deleteBy 删除人
     * @return 是否成功
     */
    boolean batchDeleteReminderConfig(List<Long> ids, String deleteBy);

    /**
     * 切换提醒配置状态
     *
     * @param id 配置ID
     * @param status 状态
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean toggleStatus(Long id, String status, String updateBy);

    /**
     * 批量切换提醒配置状态
     *
     * @param ids ID列表
     * @param status 状态
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean batchToggleStatus(List<Long> ids, String status, String updateBy);

    /**
     * 验证提醒配置数据
     * 
     * @param reminderConfig 提醒配置
     * @return 验证结果消息，null表示验证通过
     */
    String validateReminderConfig(FsSysReminderConfig reminderConfig);

    /**
     * 查询需要执行的提醒配置
     * 根据当前时间查询需要发送提醒的配置
     * 
     * @return 需要执行的提醒配置列表
     */
    List<FsSysReminderConfig> queryReminderConfigsToExecute();

    /**
     * 根据提醒类型查询启用的提醒配置
     * 
     * @param reminderType 提醒类型
     * @return 提醒配置列表
     */
    List<FsSysReminderConfig> queryEnabledByReminderType(String reminderType);

    /**
     * 统计工作空间的提醒配置数量
     * 
     * @param projectKey 工作空间标识
     * @return 配置数量
     */
    int countByProjectKey(String projectKey);

    /**
     * 检查工作空间是否存在相同的提醒配置
     * 
     * @param reminderConfig 提醒配置
     * @param projectKey 工作空间标识
     * @return 是否存在相同配置
     */
    boolean existsSameConfig(FsSysReminderConfig reminderConfig, String projectKey);
}
