package org.jeecg.modules.hours.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 人员差值数据DTO
 * 用于MyBatis查询结果映射
 */
@Data
public class UserVarianceDataDTO {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户姓名
     */
    private String userName;
    
    /**
     * 工号
     */
    private String userCode;
    
    /**
     * 部门ID
     */
    private String deptId;
    
    /**
     * 部门名称
     */
    private String deptName;
    
    /**
     * 实际工时
     */
    private BigDecimal actualHours;
    
    /**
     * 可登记工时
     */
    private BigDecimal requiredHours;
}