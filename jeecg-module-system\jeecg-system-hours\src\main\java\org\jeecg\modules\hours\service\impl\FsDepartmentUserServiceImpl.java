package org.jeecg.modules.hours.service.impl;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.jeecg.modules.hours.entity.FsDepartmentUser;
import org.jeecg.modules.hours.mapper.FsDepartmentUserMapper;
import org.jeecg.modules.hours.service.IFsDepartmentUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class FsDepartmentUserServiceImpl extends ServiceImpl<FsDepartmentUserMapper, FsDepartmentUser> implements IFsDepartmentUserService {

	@Override
	public Map<String, String> selectDeptNamesByUnionIds(Collection<String> unionIds) {
		Map<String, String> map = new HashMap<String, String>();
		if(CollectionUtils.isEmpty(unionIds)) {
			return map;
		}
		List<Map<String,String>> mapList = this.baseMapper.selectDeptNamesByUnionIds(unionIds);
		if(CollectionUtils.isEmpty(unionIds)) {
			return map;
		}
		for (Map<String, String> map2 : mapList) {
			// unionId, GROUP_CONCAT(fd.name) as deptName
			map.put(map2.get("unionId"), map2.get("deptName"));
		}
		return map;
	}
	
} 