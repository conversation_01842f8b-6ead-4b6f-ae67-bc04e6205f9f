package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 日期百分比VO
 *
 * <AUTHOR>
 * @date 2023-07-01
 */
@Data
@ApiModel(value = "日期百分比VO", description = "日期百分比VO")
public class DatePercentageVO {
    
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private String date;
    
    /**
     * 百分比
     */
    @ApiModelProperty(value = "百分比")
    private BigDecimal percentage;
} 