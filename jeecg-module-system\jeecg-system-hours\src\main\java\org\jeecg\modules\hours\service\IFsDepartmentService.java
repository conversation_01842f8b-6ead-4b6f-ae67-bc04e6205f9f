package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.entity.FsDepartment;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 部门表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
public interface IFsDepartmentService extends IService<FsDepartment> {

    /**
     * 根据部门ID获取部门树结构
     * @param departmentId 部门ID
     * @return 部门树结构
     */
    FsDepartment getDepartmentTreeById(String departmentId);
    
    /**
     * 获取过滤后的部门树结构（只包含研发中心和项目管理及其下属部门）
     * @return 过滤后的部门树列表
     */
    List<FsDepartment> getFilteredDepartmentTree();
    
    /**
     * 说明：isHour 字段已支持，Service 层无需特殊处理，MyBatis-Plus自动映射
     */
} 