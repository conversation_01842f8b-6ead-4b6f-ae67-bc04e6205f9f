package org.jeecg.modules.hours.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.hours.mapper.ReportAnalysisMapper;
import org.jeecg.modules.hours.service.IReportAnalysisService;
import org.jeecg.modules.hours.service.ISysConfigService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.hours.constant.ConfigKeyConstant;
import org.jeecg.modules.hours.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLEncoder;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 报表分析服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
@Slf4j
public class ReportAnalysisServiceImpl implements IReportAnalysisService {

    @Autowired
    private ReportAnalysisMapper reportAnalysisMapper;
    
    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public InstanceHoursReportResponse getInstanceHoursReport(ReportQueryParams params) {
        log.info("获取实例工时周期汇总报表，参数：{}", params);

        // 生成时间周期列表
        List<String> periods = generatePeriods(params.getDimension(), params.getStartDate(), params.getEndDate());

        // 分页查询实例列表
        IPage<String> page = new Page<>(params.getPage(), params.getSize());
        IPage<String> instancePage = reportAnalysisMapper.queryInstanceListPage(page, params);

        // 处理数据
        InstanceHoursReportResponse response = new InstanceHoursReportResponse();
        response.setPeriods(periods);
        response.setTotal(instancePage.getTotal());

        // 如果没有实例数据，直接返回空结果
        if (instancePage.getRecords().isEmpty()) {
            response.setData(new ArrayList<>());
            response.setSummary(new HashMap<>());
            return response;
        }

        // 查询这些实例的详细工时数据
        List<Map<String, Object>> rawData = reportAnalysisMapper.queryInstanceHoursDataByInstances(params, instancePage.getRecords());

        // 按实例分组数据
        Map<String, Map<String, Double>> instanceDataMap = new HashMap<>();
        Map<String, Double> summaryMap = new HashMap<>();

        // 初始化合计数据
        periods.forEach(period -> summaryMap.put(period, 0.0));

        for (Map<String, Object> row : rawData) {
            String instanceName = (String) row.get("instance_name");
            String period = (String) row.get("period");
            Double hours = ((Number) row.get("total_hours")).doubleValue();

            instanceDataMap.computeIfAbsent(instanceName, k -> new HashMap<>()).put(period, hours);
            Double value = new Double(0);
            if(summaryMap.containsKey(period) && null!=summaryMap.get(period)) {
            	value = summaryMap.get(period);
            }
            summaryMap.put(period, value + hours);
        }

        // 构建响应数据
        List<InstanceHoursReportResponse.InstanceHoursReportData> dataList = new ArrayList<>();
        for (String instanceName : instancePage.getRecords()) {
            InstanceHoursReportResponse.InstanceHoursReportData data = new InstanceHoursReportResponse.InstanceHoursReportData();
            data.setInstanceName(instanceName);
            Map<String, Double> instancePeriods = instanceDataMap.getOrDefault(instanceName, new HashMap<>());
            data.setPeriods(instancePeriods);
            data.setTotal(instancePeriods.values().stream().mapToDouble(Double::doubleValue).sum());
            dataList.add(data);
        }

        response.setData(dataList);
        response.setSummary(summaryMap);

        return response;
    }

    @Override
    public EmployeeHoursReportResponse getEmployeeHoursReport(ReportQueryParams params) {
        log.info("获取员工工时周期汇总报表，参数：{}", params);
        
        // 生成时间周期列表
        List<String> periods = generatePeriods(params.getDimension(), params.getStartDate(), params.getEndDate());
        
        // 分页查询员工工时数据
        IPage<Map<String, Object>> page = new Page<>(params.getPage(), params.getSize());
        IPage<Map<String, Object>> result = reportAnalysisMapper.queryEmployeeHoursData(page, params);
        
        // 获取工时每天标准配置
        double workHourPerDay = getWorkHourPerDay(RequestHeaderUtil.getSpaceIdFromHeader());
        
        // 处理数据
        EmployeeHoursReportResponse response = new EmployeeHoursReportResponse();
        response.setPeriods(periods);
        response.setTotal(result.getTotal());
        
        // 按员工分组数据
        Map<String, Map<String, Double>> employeeDataMap = new HashMap<>();
        Map<String, String> employeeNameMap = new HashMap<>();
        Map<String, Double> summaryPeriods = new HashMap<>();

        // 初始化合计数据
        periods.forEach(period -> summaryPeriods.put(period, 0.0));

        for (Map<String, Object> row : result.getRecords()) {
            String employeeId = (String) row.get("employee_id");
            String employeeName = (String) row.get("employee_name");
            String period = (String) row.get("period");
            Double hours = ((Number) row.get("total_hours")).doubleValue();

            employeeNameMap.put(employeeId, employeeName);
            employeeDataMap.computeIfAbsent(employeeId, k -> new HashMap<>()).put(period, hours);
            summaryPeriods.put(period, summaryPeriods.get(period) + hours);
        }
        
        // 构建响应数据
        List<EmployeeHoursReportResponse.EmployeeHoursReportData> dataList = new ArrayList<>();
        double grandTotalHours = 0.0;

        for (Map.Entry<String, Map<String, Double>> entry : employeeDataMap.entrySet()) {
            EmployeeHoursReportResponse.EmployeeHoursReportData data = new EmployeeHoursReportResponse.EmployeeHoursReportData();
            data.setEmployeeId(entry.getKey());
            data.setEmployeeName(employeeNameMap.get(entry.getKey()));
            data.setPeriods(entry.getValue());

            double totalHours = entry.getValue().values().stream().mapToDouble(Double::doubleValue).sum();
            data.setTotalHours(totalHours);
            data.setTotalDays(totalHours / workHourPerDay);

            grandTotalHours += totalHours;
            dataList.add(data);
        }
        
        // 构建合计数据
        EmployeeHoursReportResponse.EmployeeHoursSummary summary = new EmployeeHoursReportResponse.EmployeeHoursSummary();
        summary.setPeriods(summaryPeriods);
        summary.setGrandTotalHours(grandTotalHours);
        summary.setGrandTotalDays(grandTotalHours / workHourPerDay);
        
        response.setData(dataList);
        response.setSummary(summary);
        
        return response;
    }

    @Override
    public EmployeeInstanceDistributionResponse getEmployeeInstanceDistributionReport(ReportQueryParams params) {
        log.info("获取员工工时实例分布报表，参数：{}", params);
        
        // 获取实例列表
        List<String> instances = reportAnalysisMapper.queryInstanceNames(params);
        
        // 分页查询员工实例分布数据
        IPage<Map<String, Object>> page = new Page<>(params.getPage(), params.getSize());
        IPage<Map<String, Object>> result = reportAnalysisMapper.queryEmployeeInstanceDistributionData(page, params);
        
        // 获取工时每天标准配置
        double workHourPerDay = getWorkHourPerDay(RequestHeaderUtil.getSpaceIdFromHeader());
        
        // 处理数据
        EmployeeInstanceDistributionResponse response = new EmployeeInstanceDistributionResponse();
        response.setInstances(instances);
        response.setTotal(result.getTotal());
        
        // 按员工分组数据
        Map<String, Map<String, Double>> employeeDataMap = new HashMap<>();
        Map<String, String> employeeNameMap = new HashMap<>();
        Map<String, Double> summaryInstances = new HashMap<>();

        // 初始化合计数据
        instances.forEach(instance -> summaryInstances.put(instance, 0.0));

        for (Map<String, Object> row : result.getRecords()) {
            String employeeId = (String) row.get("employee_id");
            String employeeName = (String) row.get("employee_name");
            String instanceName = (String) row.get("instance_name");
            Double hours = ((Number) row.get("total_hours")).doubleValue();

            employeeNameMap.put(employeeId, employeeName);
            employeeDataMap.computeIfAbsent(employeeId, k -> new HashMap<>()).put(instanceName, hours);
            summaryInstances.put(instanceName, summaryInstances.get(instanceName) + hours);
        }
        
        // 构建响应数据
        List<EmployeeInstanceDistributionResponse.EmployeeInstanceDistributionData> dataList = new ArrayList<>();
        double grandTotalHours = 0.0;

        for (Map.Entry<String, Map<String, Double>> entry : employeeDataMap.entrySet()) {
            EmployeeInstanceDistributionResponse.EmployeeInstanceDistributionData data = new EmployeeInstanceDistributionResponse.EmployeeInstanceDistributionData();
            data.setEmployeeId(entry.getKey());
            data.setEmployeeName(employeeNameMap.get(entry.getKey()));
            data.setInstances(entry.getValue());

            double totalHours = entry.getValue().values().stream().mapToDouble(Double::doubleValue).sum();
            data.setTotalHours(totalHours);
            data.setTotalDays(totalHours / workHourPerDay);

            grandTotalHours += totalHours;
            dataList.add(data);
        }
        
        // 构建合计数据
        EmployeeInstanceDistributionResponse.EmployeeInstanceSummary summary = new EmployeeInstanceDistributionResponse.EmployeeInstanceSummary();
        summary.setInstances(summaryInstances);
        summary.setGrandTotalHours(grandTotalHours);
        summary.setGrandTotalDays(grandTotalHours / workHourPerDay);
        
        response.setData(dataList);
        response.setSummary(summary);
        
        return response;
    }

    @Override
    public List<UserVO> getProjectUsers(String projectKey) {
        return reportAnalysisMapper.queryProjectUsers(projectKey);
    }

    @Override
    public List<InstanceVO> getProjectInstances(String projectKey) {
        return reportAnalysisMapper.queryProjectInstances(projectKey);
    }

    @Override
    public ModelAndView exportInstanceHoursReport(HttpServletRequest request, HttpServletResponse response, ReportQueryParams params) {
        log.info("导出实例工时周期汇总报表，参数：{}", params);

        try {
            // 获取所有数据（不分页）
            ReportQueryParams allDataParams = new ReportQueryParams();
            allDataParams.setDimension(params.getDimension());
            allDataParams.setStartDate(params.getStartDate());
            allDataParams.setEndDate(params.getEndDate());
            allDataParams.setProjectKey(params.getProjectKey());
            allDataParams.setUserIds(params.getUserIds());
            allDataParams.setInstanceIds(params.getInstanceIds());
            allDataParams.setPage(1);
            allDataParams.setSize(10000); // 获取大量数据

            InstanceHoursReportResponse reportData = getInstanceHoursReport(allDataParams);

            // 构建导出数据
            List<Map<String, Object>> exportList = new ArrayList<>();

            for (InstanceHoursReportResponse.InstanceHoursReportData data : reportData.getData()) {
                Map<String, Object> row = new HashMap<>();
                row.put("实例名称", data.getInstanceName());

                // 添加时间周期数据
                for (String period : reportData.getPeriods()) {
                    Double hours = data.getPeriods().get(period);
                    row.put(period, hours != null ? hours + "h" : "0h");
                }

                row.put("小计", data.getTotal() + "h");
                exportList.add(row);
            }

            // 添加合计行
            if (reportData.getSummary() != null) {
                Map<String, Object> summaryRow = new HashMap<>();
                summaryRow.put("实例名称", "合计");

                double grandTotal = 0.0;
                for (String period : reportData.getPeriods()) {
                    Double hours = reportData.getSummary().get(period);
                    summaryRow.put(period, hours != null ? hours + "h" : "0h");
                    grandTotal += hours != null ? hours : 0.0;
                }

                summaryRow.put("小计", grandTotal + "h");
                exportList.add(summaryRow);
            }

            // 设置文件名
            String fileName = "实例工时周期汇总报表_" + params.getStartDate() + "_" + params.getEndDate();

            // 使用简单的CSV导出（可以后续升级为Excel）
            exportToCsv(response, exportList, fileName);

            return null;
        } catch (Exception e) {
            log.error("导出实例工时报表失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    @Override
    public ModelAndView exportEmployeeHoursReport(HttpServletRequest request, HttpServletResponse response, ReportQueryParams params) {
        log.info("导出员工工时周期汇总报表，参数：{}", params);

        try {
            // 获取所有数据（不分页）
            ReportQueryParams allDataParams = new ReportQueryParams();
            allDataParams.setDimension(params.getDimension());
            allDataParams.setStartDate(params.getStartDate());
            allDataParams.setEndDate(params.getEndDate());
            allDataParams.setProjectKey(RequestHeaderUtil.getSpaceIdFromHeader());
            allDataParams.setUserIds(params.getUserIds());
            allDataParams.setPage(1);
            allDataParams.setSize(10000); // 获取大量数据

            EmployeeHoursReportResponse reportData = getEmployeeHoursReport(allDataParams);

            // 构建导出数据
            List<Map<String, Object>> exportList = new ArrayList<>();

            for (EmployeeHoursReportResponse.EmployeeHoursReportData data : reportData.getData()) {
                Map<String, Object> row = new HashMap<>();
                row.put("员工姓名", data.getEmployeeName());

                // 添加时间周期数据
                for (String period : reportData.getPeriods()) {
                    Double hours = data.getPeriods().get(period);
                    row.put(period, hours != null ? hours + "h" : "0h");
                }

                row.put("小计(小时)", data.getTotalHours() + "h");
                row.put("折算天数(天)", String.format("%.1f天", data.getTotalDays()));
                exportList.add(row);
            }

            // 添加合计行
            if (reportData.getSummary() != null) {
                Map<String, Object> summaryRow = new HashMap<>();
                summaryRow.put("员工姓名", "合计");

                for (String period : reportData.getPeriods()) {
                    Double hours = reportData.getSummary().getPeriods().get(period);
                    summaryRow.put(period, hours != null ? hours + "h" : "0h");
                }

                summaryRow.put("小计(小时)", reportData.getSummary().getGrandTotalHours() + "h");
                summaryRow.put("折算天数(天)", String.format("%.1f天", reportData.getSummary().getGrandTotalDays()));
                exportList.add(summaryRow);
            }

            // 设置文件名
            String fileName = "员工工时周期汇总报表_" + params.getStartDate() + "_" + params.getEndDate();

            // 导出CSV
            exportToCsv(response, exportList, fileName);

            return null;
        } catch (Exception e) {
            log.error("导出员工工时报表失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    @Override
    public ModelAndView exportEmployeeInstanceDistributionReport(HttpServletRequest request, HttpServletResponse response, ReportQueryParams params) {
        log.info("导出员工工时实例分布报表，参数：{}", params);

        try {
            // 获取所有数据（不分页）
            ReportQueryParams allDataParams = new ReportQueryParams();
            allDataParams.setDimension(params.getDimension());
            allDataParams.setStartDate(params.getStartDate());
            allDataParams.setEndDate(params.getEndDate());
            allDataParams.setProjectKey(RequestHeaderUtil.getSpaceIdFromHeader());
            allDataParams.setUserIds(params.getUserIds());
            allDataParams.setInstanceIds(params.getInstanceIds());
            allDataParams.setPage(1);
            allDataParams.setSize(10000); // 获取大量数据

            EmployeeInstanceDistributionResponse reportData = getEmployeeInstanceDistributionReport(allDataParams);

            // 构建导出数据
            List<Map<String, Object>> exportList = new ArrayList<>();

            for (EmployeeInstanceDistributionResponse.EmployeeInstanceDistributionData data : reportData.getData()) {
                Map<String, Object> row = new HashMap<>();
                row.put("员工姓名", data.getEmployeeName());

                // 添加实例数据
                for (String instance : reportData.getInstances()) {
                    Double hours = data.getInstances().get(instance);
                    row.put(instance, hours != null ? hours + "h" : "0h");
                }

                row.put("小计(小时)", data.getTotalHours() + "h");
                row.put("折算天数(天)", String.format("%.1f天", data.getTotalDays()));
                exportList.add(row);
            }

            // 添加合计行
            if (reportData.getSummary() != null) {
                Map<String, Object> summaryRow = new HashMap<>();
                summaryRow.put("员工姓名", "合计");

                for (String instance : reportData.getInstances()) {
                    Double hours = reportData.getSummary().getInstances().get(instance);
                    summaryRow.put(instance, hours != null ? hours + "h" : "0h");
                }

                summaryRow.put("小计(小时)", reportData.getSummary().getGrandTotalHours() + "h");
                summaryRow.put("折算天数(天)", String.format("%.1f天", reportData.getSummary().getGrandTotalDays()));
                exportList.add(summaryRow);
            }

            // 设置文件名
            String fileName = "员工工时实例分布报表_" + params.getStartDate() + "_" + params.getEndDate();

            // 导出CSV
            exportToCsv(response, exportList, fileName);

            return null;
        } catch (Exception e) {
            log.error("导出员工工时实例分布报表失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取工时每天标准配置
     */
    private double getWorkHourPerDay(String projectKey) {
        String workHourPerDayStr = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.WORK_HOUR_PER_DAY);
        
        double workHourPerDay = 8.0; // 默认8小时
        if (StringUtils.isNotBlank(workHourPerDayStr)) {
            try {
                workHourPerDay = Double.parseDouble(workHourPerDayStr);
                // 配置值校验：必须大于0且小于等于24
                if (workHourPerDay <= 0 || workHourPerDay > 24) {
                    log.warn("每天标准工时配置值异常: {}, 使用默认值8小时", workHourPerDayStr);
                    workHourPerDay = 8.0;
                }
            } catch (NumberFormatException e) {
                log.warn("解析每天标准工时配置失败: {}, 使用默认值8小时", workHourPerDayStr, e);
                workHourPerDay = 8.0;
            }
        } else {
            log.info("未配置每天标准工时，使用默认值8小时");
        }
        
        return workHourPerDay;
    }
    
    public static void main(String[] args) {
		
    	List<String> dataList = new ReportAnalysisServiceImpl().generatePeriods("week", "2025-06-01", "2025-07-01");
    	for (String string : dataList) {
			System.out.println(string);
		}
    	
	}

    /**
     * 生成时间周期列表
     */
    private List<String> generatePeriods(String dimension, String startDate, String endDate) {
        List<String> periods = new ArrayList<>();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        
        switch (dimension) {
            case "month":
                LocalDate monthCurrent = start.withDayOfMonth(1);
                while (!monthCurrent.isAfter(end)) {
                    periods.add(monthCurrent.format(DateTimeFormatter.ofPattern("yyyy-MM")));
                    monthCurrent = monthCurrent.plusMonths(1);
                }
                break;
            case "week":
                LocalDate weekCurrent = start;
                while (!weekCurrent.isAfter(end)) {
                    // 调整为周一作为周开始
                    LocalDate monday = weekCurrent.with(DayOfWeek.MONDAY);
                    // 调整为周日作为周结束
                    LocalDate sunday = monday.plusDays(6);
                    
                    if (sunday.isAfter(end)) {
                        sunday = end;
                    }
                    periods.add(monday.format(DateTimeFormatter.ofPattern("MM/dd")) + "-" + 
                               sunday.format(DateTimeFormatter.ofPattern("MM/dd")));
                    weekCurrent = sunday.plusDays(1); // 跳到下一周
                }
                break;
            case "day":
                LocalDate dayCurrent = start;
                while (!dayCurrent.isAfter(end)) {
                    periods.add(dayCurrent.format(DateTimeFormatter.ofPattern("yyyy/MM/dd")));
                    dayCurrent = dayCurrent.plusDays(1);
                }
                break;
        }
        
        return periods;
    }

    /**
     * 导出CSV文件
     */
    private void exportToCsv(HttpServletResponse response, List<Map<String, Object>> data, String fileName) throws IOException {
        if (data == null || data.isEmpty()) {
            throw new RuntimeException("导出数据为空");
        }

        // 设置响应头
        response.setContentType("text/csv;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");

        try {
            String encodedFileName = URLEncoder.encode(fileName + ".csv", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
        } catch (Exception e) {
            response.setHeader("Content-Disposition", "attachment; filename=\"report.csv\"");
        }

        // 写入CSV内容
        PrintWriter writer = response.getWriter();

        // 写入BOM以支持Excel正确显示中文
        writer.write('\ufeff');

        // 获取表头
        Set<String> headers = data.get(0).keySet();
        writer.println(String.join(",", headers));

        // 写入数据行
        for (Map<String, Object> row : data) {
            List<String> values = new ArrayList<>();
            for (String header : headers) {
                Object value = row.get(header);
                String valueStr = value != null ? value.toString() : "";
                // 如果包含逗号或引号，需要用引号包围并转义
                if (valueStr.contains(",") || valueStr.contains("\"") || valueStr.contains("\n")) {
                    valueStr = "\"" + valueStr.replace("\"", "\"\"") + "\"";
                }
                values.add(valueStr);
            }
            writer.println(String.join(",", values));
        }

        writer.flush();
    }
}
