//package org.jeecg.modules.hours.controller;
//
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
//
//import java.util.Arrays;
//import java.util.Date;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.http.MediaType;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.setup.MockMvcBuilders;
//
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.jeecg.modules.hours.constant.ReminderConstant;
//import org.jeecg.modules.hours.entity.FsSysReminderConfig;
//import org.jeecg.modules.hours.service.IFsSysReminderConfigService;
//
///**
// * 登记提醒配置控制器测试类
// * 
// * <AUTHOR>
// * @date 2024-01-01
// */
//@ExtendWith(MockitoExtension.class)
//class FsSysReminderConfigControllerTest {
//
//    @Mock
//    private IFsSysReminderConfigService reminderConfigService;
//
//    @InjectMocks
//    private FsSysReminderConfigController reminderConfigController;
//
//    private MockMvc mockMvc;
//    private ObjectMapper objectMapper;
//    private FsSysReminderConfig testConfig;
//
//    @BeforeEach
//    void setUp() {
//        mockMvc = MockMvcBuilders.standaloneSetup(reminderConfigController).build();
//        objectMapper = new ObjectMapper();
//        
//        testConfig = new FsSysReminderConfig();
////        testConfig.setId("test_id_001");
//        testConfig.setProjectKey("test_project_001");
//        testConfig.setReminderType(ReminderConstant.ReminderType.DAILY);
//        testConfig.setRepeatMode(ReminderConstant.RepeatMode.CURRENT_DAY);
//        testConfig.setReminderTime("09:00");
//        testConfig.setStatus(ReminderConstant.Status.ENABLED);
//        testConfig.setRemark("测试提醒");
//        testConfig.setCreateBy("test_user");
//        testConfig.setCreateTime(new Date());
//        testConfig.setDelFlag(0);
//    }
//
//    @Test
//    void testQueryPageList_Success() throws Exception {
//        // 准备测试数据
//        Page<FsSysReminderConfig> page = new Page<>(1, 10);
//        page.setRecords(Arrays.asList(testConfig));
//        page.setTotal(1);
//        
//        when(reminderConfigService.queryPageList(any(Page.class), any(FsSysReminderConfig.class), anyString()))
//            .thenReturn(page);
//
//        // 执行测试
//        mockMvc.perform(get("/hapi/hours/fsSysReminderConfig/list")
//                .param("pageNo", "1")
//                .param("pageSize", "10")
//                .header("X-Space-Id", "test_project_001")
//                .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.result.records").isArray())
//                .andExpect(jsonPath("$.result.total").value(1));
//
//        verify(reminderConfigService).queryPageList(any(Page.class), any(FsSysReminderConfig.class), eq("test_project_001"));
//    }
//
//    @Test
//    void testQueryPageList_MissingProjectKey() throws Exception {
//        // 测试缺少项目标识的情况
//        mockMvc.perform(get("/hapi/hours/fsSysReminderConfig/list")
//                .param("pageNo", "1")
//                .param("pageSize", "10")
//                .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(false))
//                .andExpect(jsonPath("$.message").value("未提供有效的工作空间标识"));
//
//        verify(reminderConfigService, never()).queryPageList(any(), any(), any());
//    }
//
//    @Test
//    void testAdd_Success() throws Exception {
//        // 准备测试数据
//        when(reminderConfigService.addReminderConfig(any(FsSysReminderConfig.class), anyString(), anyString()))
//            .thenReturn(true);
//
//        // 执行测试
//        mockMvc.perform(post("/hapi/hours/fsSysReminderConfig/add")
//                .header("X-Space-Id", "test_project_001")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(testConfig)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.message").value("添加成功！"));
//
//        verify(reminderConfigService).addReminderConfig(any(FsSysReminderConfig.class), eq("test_project_001"), anyString());
//    }
//
//    @Test
//    void testAdd_ValidationError() throws Exception {
//        // 准备测试数据 - 模拟验证失败
//        when(reminderConfigService.addReminderConfig(any(FsSysReminderConfig.class), anyString(), anyString()))
//            .thenThrow(new RuntimeException("提醒类型不能为空"));
//
//        // 执行测试
//        mockMvc.perform(post("/hapi/hours/fsSysReminderConfig/add")
//                .header("X-Space-Id", "test_project_001")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(testConfig)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(false))
//                .andExpect(jsonPath("$.message").value("添加失败：提醒类型不能为空"));
//    }
//
//    @Test
//    void testEdit_Success() throws Exception {
//        // 准备测试数据
//        when(reminderConfigService.updateReminderConfig(any(FsSysReminderConfig.class), anyString()))
//            .thenReturn(true);
//
//        // 执行测试
//        mockMvc.perform(put("/hapi/hours/fsSysReminderConfig/edit")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(testConfig)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.message").value("编辑成功！"));
//
//        verify(reminderConfigService).updateReminderConfig(any(FsSysReminderConfig.class), anyString());
//    }
//
//    @Test
//    void testDelete_Success() throws Exception {
//        // 准备测试数据
//        when(reminderConfigService.deleteReminderConfig(anyString(), anyString()))
//            .thenReturn(true);
//
//        // 执行测试
//        mockMvc.perform(delete("/hapi/hours/fsSysReminderConfig/delete")
//                .param("id", "test_id_001"))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.message").value("删除成功!"));
//
//        verify(reminderConfigService).deleteReminderConfig(eq("test_id_001"), anyString());
//    }
//
//    @Test
//    void testDeleteBatch_Success() throws Exception {
//        // 准备测试数据
//        when(reminderConfigService.batchDeleteReminderConfig(anyList(), anyString()))
//            .thenReturn(true);
//
//        // 执行测试
//        mockMvc.perform(delete("/hapi/hours/fsSysReminderConfig/deleteBatch")
//                .param("ids", "id1,id2,id3"))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.message").value("批量删除成功!"));
//
//        verify(reminderConfigService).batchDeleteReminderConfig(eq(Arrays.asList("id1", "id2", "id3")), anyString());
//    }
//
//    @Test
//    void testDeleteBatch_EmptyIds() throws Exception {
//        // 测试空ID参数
//        mockMvc.perform(delete("/hapi/hours/fsSysReminderConfig/deleteBatch")
//                .param("ids", ""))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(false))
//                .andExpect(jsonPath("$.message").value("参数不能为空"));
//
//        verify(reminderConfigService, never()).batchDeleteReminderConfig(any(), any());
//    }
//
//    @Test
//    void testQueryById_Success() throws Exception {
//        // 准备测试数据
//        when(reminderConfigService.getById("test_id_001"))
//            .thenReturn(testConfig);
//
//        // 执行测试
//        mockMvc.perform(get("/hapi/hours/fsSysReminderConfig/queryById")
//                .param("id", "test_id_001"))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.result.id").value("test_id_001"))
//                .andExpect(jsonPath("$.result.reminderType").value(ReminderConstant.ReminderType.DAILY));
//
//        verify(reminderConfigService).getById("test_id_001");
//    }
//
//    @Test
//    void testQueryById_NotFound() throws Exception {
//        // 准备测试数据 - 记录不存在
//        when(reminderConfigService.getById("non_existent_id"))
//            .thenReturn(null);
//
//        // 执行测试
//        mockMvc.perform(get("/hapi/hours/fsSysReminderConfig/queryById")
//                .param("id", "non_existent_id"))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(false))
//                .andExpect(jsonPath("$.message").value("未找到对应数据"));
//    }
//
//    @Test
//    void testToggleStatus_Success() throws Exception {
//        // 准备测试数据
//        when(reminderConfigService.toggleStatus(anyString(), anyString(), anyString()))
//            .thenReturn(true);
//
//        // 执行测试
//        mockMvc.perform(put("/hapi/hours/fsSysReminderConfig/toggleStatus")
//                .param("id", "test_id_001")
//                .param("status", ReminderConstant.Status.DISABLED))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.message").value("状态更新成功!"));
//
//        verify(reminderConfigService).toggleStatus(eq("test_id_001"), eq(ReminderConstant.Status.DISABLED), anyString());
//    }
//
//    @Test
//    void testBatchToggleStatus_Success() throws Exception {
//        // 准备测试数据
//        when(reminderConfigService.batchToggleStatus(anyList(), anyString(), anyString()))
//            .thenReturn(true);
//
//        // 执行测试
//        mockMvc.perform(put("/hapi/hours/fsSysReminderConfig/batchToggleStatus")
//                .param("ids", "id1,id2,id3")
//                .param("status", ReminderConstant.Status.ENABLED))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.message").value("批量状态更新成功!"));
//
//        verify(reminderConfigService).batchToggleStatus(eq(Arrays.asList("id1", "id2", "id3")), eq(ReminderConstant.Status.ENABLED), anyString());
//    }
//
//    @Test
//    void testGetStatistics_Success() throws Exception {
//        // 准备测试数据
//        when(reminderConfigService.countByProjectKey("test_project_001")).thenReturn(10);
//        when(reminderConfigService.queryByProjectKeyAndStatus("test_project_001", ReminderConstant.Status.ENABLED))
//            .thenReturn(Arrays.asList(testConfig, testConfig, testConfig)); // 3个启用的
//        when(reminderConfigService.queryByProjectKeyAndStatus("test_project_001", ReminderConstant.Status.DISABLED))
//            .thenReturn(Arrays.asList(testConfig, testConfig)); // 2个禁用的
//
//        // 执行测试
//        mockMvc.perform(get("/hapi/hours/fsSysReminderConfig/statistics")
//                .header("X-Space-Id", "test_project_001"))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.result.total").value(10))
//                .andExpect(jsonPath("$.result.enabled").value(3))
//                .andExpect(jsonPath("$.result.disabled").value(2));
//
//        verify(reminderConfigService).countByProjectKey("test_project_001");
//        verify(reminderConfigService).queryByProjectKeyAndStatus("test_project_001", ReminderConstant.Status.ENABLED);
//        verify(reminderConfigService).queryByProjectKeyAndStatus("test_project_001", ReminderConstant.Status.DISABLED);
//    }
//}
