package org.jeecg.modules.hours.service;

import java.util.Date;
import java.util.List;

import org.jeecg.modules.hours.entity.UserProjectHourStatistics;
import org.jeecg.modules.hours.vo.UserProjectHourReportResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 人员项目工时统计表
 * @Author: jeecg-boot
 * @Date: 2025-07-08
 * @Version: V1.0
 */
public interface IUserProjectHourStatisticsService extends IService<UserProjectHourStatistics> {
    
    /**
     * 获取人员项目工时统计报表数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statPeriod 统计周期
     * @param userIds 用户ID列表
     * @param projectIds 项目ID列表
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @return 报表数据
     */
    UserProjectHourReportResult getUserProjectHourReport(Date startDate, Date endDate, String statPeriod,
                                                        List<String> userIds, List<String> projectIds, Integer isHoliday);
    
    /**
     * 计算工时统计数据
     * @param statDate 统计日期
     * @param statPeriod 统计周期
     * @return 处理记录数
     */
    int calculateHourStatistics(Date statDate, String statPeriod);
    
    /**
     * 批量计算工时统计数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statPeriod 统计周期
     * @return 总处理记录数
     */
    int batchCalculateHourStatistics(Date startDate, Date endDate, String statPeriod);
    
    /**
     * 重新计算工时统计数据
     * @param statDate 统计日期
     * @param statPeriod 统计周期
     * @return 处理记录数
     */
    int recalculateHourStatistics(Date statDate, String statPeriod);
}