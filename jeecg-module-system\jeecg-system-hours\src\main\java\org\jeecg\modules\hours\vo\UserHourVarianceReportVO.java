package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 人员工时差值报表VO
 */
@Data
@ApiModel("人员工时差值报表VO")
public class UserHourVarianceReportVO {

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("用户姓名")
    private String userName;

    @ApiModelProperty("工号")
    private String userCode;

    @ApiModelProperty("部门ID")
    private String deptId;

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("统计年份")
    private Integer statYear;

    @ApiModelProperty("月度数据")
    private Map<String, BigDecimal> monthlyData;

    @ApiModelProperty("年度总计")
    private YearlyTotal yearlyTotal;

    @Data
    public static class YearlyTotal {
        @ApiModelProperty("年度实际工时总计")
        private BigDecimal totalActualHours;

        @ApiModelProperty("年度可登记工时总计")
        private BigDecimal totalRequiredHours;

        @ApiModelProperty("年度差值工时总计")
        private BigDecimal totalVarianceHours;
    }
}