package org.jeecg.modules.hours.controller;

import java.util.List;

import org.jeecg.modules.hours.dto.HourRecordDTO;
import org.jeecg.modules.hours.service.ApprovalService;
import org.jeecg.modules.hours.vo.ApiResponse;
import org.jeecg.modules.hours.vo.ApprovalRequest;
import org.jeecg.modules.hours.vo.TransferRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/work-hours")
public class ApprovalController {

    @Autowired
    private ApprovalService approvalService;

    @GetMapping("/approvals")
    public List<HourRecordDTO> listApprovals() {
        return approvalService.listApprovals();
    }

    @PostMapping("/approve")
    public ApiResponse<Boolean> approve(@RequestBody ApprovalRequest req) {
        return approvalService.approve(req);
    }

    @PostMapping("/transfer-approver")
    public ApiResponse<Boolean> transferApprover(@RequestBody TransferRequest req) {
        return approvalService.transferApprover(req);
    }
} 