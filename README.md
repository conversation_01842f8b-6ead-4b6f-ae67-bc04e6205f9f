# 飞书工时管理系统

## 项目概述

飞书工时管理系统是一个基于飞书插件开发的企业工时管理平台，用于员工工时记录、项目工时统计、审批管理和数据分析。系统作为飞书插件运行，与飞书项目系统深度集成，实现工时填报、审批、统计和分析等完整功能。

## 项目架构

整个项目分为三个主要部分：

### 1. 前端（飞书插件）

- **目录位置**: `D:\workspace_sgm\hh\feishu\project-hours-front`
- **技术栈**: 
  - React 18
  - TypeScript
  - Semi Design UI (@douyinfe/semi-ui)
  - 飞书SDK (@lark-project/js-sdk)
  - Vite5 构建工具

### 2. 后端（业务逻辑层）

- **目录位置**: `D:\workspace_sgm\hh\feishu\web`
- **技术栈**:
  - jeecg-boot 框架
  - Spring Boot
  - MyBatis-Plus
  - JWT + Shiro 权限控制

### 3. Java 服务（数据服务层）

- **目录位置**: `D:\workspace_sgm\hh\feishu\java`
- **技术栈**:
  - jeecg-boot 低代码开发平台 (3.7.3)
  - Spring Boot 2.7.18
  - Spring Cloud Alibaba 2021.0.1.0
  - MyBatis-Plus *******
  - Shiro + JWT 安全框架
  - MySQL 数据库

## 功能模块

### 工时管理

- 单个工时登记：选择项目、日期、工时进行单条记录
- 批量工时登记：按照日期、人员进行批量工时填报
- 工时明细查看：支持展开查看详细工时信息
- 工时数据校验：确保工时数据的准确性和完整性

### 审批管理

- 工时审批流程：支持"提交人→直属领导+项目经理"的审批流程
- 审批列表管理：查看待审批、已审批、已拒绝的工时记录
- 审批权限控制：基于角色的审批权限管理
- 消息通知：审批状态变更自动通知相关人员

### 报表分析

- 多维度统计：按项目、部门、人员等维度统计工时数据
- 图表可视化：直观展示工时分布和使用情况
- 数据导出：支持导出Excel报表
- 趋势分析：工时使用趋势分析和预测

### 系统设置

- 用户管理：系统用户的添加、编辑、删除
- 角色权限：基于角色的权限控制
- 项目管理：项目的创建、编辑、归档
- 系统配置：系统参数配置和管理

## 界面原型

系统界面主要包括以下几个部分：

### 1. 工时列表页面

- **导航结构**：顶部导航栏包含"工作台"、"工时管理"、"审批"、"报表"和"系统设置"等模块
- **列表展示**：
  - 显示工时记录，包含日期范围、工作人员、状态、审批时间和审批人等信息
  - 状态标签使用不同颜色区分（待审核、审批通过、审批驳回等）
  - 支持按日期范围、工作人员等条件筛选
- **操作功能**：
  - 提供"批量提交"按钮进行批量操作
  - 每条记录支持"编辑"、"查看"和"删除"等操作

### 2. 工时登记页面

- **基本信息区**：
  - 工作日期选择（支持日期范围选择）
  - 工作人员选择
- **工时明细表格**：
  - 按项目/任务分行显示
  - 包含周一至周日的工时填写列
  - 支持多行工时记录
- **操作按钮**：
  - 底部提供"保存"和"提交审批"按钮
  - 支持填写备注信息
- **数据校验**：
  - 实时显示工时合计
  - 对超出限制的工时进行提示

### 3. 审批页面

- **审批列表**：展示待审批、已审批、已拒绝的工时记录
- **审批操作**：支持批量审批、单条审批、驳回等操作
- **审批详情**：可查看工时明细和提交信息

### 4. 报表页面

- **报表类型选择**：支持多种报表类型切换
- **筛选条件**：按时间、部门、项目等维度筛选
- **数据可视化**：图表展示工时分布情况
- **导出功能**：支持将报表导出为Excel

## 系统集成

- 与飞书项目系统深度集成
- 支持飞书单点登录认证
- 利用飞书组织架构自动获取用户信息
- 支持飞书消息通知和提醒

## 开发指南

### 前端开发

```bash
# 进入前端目录
cd D:\workspace_sgm\hh\feishu\front\project-hours-front

# 安装依赖
npm install
# 或
yarn

# 启动开发服务器
lpm start

# 构建生产环境代码
lpm release
```


### 后端开发

后端基于jeecg-boot框架开发，需按照jeecg-boot的开发规范进行开发。

### 实体类开发规范
1. 必须继承`BaseEntity`基类
2. 使用Lombok注解：`@Data` + `@Accessors(chain=true)`
3. 主键注解：`@TableId(type=IdType.ASSIGN_ID)`
4. 逻辑删除字段：`@TableLogic`
5. Excel注解：`@Excel(name="用户ID")`
6. 时间格式注解：`@JsonFormat(pattern="yyyy-MM-dd")`

### Mapper层规范
1. 接口文件路径：`java/org/jeecg/modules/hours/mapper`
2. XML文件路径：`resources/org/jeecg/modules/hours/mapper/xml`
3. 基础继承：`extends BaseMapper<Entity>`
4. 结果映射：`<resultMap id="BaseResultMap">`

### Service层规范
```java
public interface ISysUserAttendanceStatService extends IService<SysUserAttendanceStat> {
    // 自定义统计方法
    AttendanceStatDTO getMonthlyStat(String userId);
}

@Service
public class SysUserAttendanceStatServiceImpl 
    extends ServiceImpl<SysUserAttendanceStatMapper, SysUserAttendanceStat>
    implements ISysUserAttendanceStatService {
    // 实现类需添加@Slf4j日志注解
}
```

### 通用要求
1. 所有DTO需放在`dto`包下
2. 查询参数封装类命名规范：`XXXQuery`
3. 返回对象命名规范：`XXXDTO`

### Java服务开发

Java服务基于jeecg-boot低代码平台开发，支持微服务架构，可根据需要进行单体应用或微服务部署。

### 常见问题梳理,一定要牢记!!
#### projectKey问题
- 需求中提及的projectKey默认均指从后端从全局请求header中获取，接口请求时已默认传了projectKey,不需要页面中单独传递projectKey
#### 生成Mapper.xml文件路径问题
- 严格 开发指南 中的描述进行生成，不要总是生成resources/mapper目录,需要
跟着实际的Mapper接口目录生成，生成xml目录中;

### 开发经验总结
为了提高开发效率和代码质量，我们总结了开发过程中的常见错误和最佳实践：

📖 **[开发常见错误总结](./开发常见错误总结.md)**

该文档包含以下重要内容：
- 项目结构和路径错误
- 数据库字段映射错误
- 前端组件开发错误
- API设计错误
- UI/UX设计错误
- 架构设计错误
- 测试和验证错误
- 文档和规范错误
- 版本控制和部署错误
- 最佳实践建议

**强烈建议所有开发人员在开始开发前仔细阅读此文档，避免重复犯错，提高开发效率。**



## 部署指南

详细的部署步骤请参考各目录下的部署文档。

## 项目维护

- 负责人：[项目负责人]
- 开发团队：[开发团队信息]
- 最后更新时间：[更新日期]
