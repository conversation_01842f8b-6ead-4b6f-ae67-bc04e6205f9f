# 利氪项目工时管理系统需求分析

- **项目启动命令**：
  lpm start

- **项目打包命令**：
  lpm release


- **切换环境**：
  lpm config set pluginId *****

  lpm config set pluginSecret ****

- **打包check**：
  1.utils/config.ts中 SERVER_BASE_URL

- **初始化**:
  - 部署接口
  - 插件配置 + 部署 + 发布
  - 空间-安装插件
  - 插件管理员第1次打开插件-初始化: hapi/init/initConfig 
  - 初始化PLM：PlmSyncJobTest| hapi/init/initPlm
  - 配置飞书开放平台
  - 初始化通讯录：FeiShuOpenInitTest | hapi/init/initDeptUser
  - 同步考勤：hapi/init/initDeptUser
  
## 1. 项目概述

利氪项目工时管理系统主要围绕工时登记、项目数据同步、审批流程和报表统计等核心功能展开，旨在提供完整的工时管理解决方案。系统将与飞书项目和考勤系统进行集成，实现数据互通和流程自动化。

## 2. 核心功能模块

### 2.1 工时登记模块

#### 2.1.1 按周登记与截断规则

- **基本规则**：工时登记按天进行，支持一次登记多天数据，并按周为单位进行登记
- **跨月处理**：当一个自然周跨月时，系统将以月份为边界进行截断
  - 示例：6月30日和7月1-6日虽为同一周，但需分别在各自月份中登记
- **交互设计**：
  - 用户选择月份后，系统自动显示按规则截断好的日期供选择
  - 不同月份的日期选择数量可能有差异

#### 2.1.2 填写内容与数据来源

- **工作内容来源**：对接项目数据，无级别区分
- **项目数据同步条件**：
  - 仅同步已启动的项目
  - 示例：ERP中100个项目，其中90个已启动，则只同步这90个到飞书项目
- **项目信息维护**：
  - 项目状态、名称、编码以PLM同步数据为准
  - 项目经理（单选）和成员（多选）可在系统中维护，均从飞书项目空间用户中选择

#### 2.1.3 考勤与工时登记关系

- **考勤数据对接**：
  - 系统需对接考勤数据
  - 当天无考勤记录则不允许登记工时
  - 用户可查看自己的考勤数据了解情况
- **加班工时处理**：
  - 周六加班是否可登记工时及处理方式需与客户确认
  - 工时登记时填写百分比，报表显示工时数
  - 当前报表统计暂不包含加班工时

### 2.2 项目数据同步模块

#### 2.2.1 同步范围与确认事项

- **同步方向**：
  - PLM项目数据需同步到插件
  - 是否同步到飞书项目待确认
- **数据维护权限**：
  - 项目状态、名称、编码以PLM同步数据为准（只读）
  - 项目经理、成员及备注信息可在系统中维护编辑

#### 2.2.2 项目选择与展示规则

- **初始展示逻辑**：
  - 员工登记工时时，默认展示其参与的项目
  - 若未维护到对应项目，可通过"+"按钮添加
- **编辑与查看规则**：
  - 编辑和查看时，展示规则按新增时最终保存效果
  - 或展示填写数据的项目和当前参与的项目
  - 已结束的项目不会自动拉取

#### 2.2.3 项目人员数据维护

- **数据来源**：
  - 项目基础信息通过对接PLM系统获取
  - 包含项目ID、项目名称、项目状态、项目级别等基础信息
- **可维护内容**：
  - 项目基础信息（如项目ID、名称、状态等）为只读，不可修改
  - 项目经理可修改为飞书空间中的用户（单选）
  - 项目成员可从飞书空间用户中选择（多选）
- **维护界面**：
  - 表格式展示所有项目基本信息
  - 支持按项目ID、名称等条件筛选
  - 提供编辑功能进行项目人员调整
  - 记录最后更新时间和更新人

### 2.3 审批流程模块

#### 2.3.1 审批人员与流程

- **审批人确定**：
  - 审批单由员工直属上级和填写数据项目的负责人会签
  - 需对接组织架构数据获取直属上级信息
- **审批通过条件**：
  - 所有审批人都审批通过，整个单子才算审批通过

#### 2.3.2 审批状态展示

- **状态展示参考**：
  - 参考飞书审批，用"待办"/"已办"区分
  - 单子整体状态为"审批中"
- **特殊情况处理**：
  - 若有审批人拒绝，整个审批单结束
  - 支持退回后重新提交功能

### 2.4 报表统计模块

#### 2.4.1 报表内容与计算方式

- **统计维度**：
  - 部门和项目工时统计
  - 人员和项目工时统计
  - 支持数据导出功能
- **差值分析**：
  - 计算人员和部门可登记工时与实际工时的差值
  - 可登记工时根据考勤数据计算
  - 当前暂不包含加班工时

#### 2.4.2 数据展示与计算规则

- **数值转换**：
  - 登记时填写百分比
  - 报表显示实际工时数
  - 根据可登记工时计算实际工时
- **日期统计规则**：
  - 统计按月度进行
  - 日期连续展示
  - 未填工时的日期记为0

### 2.5 消息提醒与系统设置

#### 2.5.1 消息提醒内容

- **审批相关提醒**：
  - 提醒审批人有工时数据待审批
  - 通知发起人审批结果
- **系统设置**：
  - 去掉登记通用校验
  - 配置登记规则和项目人员数据
  - 设置管理员配置

#### 2.5.2 工作台功能

- **操作入口**：
  - 登录后可点击"立即报工"弹出报工弹窗
  - 点击"审批"跳转到审批页签，显示待审批列表
- **待登记提示**：
  - 显示待登记工时日期
  - 可点击选择工作日期周期进行登记

## 3. 待确认事项

1. **加班工时处理**：周六加班是否可登记工时及如何处理
2. **项目数据同步范围**：PLM项目数据是否需要同步到飞书项目
3. **审批流程细节**：具体审批流转规则和权限设置

## 4. 技术集成要点

1. **飞书项目集成**：对接飞书项目数据，同步项目信息
2. **考勤系统集成**：对接考勤数据，作为工时登记的前置条件
3. **组织架构集成**：获取直属上级信息，用于审批流程
4. **PLM系统集成**：同步项目状态、名称、编码等基础信息

## 5. 系统流程图

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  考勤系统   │────>│  工时登记   │────>│  审批流程   │
└─────────────┘     └─────────────┘     └─────────────┘
       │                   ▲                   │
       │                   │                   │
       ▼                   │                   ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  PLM系统    │────>│ 项目数据同步 │────>│  报表统计   │
└─────────────┘     └─────────────┘     └─────────────┘
```

## 6. 开发优先级建议

1. **P0**: 工时登记基本功能、项目数据同步
2. **P1**: 审批流程、考勤系统集成
3. **P2**: 报表统计功能
4. **P3**: 消息提醒、工作台优化

## 7. 界面原型设计

### 7.1 工时列表页面

- **导航结构**：
  - 顶部导航栏包含"工作台"、"工时管理"、"审批"、"报表"和"系统设置"等模块
  - 清晰划分功能区域，便于用户快速定位

- **列表展示**：
  - 显示工时记录，包含日期范围、工作人员、状态、审批时间和审批人等信息
  - 状态标签使用不同颜色区分（待审核、审批通过、审批驳回等）
  - 支持按日期范围、工作人员等条件筛选
  - 分页展示大量数据，提升用户体验

- **操作功能**：
  - 提供"批量提交"按钮进行批量操作
  - 每条记录支持"编辑"、"查看"和"删除"等操作
  - 操作按钮布局合理，减少误操作可能性

### 7.2 工时登记页面

- **基本信息区**：
  - 工作日期选择（支持日期范围选择）
  - 工作人员选择
  - 清晰展示当前操作的时间范围

- **工时明细表格**：
  - 按项目/任务分行显示
  - 包含周一至周日的工时填写列
  - 支持多行工时记录
  - 表格布局清晰，便于批量填写

- **操作按钮**：
  - 底部提供"保存"和"提交审批"按钮
  - 支持填写备注信息
  - 操作流程符合用户习惯

- **数据校验**：
  - 实时显示工时合计
  - 对超出限制的工时进行提示
  - 提供即时反馈，减少填写错误

### 7.3 审批页面

- **审批列表**：
  - 展示待审批、已审批、已拒绝的工时记录
  - 清晰标识审批状态和优先级
  - 支持按条件筛选审批记录

- **审批操作**：
  - 支持批量审批、单条审批、驳回等操作
  - 提供意见填写区域
  - 操作流程简洁明了

- **审批详情**：
  - 可查看工时明细和提交信息
  - 展示完整的审批流程和历史记录
  - 支持查看相关附件和说明

### 7.4 报表页面

- **报表类型选择**：
  - 支持多种报表类型切换
  - 提供预设报表模板
  - 用户界面友好，易于操作

- **筛选条件**：
  - 按时间、部门、项目等维度筛选
  - 支持自定义筛选条件组合
  - 条件设置直观明了

- **数据可视化**：
  - 图表展示工时分布情况
  - 支持多种图表类型（柱状图、饼图、折线图等）
  - 图表交互性强，支持钻取分析

- **导出功能**：
  - 支持将报表导出为Excel
  - 提供多种导出格式选项
  - 导出过程简单快捷

### 7.5 项目人员数据维护页面

- **页面布局**：
  - 标题为"项目人员数据维护"
  - 表格式展示所有项目信息
  - 支持分页浏览大量项目数据

- **数据展示**：
  - 包含项目ID、项目名称、项目状态、项目级别、备注等基础信息列
  - 显示项目经理、最后更新时间和更新人信息
  - 基础信息以只读形式展示

- **操作功能**：
  - 每行项目记录提供"编辑"操作按钮
  - 支持按项目ID、名称等条件筛选
  - 可查看项目详细信息

- **数据维护**：
  - 项目基础信息（ID、名称、状态等）为只读，来源于PLM系统
  - 可修改项目经理（从飞书空间用户中单选）
  - 可修改项目成员（从飞书空间用户中多选）
  - 修改后自动记录更新时间和更新人



