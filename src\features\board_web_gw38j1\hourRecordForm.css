/* 自定义 SideSheet 页脚样式 */
.semi-sidesheet-footer {
  padding: 0 !important;
}

/* 确保按钮部分仍然有足够的内边距 */
.hour-record-form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 24px;
  border-top: 1px solid var(--semi-color-border);
  background-color: #fff;
  flex-shrink: 0; /* 防止压缩 */
}

/* Modal模式下的样式 */
.hour-record-modal .semi-modal-content {
  max-height: 90vh;
  overflow: hidden;
}

/* 工时登记容器布局 */
.hour-record-container {
  display: flex;
  flex-direction: column;
  height: auto;
  padding: 0;
  overflow: hidden; /* 禁用容器滚动条 */
}

/* 头部区域 */
.hour-record-header {
  padding: 16px 24px 8px;
  display: flex;
  gap: 16px;
}

/* 信息区域 */
.hour-record-info {
  padding: 0 24px 16px;
  background-color: rgba(var(--semi-blue-0), .2);
  border-radius: 6px;
  margin: 0 24px 16px;
}

/* 内容区域 - 不可滚动 */
.hour-record-content {
  padding: 0 24px;
  margin-bottom: 10px; /* 为底部按钮留出空间 */
  overflow: visible; /* 内容区域不滚动 */
}

/* 批量登记模式下的表格容器 - 只有这里可以滚动 */
.batch-table-container {
  max-height: 280px; /* 减小表格高度 */
  overflow: hidden; /* 容器本身不滚动 */
  border: 1px solid var(--semi-color-border);
  border-radius: 4px;
}

/* 固定表头，只让表体滚动 */
.batch-table-container .semi-table-wrapper {
  overflow: hidden; /* 表格包装器不滚动 */
}

.batch-table-container .semi-table-header {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #fff;
}

.batch-table-container .semi-table-body {
  max-height: 230px; /* 减小表体最大高度 */
  overflow-y: auto; /* 只有表体可以垂直滚动 */
  overflow-x: hidden; /* 禁用水平滚动 */
}

/* 单个登记模式下的表格容器 */
.hour-record-table-container {
  overflow-x: hidden;
  width: 100%;
}

.hour-record-table-container .semi-table-body {
  overflow-x: auto;
}

/* 确保按钮部分仍然有足够的内边距 - 常规版本 */
.hour-record-form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 24px;
  border-top: 1px solid var(--semi-color-border);
  background-color: #fff;
  flex-shrink: 0; /* 防止压缩 */
}

/* 固定在底部的按钮区域 */
.hour-record-form-footer-fixed {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 24px;
  border-top: 1px solid var(--semi-color-border);
  background-color: #fff;
  z-index: 10;
}

/* 确保操作列固定在右侧 */
.hour-record-table-container .semi-table-fixed-right,
.batch-table-container .semi-table-fixed-right {
  box-shadow: -6px 0 6px -4px rgba(0,0,0,0.15);
}

/* 确保表头样式正确 */
.batch-table-container .semi-table-header-row {
  border-bottom: 1px solid var(--semi-color-border);
} 

.hour-record-modal .hour-record-form-input-number {
  padding: 1px;
  min-height: 30px;
}

.hour-record-modal .semi-table-tbody > .semi-table-row > .semi-table-row-cell{
  padding: 1px !important;
}