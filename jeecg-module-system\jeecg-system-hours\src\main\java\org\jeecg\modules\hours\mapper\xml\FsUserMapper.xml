<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.FsUserMapper">

    <!-- 获取指定项目下所有不是管理员角色的活跃用户 -->
    <select id="getNonAdminActiveUsersByProject" resultType="org.jeecg.modules.hours.entity.FsUser">
        SELECT fu.* 
        FROM fs_user fu 
        WHERE NOT EXISTS (
            SELECT 0 
            FROM fs_user_role fur 
            LEFT JOIN fs_role fr ON fur.role_id = fr.id
            WHERE fr.code = #{adminRoleCode} 
            AND fur.user_id = fu.id
            AND fr.del_flag = 0
        ) 
        AND fu.status = '1' 
        AND fu.project_key = #{projectKey}
    </select>
    
    <select id="getAdminUserLimit1" resultType="org.jeecg.modules.hours.entity.FsUser">
	    select fu.* from fs_user fu
		where exists(
			select 0 from fs_user_role fur where fur.user_id =fu.id
			and fur.role_id =#{roleId}
		)
		and fu.project_key =#{projectKey}
		and fu.status ='1'
		order by fu.created_at desc
		limit 1
    </select>
    
    <select id="getLeadersByUnionId" parameterType="java.util.Map" resultType="org.jeecg.modules.hours.entity.FsUser">
    	select 
		fu2.id, fu2.user_key, fu2.name, fu2.email, fu2.mobile, fu2.dept_id, fu2.status, 
		fu2.leader_user_id, fu2.sync_time, fu2.union_id, 
		fu2.created_at, fu2.create_by, fu2.project_key, fu2.user_key as user_id
		from fs_user fu 
		left join fs_department_user fdu on fu.union_id =fdu.union_id
		left join fs_department fd on fd.dept_id =fdu.dept_id
		left join fs_user fu2 on fd.leader_user_id=fu2.union_id
		where fu.union_id =#{unionId} and fd.leader_user_id is not null
    </select>
    
</mapper> 