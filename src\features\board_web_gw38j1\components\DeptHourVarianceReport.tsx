import React, { useState, useEffect } from 'react';
import { Table, Select, Button, Toast, Spin, Card, Row, Col, Space, Typography, TreeSelect } from '@douyinfe/semi-ui';
import { IconDownload } from '@douyinfe/semi-icons';
import '../workPage.css';
import {
  getDeptHourVarianceReport,
  exportDeptHourVarianceReport,
  getAllDepartments,
  type DeptHourVarianceReportParams,
  type DeptHourVarianceReportResult,
  type Department
} from '../../../api/report';

const { Text } = Typography;

interface FilterParams {
  year: number; // 改回number类型
  deptIds?: string[];
  includeHoliday: number | undefined; // 改为和人员每日工时分布一致的类型
}

const DeptHourVarianceReport: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<DeptHourVarianceReportResult[]>([]);
  const [filters, setFilters] = useState<FilterParams>({
    year: new Date().getFullYear(), // 初始化为当前年份
    includeHoliday: undefined
  });
  const [departments, setDepartments] = useState<Department[]>([]);

  // 生成年份选项
  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 5; i <= currentYear + 2; i++) {
      years.push(i);
    }
    return years;
  };

  // 动态生成表格列
  const generateColumns = () => {
    const columns = [
      {
        title: '部门',
        dataIndex: 'deptName',
        key: 'deptName',
        fixed: 'left' as const,
        width: 160,
      }
    ];

    // 动态添加12个月的列
    for (let month = 1; month <= 12; month++) {
      columns.push(
        {
          title: `${month}月份实际工时`,
          dataIndex: `month${month}Actual`,
          key: `month${month}Actual`,
          width: 160,
          render: (value: number) => value ? value.toFixed(2) : '0.00'
        },
        {
          title: `${month}月份可登记工时`,
          dataIndex: `month${month}Required`,
          key: `month${month}Required`,
          width: 170,
          render: (value: number) => value ? value.toFixed(2) : '0.00'
        },
        {
          title: `${month}月份差值`,
          dataIndex: `month${month}Variance`,
          key: `month${month}Variance`,
          width: 180,
          render: (value: number) => (
            <span style={{ color: value > 0 ? '#52c41a' : value < 0 ? '#ff4d4f' : '#666' }}>
              {value ? value.toFixed(2) : '0.00'}
            </span>
          )
        }
      );
    }

    return columns;
  };

  // 转换数据格式用于表格显示
  const transformDataForTable = (rawData: DeptHourVarianceReportResult[]) => {
    return rawData.map(item => {
      const tableRow: any = {
        key: item.deptId,
        deptId: item.deptId,
        deptName: item.deptName,
      };

      // 添加月度数据
      for (let month = 1; month <= 12; month++) {
        const actualKey = `${month}月份实际工时`;
        const requiredKey = `${month}月份可登记工时`;
        const varianceKey = `${month}月份差值`;

        tableRow[`month${month}Actual`] = item.monthlyData[actualKey] || 0;
        tableRow[`month${month}Required`] = item.monthlyData[requiredKey] || 0;
        tableRow[`month${month}Variance`] = item.monthlyData[varianceKey] || 0;
      }

      return tableRow;
    });
  };


  // 获取部门列表
  const fetchDepartments = async () => {
    try {
      const deptList = await getAllDepartments();
      setDepartments(deptList);
    } catch (error) {
      console.error('获取部门列表失败:', error);
      // 如果接口调用失败，设置为空数组
      setDepartments([]);
      Toast.warning('部门接口调用失败，部门筛选功能暂时不可用');
    }
  };

  // 递归获取所有部门ID
  const getAllDepartmentIds = (departments: Department[]): string[] => {
    let allIds: string[] = [];
    departments.forEach(dept => {
      allIds.push(dept.id);
      if (dept.children && dept.children.length > 0) {
        allIds = [...allIds, ...getAllDepartmentIds(dept.children)];
      }
    });
    return allIds;
  };

  // 递归获取所有选中部门的完整ID列表（包括父级选中时的所有子级）
  const getAllSelectedDepartmentIds = (departments: Department[], selectedIds: string[]): string[] => {
    let allIds: string[] = [];
    
    departments.forEach(dept => {
      if (selectedIds.includes(dept.id)) {
        // 添加当前部门ID
        allIds.push(dept.id);
        
        // 如果有子部门，递归添加所有子部门ID
        if (dept.children && dept.children.length > 0) {
          const allChildIds = getAllDepartmentIds(dept.children);
          allIds = [...allIds, ...allChildIds];
        }
      } else if (dept.children && dept.children.length > 0) {
        // 如果父级未被选中，但可能有子级被选中
        allIds = [...allIds, ...getAllSelectedDepartmentIds(dept.children, selectedIds)];
      }
    });
    
    return [...new Set(allIds)]; // 去重
  };

  // 将部门数据转换为TreeSelect所需的格式
  const transformDepartmentData = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      label: dept.name,
      value: dept.id,
      key: dept.id,
      children: dept.children && dept.children.length > 0
        ? transformDepartmentData(dept.children)
        : undefined
    }));
  };

  // 查询报表数据
  const fetchReportData = async () => {
    setLoading(true);
    try {
      // 将用户选择的部门ID展开为所有实际需要查询的部门ID
      const expandedDepartmentIds = filters.deptIds && filters.deptIds.length > 0 
        ? getAllSelectedDepartmentIds(departments, filters.deptIds)
        : [];

      const params: DeptHourVarianceReportParams = {
        year: filters.year, // 直接使用年份数字
        includeHoliday: filters.includeHoliday === 1 ? true : filters.includeHoliday === 0 ? false : undefined
      };

      if (expandedDepartmentIds.length > 0) {
        params.deptIds = expandedDepartmentIds.join(',');
      }

      const reportData = await getDeptHourVarianceReport(params);
      setData(reportData);
    } catch (error) {
      console.error('查询报表数据失败:', error);
      Toast.error('查询失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 导出报表
  const handleExport = async () => {
    try {
      // 将用户选择的部门ID展开为所有实际需要查询的部门ID
      const expandedDepartmentIds = filters.deptIds && filters.deptIds.length > 0 
        ? getAllSelectedDepartmentIds(departments, filters.deptIds)
        : [];

      const params: DeptHourVarianceReportParams = {
        year: filters.year, // 直接使用年份数字
        includeHoliday: filters.includeHoliday === 1 ? true : filters.includeHoliday === 0 ? false : undefined
      };

      if (expandedDepartmentIds.length > 0) {
        params.deptIds = expandedDepartmentIds.join(',');
      }

      await exportDeptHourVarianceReport(params);
      Toast.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      Toast.error('导出失败，请稍后重试');
    }
  };

  // 计算总计行
  const calculateSummaryRow = () => {
    if (!data.length) return null;

    const summary: any = {
      key: 'summary',
      deptName: '总计',
    };

    for (let month = 1; month <= 12; month++) {
      let monthActualTotal = 0;
      let monthRequiredTotal = 0;
      let monthVarianceTotal = 0;

      data.forEach(item => {
        monthActualTotal += item.monthlyData[`${month}月份实际工时`] || 0;
        monthRequiredTotal += item.monthlyData[`${month}月份可登记工时`] || 0;
        monthVarianceTotal += item.monthlyData[`${month}月份差值`] || 0;
      });

      summary[`month${month}Actual`] = monthActualTotal;
      summary[`month${month}Required`] = monthRequiredTotal;
      summary[`month${month}Variance`] = monthVarianceTotal;
    }

    return summary;
  };

  useEffect(() => {
    // 只需要获取部门列表
    fetchDepartments();
  }, []);

  const tableData = transformDataForTable(data);
  const summaryRow = calculateSummaryRow();
  if (summaryRow) {
    tableData.push(summaryRow);
  }

  return (
    <div style={{ padding: '16px' }}>
      {/* 筛选条件 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[12, 8]}>
          <Col span={4}>
            <Space vertical style={{ width: '100%' }}>
              <Select
                value={filters.year}
                onChange={(value) => setFilters(prev => ({ ...prev, year: value as number }))}
                style={{ width: '100%' }}
                placeholder="选择年份"
              >
                {generateYearOptions().map(year => (
                  <Select.Option key={year} value={year}>{year}年</Select.Option>
                ))}
              </Select>
            </Space>
          </Col>

          <Col span={6}>
            <Space vertical style={{ width: '100%' }}>
              <TreeSelect
                multiple
                value={filters.deptIds}
                onChange={(value) => {
                  // 保存用户选择的原始部门ID
                  setFilters(prev => ({ ...prev, deptIds: value }));
                }}
                style={{ width: '100%' }}
                placeholder="选择部门"
                maxTagCount={1}
                showClear
                treeData={transformDepartmentData(departments)}
                searchable
                showSearchClear
                expandAll
                checkRelation="related"
              />
            </Space>
          </Col>

          <Col span={4}>
            <Space vertical style={{ width: '100%' }}>
              <Select
                value={filters.includeHoliday}
                onChange={(value) => setFilters(prev => ({ ...prev, includeHoliday: value }))}
                style={{ width: '100%' }}
                placeholder="请选择"
                showClear
              >
                <Select.Option value={1}>统计假日</Select.Option>
                <Select.Option value={0}>不统计假日</Select.Option>
              </Select>
            </Space>
          </Col>

          <Col span={10}>
            <Space>
              <Button
                theme="solid"
                type="primary"
                onClick={fetchReportData}
                loading={loading}
              >
                查询
              </Button>
              <Button
                type="secondary"
                onClick={handleExport}
                disabled={!data.length || loading}
                icon={<IconDownload />}
              >
                导出Excel
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 报表表格 */}
      <Card style={{ marginBottom: '16px' }}>
        <Spin spinning={loading}>
          <Table
            columns={generateColumns()}
            dataSource={tableData}
            pagination={false}
            scroll={{ x: 'max-content', y: 600 }}
            size="small"
            bordered
            rowClassName={(record) => record.key === 'summary' ? 'summary-row' : ''}
          />
        </Spin>
      </Card>

    </div>
  );
};

export default DeptHourVarianceReport;
