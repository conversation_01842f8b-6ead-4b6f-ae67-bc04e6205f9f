.project-member-settings {
  box-sizing: border-box;
  max-width: 100%;
  padding: 0;
  margin: 0;
  
  .search-area {
    margin-bottom: 16px;
    padding: 16px;
    background-color: var(--semi-color-bg-2);
    border-radius: 4px;
    box-sizing: border-box;
    max-width: 100%;
  }

  .semi-form-field {
    margin-bottom: 0;
  }

  .semi-table-row:hover .semi-button {
    visibility: visible;
  }

  .semi-table-row .semi-button {
    visibility: visible;
  }

  .project-info-item {
    margin-bottom: 8px;
    
    .label {
      color: var(--semi-color-text-2);
      margin-bottom: 4px;
      font-size: 12px;
    }
    
    .value {
      font-size: 14px;
    }
  }

  .project-member-edit-form {
    .semi-form-field {
      margin-bottom: 16px;
    }
  }
  
  /* 表格容器样式，允许表格内容滚动 */
  .semi-table-wrapper {
    overflow: auto;
    width: 100%;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    margin-top: 16px;
    margin-bottom: 16px;
  }
  
  /* 确保表格内容不会导致页面横向滚动 */
  .semi-table {
    width: 100%;
    min-width: auto;
    box-sizing: border-box;
  }
  
  /* 覆盖Semi Design的表格样式 */
  :global(.semi-table-wrapper) {
    max-width: 100%;
    overflow-x: auto;
    box-sizing: border-box;
  }
  
  :global(.semi-table) {
    width: auto;
    table-layout: auto;
  }
  
  /* 分页区域样式 */
  :global(.semi-pagination) {
    margin-top: 16px;
    margin-bottom: 16px;
  }
} 