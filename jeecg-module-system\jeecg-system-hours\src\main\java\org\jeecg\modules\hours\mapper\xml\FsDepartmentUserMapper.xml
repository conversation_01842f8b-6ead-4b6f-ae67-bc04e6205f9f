<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.FsDepartmentUserMapper">

    <select id="selectDepartIdsByUserId" resultType="java.lang.String">
        SELECT dept_id FROM fs_department_user WHERE user_id = #{userId}
    </select>

    <select id="selectDeptNamesByUnionIds" resultType="java.util.HashMap">
        SELECT fdu.union_id as unionId, GROUP_CONCAT(fd.name) as deptName
        FROM fs_department_user fdu
        LEFT JOIN fs_department fd ON fdu.dept_id = fd.id
        WHERE fdu.dept_id != '0'
          AND fdu.union_id IN
          <foreach collection="unionIds" item="id" open="(" separator="," close=")">#{id}</foreach>
        GROUP BY fdu.union_id
    </select>
</mapper>