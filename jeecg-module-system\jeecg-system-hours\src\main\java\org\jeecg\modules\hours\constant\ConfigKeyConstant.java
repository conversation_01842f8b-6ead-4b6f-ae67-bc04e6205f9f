package org.jeecg.modules.hours.constant;

/**
 * 系统配置键值常量
 */
public class ConfigKeyConstant {
    
    /**
     * 工作内容范围配置键
     * 示例值: sub_task,story
     * 用途: 用于过滤工作项类型，多个类型用逗号分隔
     * 分组: work_content
     */
    public static final String WORK_CONTENT_RANGE = "work_content_range";

    /**
     * 每天标准工时配置键
     * 示例值: 8
     * 用途: 用于工时单位转换（天和小时之间的转换）
     * 分组: work_hour
     */
    public static final String WORK_HOUR_PER_DAY = "work_hour_per_day";

    /**
     * 最小登记工时配置键
     * 示例值: 0.5
     * 用途: 用于限制每次登记的最小工时
     * 分组: work_hour
     */
    public static final String MIN_WORK_HOUR = "min_work_hour";

    /**
     * 工时规则配置键
     * 示例值: manual/feishu
     * 用途: 用于选择工时校验规则
     * 分组: work_hour
     */
    public static final String WORK_HOUR_RULE = "wok_hour_rule";

    /**
     * 飞书假勤校验开关
     * 示例值: 0-关闭；1-开启
     * 用途: 用于选择工时校验规则
     * 分组: work_hour
     */
    public static final String FEISHU_ATTENDANCE_VALIDATE_SWITCH = "feishu_attendance_validate_switch";

    /**
     * 每日可登记工时上限配置键
     * 示例值: 10
     * 用途: 用于限制每日可登记的最大工时
     * 分组: work_hour
     */
    public static final String MAX_WORK_HOUR_PER_DAY = "max_work_hour_per_day";

    /**
     * 飞书应用AppID配置键
     * 示例值: cli_xxxxxx
     * 用途: 用于飞书应用绑定
     * 分组: feishu
     */
    public static final String BIND_APP_ID = "bind_app_id";

    /**
     * 飞书应用AppSecret配置键
     * 示例值: xxxxxx-xxxx-xxxx-xxxx-xxxxxx
     * 用途: 用于飞书应用绑定
     * 分组: feishu
     */
    public static final String BIND_APP_SECRET = "bind_app_secret";


    private ConfigKeyConstant() {
        // 私有构造函数，防止实例化
    }
}