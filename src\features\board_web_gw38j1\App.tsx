import React, {useEffect, useState} from 'react';
import './index.css';
import WorkPage from './WorkPage';
import ApprovalPage from './ApprovalPage';
import ReportPage from './ReportPage';
import SettingsPage from './SettingsPage';
import CheckAuth from './auth/CheckAuth';
import './auth/index.css';
import Workbenches from "./Workbenches";
import {useSelector} from "react-redux";
import store, {RootState, setActiveTab} from "../../services/AppState";
import { Tooltip } from '@douyinfe/semi-ui';
import { IconHelpCircle } from '@douyinfe/semi-icons';
const NAV_TABS = [
  { key: 'Workbenches', label: '工作台' },
  { key: 'work', label: '工时管理' },
  { key: 'approval', label: '审批管理' },
  { key: 'report', label: '报表分析' },
  { key: 'settings', label: '系统设置' },
];

const App: React.FC = () => {
  const activeTab = useSelector((state: RootState) => state.activeTab);
 // const [activeTab, setActiveTab] = useState('Workbenches');

  const openUrl = (url: string) => {
    window.JSSDK.navigation.open(url);
  };

  // 内容区渲染
  const renderContent = () => {
    if (activeTab === 'Workbenches') return <Workbenches />;
    if (activeTab === 'work') return <WorkPage />;
    if (activeTab === 'approval') return <ApprovalPage />;
    if (activeTab === 'report') return <ReportPage />;
    if (activeTab === 'settings') return <div className="content-wrapper">{<SettingsPage />}</div>;
    return null;
  };

  const setTab=(key)=>{
  }

  useEffect(() => {
    store.dispatch(setActiveTab('Workbenches'));
  },[])


  return (
    <CheckAuth>
      <div className="layout-root">
        {/* 顶部导航栏 */}
        <header className="header">
          <div className="nav-container">
            <nav className="header-nav header-nav-center">
              {NAV_TABS.map(tab => (
                <span
                  key={tab.key}
                  className={`nav-item${activeTab === tab.key ? ' nav-active' : ''}`}
                  onClick={() => {
                    setTab(tab.key);
                    store.dispatch(setActiveTab(tab.key));
                  }}
                  style={tab.key === 'settings' ? {
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    position: 'relative'
                  } : {}}
                >
                  {tab.label}
                  {tab.key === 'settings' && (
                    <Tooltip content="系统设置帮助说明" position="bottom">
                      <IconHelpCircle
                        style={{
                          position: 'absolute',
                          right: '8px',
                          fontSize: '14px',
                          color: '#999',
                          cursor: 'pointer'
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          // 这里可以添加帮助说明的逻辑
                          console.log('显示系统设置帮助');
                        }}
                      />
                    </Tooltip>
                  )}
                </span>
              ))}
            </nav>
          </div>
        </header>
        {/* 主体区域 */}
        <main className="main-content">
          {renderContent()}
        </main>
      </div>
    </CheckAuth>
  );
}

export default App;