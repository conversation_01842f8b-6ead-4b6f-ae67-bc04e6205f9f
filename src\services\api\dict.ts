import { get } from '../../utils/request';

// 定义接口响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  result: T;
}

// 字典数据缓存
const dictCache: Record<string, ApiResponse<any>> = {};

/**
 * 获取数据字典项
 * @param dictCode 字典代码
 * @param forceRefresh 是否强制刷新缓存
 * @returns 字典项列表
 */
export async function getDictItems(dictCode: string, forceRefresh = false) {
  // 如果缓存中存在且不强制刷新，则直接返回缓存数据
  if (!forceRefresh && dictCache[dictCode]) {
    return dictCache[dictCode];
  }
  
  // 否则请求后端接口
  const response = await get('/dict/get', { dictCode }) as ApiResponse<any>;
  
  // 缓存响应数据
  if (response && response.success) {
    dictCache[dictCode] = response;
  }
  
  return response;
} 