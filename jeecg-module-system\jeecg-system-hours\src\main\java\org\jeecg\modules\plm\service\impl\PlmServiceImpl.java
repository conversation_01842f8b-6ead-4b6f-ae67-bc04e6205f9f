package org.jeecg.modules.plm.service.impl;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.plm.service.IPlmService;
import org.jeecg.modules.plm.vo.PlmProjectVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;

/**
 * PLM服务实现类
 */
@Service
@Slf4j
public class PlmServiceImpl implements IPlmService {

	@Value("${plm.api.base-url:http://**************:9200}")
	private String plmApiBaseUrl;

	@Value("${plm.api.account:62a9a9e7aa9aae}")
	private String plmApiAccount;

	@Value("${plm.api.username:kingdee}")
	private String plmApiUsername;

	@Value("${plm.api.password:Abcd.1234}")
	private String plmApiPassword;

	@Autowired
	private RedisUtil redisUtil;

	// Token过期时间，单位：毫秒
	private static final long TOKEN_EXPIRE_TIME = 2 * 60 * 60; // 2小时
	// Token缓存时间戳
	private static long tokenCacheTimestamp = 0L;

	private String getToken() {
		// 检查缓存是否过期
		long currentTime = System.currentTimeMillis();
		String key = "plm_token";
		if (redisUtil.hasKey(key) && (currentTime - tokenCacheTimestamp < TOKEN_EXPIRE_TIME)) {
			log.debug("使用缓存的PLM Token");
			return redisUtil.get(key) + "";
		}

		// 缓存过期或不存在，重新获取Token
		log.info("PLM Token缓存过期或不存在，重新获取Token");
		try {
			String token = login();
			if (token != null && !token.isEmpty()) {
				// 更新缓存
				redisUtil.set(key, token, TOKEN_EXPIRE_TIME);
//                TOKEN_CACHE.put("plm_token", token);
				tokenCacheTimestamp = currentTime;
				return token;
			}
		} catch (Exception e) {
			log.error("获取PLM Token失败", e);
		}

		return null;
	}

//	@Override
	private void clearTokenCache() {
		redisUtil.del("plm_token");
		tokenCacheTimestamp = 0L;
		log.info("已清除PLM Token缓存");
	}

	/**
	 * 登录PLM系统
	 * 
	 * @return 登录成功后的Cookie信息
	 * @throws Exception 请求异常
	 */
	private String login() throws Exception {
		String url = plmApiBaseUrl + "/k3cloud/Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc";

		// 构建请求参数
		JSONObject requestBody = new JSONObject();
		requestBody.put("acctid", plmApiAccount);
		requestBody.put("username", plmApiUsername);
		requestBody.put("password", plmApiPassword);
		requestBody.put("lcid", 2052);

		// 发送请求
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("Content-Type", "application/json");

		StringEntity entity = new StringEntity(requestBody.toString(), StandardCharsets.UTF_8);
		httpPost.setEntity(entity);

		CloseableHttpResponse response = httpClient.execute(httpPost);

		try {
			HttpEntity responseEntity = response.getEntity();
			String responseBody = EntityUtils.toString(responseEntity);
			log.debug("登录响应: {}", responseBody);

			// 获取Cookie
			String cookie = response.getFirstHeader("Set-Cookie").getValue();
			log.info("登录成功，获取Cookie: {}", cookie);

			return cookie;
		} finally {
			response.close();
			httpClient.close();
		}
	}

	@Override
	public List<PlmProjectVO> getProjectList() {
		try {
			// 获取Token
			String token = getToken();
			if (token == null || token.isEmpty()) {
				log.error("获取PLM Token失败，无法获取项目列表");
				return Collections.emptyList();
			}

			// 获取项目全量信息
			JSONArray jsonArray = getProjectFullInfo(token);

			// 解析项目信息
			return parseProjectInfo(jsonArray);
		} catch (Exception e) {
			log.error("获取PLM项目列表异常", e);
			return Collections.emptyList();
		}
	}

	@Override
	public List<PlmProjectVO> getIncrementalProjectList(String dateFrom) {
		try {
			// 获取Token
			String token = getToken();
			if (token == null || token.isEmpty()) {
				log.error("获取PLM Token失败，无法获取增量项目列表");
				return Collections.emptyList();
			}

			// 获取项目增量信息
			JSONArray jsonArray = getIncrementalProjectInfo(token, dateFrom);

			// 解析项目信息
			return parseProjectInfo(jsonArray);
		} catch (Exception e) {
			log.error("获取PLM增量项目列表异常", e);
			return Collections.emptyList();
		}
	}

	/**
	 * 获取项目全量信息
	 * 
	 * @param cookie 登录后的Cookie
	 * @return 项目全量信息
	 * @throws Exception 请求异常
	 */
	private JSONArray getProjectFullInfo(String cookie) throws Exception {
		String url = plmApiBaseUrl
				+ "/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc";

		// 构建请求参数
		JSONObject dataObj = new JSONObject();
		dataObj.put("FormId", "PLM_PDM_1080000000000000000");
		dataObj.put("FieldKeys",
				"FCODE,FNAME,FCATEGORYID,FPRJSTATUS,FFLOWCREATETIME,FLASTCHECKINDATE,FMODIFYDATE,FCREATEDATE");
		dataObj.put("FilterString", "FCATEGORYID='1080000000000000000' and FLIFECIRCLESTAGE!='AN' ");
		dataObj.put("OrderString", "");
		dataObj.put("TopRowCount", 0);
		dataObj.put("StartRow", 0);
		dataObj.put("Limit", 1000);
		dataObj.put("SubSystemId", "");

		JSONObject requestBody = new JSONObject();
		requestBody.put("data", dataObj);

		// 发送请求
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("Content-Type", "application/json");

		// 添加Cookie到请求头
		if (cookie != null && !cookie.isEmpty()) {
			httpPost.setHeader("Cookie", cookie);
		}

		StringEntity entity = new StringEntity(requestBody.toString(), StandardCharsets.UTF_8);
		httpPost.setEntity(entity);

		CloseableHttpResponse response = httpClient.execute(httpPost);

		try {
			HttpEntity responseEntity = response.getEntity();
			String responseBody = EntityUtils.toString(responseEntity);
			log.debug("获取项目全量信息响应: {}", responseBody);

			return JSONArray.parseArray(responseBody);
		} finally {
			response.close();
			httpClient.close();
		}
	}

	/**
	 * 获取项目增量信息
	 * 
	 * @param cookie   登录后的Cookie
	 * @param dateFrom 起始日期 (yyyy-MM-dd)
	 * @return 项目增量信息
	 * @throws Exception 请求异常
	 */
	private JSONArray getIncrementalProjectInfo(String cookie, String dateFrom) throws Exception {
		String url = plmApiBaseUrl
				+ "/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc";

		// 构建请求参数
		JSONObject dataObj = new JSONObject();
		dataObj.put("FormId", "PLM_PDM_1080000000000000000");
		dataObj.put("FieldKeys",
				"FCODE,FNAME,FCATEGORYID,FPRJSTATUS,FFLOWCREATETIME,FLASTCHECKINDATE,FMODIFYDATE,FCREATEDATE");
		dataObj.put("FilterString",
				"FCATEGORYID='1080000000000000000' and FLIFECIRCLESTAGE!='AN' and FMODIFYDATE>='" + dateFrom + "'");
		dataObj.put("OrderString", "");
		dataObj.put("TopRowCount", 0);
		dataObj.put("StartRow", 0);
		dataObj.put("Limit", 1000);
		dataObj.put("SubSystemId", "");

		JSONObject requestBody = new JSONObject();
		requestBody.put("data", dataObj);

		// 发送请求
		CloseableHttpClient httpClient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeader("Content-Type", "application/json");

		// 添加Cookie到请求头
		if (cookie != null && !cookie.isEmpty()) {
			httpPost.setHeader("Cookie", cookie);
		}

		StringEntity entity = new StringEntity(requestBody.toString(), StandardCharsets.UTF_8);
		httpPost.setEntity(entity);

		CloseableHttpResponse response = httpClient.execute(httpPost);

		try {
			HttpEntity responseEntity = response.getEntity();
			String responseBody = EntityUtils.toString(responseEntity);
			log.debug("获取项目增量信息响应: {}", responseBody);

			return JSONArray.parseArray(responseBody);
		} finally {
			response.close();
			httpClient.close();
		}
	}

	/**
	 * 解析项目信息
	 * 
	 * @param jsonArray 项目信息JSON
	 * @return 项目列表
	 */
	private List<PlmProjectVO> parseProjectInfo(JSONArray jsonArray) {
		List<PlmProjectVO> projectList = new ArrayList<>();

		try {
			if (jsonArray == null || jsonArray.isEmpty()) {
				return projectList;
			}
			for (int i = 0; i < jsonArray.size(); i++) {
				JSONArray projectData = jsonArray.getJSONArray(i);

				if (projectData != null && projectData.size() >= 8) {
					// FCODE,FNAME,FCATEGORYID,FPRJSTATUS,FFLOWCREATETIME,FLASTCHECKINDATE,FMODIFYDATE,FCREATEDATE
					PlmProjectVO project = new PlmProjectVO();
					project.setProjectCode(projectData.getString(0)); // FCODE
					project.setProjectName(projectData.getString(1)); // FNAME
//					project.setCategoryId(projectData.getString(2)); // FCATEGORYID
					project.setStatus(projectData.getString(3)); // FPRJSTATUS
//					project.setFlowCreateTime(projectData.getString(4)); // FFLOWCREATETIME
//					project.setLastCheckInDate(projectData.getString(5)); // FLASTCHECKINDATE
					project.setModifyDate(projectData.getString(6)); // FMODIFYDATE
					project.setCreateDate(projectData.getString(7)); // FCREATEDATE

					projectList.add(project);
				}
			}
		} catch (Exception e) {
			log.error("解析项目信息异常", e);
		}

		return projectList;
	}

}