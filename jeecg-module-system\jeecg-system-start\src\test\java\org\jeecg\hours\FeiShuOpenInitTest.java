package org.jeecg.hours;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.feishu.service.FeiShuService;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@ActiveProfiles("dev")
//@ActiveProfiles("prod")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class FeiShuOpenInitTest {

	// 测试环境
	private static final String appId = "cli_a7b2f4bbcb38500d";
	private static final String appSecret = "kzen0nGwHB15eEUVsDXkp1wn0YOF4HUK";

	// 生产环境
//	private static final String appId = "cli_a8e767f76d7e501c";
//	private static final String appSecret = "RZns5zuURXZ5nIYr8EuuAdHXpCjOXNOU";
	
	
	@Autowired
	private FeiShuService feiShuService;

	@org.junit.jupiter.api.Test
	public void testname() throws Exception {
		String projectKey = "6846a6670ee55facde5f6ba5";
		feiShuService.syncAllDepartmentsAndUsers(appId, appSecret,projectKey);
	}

}