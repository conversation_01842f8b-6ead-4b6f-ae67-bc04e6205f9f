# 飞书工时管理系统
 
 ## 项目介绍
 
 该模块是基于JeecgBoot开发的工时管理系统，与飞书项目集成，实现工时登记、工时审批、工时统计等功能。
 
 ## 代码规范与最佳实践
 
 ### 导入规范
 1. 所有需要的类必须显式导入，不要依赖隐式导入
 2. 使用完整的包路径导入类，避免使用通配符导入
 3. 导入语句应按以下顺序分组，组间用空行分隔：
    - Java标准库类
    - 第三方库类
    - 项目内部类
 4. 每个导入语句应独占一行
 
 示例：
 ```java
 import java.util.List;  // Java标准库
 
 import com.baomidou.mybatisplus.extension.service.IService;  // 第三方库
 
 import org.jeecg.modules.hours.entity.SysUserAttendanceStat;  // 项目内部类
 ```