package org.jeecg.modules.hours.vo;

import java.util.Map;

import org.jeecgframework.poi.excel.annotation.Excel;

import lombok.Data;

/**
 * 实例工时周期汇总报表导出VO
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class InstanceHoursExportVO {

    @Excel(name = "实例名称", width = 20, orderNum = "1")
    private String instanceName;

    @Excel(name = "小计(小时)", width = 15, orderNum = "999")
    private Double total;

    // 动态列将通过反射设置
    private Map<String, Double> periods;
}
