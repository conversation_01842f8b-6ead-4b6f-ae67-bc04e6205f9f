# 工时审批批次化改造功能需求（无审批日志表版）

## 一、业务背景与目标

当前工时审批流仅支持单一审批批次，驳回后再次提交会覆盖原审批人清单，导致历史审批流无法完整追溯。为满足企业级审批流的合规性、可追溯性和灵活扩展需求，需对工时审批流程进行“批次化”改造，实现每次提交生成独立审批批次，所有审批历史均可分批次追溯。

本次改造**完全去除 hour_record_approval（审批操作日志表）及相关逻辑**，所有审批流历史、状态、操作均通过 hour_record_apply（批次表）+ hour_record_approver（审批人清单表）实现。

## 二、表结构设计

### 1. 工时申请批次表（hour_record_apply）
| 字段名         | 类型           | 说明           |
|----------------|----------------|----------------|
| id             | bigint(20)     | 主键，自增     |
| record_id      | varchar(36)    | 工时单ID       |
| batch_no       | int            | 批次号/版本号  |
| applicant_id   | varchar(64)    | 申请人ID       |
| applicant_name | varchar(50)    | 申请人姓名     |
| apply_time     | datetime       | 申请时间       |
| description    | varchar(200)   | 申请说明       |
| status         | tinyint(1)     | 状态(0:审批中,1:已通过,2:已驳回) |
| create_time    | datetime       | 创建时间       |
| update_time    | datetime       | 更新时间       |

### 2. 审批人清单表（hour_record_approver）
> 新增 apply_id 字段，所有审批人记录均绑定到具体批次。
| 字段名         | 类型           | 说明           |
|----------------|----------------|----------------|
| id             | bigint(20)     | 主键，自增     |
| apply_id       | bigint(20)     | 申请批次ID     |
| approver_id    | varchar(64)    | 审批人ID       |
| approver_name  | varchar(50)    | 审批人姓名     |
| role           | varchar(20)    | 审批人角色     |
| status         | tinyint(1)     | 审批状态(0:待审批,1:已通过,2:已拒绝,3:已移交) |
| action_time    | datetime       | 审批时间       |
| remarks        | varchar(200)   | 审批意见       |
| transferred_from | varchar(64)   | 移交前审批人ID |
| transferred_time | datetime      | 移交时间       |
| create_time    | datetime       | 创建时间       |
| update_time    | datetime       | 更新时间       |

## 三、变更增量SQL（MySQL）

```sql
-- 1. 新建工时申请批次表
CREATE TABLE `hour_record_apply` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `record_id` varchar(36) NOT NULL COMMENT '工时单ID',
  `batch_no` int NOT NULL COMMENT '批次号/版本号',
  `applicant_id` varchar(64) NOT NULL COMMENT '申请人ID',
  `applicant_name` varchar(50) NOT NULL COMMENT '申请人姓名',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `description` varchar(200) DEFAULT NULL COMMENT '申请说明',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态(0:审批中,1:已通过,2:已驳回)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工时单申请批次表';

-- 2. 审批人清单表增加 apply_id 字段
ALTER TABLE `hour_record_approver` ADD COLUMN `apply_id` bigint(20) DEFAULT NULL COMMENT '申请批次ID' AFTER `id`;
ALTER TABLE `hour_record_approver` ADD KEY `idx_apply_id` (`apply_id`);

-- 3. 删除审批操作日志表（如已存在）
DROP TABLE IF EXISTS `hour_record_approval`;
```

## 四、前端调整任务清单

### 1. 工时单提交/编辑页
- **无需前端调整**，但后端接口需与 hour_record_apply 做对接，确保每次提交生成批次记录。

### 2. 审批管理列表页
- 前端列表页展示的 hour_record 信息需适配 hour_record_apply，展示最新批次的状态、申请人、申请时间等。
- 查询接口需支持按最新批次（apply_id）聚合展示。
- “待办”列表：查最新批次下 status=0 的审批人。
- “已办”列表：聚合所有批次下当前用户审批过的记录。
- “全部”列表：聚合所有批次下与当前用户相关的审批记录。

### 3. 审批详情页/审批流时间线
- 在【已办】【全部】列表中，用户点击【详情】时，系统应展示该审批操作所归属批次（apply_id）的申请信息和审批人清单。
- 详情页无需支持批次切换，仅需还原该批次的审批流。
- 相关接口：
  - 获取批次详情（/api/hourRecordApply/get?applyId=xxx）
  - 获取该批次下所有审批人（/api/hourRecordApprover/list?applyId=xxx）
  - 审批操作接口需带 apply_id

### 4. API对接与数据结构适配
- 适配 hour_record_apply、hour_record_approver 新增/变更字段。
- 查询接口需支持按 apply_id 过滤与聚合。

### 5. 历史数据兼容与迁移
- 兼容老数据展示，必要时提供数据迁移脚本或兼容逻辑。

### 6. 测试与验收
- 批次化审批流的新增、驳回、再次提交、历史追溯等全流程测试。
- 前端各列表、审批流时间线的正确性与交互体验。 

## 五、接口改造影响点清单

本次批次化改造涉及以下接口的新增、调整与废弃，具体如下：

### 1. 需新增/调整的接口

| 业务场景         | 接口路径/方法                                         | 说明/改造点 |
|------------------|------------------------------------------------------|-------------|
| 查询批次审批流   | GET /api/hourRecordApprover/approvalFlow?applyId=xxx | 返回该批次的申请信息和审批人清单，取代原有 list 接口 |
| 审批操作         | POST /api/hourRecordApprover/approve                 | 审批操作需带 apply_id，更新对应审批人记录 |
| 列表聚合         | GET /api/hourRecord/list                             | 列表页需聚合最新批次信息展示 |

### 2. 需废弃/删除的接口

| 旧接口路径/方法                        | 说明/替换方案 |
|----------------------------------------|--------------|
| /api/hourRecordApply/save              | 不再需要，批次由后端自动生成 |
| /api/hourRecordApply/get               | 合并到 approvalFlow 接口 |
| /api/hourRecordApprover/list           | 合并到 approvalFlow 接口 |
| /api/hourRecordApproval/*              | 审批操作日志相关接口全部废弃 |
| 任何基于 hour_record_approval 的查询   | 替换为基于 hour_record_apply + hour_record_approver 的查询 |

### 3. 需调整的接口

| 旧接口路径/方法                        | 说明/改造点 |
|----------------------------------------|------------|
| /api/hourRecord/submit                 | 提交时需同步生成 hour_record_apply 记录，并返回 apply_id（仅后端处理，前端无需感知）|
| /api/hourRecordApprover/approvalFlow   | 增加 applyId 参数，支持按批次查询审批流 |
| /api/hourRecord/list                   | 列表页展示的“状态”字段，依然以 hour_record 主表的 status 字段为准，不展示 hour_record_apply（批次）的状态。如需补充“最新批次的申请人、申请时间”等信息，可通过 hour_record.id 关联 hour_record_apply，取 batch_no 最大的一条补充展示，但不影响主状态字段的定义和含义。 | 

## 六、补充说明

- `/api/hourRecordApprover/approvalFlow?applyId=xxx` 返回申请信息+审批人清单，仅为原有接口增加信息，不改变已有功能。
- 本次改造**不涉及老数据迁移**，也**没有历史审批流追溯需求**。
- 权限与安全暂不考虑。
- 审批人清单的生成规则支持动态调整（如移交），但本版本暂不实现。
- 测试用例本轮可暂时跳过。
- 本次所有接口和数据结构的改造，均为**兼容性增强**，不会破坏原有功能或接口。
- 例如：`/api/hourRecord/submit` 原有逻辑已生成审批人清单（hour_record_approver），本次仅在此基础上自动生成批次（hour_record_apply）并追加 apply_id 关联，前端和原有业务逻辑无需感知或调整。
- 其他接口如 approvalFlow、列表聚合等，均为在原有接口上追加返回信息或参数，**不影响现有功能**。 


## 移交功能
- 页面中【原审批人】去掉，保留【新审批人】并修改为下拉框方式
- 【新审批人】的接口数据来源于fs_user,同 UserSelector 控件,不同点时,【新审批人】要调用的接口需要排除当前用户
- 点击【确定移交】，弹出二次确认框
- 再次确认后，调用批量移交的接口,接口可添加 HourRecordApproverController 这个类中,传参是applyIds 和 新移交人, applyIds为逗号拼接的applyId，hour_record_apply中的主键，需要hour_record_approver中apply_id + approver_id=登录用户的记录 批量修改为 【新移交人】，同时将【老approver_id】转移到【transferred_from】,当然approver_name, transferred_time需要同步变化
- hour_record_approver中approver_id再添加时有去重处理,移交时也需要这样后，需保障移交后是不会存在重复的approver_id，具体请查阅 HourRecordServiceImpl.batchSubmit


1. 新审批人下拉数据源 --不能复用，需要参与UserSelector的接口，单独添加一个接口，后端过滤
2. applyIds的来源和范围 --
applyIds：是否只允许批量移交“当前登录用户为审批人”的批次？（即只能移交自己待审批的记录）
  --是的，列表中已有过滤,
applyIds格式：接口参数是字符串（如"1,2,3"）还是数组？（建议统一为字符串，文档和接口保持一致）
  --字符串
3. 二次确认弹窗
内容：弹窗内容是否有特殊要求？（如需显示移交的批次数量、新审批人姓名等）
操作：确认后是否需要loading状态/防止重复提交？
  --可以
4. 后端移交逻辑
去重规则：如果新审批人已是该批次审批人，是否直接跳过？还是报错？（建议跳过并提示“部分批次已存在该审批人，已自动忽略”）
  --报错进行提示,前端显示提示
字段同步：移交时approver_name、transferred_from、transferred_time等字段是否都要更新？（建议全部同步）
  --需要同步
操作人校验：后端是否校验“不能移交给自己”？
  --可以校验
5. 权限与安全
操作权限：是否只允许当前审批人移交？管理员是否有特殊权限？
  --暂不用考虑
接口幂等性：多次移交同一批次是否安全？
  --允许
6. 交互细节
移交后刷新：移交成功后，是否自动刷新列表？是否清空已选项？
  --自动刷新列表
失败提示：部分批次移交失败时，前端如何提示？
  --Toast.warning(response.message || '请求失败');