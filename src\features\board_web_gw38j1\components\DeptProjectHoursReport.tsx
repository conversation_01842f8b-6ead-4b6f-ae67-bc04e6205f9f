import React, { useState, useEffect } from 'react';
import {
  Table,
  Select,
  TreeSelect,
  Button,
  Space,
  Spin,
  Toast,
  Typography,
  Card,
  Row,
  Col
} from '@douyinfe/semi-ui';
import DateRangePicker from './DateRangePicker';
import { IconDownload, IconRefresh2 } from '@douyinfe/semi-icons';
import {
  getDeptProjectHourReport,
  exportDeptProjectHourReport,
  getAllDepartments,
  getAllProjects,
  type Department,
  type Project
} from '../../../api/report';
import type { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';

const { Text } = Typography;

interface ProjectColumn {
  projectId: string;
  projectName: string;
  projectCode: string;
  totalHours: number;
  sortOrder: number;
  columnKey: string;
}

interface DeptHourData {
  departmentId: string;
  departmentName: string;
  totalHours: number;
  projectHours: Record<string, number>;
  projectUserCounts: Record<string, number>;
}

interface ReportSummary {
  dateRange: string;
  totalHours: number;
  deptCount: number;
  projectCount: number;
  userCount: number;
}

interface ReportResult {
  projectColumns: ProjectColumn[];
  deptHourData: DeptHourData[];
  totalRow: DeptHourData;
  summary: ReportSummary;
}

/**
 * 部门/项目工时统计报表组件
 */
const DeptProjectHoursReport: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<ReportResult | null>(null);
  const [filters, setFilters] = useState({
    dateRange: [undefined, undefined] as [string | undefined, string | undefined],
    statPeriod: 'daily',
    departmentIds: [] as string[],  // 用户选择的原始部门ID
    projectIds: [] as string[],
    isHoliday: undefined as number | undefined  // 假日统计选项：1-统计假日，0-不统计假日，undefined-请选择
  });

  // 基础数据
  const [departments, setDepartments] = useState<Department[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [dataLoading, setDataLoading] = useState(false);

  // 表格列配置
  const [tableColumns, setTableColumns] = useState<ColumnProps[]>([]);

  useEffect(() => {
    // 设置默认日期为当前月
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    setFilters(prev => ({
      ...prev,
      dateRange: [
        startOfMonth.toISOString().split('T')[0],
        endOfMonth.toISOString().split('T')[0]
      ] as [string, string]
    }));

    // 加载基础数据
    loadInitialData();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 递归获取部门树下所有部门ID
  const getAllDepartmentIds = (departments: Department[]): string[] => {
    let allIds: string[] = [];
    departments.forEach(dept => {
      allIds.push(dept.id);
      if (dept.children && dept.children.length > 0) {
        allIds = [...allIds, ...getAllDepartmentIds(dept.children)];
      }
    });
    return allIds;
  };

  // 递归获取所有选中部门的完整ID列表（包括父级选中时的所有子级）
  const getAllSelectedDepartmentIds = (departments: Department[], selectedIds: string[]): string[] => {
    let allIds: string[] = [];
    
    departments.forEach(dept => {
      if (selectedIds.includes(dept.id)) {
        // 添加当前部门ID
        allIds.push(dept.id);
        
        // 如果有子部门，递归添加所有子部门ID
        if (dept.children && dept.children.length > 0) {
          const allChildIds = getAllDepartmentIds(dept.children);
          allIds = [...allIds, ...allChildIds];
        }
      } else if (dept.children && dept.children.length > 0) {
        // 如果父级未被选中，但可能有子级被选中
        allIds = [...allIds, ...getAllSelectedDepartmentIds(dept.children, selectedIds)];
      }
    });
    
    return [...new Set(allIds)]; // 去重
  };

  // 将部门数据转换为TreeSelect所需的格式
  const transformDepartmentData = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      label: dept.name,
      value: dept.id,
      key: dept.id,
      children: dept.children && dept.children.length > 0
        ? transformDepartmentData(dept.children)
        : undefined
    }));
  };

  // 加载基础数据
  const loadInitialData = async () => {
    setDataLoading(true);
    try {
      const [deptData, projectData] = await Promise.all([
        getAllDepartments(),
        getAllProjects()
      ]);
      console.log('部门数据:', deptData);
      console.log('项目数据:', projectData);
      setDepartments(deptData);
      setProjects(projectData);
    } catch (error) {
      console.error('加载基础数据失败:', error);
      Toast.error('加载基础数据失败');
    } finally {
      setDataLoading(false);
    }
  };

  // 构建动态表格列
  const buildTableColumns = (projectColumns: ProjectColumn[]): ColumnProps[] => {
    const baseColumns: ColumnProps[] = [
      {
        title: '部门',
        dataIndex: 'departmentName',
        key: 'departmentName',
        fixed: 'left',
        width: 150,
        render: (text: string, record: DeptHourData) => (
          <Text strong>{text}</Text>
        )
      }
    ];

    // 动态项目列
    const projectCols: ColumnProps[] = projectColumns.map(project => ({
      title: (
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontWeight: 'bold' }}>{project.projectName}</div>
          <div style={{ fontSize: '12px', color: '#888' }}>{project.projectCode}</div>
        </div>
      ),
      dataIndex: project.columnKey,
      key: project.columnKey,
      width: 120,
      align: 'center',
      render: (text: any, record: DeptHourData) => {
        const hours = record.projectHours[project.columnKey] || 0;
        const userCount = record.projectUserCounts[project.columnKey] || 0;

        return hours > 0 ? (
          <div style={{ fontWeight: 'bold' }}>{hours}</div>
        ) : (
          <Text type="tertiary">-</Text>
        );
      }
    }));

    const summaryColumn: ColumnProps[] = [
      {
        title: '小计',
        dataIndex: 'totalHours',
        key: 'totalHours',
        fixed: 'right',
        width: 100,
        align: 'center',
        render: (hours: number) => (
          <Text strong style={{ color: '#1890ff' }}>{hours}</Text>
        )
      }
    ];

    return [...baseColumns, ...projectCols, ...summaryColumn];
  };

  // 查询报表数据
  const fetchReportData = async () => {
    if (!filters.dateRange[0] || !filters.dateRange[1]) {
      Toast.warning('请选择查询日期范围');
      return;
    }

    // 防止重复请求
    if (loading) {
      return;
    }

    setLoading(true);
    try {
      // 将用户选择的部门ID展开为所有实际需要查询的部门ID
      const expandedDepartmentIds = filters.departmentIds.length > 0 
        ? getAllSelectedDepartmentIds(departments, filters.departmentIds)
        : [];

      const params = {
        startDate: filters.dateRange[0]!,
        endDate: filters.dateRange[1]!,
        statPeriod: filters.statPeriod,
        ...(filters.isHoliday !== undefined && { isHoliday: filters.isHoliday }),
        ...(expandedDepartmentIds.length > 0 && { departmentIds: expandedDepartmentIds.join(',') }),
        ...(filters.projectIds.length > 0 && { projectIds: filters.projectIds.join(',') })
      };

      const result = await getDeptProjectHourReport(params);
      setReportData(result);

      // 构建表格列
      const columns = buildTableColumns(result.projectColumns);
      setTableColumns(columns);

      Toast.success('报表数据加载成功');
    } catch (error) {
      console.error('获取报表数据失败:', error);
      Toast.error('获取报表数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出Excel
  const handleExport = async () => {
    if (!reportData) {
      Toast.warning('请先查询报表数据');
      return;
    }

    try {
      // 将用户选择的部门ID展开为所有实际需要查询的部门ID
      const expandedDepartmentIds = filters.departmentIds.length > 0 
        ? getAllSelectedDepartmentIds(departments, filters.departmentIds)
        : [];

      const params = {
        startDate: filters.dateRange[0]!,
        endDate: filters.dateRange[1]!,
        statPeriod: filters.statPeriod,
        ...(filters.isHoliday !== undefined && { isHoliday: filters.isHoliday }),
        ...(expandedDepartmentIds.length > 0 && { departmentIds: expandedDepartmentIds.join(',') }),
        ...(filters.projectIds.length > 0 && { projectIds: filters.projectIds.join(',') })
      };

      await exportDeptProjectHourReport(params);
      Toast.success('报表导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      Toast.error(`导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 准备表格数据
  const tableData = reportData ? [
    ...reportData.deptHourData,
    // 添加总计行
    {
      ...reportData.totalRow,
      departmentId: 'total',
      departmentName: '总计'
    }
  ] : [];

  return (
    <div style={{ padding: '24px' }}>
      {/* 筛选条件 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[12, 8]}>
          <Col span={7}>
            <Space vertical style={{ width: '100%' }}>
              <DateRangePicker
                value={filters.dateRange}
                onChange={(dateRange) => setFilters(prev => ({ ...prev, dateRange }))}
                dimension="day"
                placeholder="请选择日期范围"
                style={{ width: '100%' }}
              />
            </Space>
          </Col>
          <Col span={4}>
            <Space vertical style={{ width: '100%' }}>
              <TreeSelect
                multiple
                value={filters.departmentIds}
                onChange={(value) => {
                  // 保存用户选择的原始部门ID
                  setFilters(prev => ({ ...prev, departmentIds: value }));
                }}
                style={{ width: '100%' }}
                placeholder="选择部门"
                loading={dataLoading}
                maxTagCount={1}
                showClear
                treeData={transformDepartmentData(departments)}
                searchable
                showSearchClear
                expandAll
                checkRelation="related"
              />
            </Space>
          </Col>
          <Col span={4}>
            <Space vertical style={{ width: '100%' }}>
              <Select
                multiple
                value={filters.projectIds}
                onChange={(value) => setFilters(prev => ({ ...prev, projectIds: value }))}
                style={{ width: '100%' }}
                placeholder="选择项目"
                loading={dataLoading}
                maxTagCount={1}
                showClear
              >
                {projects.map(project => (
                  <Select.Option key={project.id} value={project.id}>
                    {project.projectName}
                  </Select.Option>
                ))}
              </Select>
            </Space>
          </Col>
          <Col span={3}>
            <Space vertical style={{ width: '100%' }}>
              <Select
                value={filters.isHoliday}
                onChange={(value) => setFilters(prev => ({ ...prev, isHoliday: value }))}
                style={{ width: '100%' }}
                placeholder="请选择"
                showClear
              >
                <Select.Option value={1}>统计假日</Select.Option>
                <Select.Option value={0}>不统计假日</Select.Option>
              </Select>
            </Space>
          </Col>
          <Col span={6}>
            <Space>
              <Button
                theme="solid"
                type="primary"
                onClick={fetchReportData}
                loading={loading}
              >
                查询
              </Button>
              <Button
                type="secondary"
                onClick={handleExport}
                disabled={!reportData || loading}
                icon={<IconDownload />}
              >
                导出Excel
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 统计摘要 */}
      {/*{reportData?.summary && (*/}
      {/*  <Card style={{ marginBottom: '16px' }}>*/}
      {/*    <Row gutter={16}>*/}
      {/*      <Col span={6}>*/}
      {/*        <Text type="tertiary">统计日期范围</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.dateRange}</Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={4}>*/}
      {/*        <Text type="tertiary">总工时</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong style={{ color: '#1890ff', fontSize: '18px' }}>*/}
      {/*          {reportData.summary.totalHours}小时*/}
      {/*        </Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={4}>*/}
      {/*        <Text type="tertiary">部门数量</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.deptCount}个</Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={4}>*/}
      {/*        <Text type="tertiary">项目数量</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.projectCount}个</Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={4}>*/}
      {/*        <Text type="tertiary">参与人数</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.userCount}人</Text>*/}
      {/*      </Col>*/}
      {/*    </Row>*/}
      {/*  </Card>*/}
      {/*)}*/}

      {/* 报表表格 */}
      <Card>
        <Spin spinning={loading}>
          <Table
            columns={tableColumns}
            dataSource={tableData}
            pagination={false}
            bordered
            size="small"
            scroll={{ x: 'max-content' }}
            rowKey={(record) => record.departmentId}
            rowClassName={(record, index) => {
              // 总计行高亮
              if (record.departmentId === 'total') {
                return 'total-row';
              }
              return '';
            }}
          />
        </Spin>
      </Card>

      <style>
        {`
          .total-row {
            background-color: #f6f8fa !important;
            font-weight: bold;
          }
          .total-row td {
            border-top: 2px solid #d9d9d9 !important;
          }
        `}
      </style>
    </div>
  );
};

export default DeptProjectHoursReport;
