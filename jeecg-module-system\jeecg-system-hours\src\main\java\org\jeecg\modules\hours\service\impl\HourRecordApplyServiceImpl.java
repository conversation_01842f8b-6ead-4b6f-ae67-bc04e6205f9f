package org.jeecg.modules.hours.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.jeecg.modules.hours.entity.HourRecordApply;
import org.jeecg.modules.hours.mapper.HourRecordApplyMapper;
import org.jeecg.modules.hours.service.IHourRecordApplyService;
import org.jeecg.modules.hours.vo.HourRecordApplyVO;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class HourRecordApplyServiceImpl extends ServiceImpl<HourRecordApplyMapper, HourRecordApply>
		implements IHourRecordApplyService {

	@Override
	public String generateBatchNo() {
		// 获取当前日期，格式为 yyMMdd
		String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));

		// 查询当天已生成的批次号
		String todayPrefix = "SP" + dateStr;
		LambdaQueryWrapper<HourRecordApply> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.likeRight(HourRecordApply::getBatchNo, todayPrefix).orderByDesc(HourRecordApply::getBatchNo)
				.last("LIMIT 1");

		HourRecordApply maxRecord = this.getOne(queryWrapper);

		// 找出当天最大序号
		int maxSequence = 0;
		if (maxRecord != null && maxRecord.getBatchNo() != null) {
			String batchNo = maxRecord.getBatchNo();
			if (batchNo.startsWith(todayPrefix) && batchNo.length() >= 14) {
				try {
					String sequenceStr = batchNo.substring(8); // 取后6位序号
					maxSequence = Integer.parseInt(sequenceStr);
				} catch (NumberFormatException e) {
					log.warn("解析批次号序号失败: {}", batchNo);
				}
			}
		}

		// 下一个序号 = 最大序号 + 1
		int nextSequence = maxSequence + 1;

		// 格式化为6位数字，不足补0
		String sequenceStr = String.format("%06d", nextSequence);

		// 拼接批次号：SP + 日期 + 6位序号
		String batchNo = todayPrefix + sequenceStr;

		log.info("生成批次号: {}, 当天最大序号: {}, 下一个序号: {}", batchNo, maxSequence, nextSequence);
		return batchNo;
	}

	/**
	 * 生成数字类型的批次号（用于向后兼容） 获取指定记录的最大批次号并加1
	 * 
	 * @param recordId 记录ID
	 * @return 下一个批次号
	 */
	public Integer generateNumericBatchNo(String recordId) {
		Integer maxBatchNo = this.lambdaQuery().eq(HourRecordApply::getRecordId, recordId).list().stream()
				.map(apply -> {
					try {
						return Integer.parseInt(apply.getBatchNo());
					} catch (NumberFormatException e) {
						return 0;
					}
				}).max(Integer::compareTo).orElse(0);

		return maxBatchNo + 1;
	}

	@Override
	public IPage<HourRecordApplyVO> queryApprovalRecords(Page<HourRecordApplyVO> page, String startDate, String endDate,
			List<String> employeeIdList, String tab, String currentUserId,String status) {
		return this.baseMapper.queryApprovalRecords(page, startDate, endDate, employeeIdList, tab, currentUserId,status);
	}

	@Override
	public IPage<HourRecordApplyVO> queryHandoverApproverRecords(Page<HourRecordApplyVO> page, String startDate,
			String endDate, List<String> employeeIdList, String tab, String status, String projectId) {
		return this.baseMapper.queryHandoverApproverRecords(page, startDate, endDate, employeeIdList, tab, status, projectId);
	}
	
}