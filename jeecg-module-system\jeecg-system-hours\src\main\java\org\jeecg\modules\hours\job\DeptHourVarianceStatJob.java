package org.jeecg.modules.hours.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.hours.service.IDeptHourVarianceStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

@Slf4j
@Component
public class DeptHourVarianceStatJob {

    @Autowired
    private IDeptHourVarianceStatService deptHourVarianceStatService;

    /**
     * 每月1号凌晨4点执行部门工时差值统计
     * 计算上个月的部门工时差值数据
     */
//    @Scheduled(cron = "0 0 4 1 * ?")
    @Scheduled(cron = "0 */10 * * * ?")
    public void executeMonthlyVarianceStat() {
        log.info("开始执行部门工时差值月度统计任务");
        
        try {
            // 计算上个月的数据
            YearMonth lastMonth = YearMonth.now().minusMonths(1);
            int year = lastMonth.getYear();
            int month = lastMonth.getMonthValue();
            int monthValue = YearMonth.now().getMonthValue();
            log.info("计算{}年{}月的部门工时差值统计", year, month);
            
            // 执行统计计算（包含假日和不包含假日两种情况）
            int processedCount = deptHourVarianceStatService.calculateMonthlyVarianceStat(year, month);

            int processedCount1 = deptHourVarianceStatService.calculateMonthlyVarianceStat(year, monthValue);
            log.info("部门工时差值月度统计任务完成，处理了{}条记录", processedCount1);
            
            log.info("部门工时差值月度统计任务完成，处理了{}条记录", processedCount);
            
        } catch (Exception e) {
            log.error("部门工时差值月度统计任务执行失败", e);
        }
    }

//    public static void main(String[] args) {
//        System.out.println(YearMonth.now().getMonthValue());
//    }
    /**
     * 手动触发统计任务（用于补数据）
     */
    public void executeManualVarianceStat(int year, int month) {
        log.info("手动执行部门工时差值统计任务: {}年{}月", year, month);
        
        try {
            int processedCount = deptHourVarianceStatService.calculateMonthlyVarianceStat(year, month);
            log.info("手动统计任务完成，处理了{}条记录", processedCount);
        } catch (Exception e) {
            log.error("手动部门工时差值统计任务执行失败", e);
            throw new RuntimeException("统计任务执行失败: " + e.getMessage());
        }
    }
}