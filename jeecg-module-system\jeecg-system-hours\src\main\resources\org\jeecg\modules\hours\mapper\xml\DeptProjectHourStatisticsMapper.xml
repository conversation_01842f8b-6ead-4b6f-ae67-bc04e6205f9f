<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.DeptProjectHourStatisticsMapper">

    <!-- 获取项目列信息（用于动态列） -->
    <select id="getProjectColumns" resultType="org.jeecg.modules.hours.vo.ProjectColumnVO">
        SELECT 
            s.project_id,
            s.project_name,
            s.project_code,
            SUM(s.total_hours) as total_hours
        FROM dept_project_hour_statistics s
        WHERE s.stat_date BETWEEN #{startDate} AND #{endDate}
          AND s.stat_period = #{statPeriod}
        <if test="departmentIds != null and departmentIds.size() > 0">
            AND s.department_id IN
            <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            AND s.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="isHoliday != null">
            AND s.is_holiday = #{isHoliday}
        </if>
        GROUP BY s.project_id, s.project_name, s.project_code
        ORDER BY total_hours DESC
    </select>

    <!-- 获取部门项目工时统计报表数据 -->
    <select id="getDeptProjectHourReport" resultType="org.jeecg.modules.hours.vo.DeptProjectHourReportVO">
        SELECT 
            s.department_id,
            s.department_name,
            SUM(s.total_hours) as total_hours,
            CONCAT('project_', s.project_id) as project_key,
            s.project_id,
            s.total_hours as single_project_hours,
            s.user_count as project_user_count
        FROM dept_project_hour_statistics s
        WHERE s.stat_date BETWEEN #{startDate} AND #{endDate}
          AND s.stat_period = #{statPeriod}
        <if test="departmentIds != null and departmentIds.size() > 0">
            AND s.department_id IN
            <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            AND s.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="isHoliday != null">
            AND s.is_holiday = #{isHoliday}
        </if>
        GROUP BY s.department_id, s.department_name, s.project_id, s.total_hours, s.user_count
        ORDER BY s.department_name, s.project_id
    </select>

    <!-- 获取总计行数据 -->
    <select id="getTotalRow" resultType="org.jeecg.modules.hours.vo.DeptProjectHourReportVO">
        SELECT 
            'total' as department_id,
            '总计' as department_name,
            SUM(s.total_hours) as total_hours,
            CONCAT('project_', s.project_id) as project_key,
            s.project_id,
            SUM(s.total_hours) as single_project_hours,
            SUM(s.user_count) as project_user_count
        FROM dept_project_hour_statistics s
        WHERE s.stat_date BETWEEN #{startDate} AND #{endDate}
          AND s.stat_period = #{statPeriod}
        <if test="departmentIds != null and departmentIds.size() > 0">
            AND s.department_id IN
            <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            AND s.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="isHoliday != null">
            AND s.is_holiday = #{isHoliday}
        </if>
        GROUP BY s.project_id
    </select>

    <!-- 批量插入或更新统计数据 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO dept_project_hour_statistics 
        (stat_date, stat_period, department_id, department_name, project_id, project_name, project_code, 
         total_hours, user_count, record_count, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.statDate}, #{item.statPeriod}, #{item.departmentId}, #{item.departmentName}, 
             #{item.projectId}, #{item.projectName}, #{item.projectCode}, #{item.totalHours}, 
             #{item.userCount}, #{item.recordCount}, NOW(), NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE
        total_hours = VALUES(total_hours),
        user_count = VALUES(user_count),
        record_count = VALUES(record_count),
        update_time = NOW()
    </insert>

    <!-- 删除指定日期的统计数据 -->
    <delete id="deleteByStatDate">
        DELETE FROM dept_project_hour_statistics 
        WHERE stat_date = DATE(#{statDate}) AND stat_period = #{statPeriod}
    </delete>

    <!-- 获取汇总统计数据 -->
    <select id="getSummaryData" resultType="org.jeecg.modules.hours.vo.DeptProjectHourReportResult$ReportSummaryVO">
        SELECT 
            CONCAT(DATE_FORMAT(#{startDate}, '%Y-%m-%d'), ' 至 ', DATE_FORMAT(#{endDate}, '%Y-%m-%d')) as date_range,
            SUM(s.total_hours) as total_hours,
            COUNT(DISTINCT s.department_id) as dept_count,
            COUNT(DISTINCT s.project_id) as project_count,
            SUM(s.user_count) as user_count
        FROM dept_project_hour_statistics s
        WHERE s.stat_date BETWEEN #{startDate} AND #{endDate}
          AND s.stat_period = #{statPeriod}
        <if test="departmentIds != null and departmentIds.size() > 0">
            AND s.department_id IN
            <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            AND s.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="isHoliday != null">
            AND s.is_holiday = #{isHoliday}
        </if>
    </select>

</mapper>