package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 报表查询参数
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "ReportQueryParams", description = "报表查询参数")
public class ReportQueryParams {

    @ApiModelProperty(value = "时间维度", required = true, example = "month")
    private String dimension;

    @ApiModelProperty(value = "开始日期", required = true, example = "2025-01-01")
    private String startDate;

    @ApiModelProperty(value = "结束日期", required = true, example = "2025-12-31")
    private String endDate;

    @ApiModelProperty(value = "项目标识", example = "PROJECT_001")
    private String projectKey;

    @ApiModelProperty(value = "实例ID列表")
    private List<String> instanceIds;

    @ApiModelProperty(value = "用户ID列表")
    private List<String> userIds;

    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer size = 20;
}
