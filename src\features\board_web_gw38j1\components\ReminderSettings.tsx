import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Switch, 
  Typography, 
  Select, 
  TimePicker, 
  Modal, 
  Form, 
  TextArea, 
  Toast, 
  Popconfirm,
  Space,
  Tag,
  Spin
} from '@douyinfe/semi-ui';
import { IconEdit, IconDelete, IconPlus } from '@douyinfe/semi-icons';
import { 
  getReminderConfigList,
  addReminderConfig,
  updateReminderConfig,
  deleteReminderConfig,
  batchDeleteReminderConfig,
  toggleReminderConfigStatus,
  getReminderTypeOptions,
  getRepeatModeOptions,
  getReminderDayOptions,
  validateReminderConfig,
  formatReminderConfigText
} from '../../../services/api/reminderConfig';
import { ReminderConfig, ReminderConfigFormData } from '../../../types/api';

const { Title } = Typography;

interface ReminderSettingsProps {
  visible?: boolean;
  onAddClick?: () => void;
}

const ReminderSettings = forwardRef<any, ReminderSettingsProps>(({ visible = true, onAddClick }, ref) => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [reminderList, setReminderList] = useState<ReminderConfig[]>([]);
  
  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<ReminderConfig | null>(null);
  
  // 表单状态
  const [formData, setFormData] = useState<ReminderConfigFormData>({
    reminderType: 'daily',
    repeatMode: 'current_day',
    reminderDay: '',
    reminderTime: '09:00',
    remark: '',
    status: '启用'
  });

  // 加载提醒配置列表
  const loadReminderList = useCallback(async () => {
    try {
      setLoading(true);
      // 传递较大的pageSize以获取所有数据，实现无分页效果
      const response = await getReminderConfigList({
        pageSize: 1000
      });

      if (response && response.success) {
        setReminderList(response.result?.records || []);
      } else {
        Toast.error(response?.message || '获取提醒配置列表失败');
      }
    } catch (error) {
      console.error('加载提醒配置列表失败:', error);
      Toast.error('加载提醒配置列表失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 组件挂载时加载数据
  useEffect(() => {
    if (visible) {
      loadReminderList();
    }
  }, [visible, loadReminderList]);

  // 打开新增弹窗
  const handleAdd = useCallback(() => {
    setEditingRecord(null);
    setFormData({
      reminderType: 'daily',
      repeatMode: 'current_day',
      reminderDay: '',
      reminderTime: '09:00',
      remark: '',
      status: '启用'
    });
    setModalVisible(true);
  }, []);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    handleAdd
  }), [handleAdd]);

  // 打开编辑弹窗
  const handleEdit = (record: ReminderConfig) => {
    setEditingRecord(record);
    setFormData({
      reminderType: record.reminderType,
      repeatMode: record.repeatMode,
      reminderDay: record.reminderDay || '',
      reminderTime: record.reminderTime,
      remark: record.remark || '',
      status: record.status
    });
    setModalVisible(true);
  };

  // 删除单条记录
  const handleDelete = async (id: number) => {
    try {
      const response = await deleteReminderConfig(id);
      if (response && response.success) {
        Toast.success('删除成功');
        loadReminderList();
      } else {
        Toast.error(response?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      Toast.error('删除失败');
    }
  };

  // 切换状态
  const handleToggleStatus = async (id: number, status: '启用' | '暂停') => {
    try {
      const response = await toggleReminderConfigStatus(id, status);
      if (response && response.success) {
        Toast.success('状态更新成功');
        loadReminderList();
      } else {
        Toast.error(response?.message || '状态更新失败');
      }
    } catch (error) {
      console.error('状态更新失败:', error);
      Toast.error('状态更新失败');
    }
  };

  // 保存表单
  const handleSave = async () => {
    // 表单验证
    const errors = validateReminderConfig(formData);
    if (errors.length > 0) {
      Toast.error(errors[0]);
      return;
    }

    try {
      let response;
      if (editingRecord) {
        // 编辑模式
        response = await updateReminderConfig({
          ...editingRecord,
          ...formData
        });
      } else {
        // 新增模式
        response = await addReminderConfig(formData);
      }

      if (response && response.success) {
        Toast.success(editingRecord ? '更新成功' : '添加成功');
        setModalVisible(false);
        loadReminderList();
      } else {
        Toast.error(response?.message || (editingRecord ? '更新失败' : '添加失败'));
      }
    } catch (error) {
      console.error('保存失败:', error);
      Toast.error('保存失败');
    }
  };

  // 表单字段变化处理
  const handleFormChange = (field: keyof ReminderConfigFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      
      // 当提醒类型变化时，重置相关字段
      if (field === 'reminderType') {
        const repeatModeOptions = getRepeatModeOptions(value);
        newData.repeatMode = repeatModeOptions[0]?.value || '';
        newData.reminderDay = '';
      }
      
      // 当重复方式变化时，如果是每天提醒，清空提醒日期
      if (field === 'repeatMode' && newData.reminderType === 'daily') {
        newData.reminderDay = '';
      }
      
      return newData;
    });
  };



  if (!visible) {
    return null;
  }

  return (
    <div className="reminder-settings">
      <Spin spinning={loading}>
        {/* 表格区域 */}
        <div className="settings-table-wrapper" style={{ overflowX: 'auto' }}>
          <Table
            dataSource={reminderList}
            columns={[
              {
                title: '序号',
                key: 'index',
                width: 80,
                render: (_, __, index) => index + 1
              },
              {
                title: '提醒方式',
                dataIndex: 'reminderType',
                key: 'reminderType',
                width: 90,
                render: (value) => {
                  const typeMap = {
                    daily: '每天',
                    weekly: '每周',
                    monthly: '每月'
                  };
                  return typeMap[value] || value;
                }
              },
              {
                title: '提醒类型',
                dataIndex: 'repeatMode',
                key: 'repeatMode',
                width: 90,
                render: (value) => {
                  const modeMap = {
                    current_day: '当天',
                    next_day: '次日',
                    current_week: '本周',
                    next_week: '次周',
                    current_month: '本月',
                    next_month: '次月'
                  };
                  return modeMap[value] || value;
                }
              },
              {
                title: '提醒日期',
                dataIndex: 'reminderDay',
                key: 'reminderDay',
                width: 100,
                render: (value, record) => {
                  if (!value) return '-';

                  if (record.reminderType === 'weekly') {
                    const dayMap = {
                      monday: '周一',
                      tuesday: '周二',
                      wednesday: '周三',
                      thursday: '周四',
                      friday: '周五',
                      saturday: '周六',
                      sunday: '周日'
                    };
                    return dayMap[value] || value;
                  } else if (record.reminderType === 'monthly') {
                    return value === 'last_day' ? '月末' : `${value}号`;
                  }

                  return '-';
                }
              },
              {
                title: '提醒时间',
                dataIndex: 'reminderTime',
                key: 'reminderTime',
                width: 90
              },
              {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
                width: 100,
                render: (value) => {
                  if (value === '启用') {
                    return <span style={{ color: '#52c41a' }}>启用</span>;
                  } else if (value === '暂停') {
                    return <span style={{ color: '#ff4d4f' }}>禁用</span>;
                  } else {
                    return <span style={{ color: '#999' }}>{value}</span>;
                  }
                }
              },
              {
                title: '备注',
                dataIndex: 'remark',
                key: 'remark',
                width: 150,
                ellipsis: {
                  showTitle: false
                },
                render: (value) => (
                  <Typography.Text
                    ellipsis={{
                      showTooltip: true,
                      pos: 'middle'
                    }}
                    style={{ width: '130px' }}
                  >
                    {value || '-'}
                  </Typography.Text>
                )
              },
              {
                title: '添加时间',
                dataIndex: 'createTime',
                key: 'createTime',
                width: 150,
                render: (value) => value ? new Date(value).toLocaleString() : '-'
              },
              {
                title: '添加人',
                dataIndex: 'createBy_dictText',
                key: 'createBy',
                width: 100,
                render: (value) => value || '-'
              },
              {
                title: '操作',
                key: 'action',
                width: 140,
                fixed: 'right',
                align: 'center',
                render: (record) => (
                  <Space spacing={8}>
                    <Button
                      type="primary"
                      theme="light"
                      size="small"
                      icon={<IconEdit />}
                      onClick={() => handleEdit(record)}
                    >
                      编辑
                    </Button>
                    <Popconfirm
                      title="确定要删除这条记录吗？"
                      onConfirm={() => handleDelete(record.id!)}
                    >
                      <Button
                        type="danger"
                        theme="light"
                        size="small"
                        icon={<IconDelete />}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  </Space>
                )
              }
            ]}
            rowKey="id"
            scroll={{ x: 1200 }}
            pagination={false}
          />
        </div>
      </Spin>

      {/* 新增/编辑弹窗 */}
      <Modal
        title={editingRecord ? '编辑提醒配置' : '新增提醒配置'}
        visible={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={600}
        okText="保存"
        cancelText="取消"
      >
        <div style={{ padding: '16px 0' }}>
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              提醒类型 <span style={{ color: 'red' }}>*</span>
            </label>
            <Select
              value={formData.reminderType}
              onChange={(value) => handleFormChange('reminderType', value)}
              optionList={getReminderTypeOptions()}
              placeholder="请选择提醒类型"
              style={{ width: '100%' }}
            />
          </div>

          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              重复方式 <span style={{ color: 'red' }}>*</span>
            </label>
            <Select
              value={formData.repeatMode}
              onChange={(value) => handleFormChange('repeatMode', value)}
              optionList={getRepeatModeOptions(formData.reminderType)}
              placeholder="请选择重复方式"
              style={{ width: '100%' }}
            />
          </div>

          {(formData.reminderType === 'weekly' || formData.reminderType === 'monthly') && (
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                提醒日期 <span style={{ color: 'red' }}>*</span>
              </label>
              <Select
                value={formData.reminderDay}
                onChange={(value) => handleFormChange('reminderDay', value)}
                optionList={getReminderDayOptions(formData.reminderType)}
                placeholder="请选择提醒日期"
                style={{ width: '100%' }}
              />
            </div>
          )}

          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              提醒时间 <span style={{ color: 'red' }}>*</span>
            </label>
            <TimePicker
              value={formData.reminderTime}
              onChange={(time, timeString) => {
                // 使用 timeString 参数，它已经是格式化后的字符串
                handleFormChange('reminderTime', timeString as string);
              }}
              format="HH:mm"
              placeholder="请选择提醒时间"
              style={{ width: '100%' }}
            />
          </div>

          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              备注
            </label>
            <TextArea
              value={formData.remark}
              onChange={(value) => handleFormChange('remark', value)}
              placeholder="请输入备注信息"
              rows={3}
              maxLength={500}
              showClear
            />
          </div>

          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              状态
            </label>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Typography.Title heading={6} style={{ margin: '0 8px 0 0' }}>
                {formData.status === '启用' ? '已启用' : '已禁用'}
              </Typography.Title>
              <Switch
                checked={formData.status === '启用'}
                onChange={(checked) => handleFormChange('status', checked ? '启用' : '暂停')}
                aria-label="状态开关"
              />
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
});

export default ReminderSettings;
