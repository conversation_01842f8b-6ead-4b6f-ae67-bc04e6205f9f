package org.jeecg.modules.hours.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.feishu.service.FeiShuMessageService;
import org.jeecg.modules.hours.constant.ConfigKeyConstant;
import org.jeecg.modules.hours.entity.FsSysReminderConfig;
import org.jeecg.modules.hours.mapper.WorkHourReminderMapper;
import org.jeecg.modules.hours.service.ISysConfigService;
import org.jeecg.modules.hours.service.IWorkHourReminderService;
import org.jeecg.modules.hours.util.BusinessDateCalculator;
import org.jeecg.modules.hours.vo.UnsubmittedWorkHourVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 工时登记提醒服务实现类
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Slf4j
@Service
public class WorkHourReminderServiceImpl implements IWorkHourReminderService {

    @Autowired
    private WorkHourReminderMapper workHourReminderMapper;
    
    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public void executeReminder(FsSysReminderConfig reminderConfig) {
        log.info("开始执行提醒逻辑，配置ID: {}, 项目: {}", 
                reminderConfig.getId(), reminderConfig.getProjectKey());
        
        try {
            // 1. 计算业务日期范围
            List<LocalDate> businessDates = BusinessDateCalculator.calculateBusinessDates(reminderConfig);
            if (CollectionUtils.isEmpty(businessDates)) {
                log.info("没有需要检查的业务日期，跳过提醒");
                return;
            }
            
            // 2. 查询未提交工时的用户和日期
            List<UnsubmittedWorkHourVO> unsubmittedList = findUnsubmittedWorkHours(
                    reminderConfig.getProjectKey(), businessDates);
            
            if (CollectionUtils.isEmpty(unsubmittedList)) {
                log.info("没有发现未提交工时的用户，无需发送提醒");
                return;
            }
            
            log.info("发现 {} 个用户有未提交的工时记录", unsubmittedList.size());
            
            // 3. 发送提醒消息
            sendReminderMessages(reminderConfig, unsubmittedList);
            
            log.info("提醒逻辑执行完成");
            
        } catch (Exception e) {
            log.error("执行提醒逻辑时发生异常", e);
            throw new RuntimeException("提醒执行失败", e);
        }
    }

    @Override
    public List<UnsubmittedWorkHourVO> findUnsubmittedWorkHours(String projectKey, List<LocalDate> businessDates) {
        log.info("查询未提交工时，项目: {}, 日期范围: {}", projectKey, businessDates);
        
        try {
            // 检查考勤配置开关
            String attendanceValidateSwitch = sysConfigService.getConfigValue(
                    projectKey, ConfigKeyConstant.FEISHU_ATTENDANCE_VALIDATE_SWITCH, "0");
            
            log.info("考勤校验开关配置: {}", attendanceValidateSwitch);
            
            List<UnsubmittedWorkHourVO> result;
            
            if ("1".equals(attendanceValidateSwitch)) {
                // 开启飞书考勤校验：查询有考勤无工时登记的用户
                result = workHourReminderMapper.findUnsubmittedWithAttendance(projectKey, businessDates);
                log.info("使用飞书考勤校验模式，查询到 {} 条记录", result.size());
            } else {
                // 未开启飞书考勤校验：根据每日工时上限进行验证
                String maxWorkHourPerDay = sysConfigService.getConfigValue(
                        projectKey, ConfigKeyConstant.MAX_WORK_HOUR_PER_DAY, "8");
                
                result = workHourReminderMapper.findUnsubmittedByWorkHourLimit(
                        projectKey, businessDates, Double.parseDouble(maxWorkHourPerDay));
                log.info("使用工时上限校验模式（上限: {}小时），查询到 {} 条记录", maxWorkHourPerDay, result.size());
            }
            
            log.info("查询到的未提交工时用户数: {}", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("查询未提交工时时发生异常", e);
            throw new RuntimeException("查询未提交工时失败", e);
        }
    }

    @Override
    public void sendReminderMessages(FsSysReminderConfig reminderConfig, List<UnsubmittedWorkHourVO> unsubmittedList) {
        log.info("开始发送提醒消息，用户数: {}", unsubmittedList.size());
        
        try {
            // 获取飞书配置
            String appId = sysConfigService.getConfigValue(
                    reminderConfig.getProjectKey(), ConfigKeyConstant.BIND_APP_ID);
            String appSecret = sysConfigService.getConfigValue(
                    reminderConfig.getProjectKey(), ConfigKeyConstant.BIND_APP_SECRET);
            
            if (!StringUtils.hasText(appId) || !StringUtils.hasText(appSecret)) {
                log.error("飞书应用配置不完整，appId: {}, appSecret: {}", appId, 
                        StringUtils.hasText(appSecret) ? "已配置" : "未配置");
                return;
            }
            
            log.info("使用飞书应用配置 - appId: {}", appId);
            
            // 生成消息模板
            String title = generateMessageTitle(reminderConfig.getReminderType());
            String buttonText = generateButtonText(reminderConfig.getReminderType());
            String buttonUrl = getButtonUrl();
            
            // 为每个用户发送个性化消息
            int successCount = 0;
            int failCount = 0;
            
            for (UnsubmittedWorkHourVO unsubmitted : unsubmittedList) {
                try {
                    if (!StringUtils.hasText(unsubmitted.getUnionId())) {
                        log.warn("用户 {} 的unionId为空，跳过发送消息", unsubmitted.getUserId());
                        failCount++;
                        continue;
                    }
                    
                    // 生成个性化消息内容
                    String content = generateMessageContent(reminderConfig, unsubmitted.getUnsubmittedDates());
                    
                    log.info("发送提醒消息给用户: {} ({}), 内容: {}", 
                            unsubmitted.getUserName(), unsubmitted.getUnionId(), content);
                    
                    // 发送消息
                    FeiShuMessageService.instance(appId, appSecret)
                            .sendMessage(unsubmitted.getUnionId(), title, content, buttonText, buttonUrl);
                    
                    successCount++;
                    log.info("成功发送提醒消息给用户: {}", unsubmitted.getUserName());
                    
                } catch (Exception e) {
                    failCount++;
                    log.error("发送提醒消息失败，用户: {}", unsubmitted.getUserName(), e);
                }
            }
            
            log.info("提醒消息发送完成，成功: {}, 失败: {}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("发送提醒消息时发生异常", e);
            throw new RuntimeException("发送提醒消息失败", e);
        }
    }

    @Override
    public String generateMessageTitle(String reminderType) {
        switch (reminderType) {
            case "daily":
                return "日工时登记提醒";
            case "weekly":
                return "周工时登记提醒";
            case "monthly":
                return "月工时登记提醒";
            default:
                return "工时登记提醒";
        }
    }

    @Override
    public String generateMessageContent(FsSysReminderConfig reminderConfig, List<LocalDate> unsubmittedDates) {
        String reminderType = reminderConfig.getReminderType();
        String repeatMode = reminderConfig.getRepeatMode();
        
        StringBuilder content = new StringBuilder();
        
        switch (reminderType) {
            case "daily":
                if ("current_day".equals(repeatMode)) {
                    content.append("您今天有工时未登记");
                } else if ("next_day".equals(repeatMode)) {
                    content.append("您昨日有工时未登记");
                }
                break;
            case "weekly":
                if ("current_week".equals(repeatMode)) {
                    content.append("您本周有工时未登记");
                } else if ("next_week".equals(repeatMode)) {
                    content.append("您上周有工时未登记");
                }
                // 添加具体日期
                if (!CollectionUtils.isEmpty(unsubmittedDates)) {
                    content.append("<br>未提交日期：");
                    content.append(BusinessDateCalculator.formatDatesForMessage(unsubmittedDates));
                }
                break;
            case "monthly":
                if ("current_month".equals(repeatMode)) {
                    content.append("您本月有工时未登记");
                } else if ("next_month".equals(repeatMode)) {
                    content.append("您上月有工时未登记");
                }
                // 添加具体日期
                if (!CollectionUtils.isEmpty(unsubmittedDates)) {
                    content.append("<br>未提交日期：");
                    content.append(BusinessDateCalculator.formatDatesForMessage(unsubmittedDates));
                }
                break;
        }
        
        return content.toString();
    }

    @Override
    public String generateButtonText(String reminderType) {
        switch (reminderType) {
            case "daily":
                return "立即填写";
            case "weekly":
            case "monthly":
                return "查看详情";
            default:
                return "查看详情";
        }
    }

    @Override
    public String getButtonUrl() {
        return "https://project.feishu.cn/xqr1jq/meegoPlg/MII_6846998B99B18004_board_p35kx7";
    }
    

}
