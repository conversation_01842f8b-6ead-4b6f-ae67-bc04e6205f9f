package org.jeecg.modules.hours.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.HourRecord;
import org.jeecg.modules.hours.vo.InstanceVO;
import org.jeecg.modules.hours.vo.ReportQueryParams;
import org.jeecg.modules.hours.vo.UserVO;

import java.util.List;
import java.util.Map;

/**
 * 报表分析Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface ReportAnalysisMapper extends BaseMapper<HourRecord> {

    /**
     * 查询实例工时数据
     */
    List<Map<String, Object>> queryInstanceHoursData(@Param("params") ReportQueryParams params);

    /**
     * 查询实例工时数据（分页）
     */
    IPage<Map<String, Object>> queryInstanceHoursDataPage(IPage<Map<String, Object>> page, @Param("params") ReportQueryParams params);

    /**
     * 查询实例列表（分页）
     */
    IPage<String> queryInstanceListPage(IPage<String> page, @Param("params") ReportQueryParams params);

    /**
     * 查询指定实例的工时数据
     */
    List<Map<String, Object>> queryInstanceHoursDataByInstances(@Param("params") ReportQueryParams params, @Param("instanceNames") List<String> instanceNames);

    /**
     * 查询员工工时数据（分页）
     */
    IPage<Map<String, Object>> queryEmployeeHoursData(IPage<Map<String, Object>> page, @Param("params") ReportQueryParams params);

    /**
     * 查询员工工时实例分布数据（分页）
     */
    IPage<Map<String, Object>> queryEmployeeInstanceDistributionData(IPage<Map<String, Object>> page, @Param("params") ReportQueryParams params);

    /**
     * 查询实例名称列表
     */
    List<String> queryInstanceNames(@Param("params") ReportQueryParams params);

    /**
     * 查询项目下的用户列表
     */
    List<UserVO> queryProjectUsers(@Param("projectKey") String projectKey);

    /**
     * 查询项目下的实例列表
     */
    List<InstanceVO> queryProjectInstances(@Param("projectKey") String projectKey);
}
