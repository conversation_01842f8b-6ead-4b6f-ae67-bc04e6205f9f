import React, { useState, useEffect, useCallback } from 'react';
import { TreeSelect,Table, Button, Select, Tag, Notification, Pagination, Popconfirm, Tooltip } from '@douyinfe/semi-ui';
import { get, request } from '../../utils/request';
import HourRecordForm from './HourRecordForm';
import DateRangePicker from './components/DateRangePicker';
import UserSelector from './components/UserSelector';
import HourRecordEditForm from './HourRecordEditForm';
import HourRecordAuditDetail from './HourRecordAuditDetail';
import './workPage.css';
import {
  getAllDepartments,
  type Department
} from '../../api/report';

// 添加可截断文本的展示组件
const TruncateText: React.FC<{ content: string, width?: number }> = ({ content, width }) => {
  if (!content) return <span>-</span>;
  
  return (
    <Tooltip content={content}>
      <span className="truncate-cell" style={{ width: width ? `${width}px` : '100%' }}>
        {content}
      </span>
    </Tooltip>
  );
};

// 接口返回的工时记录类型
interface HourRecord {
  id: string | number;
  userId: string;
  userName: string;
  workDate: string;
  hours: number;
  description: string;
  workItemId: string;       // 工作项ID
  workItemName: string;     // 工作项名称
  projectId: string;
  projectName: string;
  instanceId?: string;
  instanceName?: string;
  nodeId?: string;
  nodeName?: string;
  taskId?: string;
  taskName?: string;
  status: number | string;
  statusName: string;
  createdAt: string;
  submitTime?: string | null;
  approverName?: string | null;
  rejectReason?: string | null;
  workPath?: string;
  workPeriodStart?: string;
  workPeriodEnd?: string;
  applyId?: number;
}

// 分页查询参数接口
interface QueryParams {
  pageNo: number;
  pageSize: number;
  startDate?: string;
  endDate?: string;
  userId?: string;
  projectId?: string;
  workContent?: string;
  status?: string;
}

// 分页结果接口
interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages?: number;
}


// 获取本周日期范围的工具函数（与DateRangePicker中"本周"逻辑一致）
const getThisWeekRange = (): [string, string] => {
  const now = new Date();
  // 本周开始：当前日期减去（星期几-1）天，即周一
  const startDate = new Date(now.getTime() - (now.getDay() - 1) * 24 * 60 * 60 * 1000);
  // 本周结束：当前日期加上（7-星期几）天，即周日
  const endDate = new Date(now.getTime() + (7 - now.getDay()) * 24 * 60 * 60 * 1000);

  // 格式化为YYYY-MM-DD
  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  return [formatDate(startDate), formatDate(endDate)];
};

// 更新 API 接口定义，匹配实际返回结构
interface ApiResponse<T> {
  success: boolean;
  message: string;
  code: number;
  result: PageResult<T>;
  timestamp: number;
}

// 调用工时记录接口
const getHourRecordList = (params: QueryParams): Promise<ApiResponse<PageResult<HourRecord>>> => {
  return get('/hours/hourRecord/list', params);
};

// 更新删除记录的接口函数，使用查询字符串传递id参数
const deleteHourRecord = (id: string | number): Promise<any> => {
  return request('DELETE', `/hours/hourRecord/delete?id=${id}`);
};

function toFormInitialData(record: HourRecord | null): any {
  if (!record) return undefined;
  return {
    workDate: record.workDate ? new Date(record.workDate) : undefined,
    worker: record.userId,
    workContent: [record.workItemId, record.instanceId, record.nodeId, record.taskId].filter(Boolean),
    projectKey: record.projectId,
    recordId: record.id,
    projectName: record.projectName,
    instanceName: record.instanceName,
    nodeName: record.nodeName,
    taskName: record.taskName,
    applyId: record.applyId
  };
}

const WorkPage: React.FC = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<HourRecord[]>([]);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    pageNo: 1,
    pageSize: 10
  });
  // 日期范围状态 - 使用DateRangePicker格式
  const [dateRange, setDateRange] = useState<[string, string]>(() => {
    // 默认为本周（与DateRangePicker中"本周"快捷方式逻辑一致）
    return getThisWeekRange();
  });
  // 用户选择状态
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  // 状态选择状态
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  // 添加记录模式状态，用于控制HourRecordForm的登记模式
  const [recordMode, setRecordMode] = useState<'single' | 'batch'>('single');
  // 添加当前编辑记录的状态
  const [currentRecord, setCurrentRecord] = useState<HourRecord | null>(null);
  // 添加工作内容数组的状态
  const [workContent, setWorkContent] = useState<(string | number)[]>([]);
  // 添加editMode状态区分'add'和'edit'
  const [editMode, setEditMode] = useState<'add' | 'edit' | 'view'>('add');
  const [departments, setDepartments] = useState<Department[]>([]);
  // 添加部门选择状态
  const [selectedDeptIds, setSelectedDeptIds] = useState<string[]>([]);

  // 获取工时记录列表
  const fetchRecords = useCallback(async (params?: Partial<QueryParams>) => {
    setLoading(true);
    
    try {
      const mergedParams = { ...queryParams, ...params };
      if (params) {
        setQueryParams(mergedParams as QueryParams);
      }
      
      const response = await getHourRecordList(mergedParams as QueryParams);
      console.log('API 完整响应数据:', response);

      // 恢复正确的数据解析逻辑
      if (response && response.result) {
        try {
          // 从result字段中获取分页数据
          const result = response.result;
          
          // 使用any类型断言来避免TypeScript类型错误
          if (result.records && Array.isArray(result.records)) {
            setRecords(result.records as any);
            setTotal(result.total || 0);
          } else {
            console.error('API返回的records不是数组或为空:', result.records);
            setRecords([]);
            setTotal(0);
          }
        } catch (parseError) {
          console.error('解析API返回数据出错:', parseError);
          setRecords([]);
          setTotal(0);
        }
      } else {
        console.error('API 返回格式不符合预期:', response);
        setRecords([]);
        setTotal(0);
      }
      
    } catch (error) {
      console.error('API 请求出错:', error);
      Notification.error({
        title: '获取工时记录失败',
        content: error.message
      });
    } finally {
      setLoading(false);
    }
  }, [queryParams]);

  // 构建查询参数的辅助函数
  const buildQueryParams = useCallback((overrides: Partial<QueryParams & { deptIds?: string } > = {}) => {
    return {
      ...queryParams,
      startDate: dateRange[0],
      endDate: dateRange[1],
      userId: selectedUsers.length > 0 ? selectedUsers.join(',') : undefined,
      status: selectedStatus || undefined,
      deptIds: selectedDeptIds.length > 0 ? selectedDeptIds.join(',') : undefined,
      ...overrides
    };
  }, [queryParams, dateRange, selectedUsers, selectedStatus, selectedDeptIds]);

  // 处理日期范围变化的函数 - 适配DateRangePicker
  const handleDateRangeChange = useCallback((dates: [string, string]) => {
    setDateRange(dates);

    // 使用新的日期范围进行查询，保持其他筛选条件
    const params = buildQueryParams({
      startDate: dates[0],
      endDate: dates[1],
      pageNo: 1
    });
    fetchRecords(params);

  }, [buildQueryParams, fetchRecords,  selectedUsers]);

  // 处理用户选择变化
  const handleUserChange = useCallback((userIds: string[]) => {
    setSelectedUsers(userIds);

    // 使用新的用户选择进行查询，保持其他筛选条件
    const params = buildQueryParams({
      userId: userIds.length > 0 ? userIds.join(',') : undefined,
      pageNo: 1
    });
    fetchRecords(params);

  }, [buildQueryParams, fetchRecords,  dateRange]);

  // 处理状态选择变化
  const handleStatusChange = useCallback((status: string) => {
    setSelectedStatus(status);

    // 使用新的状态选择进行查询，保持其他筛选条件
    const params = buildQueryParams({
      status: status || undefined,
      pageNo: 1
    });
    fetchRecords(params);
  }, [buildQueryParams, fetchRecords]);

  // 处理部门选择变化
  const handleDeptChange = useCallback((deptIds: string[]) => {
    setSelectedDeptIds(deptIds);
    // 使用新的部门选择进行查询，保持其他筛选条件
    const params = buildQueryParams({
      deptIds: deptIds.length > 0 ? deptIds.join(',') : undefined,
      pageNo: 1
    });
    fetchRecords(params);
  }, [buildQueryParams, fetchRecords]);


  // 首次加载数据 - 使用默认日期范围
  useEffect(() => {
    // 使用默认的日期范围进行初始加载
    const params = buildQueryParams({ pageNo: 1 });
    fetchRecords(params);

    loadDeptInitialData();
  }, []); // 依赖空数组，仅在组件挂载时执行一次

  // 加载基础数据
  const loadDeptInitialData = async () => {
    try {
      const [deptData] = await Promise.all([
        getAllDepartments(),
      ]);
      console.log('部门数据:', deptData);
      setDepartments(deptData);
    } catch (error) {
      console.error('加载基础数据失败:', error);
    } finally {

    }
  };

  // 表格列定义（新版）
  const columns = [
    {
      title: '登记周期',
      dataIndex: 'workDate',
      width: 150,
      render: (_: any, record: any) => {
        const start = record.workPeriodStart ? record.workPeriodStart.replace(/-/g, '.').slice(0, 10) : '';
        const end = record.workPeriodEnd ? record.workPeriodEnd.replace(/-/g, '.').slice(0, 10) : '';
        return <TruncateText content={start && end ? `${start}-${end}` : '-'} />;
      }
    },
    {
      title: '工作人员',
      dataIndex: 'userName',
      width: 90,
      render: (userName: string) => <TruncateText content={userName || '-'} />
    },
    {
      title: '状态',
      dataIndex: 'statusName',
      width: 90,
      render: (statusName: string, record: HourRecord) => {
        // let color: import('@douyinfe/semi-ui/lib/es/tag').TagColor = 'amber';
        // switch (record.status) {
        //   case 0: // 待提交
        //     color = 'amber';
        //     break;
        //   case 1: // 审批中
        //     color = 'blue';
        //     break;
        //   case 2: // 审批通过
        //     color = 'green';
        //     break;
        //   case 3: // 审批退回
        //     color = 'red';
        //     break;
        //   default:
        //     color = 'grey';
        // }
        // return <Tag color={color}>{statusName}</Tag>;
        return <span>{statusName}</span>
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 120,
      render: (submitTime: string) => <TruncateText content={submitTime || '-'} />
    },
    {
      title: '更新人',
      dataIndex: 'updateBy_dictText',
      width: 90,
      render: (approverName: string) => <TruncateText content={approverName || '-'} />
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right' as const,
      width: 120,
      render: (_: any, record: HourRecord) => {
        // 检查记录是否处于"驳回"或"待提交"状态
        const canEdit = 
          record.status === 0 || record.statusName === '待提交' || 
          record.status === 3 || record.statusName === '审批退回';
        
        return (
          <div style={{ whiteSpace: 'nowrap', display: 'flex', gap: '4px' }}>
            {!canEdit && (
              <Button 
                theme="borderless" 
                type="primary" 
                size="small"
                style={{ padding: '0 4px' }}
                onClick={() => handleOpenDetailRecord(record)}
              >
                详情
              </Button>
            )}
            {canEdit && (
              <>
                <Button 
                  theme="borderless" 
                  type="primary" 
                  size="small"
                  style={{ padding: '0 4px' }}
                  onClick={() => handleOpenEditRecord(record)}
                >
                  编辑
                </Button>
                <Popconfirm
                  title="确定要删除这条工时记录吗？"
                  content="删除后数据将无法恢复"
                  okType="danger"
                  onConfirm={() => handleDelete(record.id)}
                >
                  <Button 
                    theme="borderless" 
                    type="danger" 
                    size="small"
                    style={{ padding: '0 4px' }}
                  >
                    删除
                  </Button>
                </Popconfirm>
              </>
            )}
          </div>
        );
      },
    },
  ];

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: (string | number)[]) => setSelectedRowKeys(selectedKeys),
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    fetchRecords({
      pageNo: page,
      pageSize
    });
    setCurrentPage(page);
  };

  // 处理每页条数变化
  const handlePageSizeChange = (size: number) => {
    fetchRecords({
      pageNo: 1, // 改变页面大小时重置到第一页
      pageSize: size
    });
    setCurrentPage(1);
    setPageSize(size);
  };

  // 统一的分页处理函数
  const handlePaginationChange = (page: number, size: number) => {
    if (size !== pageSize) {
      // 页面大小改变
      handlePageSizeChange(size);
    } else {
      // 只是页码改变
      handlePageChange(page);
    }
  };

  // 在 WorkPage 组件中添加删除记录的处理函数
  const handleDelete = async (id: string | number) => {
    try {
      setLoading(true);
      const response = await deleteHourRecord(id);
      if (response && response.success) {
        Notification.success({
          title: '删除成功',
          content: '工时记录已成功删除'
        });
        // 重新加载数据
        fetchRecords();
      } else {
        Notification.error({
          title: '删除失败',
          content: response.message || '操作未完成，请重试'
        });
      }
    } catch (error) {
      console.error('删除记录出错:', error);
      Notification.error({
        title: '删除失败',
        content: error.message || '操作未完成，请重试'
      });
    } finally {
      setLoading(false);
    }
  };

  // 处理工时保存
  const handleSaveHourRecord = (data: any) => {
    console.log('保存工时记录', data);
    // 关闭表单并刷新数据
    setVisible(false);
    // 清空当前编辑的记录和工作内容
    setCurrentRecord(null);
    setWorkContent([]);
    // 重新加载数据以显示最新保存的记录
    fetchRecords();
  };

  // 处理工时提交
  const handleSubmitHourRecord = (data: any) => {
    console.log('提交工时记录', data);
    // 关闭表单并刷新数据
    setVisible(false);
    // 清空当前编辑的记录和工作内容
    setCurrentRecord(null);
    setWorkContent([]);
    // 重新加载数据以显示最新提交的记录
    fetchRecords();
  };

  // 处理打开编辑模态窗
  const handleOpenEditRecord = (record: HourRecord) => {
    // 在控制台输出调试信息
    console.log('===== 编辑记录详情 =====');
    console.log('完整记录:', record);
    console.log('记录ID:', record.id);
    console.log('记录ID类型:', typeof record.id);
    console.log('========================');
    
    // 构建工作内容数组，包含所有非空的工作项、实例、节点、任务ID
    const workContentArray: (string | number)[] = [];
    if (record.workItemId) workContentArray.push(record.workItemId);
    if (record.instanceId) workContentArray.push(record.instanceId);
    if (record.nodeId) workContentArray.push(record.nodeId);
    if (record.taskId) workContentArray.push(record.taskId);
    
    console.log('构建的工作内容数组:', workContentArray);
    
    // 设置当前编辑的记录
    setCurrentRecord(record);
    // 保存工作内容数组，在传递给HourRecordForm时使用
    setWorkContent(workContentArray);
    
    setRecordMode('single');
    setEditMode('edit');
    setVisible(true);
  };

  // 处理打开批量登记
  const handleOpenBatchRecord = () => {
    setCurrentRecord(null);
    setWorkContent([]);
    setRecordMode('batch');
    setEditMode('add');
    setVisible(false);
    setTimeout(() => {
      setVisible(true);
    }, 10);
  };

  // 处理打开详情模态窗
  const handleOpenDetailRecord = (record: HourRecord) => {
    console.log('===== 打开详情记录 =====');
    console.log('完整记录:', record);
    console.log('记录ID:', record.id);
    console.log('记录ID类型:', typeof record.id);
    console.log('========================');
    

    // 设置当前编辑的记录
    setCurrentRecord(record);
    
    setEditMode('view');
    setVisible(true);
    console.log('1>>>>>>editMode=', editMode,',visible=', visible,',currentRecord=', currentRecord);
  };

  // 处理批量提交
  const handleBatchSubmit = async () => {
    // 验证是否选择了记录
    if (!selectedRowKeys || selectedRowKeys.length === 0) {
      Notification.warning({
        title: '请选择记录',
        content: '请先选择需要提交的工时记录',
        duration: 3
      });
      return;
    }

    // 验证选中记录的状态
    const invalidRecords = records.filter(
      record => selectedRowKeys.includes(record.id) && 
      (record.status !== 0 && record.status !== 3 && 
       record.statusName !== '待提交' && record.statusName !== '审批退回')
    );

    if (invalidRecords.length > 0) {
      Notification.warning({
        title: '包含无效记录',
        content: '只能提交"待提交"或"审批退回"状态的记录',
        duration: 3
      });
      return;
    }

    try {
      setLoading(true);
      
      // 调用批量提交接口
      const response = await request<{success: boolean; message?: string}>('POST', '/hours/hourRecord/batchSubmit?ids='+selectedRowKeys, {
        
      });
      
      if (response && response.success) {
        Notification.success({
          title: '提交成功',
          content: `已成功提交 ${selectedRowKeys.length} 条工时记录`,
          duration: 3
        });
        
        // 重新加载数据
        fetchRecords();
        // 清空选中状态
        setSelectedRowKeys([]);
      } else {
        Notification.error({
          title: '提交失败',
          content: response?.message || '批量提交工时记录失败',
          duration: 3
        });
      }
    } catch (error) {
      console.error('批量提交工时记录失败:', error);
      Notification.error({
        title: '提交失败',
        content: '批量提交工时记录失败，请稍后重试',
        duration: 3
      });
    } finally {
      setLoading(false);
    }
  };

    // 将部门数据转换为TreeSelect所需的格式
    const transformDepartmentData = (departments: Department[]): any[] => {
      return departments.map(dept => ({
        label: dept.name,
        value: dept.id,
        key: dept.id,
        children: dept.children && dept.children.length > 0
          ? transformDepartmentData(dept.children)
          : undefined
      }));
    };
  

  return (
    <div className="work-list-root">
      {/* 工具栏 */}
      <div className="work-list-toolbar">
        {/* 左侧筛选区域 */}
        <div className="toolbar-filters">
          <DateRangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            dimension="day"
            placeholder="请选择日期范围"
          />

          {/* 用户选择器 */}
          {/* <div style={{ width: 200 }}>
            <UserSelector
              value={selectedUsers}
              onChange={handleUserChange}
              placeholder="全部人员"
            />
          </div> */}

          {/* 状态选择器 */}
          <Select
            placeholder="选择状态"
            style={{ width: 120 }}
            value={selectedStatus}
            onChange={handleStatusChange}
          >
            <Select.Option value="">全部</Select.Option>
            <Select.Option value="0">待提交</Select.Option>
            <Select.Option value="1">审批中</Select.Option>
            <Select.Option value="2">审批通过</Select.Option>
            <Select.Option value="3">审批退回</Select.Option>
          </Select>

          <TreeSelect
            multiple
            style={{ width: 120 }}
            placeholder="选择部门"
            maxTagCount={1}
            showClear
            treeData={transformDepartmentData(departments)}
            showSearchClear
            expandAll
            checkRelation="related"
            value={selectedDeptIds}
            onChange={handleDeptChange}
          />

        </div>

        {/* 右侧按钮组 */}
        <div className="toolbar-buttons">
          <Button
            theme="solid"
            type="primary"
            onClick={handleBatchSubmit}
          >
            批量提交
          </Button>
          <Button
            theme="solid"
            type="primary"
            onClick={handleOpenBatchRecord}
          >
            批量登记
          </Button>
        </div>
      </div>

      {/* 工时记录表格 */}
      <Table
        columns={columns}
        dataSource={records}
        loading={loading}
        pagination={false}
        rowSelection={rowSelection}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        size="middle"
        style={{ marginTop: 16, width: '100%' }}
        tableLayout="auto"
        components={{
          header: {
            cell: (props) => <th {...props} style={{ ...props.style, whiteSpace: 'nowrap', overflow: 'visible' }} />
          }
        }}
      />

      {/* 分页 */}
      {total > 0 && (
        <div style={{
          marginTop: 16,
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-end'
        }}>
          <Pagination
            currentPage={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={handlePaginationChange}
            showSizeChanger
            showQuickJumper
            showTotal
            pageSizeOpts={[10, 20, 50]}
          />
        </div>
      )}

      {/* 工时登记表单 */}
      {editMode === 'edit' && (
        <HourRecordEditForm
          visible={visible}
          isEdit={true}
          initialData={toFormInitialData(currentRecord)}
          onCancel={() => setVisible(false)}
          onSave={() => { setVisible(false); fetchRecords(); }}
          onSubmit={() => { setVisible(false); fetchRecords(); }}
        />
      )}
      {/* 工时登记详情 */}
      {console.log('editMode=', editMode,',visible=', visible,',currentRecord=', currentRecord)}
      {editMode === 'view' && (
        <HourRecordAuditDetail
          visible={visible}
          isAudit={false}
          readOnly={true}
          initialData={currentRecord ? toFormInitialData(currentRecord as any) : undefined}
          onCancel={() => { setVisible(false); setCurrentRecord(null); }}
          onSave={fetchRecords}
          onSubmit={fetchRecords}
          />
        // <HourRecordEditForm
        //   visible={visible}
        //   isEdit={false}
        //   readOnly={true}
        //   initialData={toFormInitialData(currentRecord)}
        //   onCancel={() => setVisible(false)}
        //   onSave={() => { setVisible(false); fetchRecords(); }}
        //   onSubmit={() => { setVisible(false); fetchRecords(); }}
        // />
      )}
      {editMode === 'add' && (
        <HourRecordForm
          visible={visible}
          initialData={undefined}
          onCancel={() => setVisible(false)}
          onSave={() => { setVisible(false); fetchRecords(); }}
          onSubmit={() => { setVisible(false); fetchRecords(); }}
        />
      )}

    </div>
  );
};

export default WorkPage;