package org.jeecg.modules.hours.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 可登记工时日期DTO
 */
@Data
@ApiModel(value = "可登记工时日期DTO", description = "可登记工时日期DTO")
public class AvailableDateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private String date;

    /**
     * 考勤工时
     */
    @ApiModelProperty(value = "考勤工时")
    private BigDecimal attendanceHours;

    /**
     * 是否工作日
     */
    @ApiModelProperty(value = "是否工作日")
    private Boolean isWorkday;
} 