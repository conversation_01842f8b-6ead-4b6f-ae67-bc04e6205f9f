import { defHttp } from '@/utils/http/axios';
import { Result } from '#/axios';

enum Api {
  List = '/hapi/hours/hourRecord/list',
  Add = '/hapi/hours/hourRecord/add',
  Edit = '/hapi/hours/hourRecord/edit',
  Delete = '/hapi/hours/hourRecord/delete',
  BatchSubmit = '/hapi/hours/hourRecord/batchSubmit',
  Validate = '/hapi/hours/hourRecord/validate',
  BatchSave = '/hapi/hours/hourRecord/batchSave',
}

/**
 * 获取工时记录列表
 */
export const list = (params?: any) =>
  defHttp.get<Result>({
    url: Api.List,
    params,
  });

/**
 * 添加工时记录
 */
export const add = (params: any) =>
  defHttp.post<Result>({
    url: Api.Add,
    params,
  });

/**
 * 编辑工时记录
 */
export const edit = (params: any) =>
  defHttp.put<Result>({
    url: Api.Edit,
    params,
  });

/**
 * 删除工时记录
 */
export const del = (id: string) =>
  defHttp.delete<Result>({
    url: Api.Delete,
    params: { id },
  });

/**
 * 批量提交工时记录
 */
export const batchSubmit = (ids: (string | number)[]) =>
  defHttp.post<Result>({
    url: Api.BatchSubmit,
    params: ids,
  });

/**
 * 校验工时记录
 * @param records 工时记录（单条或多条）
 * @returns 校验结果
 */
export const validate = (records: any | any[]) => {
  // 如果是数组，转换为 Map 格式
  const params = Array.isArray(records)
    ? records.reduce((acc, record, index) => {
        acc[index.toString()] = record;
        return acc;
      }, {} as Record<string, any>)
    : records;

  return defHttp.post<Result>({
    url: Api.Validate,
    params,
  });
};

/**
 * 批量保存工时记录
 */
export const batchSave = (records: any[]) =>
  defHttp.post<Result>({
    url: Api.BatchSave,
    params: records,
  }); 