package org.jeecg.modules.hours.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 工时单审批人清单表
 * @Author: jeecg-boot
 * @Date: 2024-05-01
 * @Version: V1.0
 */
@Data
@TableName("hour_record_approver")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "hour_record_approver对象", description = "工时单审批人清单表")
public class HourRecordApprover implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "工时记录ID")
    private String recordId;

    @ApiModelProperty(value = "审批人ID")
    private String approverId;

    @ApiModelProperty(value = "审批人姓名")
    private String approverName;

    @ApiModelProperty(value = "审批人角色（如 leader/pm/other）")
    private String role;

    @ApiModelProperty(value = "审批状态(0:待审批,1:已通过,2:已拒绝,3:已移交)")
    private Integer status;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审批时间")
    private Date actionTime;

    @ApiModelProperty(value = "审批备注")
    private String remarks;

    @ApiModelProperty(value = "如为移交产生，记录原审批人ID")
    private String transferredFrom;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "移交时间")
    private Date transferredTime;

    @ApiModelProperty(value = "申请批次ID")
    private Long applyId;
} 