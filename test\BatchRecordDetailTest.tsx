import React, { useState } from 'react';
import { Button, Card, Typography, Space, DatePicker, Input } from '@douyinfe/semi-ui';
import BatchRecordForm from '../src/features/board_web_gw38j1/BatchRecordForm';
import { WorkDetail } from '../src/features/board_web_gw38j1/types';

const { Title, Text } = Typography;

/**
 * 批量登记表格明细功能测试页面
 * 
 * 测试功能：
 * 1. 批量登记表格的操作列显示
 * 2. "明细"超链接的点击功能
 * 3. 工时明细模态窗的弹出和显示
 * 4. 不同工作内容的参数传递
 * 5. 模态窗的关闭功能
 */
const BatchRecordDetailTest: React.FC = () => {
  const [workDate, setWorkDate] = useState<Date>(new Date());
  const [worker, setWorker] = useState<string>('测试用户');
  const [workDetails, setWorkDetails] = useState<WorkDetail[]>([]);

  const handleWorkDateChange = (date: Date) => {
    console.log('工作日期变更:', date);
    setWorkDate(date);
  };

  const handleWorkerChange = (value: string) => {
    console.log('工作人员变更:', value);
    setWorker(value);
  };

  const handleWorkDetailsChange = (details: WorkDetail[]) => {
    console.log('工作明细变更:', details);
    setWorkDetails(details);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1400px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>批量登记表格明细功能测试</Title>
        
        <div style={{ marginBottom: '24px' }}>
          <Text>
            此测试页面用于验证批量登记表格中"明细"功能，包括：
          </Text>
          <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
            <li>表格操作列的正确显示</li>
            <li>"明细"超链接的样式和交互</li>
            <li>点击明细后工时明细模态窗的弹出</li>
            <li>工作内容参数的正确传递</li>
            <li>模态窗内容的正确加载和显示</li>
            <li>模态窗的关闭功能</li>
          </ul>
        </div>

        {/* 测试说明 */}
        <div style={{ marginBottom: '24px' }}>
          <Title heading={5}>测试说明</Title>
          <div style={{ 
            padding: '16px', 
            backgroundColor: 'var(--semi-color-fill-0)',
            borderRadius: '6px',
            marginTop: '8px'
          }}>
            <Text>
              <strong>操作步骤：</strong><br/>
              1. 选择工作日期（默认为今天）<br/>
              2. 查看批量登记表格是否正确加载<br/>
              3. 检查表格最右侧是否有"操作"列<br/>
              4. 点击任意行的"明细"超链接<br/>
              5. 验证工时明细模态窗是否正确弹出<br/>
              6. 检查模态窗中的工作内容和工时记录<br/>
              7. 关闭模态窗并测试其他行的明细功能
            </Text>
          </div>
        </div>

        {/* 功能特性说明 */}
        <div style={{ marginBottom: '24px' }}>
          <Title heading={5}>功能特性</Title>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <Text strong>操作列特性：</Text>
              <ul style={{ marginTop: '4px', paddingLeft: '16px' }}>
                <li>固定在表格右侧</li>
                <li>宽度80px</li>
                <li>包含"明细"超链接</li>
                <li>超链接样式与单个登记一致</li>
              </ul>
            </div>
            <div>
              <Text strong>明细功能特性：</Text>
              <ul style={{ marginTop: '4px', paddingLeft: '16px' }}>
                <li>传递完整的工作内容参数</li>
                <li>支持工作项、实例、节点、任务四级</li>
                <li>模态窗宽度900px</li>
                <li>显示工作内容路径和统计信息</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 参数传递说明 */}
        <div style={{ marginBottom: '24px' }}>
          <Title heading={5}>参数传递机制</Title>
          <Text>
            点击"明细"时，会将以下参数传递给工时明细模态窗：
          </Text>
          <div style={{ 
            marginTop: '8px',
            padding: '12px',
            backgroundColor: 'var(--semi-color-fill-0)',
            borderRadius: '4px',
            fontFamily: 'monospace'
          }}>
            <Text>
              workItemId: record.workItemId<br/>
              instanceId: record.instanceId<br/>
              nodeId: record.nodeId<br/>
              taskId: record.taskId
            </Text>
          </div>
        </div>

        {/* 批量登记表格组件 */}
        <div style={{ marginTop: '24px' }}>
          <Title heading={5}>批量登记表格</Title>
          <div style={{ 
            border: '1px solid var(--semi-color-border)',
            borderRadius: '6px',
            padding: '16px',
            marginTop: '16px'
          }}>
            <BatchRecordForm
              workDate={workDate}
              worker={worker}
              workDetails={workDetails}
              onWorkDateChange={handleWorkDateChange}
              onWorkerChange={handleWorkerChange}
              onWorkDetailsChange={handleWorkDetailsChange}
              disableWorker={true}
            />
          </div>
        </div>

        {/* 测试检查点 */}
        <div style={{ marginTop: '24px' }}>
          <Title heading={5}>测试检查点</Title>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <Text strong>界面检查：</Text>
              <ul style={{ marginTop: '4px', paddingLeft: '16px' }}>
                <li>✓ 操作列是否显示在最右侧</li>
                <li>✓ "明细"超链接样式是否正确</li>
                <li>✓ 表格横向滚动是否正常</li>
                <li>✓ 操作列是否固定不滚动</li>
              </ul>
            </div>
            <div>
              <Text strong>功能检查：</Text>
              <ul style={{ marginTop: '4px', paddingLeft: '16px' }}>
                <li>✓ 点击明细是否弹出模态窗</li>
                <li>✓ 模态窗内容是否正确加载</li>
                <li>✓ 工作内容路径是否正确显示</li>
                <li>✓ 工时记录表格是否正确渲染</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 预期行为 */}
        <div style={{ marginTop: '24px' }}>
          <Title heading={5}>预期行为</Title>
          <div style={{ 
            padding: '16px',
            backgroundColor: 'var(--semi-color-success-light-default)',
            borderRadius: '6px',
            border: '1px solid var(--semi-color-success-light-active)'
          }}>
            <Text>
              <strong>正常情况：</strong><br/>
              - 表格正确加载工作内容数据<br/>
              - 操作列显示在最右侧，包含"明细"超链接<br/>
              - 点击"明细"后弹出工时明细模态窗<br/>
              - 模态窗显示对应工作内容的工时记录<br/>
              - 可以正常关闭模态窗并继续操作其他行<br/><br/>
              
              <strong>异常情况：</strong><br/>
              - 如果工作项ID为空，"明细"按钮应该被禁用<br/>
              - 如果API调用失败，模态窗显示"暂无工时明细数据"<br/>
              - 网络错误时显示相应的错误提示
            </Text>
          </div>
        </div>

        {/* 开发者信息 */}
        <div style={{ marginTop: '24px' }}>
          <Title heading={5}>开发者信息</Title>
          <Text>
            <strong>组件路径：</strong> src/features/board_web_gw38j1/BatchRecordForm.tsx<br/>
            <strong>模态窗组件：</strong> src/features/board_web_gw38j1/components/WorkHourDetailModal.tsx<br/>
            <strong>API接口：</strong> GET /hours/hourRecord/detail<br/>
            <strong>测试文件：</strong> test/BatchRecordDetailTest.tsx
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default BatchRecordDetailTest;
