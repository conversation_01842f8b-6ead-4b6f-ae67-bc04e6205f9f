package org.jeecg.modules.hours.job;

import java.util.Calendar;
import java.util.concurrent.ExecutionException;

import org.jeecg.modules.hours.service.IDeptProjectDailyHourStatisticsService;
import org.jeecg.modules.hours.service.IUserProjectDailyHourStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 部门（人员）每日工时分布统计定时任务
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Component
@Slf4j
public class DeptProjectDailyHourStatisticsJob {

    @Autowired
    private IDeptProjectDailyHourStatisticsService deptProjectDailyHourStatisticsService;

    @Autowired
    private IUserProjectDailyHourStatisticsService userProjectDailyHourStatisticsService;

    //统计里面不会把当前 isHoliday 参数传递进去

    /**
     * 每月1号凌晨2点执行，生成上个月的统计数据
     */
//    @Scheduled(cron = "0 0 3 * * ?")
    @Scheduled(cron = "0 */15 * * * ?")
    public void executeMonthly() throws ExecutionException {
        log.info("部门项目每日工时分布统计定时任务开始执行（月度）");
        
        try {
            // 获取上个月
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, -1);
            String lastMonth = String.format("%d-%02d", 
                calendar.get(Calendar.YEAR), 
                calendar.get(Calendar.MONTH) + 1);
            
            // 生成部门统计数据
//            int deptCount1 = deptProjectDailyHourStatisticsService.generateMonthlyStatistics(lastMonth, 0);
            int deptCount2 = deptProjectDailyHourStatisticsService.generateMonthlyStatistics(lastMonth, 1);
            
            // 生成人员统计数据
//            int userCount1 = userProjectDailyHourStatisticsService.generateMonthlyStatistics(lastMonth, 0);
            int userCount2 = userProjectDailyHourStatisticsService.generateMonthlyStatistics(lastMonth, 1);
            
            log.info("每日工时分布统计定时任务执行完成（月度）, 月份={}, 部门统计(不含假日/含假日)={}/{}, 人员统计(不含假日/含假日)={}/{}", 
                lastMonth, 0, deptCount2, 0, userCount2);
            
        } catch (Exception e) {
            log.error("部门项目每日工时分布统计定时任务执行失败（月度）", e);
            throw new ExecutionException("部门项目每日工时分布统计定时任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 每天凌晨3点执行，更新当前月的统计数据
     */
//    @Scheduled(cron = "0 0 3 * * ?")
    @Scheduled(cron = "0 */12 * * * ?")
    public void executeDaily() throws ExecutionException {
        log.info("部门项目每日工时分布统计定时任务开始执行（每日更新）");
        
        try {
            // 获取当前月
            Calendar calendar = Calendar.getInstance();
            String currentMonth = String.format("%d-%02d", 
                calendar.get(Calendar.YEAR), 
                calendar.get(Calendar.MONTH) + 1);
            
            // 更新部门统计数据
//            int deptCount1 = deptProjectDailyHourStatisticsService.generateMonthlyStatistics(currentMonth, 0);
            int deptCount2 = deptProjectDailyHourStatisticsService.generateMonthlyStatistics(currentMonth, 1);
            
            // 更新人员统计数据
//            int userCount1 = userProjectDailyHourStatisticsService.generateMonthlyStatistics(currentMonth, 0);
            int userCount2 = userProjectDailyHourStatisticsService.generateMonthlyStatistics(currentMonth, 1);
            
            log.info("每日工时分布统计定时任务执行完成（每日更新）, 月份={}, 部门统计(不含假日/含假日)={}/{}, 人员统计(不含假日/含假日)={}/{}", 
                currentMonth, 0, deptCount2, 0, userCount2);
            
        } catch (Exception e) {
            log.error("部门项目每日工时分布统计定时任务执行失败（每日更新）", e);
            throw new ExecutionException("部门项目每日工时分布统计定时任务执行失败: " + e.getMessage(), e);
        }
    }
}