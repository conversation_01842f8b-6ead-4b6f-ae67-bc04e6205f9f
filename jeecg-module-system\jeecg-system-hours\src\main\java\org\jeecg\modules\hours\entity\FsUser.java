package org.jeecg.modules.hours.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 飞书用户表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
@Data
@TableName("fs_user")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="fs_user对象", description="飞书用户表")
public class FsUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**
     * 飞书用户标识
     */
    @Excel(name = "飞书用户标识", width = 15)
    @ApiModelProperty(value = "飞书用户标识")
    private String userKey;
    
    /**
     * 飞书unionID
     */
    @Excel(name = "飞书unionID", width = 15)
    @ApiModelProperty(value = "飞书unionID")
    private String unionId;
    
    /**
     * 用户姓名
     */
    @Excel(name = "用户姓名", width = 15)
    @ApiModelProperty(value = "用户姓名")
    private String name;
    
    /**
     * 邮箱
     */
    @Excel(name = "邮箱", width = 20)
    @ApiModelProperty(value = "邮箱")
    private String email;
    
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    
    /**
     * 主部门ID
     */
    @Excel(name = "主部门ID", width = 15)
    @ApiModelProperty(value = "主部门ID")
    private String deptId;
    
    /**
     * 状态
     */
    @Excel(name = "状态", width = 10)
    @ApiModelProperty(value = "状态")
    private String status;
    
    /**
     * 直属上级ID
     */
    @Excel(name = "直属上级ID", width = 15)
    @ApiModelProperty(value = "直属上级ID")
    private String leaderUserId;
    
    /**
     * 飞书项目标识
     */
    @Excel(name = "飞书项目标识", width = 15)
    @ApiModelProperty(value = "飞书项目标识")
    private String projectKey;
    
    /**
     * 同步时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "同步时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "同步时间")
    private Date syncTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;
    
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /**
     * 创建人姓名（非数据库字段，仅用于前端显示）
     */
    @TableField(exist = false)
    private String createByName;
    
    public String getCreateByName() {
        return createByName;
    }
    
    public void setCreateByName(String createByName) {
        this.createByName = createByName;
    }
    
    /**
     * 兼容旧代码，getUserId方法返回userKey
     * @deprecated 请使用getUserKey方法代替
     */
    @Deprecated
    public String getUserId() {
        return this.userKey;
    }
    
    /**
     * 兼容旧代码，setUserId方法设置userKey
     * @deprecated 请使用setUserKey方法代替
     */
    @Deprecated
    public void setUserId(String userId) {
        this.userKey = userId;
    }
    
    @TableField(exist = false)
    private String departmentName;
    
} 