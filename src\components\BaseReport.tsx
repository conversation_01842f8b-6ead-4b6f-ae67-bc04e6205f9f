import React, { useState, useEffect } from 'react';
import { Table, Button, Select, Spin, Notification, Space } from '@douyinfe/semi-ui';
import { IconDownload, IconRefresh } from '@douyinfe/semi-icons';
import DateRangePicker from '../features/board_web_gw38j1/components/DateRangePicker';
import UserSelector from '../features/board_web_gw38j1/components/UserSelector';
import InstanceSelector from '../features/board_web_gw38j1/components/InstanceSelector';

// 报表配置接口
export interface ReportConfig {
  title: string;
  showUserSelector?: boolean;
  showInstanceSelector?: boolean;
  showDimensionSelector?: boolean;
  showExportButton?: boolean;
}

// 报表数据接口
export interface ReportData {
  columns: any[];
  dataSource: any[];
  summary?: any;
}

// 报表组件属性接口
export interface BaseReportProps {
  config: ReportConfig;
  onFetchData: (params: any) => Promise<ReportData>;
  onExport?: (params: any) => Promise<void>;
  renderCustomFilters?: () => React.ReactNode;
}

/**
 * 报表基类组件
 * 提供通用的报表功能：筛选、查询、导出等
 * projectKey已通过全局请求header传递，无需在组件中单独传递
 */
const BaseReport: React.FC<BaseReportProps> = ({
  config,
  onFetchData,
  onExport,
  renderCustomFilters
}) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ReportData | null>(null);
  const [dimension, setDimension] = useState<'month' | 'week' | 'day'>('month');
  const [dateRange, setDateRange] = useState<[string, string]>(['2025-01-01', '2025-12-31']);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedInstances, setSelectedInstances] = useState<string[]>([]);

  // 查询报表数据
  const fetchReportData = async () => {
    if (!dateRange) return;

    setLoading(true);
    try {
      const params = {
        dimension,
        startDate: dateRange[0],
        endDate: dateRange[1],
        userIds: selectedUsers.length > 0 ? selectedUsers : undefined,
        instanceIds: selectedInstances.length > 0 ? selectedInstances : undefined
      };

      const result = await onFetchData(params);
      setData(result);
    } catch (error) {
      console.error('获取报表数据失败:', error);
      Notification.error({
        title: '获取报表数据失败',
        content: error instanceof Error ? error.message : '请稍后重试'
      });
    } finally {
      setLoading(false);
    }
  };

  // 导出报表
  const handleExport = async () => {
    if (!dateRange || !onExport) return;

    try {
      const params = {
        dimension,
        startDate: dateRange[0],
        endDate: dateRange[1],
        userIds: selectedUsers.length > 0 ? selectedUsers : undefined,
        instanceIds: selectedInstances.length > 0 ? selectedInstances : undefined
      };

      await onExport(params);
      Notification.success({
        title: '导出成功',
        content: '报表正在下载中...'
      });
    } catch (error) {
      console.error('导出报表失败:', error);
      Notification.error({
        title: '导出失败',
        content: error instanceof Error ? error.message : '请稍后重试'
      });
    }
  };

  // 初始加载
  useEffect(() => {
    fetchReportData();
  }, [dimension, dateRange, selectedUsers, selectedInstances]);

  return (
    <div>
      {/* 筛选条件 */}
      <div style={{ marginBottom: 16, padding: 16, background: '#fafafa', borderRadius: 6 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', gap: 16 }}>
          {/* 左侧筛选条件 */}
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16, alignItems: 'center', flex: 1 }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <DateRangePicker
                value={dateRange}
                onChange={setDateRange}
                dimension={dimension}
              />
            </div>

            {config.showUserSelector && (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <UserSelector
                  value={selectedUsers}
                  onChange={setSelectedUsers}
                  placeholder="全部人员"
                />
              </div>
            )}

            {config.showInstanceSelector && (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <InstanceSelector
                  value={selectedInstances}
                  onChange={setSelectedInstances}
                  placeholder="全部实例"
                />
              </div>
            )}

            {renderCustomFilters && renderCustomFilters()}

            <Button
              icon={<IconRefresh />}
              onClick={fetchReportData}
              loading={loading}
            >
              刷新
            </Button>
          </div>

          {/* 右侧操作区域 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
            {config.showDimensionSelector && (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Select
                  value={dimension}
                  onChange={setDimension}
                  style={{ width: 80 }}
                >
                  <Select.Option value="month">按月</Select.Option>
                  <Select.Option value="week">按周</Select.Option>
                  <Select.Option value="day">按日</Select.Option>
                </Select>
              </div>
            )}

            {config.showExportButton && onExport && (
              <Button
                theme="solid"
                type="primary"
                icon={<IconDownload />}
                onClick={handleExport}
                disabled={!data}
              >
                导出
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 报表表格 */}
      <Table
        columns={data?.columns || []}
        dataSource={data?.dataSource || []}
        loading={loading}
        scroll={{ x: 'max-content' }}
        size="middle"
        pagination={false}
        empty={
          <div style={{ padding: 40, textAlign: 'center' }}>
            {loading ? <Spin /> : '暂无数据'}
          </div>
        }
        className="report-table"
      />

      <style>{`
        .report-table .summary-row {
          background-color: #f6f8fa;
          font-weight: bold;
        }
      `}</style>
    </div>
  );
};

export default BaseReport; 