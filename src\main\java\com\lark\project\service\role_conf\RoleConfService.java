/*
 * Copyright (c) 2023 Lark Technologies Pte. Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lark.project.service.role_conf;

import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.role_conf.builder.QueryRoleConfDetailsReq;
import com.lark.project.service.role_conf.builder.QueryRoleConfDetailsResp;

public interface RoleConfService {

    // 获取流程角色配置详情
    public QueryRoleConfDetailsResp queryRoleConfDetails(QueryRoleConfDetailsReq req, RequestOptions reqOptions) throws Exception;

}