package org.jeecg.modules.hours.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.FsSysReminderConfig;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 登记提醒配置表
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
public interface FsSysReminderConfigMapper extends BaseMapper<FsSysReminderConfig> {

    /**
     * 根据工作空间标识查询提醒配置列表
     * 
     * @param projectKey 工作空间标识
     * @return 提醒配置列表
     */
    List<FsSysReminderConfig> selectByProjectKey(@Param("projectKey") String projectKey);

    /**
     * 根据工作空间标识和状态查询提醒配置列表
     * 
     * @param projectKey 工作空间标识
     * @param status 状态
     * @return 提醒配置列表
     */
    List<FsSysReminderConfig> selectByProjectKeyAndStatus(@Param("projectKey") String projectKey, @Param("status") String status);

    /**
     * 根据提醒类型查询启用的提醒配置
     * 
     * @param reminderType 提醒类型
     * @return 提醒配置列表
     */
    List<FsSysReminderConfig> selectEnabledByReminderType(@Param("reminderType") String reminderType);

    /**
     * 批量更新状态
     *
     * @param ids ID列表
     * @param status 状态
     * @param updateBy 更新人
     * @return 更新记录数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") String status, @Param("updateBy") String updateBy);

    /**
     * 根据工作空间标识统计提醒配置数量
     * 
     * @param projectKey 工作空间标识
     * @return 配置数量
     */
    int countByProjectKey(@Param("projectKey") String projectKey);

    /**
     * 根据工作空间标识和提醒类型查询提醒配置
     * 
     * @param projectKey 工作空间标识
     * @param reminderType 提醒类型
     * @return 提醒配置列表
     */
    List<FsSysReminderConfig> selectByProjectKeyAndReminderType(@Param("projectKey") String projectKey, @Param("reminderType") String reminderType);

    /**
     * 查询需要执行的提醒配置
     * 根据当前时间、提醒类型、重复方式等条件查询
     * 
     * @param currentTime 当前时间(HH:mm格式)
     * @param dayOfWeek 星期几(1-7，1为周一)
     * @param dayOfMonth 月份中的第几天(1-31)
     * @param isLastDayOfMonth 是否为月末
     * @return 需要执行的提醒配置列表
     */
    List<FsSysReminderConfig> selectReminderConfigsToExecute(
        @Param("currentTime") String currentTime,
        @Param("dayOfWeek") int dayOfWeek,
        @Param("dayOfMonth") int dayOfMonth,
        @Param("isLastDayOfMonth") boolean isLastDayOfMonth
    );
}
