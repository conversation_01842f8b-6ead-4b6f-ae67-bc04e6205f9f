package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 部门项目工时统计报表结果VO
 * @Author: jeecg-boot
 * @Date: 2025-07-07
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DeptProjectHourReportResult", description = "部门项目工时统计报表结果VO")
public class DeptProjectHourReportResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 动态项目列信息
     */
    @ApiModelProperty(value = "动态项目列信息")
    private List<ProjectColumnVO> projectColumns;

    /**
     * 部门工时数据
     */
    @ApiModelProperty(value = "部门工时数据")
    private List<DeptProjectHourReportVO> deptHourData;

    /**
     * 总计行数据
     */
    @ApiModelProperty(value = "总计行数据")
    private DeptProjectHourReportVO totalRow;

    /**
     * 统计摘要信息
     */
    @ApiModelProperty(value = "统计摘要信息")
    private ReportSummaryVO summary;

    @Data
    @Accessors(chain = true)
    public static class ReportSummaryVO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 统计日期范围
         */
        @ApiModelProperty(value = "统计日期范围")
        private String dateRange;

        /**
         * 总工时
         */
        @ApiModelProperty(value = "总工时")
        private BigDecimal totalHours;

        /**
         * 部门数量
         */
        @ApiModelProperty(value = "部门数量")
        private Integer deptCount;

        /**
         * 项目数量
         */
        @ApiModelProperty(value = "项目数量")
        private Integer projectCount;

        /**
         * 参与人数
         */
        @ApiModelProperty(value = "参与人数")
        private Integer userCount;
    }
}