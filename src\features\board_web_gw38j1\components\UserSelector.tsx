import React, { useState, useEffect } from 'react';
import { Select, Spin } from '@douyinfe/semi-ui';
import { get } from '../../../utils/request';

interface User {
  id: string;
  name: string;
  userKey: string;
  email?: string;
  mobile?: string;
}

interface UserSelectorProps {
  value?: string[];                           // 当前选中的用户ID列表
  onChange: (userIds: string[]) => void;      // 用户选择变化回调
  placeholder?: string;                       // 占位符
  showSelectAll?: boolean;                    // 是否显示全选选项
  disabled?: boolean;                         // 是否禁用
}

/**
 * 人员选择组件
 * 支持多选、搜索、全选功能
 * projectKey已通过全局请求header传递，无需在组件中单独传递
 */
const UserSelector: React.FC<UserSelectorProps> = ({
  value,
  onChange,
  placeholder = "请选择人员",
  showSelectAll = true,
  disabled = false
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取项目下的用户列表
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await get('/system/user/listActiveUsers') as any;

      if (response && response.success && response.result) {
        setUsers(response.result);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onChange(users.map(user => user.userKey));
    } else {
      onChange([]);
    }
  };

  // 构建选项列表
  const optionList = users.map(user => ({
    value: user.userKey,
    label: user.name,
    key: user.userKey
  }));

  // 添加全选选项
  if (showSelectAll && users.length > 0) {
    const isAllSelected = value && value.length === users.length;
    optionList.unshift({
      value: 'SELECT_ALL',
      label: isAllSelected ? '取消全选' : '全选',
      key: 'SELECT_ALL'
    });
  }

  // 处理选择变化
  const handleChange = (selectedValues: any[]) => {
    // 如果选择了全选选项
    if (selectedValues.includes('SELECT_ALL')) {
      const isAllSelected = value && value.length === users.length;
      handleSelectAll(!isAllSelected);
      return;
    }

    // 过滤掉全选选项
    const userIds = selectedValues.filter(id => id !== 'SELECT_ALL');
    onChange(userIds);
  };

  return (
    <Select
      multiple
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      loading={loading}
      disabled={disabled}
      style={{ width: '100%', minWidth: 200 }}
      optionList={optionList}
      filter
      searchPlaceholder="搜索人员"
      emptyContent={loading ? <Spin /> : "暂无数据"}
    />
  );
};

export default UserSelector;
