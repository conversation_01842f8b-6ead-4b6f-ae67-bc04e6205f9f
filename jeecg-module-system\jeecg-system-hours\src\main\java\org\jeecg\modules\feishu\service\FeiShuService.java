package org.jeecg.modules.feishu.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.hours.entity.FsDepartment;
import org.jeecg.modules.hours.entity.FsDepartmentUser;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.service.IFsDepartmentService;
import org.jeecg.modules.hours.service.IFsDepartmentUserService;
import org.jeecg.modules.hours.service.IFsUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.lark.oapi.service.contact.v3.model.Department;
import com.lark.oapi.service.contact.v3.model.DepartmentEvent;
import com.lark.oapi.service.contact.v3.model.User;
import com.lark.oapi.service.contact.v3.model.UserEvent;

import lombok.extern.slf4j.Slf4j;

// 占位：如有需要可引入实际包路径
// import org.jeecg.modules.feishu.event.DepartmentEvent;
// import org.jeecg.modules.feishu.event.UserEvent;
// 如无自定义事件类，可用飞书SDK的 Department/DepartmentEvent/UserEvent 类型占位
// 或根据实际事件对象类型调整

@Slf4j
@Component
public class FeiShuService {

	@Autowired
    private IFsDepartmentService fsDepartmentService;
    @Autowired
    private IFsDepartmentUserService deptUserService;
    @Autowired
    private IFsUserService fsUserService;
	
    /**
     * 同步飞书全量部门及部门-用户关系到本地表
     * @param appId 飞书应用ID
     * @param appSecret 飞书应用密钥
     */
    public void syncAllDepartmentsAndUsers(String appId, String appSecret,String projectKey) {
        try {
            FeiShuDeptService deptService = FeiShuDeptService.instance(appId, appSecret);
            FeiShuUserService userService = FeiShuUserService.instance(appId, appSecret);

            // 递归获取所有部门（含根节点）
            List<Department> allDepts = new ArrayList<>();
            collectAllDepartments(deptService, "0", allDepts);

            // 映射为 FsDepartment 列表
            List<FsDepartment> fsDepartments = new ArrayList<>();
            Date now = new Date();
            // 1. 收集所有部门ID
            java.util.Set<String> allDeptIdSet = allDepts.stream().map(Department::getDepartmentId).collect(java.util.stream.Collectors.toSet());
            // 2. 批量查库
            java.util.List<FsDepartment> allExistList = fsDepartmentService.lambdaQuery().in(FsDepartment::getDeptId, allDeptIdSet).list();
            java.util.Map<String, Integer> allDeptIdToIsHour = allExistList.stream()
                .collect(java.util.stream.Collectors.toMap(FsDepartment::getDeptId, FsDepartment::getIsHour, (a, b) -> a));
            // 3. for循环内直接查Map
            for (Department d : allDepts) {
                FsDepartment fsDept = new FsDepartment();
                fsDept.setId(d.getDepartmentId());
                fsDept.setDeptId(d.getDepartmentId());
                fsDept.setName(d.getName());
                fsDept.setParentId(d.getParentDepartmentId());
                fsDept.setLeaderUserId(d.getLeaderUserId());
                fsDept.setSyncTime(now);
                Integer isHour = allDeptIdToIsHour.get(d.getDepartmentId());
                fsDept.setIsHour(isHour != null ? isHour : 0); // 默认值 0
                fsDepartments.add(fsDept);
            }
            fsDepartmentService.saveOrUpdateBatch(fsDepartments);
            log.info("同步飞书部门完成, 共保存: {} 条", fsDepartments.size());

            // 同步部门-用户关系
            List<FsDepartmentUser> deptUserList = new ArrayList<>();
            java.util.Set<String> relKeySet = new java.util.HashSet<>(); // 新增：用于去重
            for (Department d : allDepts) {
                try {
                    List<User> users = userService.getUserByDepartId(d.getDepartmentId());
                    if (users != null) {
                        for (User user : users) {
                            String key = d.getDepartmentId() + "#" + user.getUnionId();
                            if (relKeySet.contains(key)) {
                                continue; // 已存在，跳过
                            }
                            relKeySet.add(key); // 记录唯一性
                            FsDepartmentUser rel = new FsDepartmentUser();
                            rel.setDeptId(d.getDepartmentId());
                            rel.setUnionId(user.getUnionId());
                            rel.setSyncTime(now);
                            deptUserList.add(rel);
                        }
                    }
                } catch (Exception ex) {
                    log.error("同步部门用户失败: deptId={}, err={}", d.getDepartmentId(), ex.getMessage());
                }
            }
            log.info("deptUserList:{}",CollectionUtils.size(deptUserList));
            // --- 查库去重，补全 id 字段 ---
            // 1. 收集所有 (deptId, unionId) 组合
            java.util.Set<String> unionIdSet = new java.util.HashSet<>();
            java.util.Set<String> deptIdSet = new java.util.HashSet<>();
            for (FsDepartmentUser rel : deptUserList) {
                unionIdSet.add(rel.getUnionId());
                deptIdSet.add(rel.getDeptId());
            }
            // 2. 批量查库，获取已存在的 (deptId, unionId) -> id
            java.util.List<FsDepartmentUser> existList = deptUserService.lambdaQuery()
                .in(FsDepartmentUser::getDeptId, deptIdSet)
                .in(FsDepartmentUser::getUnionId, unionIdSet)
                .list();
            java.util.Map<String, String> existMap = new java.util.HashMap<>();
            for (FsDepartmentUser rel : existList) {
                existMap.put(rel.getDeptId() + "#" + rel.getUnionId(), rel.getId());
            }
            // 3. 补全 id 字段
            for (FsDepartmentUser rel : deptUserList) {
                String key = rel.getDeptId() + "#" + rel.getUnionId();
                if (existMap.containsKey(key)) {
                    rel.setId(existMap.get(key));
                }
            }
            deptUserService.saveOrUpdateBatch(deptUserList,300);
            log.info("同步部门-用户关系完成, 共保存: {} 条", deptUserList.size());
            // 更新用户的直属领导
            List<FsUser> fsUserList = fsUserService.list();
            List<FsUser> saveList = new ArrayList<FsUser>();
            if(CollectionUtils.isNotEmpty(fsUserList)) {
            	List<String> unionIds = fsUserList.stream().filter(item->StringUtils.isNotEmpty(item.getUnionId())).map(item->item.getUnionId()).collect(Collectors.toList());
            	Map<String,User> openUserMap = this.fsUserService.getFsOpenUser(projectKey, unionIds);
            	for (FsUser fsUser : fsUserList) {
            		if(StringUtils.isNotEmpty(fsUser.getUnionId())) {
            			fsUser.setLeaderUserId(openUserMap.get(fsUser.getUnionId()).getLeaderUserId());
            			saveList.add(fsUser);
            		}
				}
            	fsUserService.saveOrUpdateBatch(saveList);
            }
            log.info("用户直属领导同步完成, 共保存: {} 条", CollectionUtils.size(saveList));
            
        } catch (Exception e) {
            log.error("同步飞书部门或部门-用户关系失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 递归获取所有部门（含根节点）
     */
    private void collectAllDepartments(FeiShuDeptService deptService, String deptId, List<Department> result) throws Exception {
        Department dept = deptService.getDeptById(deptId);
        if(null == dept) {
        	return;
        }
        result.add(dept);
        List<Department> children = deptService.children(deptId);
        for (Department child : children) {
        	result.add(child);
            collectAllDepartments(deptService, child.getDepartmentId(), result);
        }
    }

    /**
     * 飞书部门新增事件处理
     */
    public void addDept(DepartmentEvent fsDept) {
        try {
            // 幂等：先查后增
            List<FsDepartment> exist = fsDepartmentService.lambdaQuery().eq(FsDepartment::getDeptId, fsDept.getDepartmentId()).list();
            if (exist != null && !exist.isEmpty()) {
                log.info("[FeiShuService] addDept: 部门已存在, deptId={}", fsDept.getDepartmentId());
                return;
            }
            FsDepartment entity = new FsDepartment();
            entity.setDeptId(fsDept.getDepartmentId());
            entity.setName(fsDept.getName());
            entity.setParentId(fsDept.getParentDepartmentId());
            entity.setLeaderUserId(fsDept.getLeaderUserId());
            entity.setSyncTime(new java.util.Date());
            // 新增时默认 isHour=0
            entity.setIsHour(0);
            fsDepartmentService.save(entity);
            log.info("[FeiShuService] addDept: 新增部门, deptId={}", fsDept.getDepartmentId());
        } catch (Exception e) {
            log.error("[FeiShuService] addDept error, deptId={}, err={}", fsDept.getDepartmentId(), e.getMessage(), e);
        }
    }

    /**
     * 飞书部门修改事件处理
     */
    public void updateDept(DepartmentEvent fsDept) {
        try {
            FsDepartment entity = fsDepartmentService.lambdaQuery().eq(FsDepartment::getDeptId, fsDept.getDepartmentId()).one();
            if (entity == null) {
                // 不存在则新增
                addDept(fsDept);
                return;
            }
            entity.setName(fsDept.getName());
            entity.setParentId(fsDept.getParentDepartmentId());
            entity.setLeaderUserId(fsDept.getLeaderUserId());
            entity.setSyncTime(new java.util.Date());
            // 保留原 isHour
            // entity.setIsHour(entity.getIsHour()); // 可省略
            fsDepartmentService.updateById(entity);
            log.info("[FeiShuService] updateDept: 更新部门, deptId={}", fsDept.getDepartmentId());
        } catch (Exception e) {
            log.error("[FeiShuService] updateDept error, deptId={}, err={}", fsDept.getDepartmentId(), e.getMessage(), e);
        }
    }

    
    /**
     * 飞书部门删除事件处理
     */
    public void deleteDept(DepartmentEvent fsDept) {
        try {
            boolean removed = fsDepartmentService.lambdaUpdate().eq(FsDepartment::getDeptId, fsDept.getDepartmentId()).remove();
            log.info("[FeiShuService] deleteDept: 删除部门, deptId={}, result={}", fsDept.getDepartmentId(), removed);
        } catch (Exception e) {
            log.error("[FeiShuService] deleteDept error, deptId={}, err={}", fsDept.getDepartmentId(), e.getMessage(), e);
        }
    }

    /**
     * 飞书用户新增事件处理（批量优化，避免循环查库）
     */
    public void addUser(UserEvent fsUser) {
        try {
            if (fsUser.getDepartmentIds() == null) return;
            String unionId = fsUser.getUnionId();
            String[] deptIds = fsUser.getDepartmentIds();
            java.util.List<FsDepartmentUser> deptUserList = new java.util.ArrayList<>();
            java.util.Date now = new java.util.Date();
            for (String deptId : deptIds) {
                FsDepartmentUser rel = new FsDepartmentUser();
                rel.setDeptId(deptId);
                rel.setUnionId(unionId);
                rel.setSyncTime(now);
                deptUserList.add(rel);
            }
            // 查库去重，补全 id 字段
            java.util.Set<String> unionIdSet = new java.util.HashSet<>();
            java.util.Set<String> deptIdSet = new java.util.HashSet<>();
            for (FsDepartmentUser rel : deptUserList) {
                unionIdSet.add(rel.getUnionId());
                deptIdSet.add(rel.getDeptId());
            }
            java.util.List<FsDepartmentUser> existList = deptUserService.lambdaQuery()
                .in(FsDepartmentUser::getDeptId, deptIdSet)
                .in(FsDepartmentUser::getUnionId, unionIdSet)
                .list();
            java.util.Map<String, String> existMap = new java.util.HashMap<>();
            for (FsDepartmentUser rel : existList) {
                existMap.put(rel.getDeptId() + "#" + rel.getUnionId(), rel.getId());
            }
            for (FsDepartmentUser rel : deptUserList) {
                String key = rel.getDeptId() + "#" + rel.getUnionId();
                if (existMap.containsKey(key)) {
                    rel.setId(existMap.get(key));
                }
            }
            deptUserService.saveOrUpdateBatch(deptUserList);
            for (FsDepartmentUser rel : deptUserList) {
                log.info("[FeiShuService] addUser: upsert部门用户关系, deptId={}, unionId={}", rel.getDeptId(), rel.getUnionId());
            }
        } catch (Exception e) {
            log.error("[FeiShuService] addUser error, unionId={}, err={}", fsUser.getUserId(), e.getMessage(), e);
        }
    }
    public void updateUser(UserEvent fsUser) {
        addUser(fsUser);
    }
    public void deleteUser(UserEvent fsUser) {
        try {
            boolean removed = deptUserService.lambdaUpdate().eq(FsDepartmentUser::getUnionId, fsUser.getUserId()).remove();
            log.info("[FeiShuService] deleteUser: 删除用户部门关系, unionId={}, result={}", fsUser.getUserId(), removed);
        } catch (Exception e) {
            log.error("[FeiShuService] deleteUser error, unionId={}, err={}", fsUser.getUserId(), e.getMessage(), e);
        }
    }
}
