package org.jeecg.modules.hours.service;

import java.util.Date;
import java.util.List;

import org.jeecg.modules.hours.entity.DeptProjectHourStatistics;
import org.jeecg.modules.hours.vo.DeptProjectHourReportResult;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 部门项目工时统计表
 * @Author: jeecg-boot
 * @Date: 2025-07-07
 * @Version: V1.0
 */
public interface IDeptProjectHourStatisticsService extends IService<DeptProjectHourStatistics> {

    /**
     * 获取部门项目工时统计报表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statPeriod 统计周期：daily-按日, weekly-按周, monthly-按月
     * @param departmentIds 部门ID列表（可选）
     * @param projectIds 项目ID列表（可选）
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @return 报表结果
     */
    DeptProjectHourReportResult getDeptProjectHourReport(Date startDate, Date endDate, String statPeriod,
                                                        List<String> departmentIds, List<String> projectIds, Integer isHoliday);

    /**
     * 执行工时统计任务（单日）
     *
     * @param statDate 统计日期
     * @param statPeriod 统计周期
     * @return 成功处理的记录数
     */
    int calculateHourStatistics(Date statDate, String statPeriod);

    /**
     * 批量执行工时统计任务（日期范围）
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statPeriod 统计周期
     * @return 成功处理的记录数
     */
    int batchCalculateHourStatistics(Date startDate, Date endDate, String statPeriod);

    /**
     * 重新计算指定日期的统计数据
     *
     * @param statDate 统计日期
     * @param statPeriod 统计周期
     * @return 成功处理的记录数
     */
    int recalculateHourStatistics(Date statDate, String statPeriod);

    /**
     * 获取统计任务的执行状态
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 任务执行状态列表
     */
    List<String> getStatisticsJobStatus(Date startDate, Date endDate);
}