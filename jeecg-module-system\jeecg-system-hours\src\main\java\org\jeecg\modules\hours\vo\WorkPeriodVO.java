package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工作周期VO
 *
 * <AUTHOR>
 * @date 2023-07-01
 */
@Data
@ApiModel(value = "工作周期VO", description = "工作周期VO")
public class WorkPeriodVO {
    
    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private String label;
    
    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private String value;
    
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDate;
    
    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDate;
} 