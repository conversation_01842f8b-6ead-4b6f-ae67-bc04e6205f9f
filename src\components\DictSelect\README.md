# DictSelect 数据字典选择组件

DictSelect 是一个基于 Semi Design 的 Select 组件封装的数据字典选择组件，用于从后端获取数据字典项并渲染成下拉选择框。

## 功能特点

- 自动从后端获取数据字典项
- 支持单选和多选
- 支持禁用状态
- 支持自定义样式
- 支持筛选搜索
- 支持加载完成回调
- **内置缓存机制，避免重复请求**
- **支持预加载功能**

## 安装

该组件是项目内部组件，无需额外安装。

## 使用方法

```tsx
import DictSelect from '@/components/DictSelect';

// 基本用法
<DictSelect dictCode="project_status" />

// 带默认值
<DictSelect dictCode="project_status" value="AO" />

// 多选
<DictSelect dictCode="project_status" multiple />

// 禁用状态
<DictSelect dictCode="project_status" disabled />

// 自定义样式
<DictSelect dictCode="project_status" style={{ width: '200px' }} />

// 加载完成回调
<DictSelect 
  dictCode="project_status" 
  onLoadFinish={(items) => console.log('加载完成', items)} 
/>

// 强制刷新缓存
<DictSelect dictCode="project_status" forceRefresh={true} />
```

## 缓存机制

组件内置了两级缓存机制，可以有效避免重复请求：

1. **组件级缓存**：使用全局静态变量缓存字典数据，相同的 `dictCode` 只会请求一次
2. **API 级缓存**：在 API 服务层也实现了缓存，即使多个组件同时请求相同的字典，也只会发起一次网络请求

默认情况下，组件会优先使用缓存数据。如果需要强制刷新，可以设置 `forceRefresh` 属性为 `true`。

## 预加载功能

如果你知道页面中会用到某些字典数据，可以使用预加载功能提前加载：

```tsx
import { preloadDictItems } from '@/components/DictSelect';

// 在组件初始化时预加载
useEffect(() => {
  preloadDictItems(['project_status', 'user_role', 'task_type']);
}, []);
```

这样可以避免用户看到多个加载中的状态，提升用户体验。

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| dictCode | 字典代码 | string | - |
| value | 选择框的值 | string \| string[] | - |
| onChange | 值变化时的回调函数 | (value: string \| string[]) => void | - |
| disabled | 是否禁用 | boolean | false |
| placeholder | 占位符文本 | string | '请选择' |
| multiple | 是否支持多选 | boolean | false |
| showClear | 是否显示清除按钮 | string | true |
| style | 选择框样式 | React.CSSProperties | - |
| showSelectAll | 是否显示全选选项（仅在multiple为true时有效） | boolean | false |
| onLoadFinish | 加载完成后的回调 | (items: DictItem[]) => void | - |
| forceRefresh | 是否强制刷新缓存 | boolean | false |

### DictItem 类型

```typescript
interface DictItem {
  value: string;  // 选项值
  text: string;   // 选项文本
  title?: string; // 选项提示
  disabled?: boolean; // 是否禁用
}
```

### 导出的函数

| 函数名 | 说明 | 参数 | 返回值 |
| --- | --- | --- | --- |
| preloadDictItems | 预加载字典数据 | dictCodes: string[] | Promise<void> |

## 示例

### 基本用法

```tsx
import React, { useState } from 'react';
import DictSelect from '@/components/DictSelect';

const Example = () => {
  const [value, setValue] = useState<string>('');
  
  return (
    <DictSelect 
      dictCode="project_status" 
      value={value}
      onChange={(val) => setValue(val as string)}
    />
  );
};
```

### 在表单中使用

```tsx
import React from 'react';
import { Form } from '@douyinfe/semi-ui';
import DictSelect from '@/components/DictSelect';

const FormExample = () => {
  return (
    <Form>
      <Form.Select
        field="projectStatus"
        label="项目状态"
      >
        <DictSelect dictCode="project_status" />
      </Form.Select>
    </Form>
  );
};
```

### 在 Form.Slot 中使用

```tsx
import React, { useState } from 'react';
import { Form } from '@douyinfe/semi-ui';
import DictSelect from '@/components/DictSelect';

const FormSlotExample = () => {
  const [value, setValue] = useState<string>('');
  
  return (
    <Form>
      <Form.Slot label="项目状态">
        <DictSelect 
          dictCode="project_status" 
          value={value}
          onChange={(val) => setValue(val as string)}
        />
      </Form.Slot>
    </Form>
  );
};
```

### 预加载多个字典

```tsx
import React, { useEffect } from 'react';
import { preloadDictItems } from '@/components/DictSelect';

const PageComponent = () => {
  useEffect(() => {
    // 页面加载时预加载所有需要的字典
    preloadDictItems([
      'project_status',
      'user_role',
      'task_type',
      'priority_level'
    ]);
  }, []);
  
  // 页面内容...
};
```

## 注意事项

1. 确保后端接口 `/dict/get` 可用且返回格式正确
2. 字典代码 `dictCode` 必须与后端定义的字典代码一致
3. 组件会在挂载时自动获取字典数据，无需手动调用
4. 如果需要在多个地方使用相同的字典数据，建议使用预加载功能 