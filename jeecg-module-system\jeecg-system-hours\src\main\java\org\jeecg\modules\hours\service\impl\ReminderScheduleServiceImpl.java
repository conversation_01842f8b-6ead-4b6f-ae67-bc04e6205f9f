package org.jeecg.modules.hours.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.hours.entity.FsSysReminderConfig;
import org.jeecg.modules.hours.job.WorkHourReminderJob;
import org.jeecg.modules.hours.service.IReminderScheduleService;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 提醒定时任务调度服务实现类
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Slf4j
@Service
public class ReminderScheduleServiceImpl implements IReminderScheduleService {

    @Autowired
    private Scheduler scheduler;

    @Override
    public boolean startReminderJob(FsSysReminderConfig reminderConfig) {
        log.info("开始启动提醒定时任务，配置ID: {}, 项目: {}, 类型: {}", 
                reminderConfig.getId(), reminderConfig.getProjectKey(), reminderConfig.getReminderType());
        
        try {
            String jobName = generateJobName(reminderConfig);
            String jobGroup = generateJobGroup(reminderConfig);
            
            // 检查任务是否已存在
            if (isJobExists(reminderConfig)) {
                log.info("定时任务已存在，将更新任务配置: {}", jobName);
                return updateReminderJob(reminderConfig);
            }
            
            // 创建JobDetail
            JobDetail jobDetail = JobBuilder.newJob(WorkHourReminderJob.class)
                    .withIdentity(jobName, jobGroup)
                    .withDescription("工时登记提醒任务 - " + reminderConfig.getRemark())
                    .usingJobData("configId", reminderConfig.getId().toString())
                    .usingJobData("projectKey", reminderConfig.getProjectKey())
                    .usingJobData("reminderType", reminderConfig.getReminderType())
                    .usingJobData("repeatMode", reminderConfig.getRepeatMode())
                    .build();
            
            // 生成Cron表达式
            String cronExpression = generateCronExpression(reminderConfig);
            log.info("生成的Cron表达式: {}", cronExpression);
            
            // 创建Trigger
            CronTrigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(jobName + "_trigger", jobGroup)
                    .withDescription("工时登记提醒触发器")
                    .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression))
                    .build();
            
            // 调度任务
            scheduler.scheduleJob(jobDetail, trigger);
            log.info("成功启动提醒定时任务: {}", jobName);
            
            return true;
        } catch (Exception e) {
            log.error("启动提醒定时任务失败，配置ID: {}", reminderConfig.getId(), e);
            return false;
        }
    }

    @Override
    public boolean stopReminderJob(FsSysReminderConfig reminderConfig) {
        log.info("开始停止提醒定时任务，配置ID: {}", reminderConfig.getId());
        
        try {
            String jobName = generateJobName(reminderConfig);
            String jobGroup = generateJobGroup(reminderConfig);
            
            JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
            
            if (scheduler.checkExists(jobKey)) {
                boolean result = scheduler.deleteJob(jobKey);
                if (result) {
                    log.info("成功停止提醒定时任务: {}", jobName);
                } else {
                    log.warn("停止提醒定时任务失败: {}", jobName);
                }
                return result;
            } else {
                log.info("提醒定时任务不存在，无需停止: {}", jobName);
                return true;
            }
        } catch (Exception e) {
            log.error("停止提醒定时任务失败，配置ID: {}", reminderConfig.getId(), e);
            return false;
        }
    }

    @Override
    public boolean updateReminderJob(FsSysReminderConfig reminderConfig) {
        log.info("开始更新提醒定时任务，配置ID: {}", reminderConfig.getId());
        
        try {
            // 先停止现有任务
            if (!stopReminderJob(reminderConfig)) {
                log.error("停止现有任务失败，无法更新");
                return false;
            }
            
            // 重新启动任务
            return startReminderJob(reminderConfig);
        } catch (Exception e) {
            log.error("更新提醒定时任务失败，配置ID: {}", reminderConfig.getId(), e);
            return false;
        }
    }

    @Override
    public boolean isJobExists(FsSysReminderConfig reminderConfig) {
        try {
            String jobName = generateJobName(reminderConfig);
            String jobGroup = generateJobGroup(reminderConfig);
            JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
            return scheduler.checkExists(jobKey);
        } catch (Exception e) {
            log.error("检查任务是否存在时发生异常", e);
            return false;
        }
    }

    @Override
    public String generateJobName(FsSysReminderConfig reminderConfig) {
        return "reminder_job_" + reminderConfig.getId();
    }

    @Override
    public String generateJobGroup(FsSysReminderConfig reminderConfig) {
        return "reminder_group_" + reminderConfig.getProjectKey();
    }

    @Override
    public String generateCronExpression(FsSysReminderConfig reminderConfig) {
        String reminderTime = reminderConfig.getReminderTime();
        String[] timeParts = reminderTime.split(":");
        int hour = Integer.parseInt(timeParts[0]);
        int minute = Integer.parseInt(timeParts[1]);
        
        String reminderType = reminderConfig.getReminderType();
        String reminderDay = reminderConfig.getReminderDay();
        
        switch (reminderType) {
            case "daily":
                // 每天提醒：0 {分钟} {小时} * * ?
                return String.format("0 %d %d * * ?", minute, hour);
                
            case "weekly":
                // 每周提醒：0 {分钟} {小时} ? * {星期}
                String dayOfWeek = convertDayOfWeek(reminderDay);
                return String.format("0 %d %d ? * %s", minute, hour, dayOfWeek);
                
            case "monthly":
                // 每月提醒：0 {分钟} {小时} {日期} * ?
                if ("last_day".equals(reminderDay)) {
                    // 月末最后一天：0 {分钟} {小时} L * ?
                    return String.format("0 %d %d L * ?", minute, hour);
                } else {
                    // 具体日期：0 {分钟} {小时} {日期} * ?
                    return String.format("0 %d %d %s * ?", minute, hour, reminderDay);
                }
                
            default:
                throw new IllegalArgumentException("不支持的提醒类型: " + reminderType);
        }
    }
    
    /**
     * 转换星期几的表示方式
     * 
     * @param dayOfWeek 星期几（monday-sunday）
     * @return Cron表达式中的星期几表示
     */
    private String convertDayOfWeek(String dayOfWeek) {
        switch (dayOfWeek.toLowerCase()) {
            case "monday": return "MON";
            case "tuesday": return "TUE";
            case "wednesday": return "WED";
            case "thursday": return "THU";
            case "friday": return "FRI";
            case "saturday": return "SAT";
            case "sunday": return "SUN";
            default:
                throw new IllegalArgumentException("不支持的星期几: " + dayOfWeek);
        }
    }
}
