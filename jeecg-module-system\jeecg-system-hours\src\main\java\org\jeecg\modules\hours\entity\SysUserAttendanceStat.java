package org.jeecg.modules.hours.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "用户考勤统计", description = "用户每日考勤统计表")
@Data
@Accessors(chain = true)
@TableName("sys_user_attendance_stat")
public class SysUserAttendanceStat {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Excel(name = "用户ID", width = 15)
    @TableField("user_id")
    private String userId;

    @Excel(name = "UnionID", width = 20)
    @TableField("union_id")
    private String unionId;

    @Excel(name = "统计日期", width = 20, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField("stat_date")
    private Date statDate;

    @Excel(name = "工时数", width = 10)
    @TableField("work_hours")
    private BigDecimal workHours;

    @Excel(name = "加班工时", width = 10)
    @TableField("over_work_hours")
    private BigDecimal overWorkHours;

    
    @Excel(name = "应出勤时长", width = 10)
    @TableField("required_attend_hours")
    private BigDecimal requiredAttendHours;

    @Excel(name = "实际出勤时长", width = 10)
    @TableField("real_attend_hours")
    private BigDecimal realAttendHours;

    @Excel(name = "加班总时长", width = 10)
    @TableField("over_total_work_hours")
    private BigDecimal overTotalWorkHours;

    @Excel(name = "班次", width = 15)
    @TableField(value = "shift")
    private String shift;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

}