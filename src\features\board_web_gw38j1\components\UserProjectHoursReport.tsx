import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Table,
  Button,
  Space,
  Toast,
  Card,
  Select,
  Spin,
  Typography,
  Row,
  Col
} from '@douyinfe/semi-ui';
import { IconDownload, IconRefresh2 } from '@douyinfe/semi-icons';
import type { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import DateRangePicker from './DateRangePicker';
import {
  getAllUsers,
  getAllProjects,
  getUserProjectHourReport,
  exportUserProjectHourReport,
  type User,
  type Project,
  type UserProjectHourReportParams
} from '../../../api/report';

const { Text } = Typography;
const { Option } = Select;

// 从 API 模块导入类型定义
import type {
  ProjectColumn,
  UserHourData,
  ReportSummary,
  UserProjectHourReportResult as ReportResult
} from '../../../api/report';

interface ReportFilters {
  dateRange: [string | undefined, string | undefined];
  userIds: string[];
  projectIds: string[];
  isHoliday: number | undefined;
}

const UserProjectHoursReport: React.FC = () => {
  const [reportData, setReportData] = useState<ReportResult | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: [undefined, undefined],
    userIds: [],
    projectIds: [],
    isHoliday: undefined
  });

  // 基础数据
  const [users, setUsers] = useState<User[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [dataLoading, setDataLoading] = useState(false);

  useEffect(() => {
    // 设置默认日期为当前月
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    setFilters(prev => ({
      ...prev,
      dateRange: [
        startOfMonth.toISOString().split('T')[0],
        endOfMonth.toISOString().split('T')[0]
      ]
    }));

    // 加载基础数据
    loadInitialData();
  }, []);

  // 加载基础数据
  const loadInitialData = async () => {
    setDataLoading(true);
    try {
      const [userData, projectData] = await Promise.all([
        getAllUsers(),
        getAllProjects()
      ]);
      setUsers(userData);
      setProjects(projectData);
    } catch (error) {
      console.error('加载基础数据失败:', error);
      Toast.error('加载基础数据失败');
    } finally {
      setDataLoading(false);
    }
  };

  // 获取报表数据
  const fetchReportData = useCallback(async () => {
    if (!filters.dateRange[0] || !filters.dateRange[1]) {
      Toast.warning('请选择查询日期范围');
      return;
    }

    if (loading) {
      return;
    }

    setLoading(true);
    try {
      const params: UserProjectHourReportParams = {
        startDate: filters.dateRange[0]!,
        endDate: filters.dateRange[1]!,
        statPeriod: 'daily',
        ...(filters.isHoliday !== undefined && { isHoliday: filters.isHoliday }),
        ...(filters.userIds.length > 0 && { userIds: filters.userIds.join(',') }),
        ...(filters.projectIds.length > 0 && { projectIds: filters.projectIds.join(',') })
      };

      const result = await getUserProjectHourReport(params);
      setReportData(result);
      Toast.success('报表数据加载成功');
    } catch (error) {
      console.error('获取报表数据失败:', error);
      Toast.error('获取报表数据失败');
    } finally {
      setLoading(false);
    }
  }, [filters, loading]);

  // 导出报表
  const handleExport = useCallback(async () => {
    if (!reportData) {
      Toast.warning('请先查询报表数据');
      return;
    }

    try {
      const params: UserProjectHourReportParams = {
        startDate: filters.dateRange[0]!,
        endDate: filters.dateRange[1]!,
        statPeriod: 'daily',
        ...(filters.isHoliday !== undefined && { isHoliday: filters.isHoliday }),
        ...(filters.userIds.length > 0 && { userIds: filters.userIds.join(',') }),
        ...(filters.projectIds.length > 0 && { projectIds: filters.projectIds.join(',') })
      };

      await exportUserProjectHourReport(params);
      Toast.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      Toast.error('导出失败');
    }
  }, [filters, reportData]);

  // 构建表格列
  const buildTableColumns = useMemo(() => {
    if (!reportData?.projectColumns) return [];

    const columns: ColumnProps<UserHourData>[] = [
      {
        title: '姓名',
        dataIndex: 'userName',
        key: 'userName',
        fixed: 'left',
        width: 120,
        render: (text: string, record: UserHourData) => {
          return record.userId === 'total' ? <Text strong>{text}</Text> : text;
        },
      },
      {
        title: '工号',
        dataIndex: 'userCode',
        key: 'userCode',
        fixed: 'left',
        width: 100,
        render: (text: string, record: UserHourData) => {
          return record.userId === 'total' ? <Text strong>{text}</Text> : text;
        },
      },
      {
        title: '部门',
        dataIndex: 'departmentName',
        key: 'departmentName',
        fixed: 'left',
        width: 120,
        render: (text: string, record: UserHourData) => {
          return record.userId === 'total' ? <Text strong>{text}</Text> : text;
        },
      },
    ];

    // 添加动态项目列
    reportData.projectColumns.forEach((projectColumn: ProjectColumn) => {
      columns.push({
        title: (
          <div style={{ textAlign: 'center' }}>
            <div>{projectColumn.projectName}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {projectColumn.projectCode}
            </div>
          </div>
        ),
        dataIndex: ['projectHours', projectColumn.columnKey],
        key: projectColumn.columnKey,
        width: 100,
        align: 'center',
        render: (hours: number, record: UserHourData) => {
          const value = hours || 0;
          return record.userId === 'total' ? (
            <Text strong>{value.toFixed(1)}</Text>
          ) : (
            value.toFixed(1)
          );
        },
      });
    });

    // 添加小计列
    columns.push({
      title: '小计',
      dataIndex: 'totalHours',
      key: 'totalHours',
      fixed: 'right',
      width: 100,
      align: 'center',
      render: (hours: number, record: UserHourData) => {
        const value = hours || 0;
        return record.userId === 'total' ? (
          <Text strong style={{ color: '#3370ff' }}>{value.toFixed(1)}</Text>
        ) : (
          <Text strong>{value.toFixed(1)}</Text>
        );
      },
    });

    return columns;
  }, [reportData]);


  // 准备表格数据
  const tableData = useMemo(() => {
    if (!reportData) return [];

    const data = [...reportData.userHourData];
    if (reportData.totalRow) {
      data.push(reportData.totalRow);
    }

    return data.map((item, index) => ({
      ...item,
      key: item.userId || index,
    }));
  }, [reportData]);

  return (
    <div style={{ padding: '24px' }}>
      {/* 筛选条件 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[12, 8]}>
          <Col span={7}>
            <Space vertical style={{ width: '100%' }}>
              <DateRangePicker
                value={filters.dateRange}
                onChange={(dateRange) => setFilters(prev => ({ ...prev, dateRange }))}
                dimension="day"
                placeholder="请选择日期范围"
                style={{ width: '100%' }}
              />
            </Space>
          </Col>
          <Col span={4}>
            <Space vertical style={{ width: '100%' }}>
              <Select
                multiple
                value={filters.userIds}
                onChange={(value) => setFilters(prev => ({ ...prev, userIds: value }))}
                style={{ width: '100%' }}
                placeholder="选择人员"
                loading={dataLoading}
                maxTagCount={1}
                showClear
                filter
              >
                {users.map(user => (
                  <Select.Option key={user.userKey} value={user.userKey}>
                    {user.name}
                  </Select.Option>
                ))}
              </Select>
            </Space>
          </Col>
          <Col span={4}>
            <Space vertical style={{ width: '100%' }}>
              <Select
                multiple
                value={filters.projectIds}
                onChange={(value) => setFilters(prev => ({ ...prev, projectIds: value }))}
                style={{ width: '100%' }}
                placeholder="选择项目"
                loading={dataLoading}
                maxTagCount={1}
                showClear
                filter
              >
                {projects.map(project => (
                  <Select.Option key={project.id} value={project.id}>
                    {project.projectName}
                  </Select.Option>
                ))}
              </Select>
            </Space>
          </Col>
          <Col span={3}>
            <Space vertical style={{ width: '100%' }}>
              <Select
                value={filters.isHoliday}
                onChange={(value) => setFilters(prev => ({ ...prev, isHoliday: value }))}
                style={{ width: '100%' }}
                placeholder="请选择"
                showClear
              >
                <Select.Option value={1}>统计假日</Select.Option>
                <Select.Option value={0}>不统计假日</Select.Option>
              </Select>
            </Space>
          </Col>
          <Col span={6}>
            <Space>
              <Button
                theme="solid"
                type="primary"
                onClick={fetchReportData}
                loading={loading}
              >
                查询
              </Button>
              <Button
                type="secondary"
                onClick={handleExport}
                disabled={!reportData || loading}
                icon={<IconDownload />}
              >
                导出Excel
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/*/!* 统计摘要 *!/*/}
      {/*{reportData?.summary && (*/}
      {/*  <Card style={{ marginBottom: '16px' }}>*/}
      {/*    <Row gutter={16}>*/}
      {/*      <Col span={6}>*/}
      {/*        <Text type="tertiary">统计日期范围</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.dateRange}</Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={4}>*/}
      {/*        <Text type="tertiary">总工时</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong style={{ color: '#1890ff', fontSize: '18px' }}>*/}
      {/*          {reportData.summary.totalHours?.toFixed(1) || 0}小时*/}
      {/*        </Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={4}>*/}
      {/*        <Text type="tertiary">人员数量</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.userCount || 0}人</Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={4}>*/}
      {/*        <Text type="tertiary">项目数量</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.projectCount || 0}个</Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={4}>*/}
      {/*        <Text type="tertiary">部门数量</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.deptCount || 0}个</Text>*/}
      {/*      </Col>*/}
      {/*    </Row>*/}
      {/*  </Card>*/}
      {/*)}*/}

      {/* 报表表格 */}
      <Card>
        <Spin spinning={loading}>
          <Table
            columns={buildTableColumns}
            dataSource={tableData}
            pagination={false}
            bordered
            size="small"
            scroll={{ x: 'max-content' }}
            rowKey={(record) => record.userId}
            rowClassName={(record) =>
              record.userId === 'total' ? 'total-row' : ''
            }
            empty={
              <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
                <Text type="tertiary">暂无数据</Text>
              </div>
            }
          />
        </Spin>
      </Card>

      <style>
        {`
          .total-row {
            background-color: #f6f8fa !important;
            font-weight: bold;
          }
          .total-row td {
            border-top: 2px solid #d9d9d9 !important;
          }
        `}
      </style>
    </div>
  );
};

export default UserProjectHoursReport;
