package org.jeecg.modules.hours.service.impl;

import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jeecg.modules.hours.service.IDeptProjectHourExportService;
import org.jeecg.modules.hours.service.IDeptProjectHourStatisticsService;
import org.jeecg.modules.hours.vo.DeptProjectHourReportResult;
import org.jeecg.modules.hours.vo.DeptProjectHourReportVO;
import org.jeecg.modules.hours.vo.ProjectColumnVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 部门项目工时统计报表导出服务实现
 * @Author: jeecg-boot
 * @Date: 2025-07-07
 * @Version: V1.0
 */
@Service
@Slf4j
public class DeptProjectHourExportServiceImpl implements IDeptProjectHourExportService {

    @Autowired
    private IDeptProjectHourStatisticsService deptProjectHourStatisticsService;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public void exportDeptProjectHourReportToExcel(Date startDate, Date endDate, String statPeriod,
                                                  List<String> departmentIds, List<String> projectIds, Integer isHoliday,
                                                  HttpServletResponse response) throws Exception {
        log.info("开始导出部门项目工时统计报表Excel, startDate={}, endDate={}, isHoliday={}", startDate, endDate, isHoliday);

        try {
            // 1. 获取报表数据
            DeptProjectHourReportResult reportData = deptProjectHourStatisticsService.getDeptProjectHourReport(
                    startDate, endDate, statPeriod, departmentIds, projectIds, isHoliday);

            // 2. 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("部门项目工时统计");

            // 3. 创建样式
            CellStyle titleStyle = createTitleStyle(workbook);
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            CellStyle totalStyle = createTotalStyle(workbook);

            // 4. 创建表格内容
            int rowIndex = 0;

            // 标题行
            rowIndex = createTitleRow(sheet, titleStyle, reportData.getSummary(), isHoliday, rowIndex);

            // 表头行（双行表头）
            rowIndex = createHeaderRows(sheet, headerStyle, reportData.getProjectColumns(), rowIndex);

            // 数据行
            rowIndex = createDataRows(sheet, dataStyle, totalStyle, reportData, rowIndex);

            // 5. 自动调整列宽
            autoSizeColumns(sheet, reportData.getProjectColumns().size() + 2);

            // 6. 设置响应头并写入Excel
            String fileName = generateFileName(startDate, endDate);
            setResponseHeaders(response, fileName);

            workbook.write(response.getOutputStream());
            workbook.close();

            log.info("部门项目工时统计报表Excel导出完成");

        } catch (Exception e) {
            log.error("导出部门项目工时统计报表Excel失败", e);
            throw new Exception("导出Excel失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建标题行
     */
    private int createTitleRow(Sheet sheet, CellStyle titleStyle, 
                               DeptProjectHourReportResult.ReportSummaryVO summary, Integer isHoliday, int rowIndex) {
        Row titleRow = sheet.createRow(rowIndex++);
        Cell titleCell = titleRow.createCell(0);
        
        String holidayInfo = "";
        if (isHoliday != null) {
            holidayInfo = isHoliday == 1 ? "(统假日)" : "(不统假日)";
        }
        
        String title = String.format("部门/项目工时统计报表%s - %s", 
                holidayInfo, summary != null ? summary.getDateRange() : "");
        titleCell.setCellValue(title);
        titleCell.setCellStyle(titleStyle);

        // 空行
        sheet.createRow(rowIndex++);

        // 统计摘要行
//        if (summary != null) {
//            Row summaryRow = sheet.createRow(rowIndex++);
//            summaryRow.createCell(0).setCellValue("统计摘要：");
//            summaryRow.createCell(1).setCellValue(
//                String.format("总工时: %s小时 | 部门数: %d个 | 项目数: %d个 | 参与人数: %d人",
//                    summary.getTotalHours(), summary.getDeptCount(),
//                    summary.getProjectCount(), summary.getUserCount())
//            );
//
//            // 空行
//            sheet.createRow(rowIndex++);
//        }

        return rowIndex;
    }

    /**
     * 创建表头行（双行表头）
     */
    private int createHeaderRows(Sheet sheet, CellStyle headerStyle, 
                                List<ProjectColumnVO> projectColumns, int rowIndex) {
        // 第一行表头 - 项目名称
        Row headerRow1 = sheet.createRow(rowIndex++);
        
        // 部门列
        Cell deptCell = headerRow1.createCell(0);
        deptCell.setCellValue("部门");
        deptCell.setCellStyle(headerStyle);

        // 项目列
        int colIndex = 1;
        for (ProjectColumnVO project : projectColumns) {
            Cell cell = headerRow1.createCell(colIndex++);
            cell.setCellValue(project.getProjectName());
            cell.setCellStyle(headerStyle);
        }

        // 小计列
        Cell totalCell = headerRow1.createCell(colIndex);
        totalCell.setCellValue("小计");
        totalCell.setCellStyle(headerStyle);

        // 第二行表头 - 项目编码
        Row headerRow2 = sheet.createRow(rowIndex++);
        
        // 部门列（合并）
        Cell deptCell2 = headerRow2.createCell(0);
        deptCell2.setCellValue("");
        deptCell2.setCellStyle(headerStyle);
        
        // 合并部门列的两行
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 1, 0, 0));

        // 项目编码列
        colIndex = 1;
        for (ProjectColumnVO project : projectColumns) {
            Cell cell = headerRow2.createCell(colIndex++);
            cell.setCellValue(project.getProjectCode());
            cell.setCellStyle(headerStyle);
        }

        // 小计列（合并）
        Cell totalCell2 = headerRow2.createCell(colIndex);
        totalCell2.setCellValue("");
        totalCell2.setCellStyle(headerStyle);
        
        // 合并小计列的两行
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 2, rowIndex - 1, colIndex, colIndex));

        return rowIndex;
    }

    /**
     * 创建数据行
     */
    private int createDataRows(Sheet sheet, CellStyle dataStyle, CellStyle totalStyle,
                              DeptProjectHourReportResult reportData, int rowIndex) {
        List<ProjectColumnVO> projectColumns = reportData.getProjectColumns();
        
        // 部门数据行
        for (DeptProjectHourReportVO deptData : reportData.getDeptHourData()) {
            Row dataRow = sheet.createRow(rowIndex++);
            
            // 部门名称
            Cell deptCell = dataRow.createCell(0);
            deptCell.setCellValue(deptData.getDepartmentName());
            deptCell.setCellStyle(dataStyle);

            // 各项目工时
            int colIndex = 1;
            for (ProjectColumnVO project : projectColumns) {
                Cell cell = dataRow.createCell(colIndex++);
                Number hours = deptData.getProjectHours().get(project.getColumnKey());
                if (hours != null && hours.doubleValue() > 0) {
                    cell.setCellValue(hours.doubleValue());
                } else {
                    cell.setCellValue("");
                }
                cell.setCellStyle(dataStyle);
            }

            // 小计
            Cell totalCell = dataRow.createCell(colIndex);
            totalCell.setCellValue(deptData.getTotalHours().doubleValue());
            totalCell.setCellStyle(dataStyle);
        }

        // 总计行
        if (reportData.getTotalRow() != null) {
            Row totalRow = sheet.createRow(rowIndex++);
            
            // 总计标识
            Cell totalLabelCell = totalRow.createCell(0);
            totalLabelCell.setCellValue("总计");
            totalLabelCell.setCellStyle(totalStyle);

            // 各项目总计
            int colIndex = 1;
            for (ProjectColumnVO project : projectColumns) {
                Cell cell = totalRow.createCell(colIndex++);
                Number hours = reportData.getTotalRow().getProjectHours().get(project.getColumnKey());
                if (hours != null && hours.doubleValue() > 0) {
                    cell.setCellValue(hours.doubleValue());
                } else {
                    cell.setCellValue(0);
                }
                cell.setCellStyle(totalStyle);
            }

            // 总计的小计
            Cell grandTotalCell = totalRow.createCell(colIndex);
            grandTotalCell.setCellValue(reportData.getTotalRow().getTotalHours().doubleValue());
            grandTotalCell.setCellStyle(totalStyle);
        }

        return rowIndex;
    }

    /**
     * 自动调整列宽
     */
    private void autoSizeColumns(Sheet sheet, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            sheet.autoSizeColumn(i);
            // 设置最小宽度
            int currentWidth = sheet.getColumnWidth(i);
            if (currentWidth < 2000) {
                sheet.setColumnWidth(i, 2000);
            }
            // 设置最大宽度
            if (currentWidth > 8000) {
                sheet.setColumnWidth(i, 8000);
            }
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(Date startDate, Date endDate) {
        String startStr = DATE_FORMAT.format(startDate);
        String endStr = DATE_FORMAT.format(endDate);
        return String.format("部门项目工时统计_%s_%s", startStr, endStr);
    }

    /**
     * 设置响应头
     */
    private void setResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        
        try {
            String encodedFileName = URLEncoder.encode(fileName + ".xlsx", "UTF-8");
            response.setHeader("Content-Disposition", 
                "attachment; filename=\"" + encodedFileName + "\"");
        } catch (Exception e) {
            response.setHeader("Content-Disposition", 
                "attachment; filename=\"dept_project_hour_report.xlsx\"");
        }
    }

    /**
     * 创建标题样式
     */
    private CellStyle createTitleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 16);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        return style;
    }

    /**
     * 创建总计行样式
     */
    private CellStyle createTotalStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 设置边框
        style.setBorderTop(BorderStyle.MEDIUM);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // 设置背景色
        style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        return style;
    }
}