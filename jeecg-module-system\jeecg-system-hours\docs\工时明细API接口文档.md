# 工时明细API接口文档

## 接口概述

新增工时明细查询接口，用于根据工作内容的层级参数查询相关的工时记录明细信息。

## 接口信息

### 基本信息
- **接口路径**: `/hapi/hours/hourRecord/detail`
- **请求方法**: `GET`
- **接口描述**: 根据工作内容参数查询工时记录明细
- **权限要求**: 需要有效的用户认证和项目权限

### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| workItemId | String | 否 | 工作项ID | 672d784c9a71acdb4f8180ef |
| instanceId | String | 否 | 实例/迭代ID | 616578422 |
| nodeId | String | 否 | 节点ID | state_0 |
| taskId | String | 否 | 任务ID | 616581875 |

**注意事项：**
- 至少需要提供一个参数
- 参数按层级关系递进，支持部分参数查询
- 项目Key从请求头自动获取

### 请求示例

#### 完整参数查询
```http
GET /hapi/hours/hourRecord/detail?workItemId=672d784c9a71acdb4f8180ef&instanceId=616578422&nodeId=state_0&taskId=616581875
```

#### 只查询工作项级别
```http
GET /hapi/hours/hourRecord/detail?workItemId=672d784c9a71acdb4f8180ef
```

#### 查询到节点级别
```http
GET /hapi/hours/hourRecord/detail?workItemId=672d784c9a71acdb4f8180ef&instanceId=616578422&nodeId=state_0
```

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    "workContent": "底层/阶段1",
    "totalHours": 7.00,
    "totalDays": 0.88,
    "records": [
      {
        "id": 1,
        "userId": "user123",
        "userName": "蒋莉芬",
        "workDate": "2025-05-10",
        "hours": 5.0,
        "description": "",
        "projectId": "project123",
        "workItemId": "672d784c9a71acdb4f8180ef",
        "workItemName": "底层",
        "instanceId": "616578422",
        "instanceName": "阶段1",
        "nodeId": "state_0",
        "nodeName": "开发",
        "taskId": "616581875",
        "taskName": "功能实现",
        "status": 1,
        "createdAt": "2025-05-11T09:09:00",
        "updatedAt": "2025-05-11T09:09:00"
      },
      {
        "id": 2,
        "userId": "user123",
        "userName": "蒋莉芬",
        "workDate": "2025-05-10",
        "hours": 2.0,
        "description": "",
        "projectId": "project123",
        "workItemId": "672d784c9a71acdb4f8180ef",
        "workItemName": "底层",
        "instanceId": "616578422",
        "instanceName": "阶段1",
        "nodeId": "state_0",
        "nodeName": "开发",
        "taskId": "616581875",
        "taskName": "功能实现",
        "status": 1,
        "createdAt": "2025-05-11T09:09:00",
        "updatedAt": "2025-05-11T09:09:00"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

#### 错误响应
```json
{
  "success": false,
  "message": "未提供有效的项目Key",
  "code": 500,
  "result": null,
  "timestamp": 1640995200000
}
```

### 响应字段说明

#### 主要字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| workContent | String | 工作内容路径，格式：工作项/实例/节点/任务 |
| totalHours | BigDecimal | 总工时（小时） |
| totalDays | BigDecimal | 总人天（根据配置的每日工时计算） |
| records | Array | 工时记录列表 |

#### 工时记录字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Long | 记录ID |
| userId | String | 用户ID |
| userName | String | 用户姓名 |
| workDate | String | 工作日期 |
| hours | BigDecimal | 登记工时 |
| description | String | 工作描述 |
| status | Integer | 状态（0:待提交, 1:审批中, 2:已通过, 3:已驳回） |
| createdAt | String | 创建时间 |
| updatedAt | String | 更新时间 |

## 业务逻辑

### 查询逻辑
1. **参数验证**: 检查项目Key和至少一个工作内容参数
2. **条件构建**: 根据传入参数构建数据库查询条件
3. **数据查询**: 查询匹配条件的工时记录
4. **统计计算**: 计算总工时和总人天
5. **路径构建**: 根据记录信息构建工作内容路径

### 工作内容路径构建规则
- 按层级顺序：工作项 → 实例 → 节点 → 任务
- 使用 "/" 分隔各级名称
- 跳过空值，只显示有效层级
- 示例：`"底层/阶段1/开发/功能实现"`

### 人天计算
- 从系统配置获取每日标准工时（默认8小时）
- 公式：总人天 = 总工时 ÷ 每日标准工时
- 保留2位小数，四舍五入

## 错误处理

### 常见错误
1. **项目Key缺失**: 请求头中未提供有效的项目Key
2. **参数不足**: 未提供任何工作内容参数
3. **数据库异常**: 查询过程中发生数据库错误
4. **配置错误**: 每日工时配置格式错误

### 错误码说明
- `200`: 成功
- `500`: 服务器内部错误
- 具体错误信息在 `message` 字段中返回

## 使用场景

### 前端集成
1. **单个登记页面**: 在"已登记工时"后显示"查看明细"链接
2. **工时统计**: 查看特定工作内容的详细工时分布
3. **工时审核**: 审核人员查看工时记录详情

### 参数组合示例
```javascript
// 查看整个工作项的工时
{ workItemId: "672d784c9a71acdb4f8180ef" }

// 查看特定实例的工时
{ workItemId: "672d784c9a71acdb4f8180ef", instanceId: "616578422" }

// 查看特定节点的工时
{ workItemId: "672d784c9a71acdb4f8180ef", instanceId: "616578422", nodeId: "state_0" }

// 查看特定任务的工时
{ workItemId: "672d784c9a71acdb4f8180ef", instanceId: "616578422", nodeId: "state_0", taskId: "616581875" }
```

## 性能考虑

### 查询优化
- 使用索引优化查询性能
- 建议在以下字段建立索引：
  - `project_id`
  - `work_item_id`
  - `instance_id`
  - `node_id`
  - `task_id`
  - `created_at`

### 数据量控制
- 当前未限制返回记录数量
- 如数据量过大，建议添加分页功能
- 可考虑添加时间范围过滤

## 测试用例

### 功能测试
1. 正常查询各种参数组合
2. 空参数处理
3. 无数据情况处理
4. 大数据量查询性能

### 边界测试
1. 参数为空字符串
2. 参数包含特殊字符
3. 项目Key无效
4. 用户权限不足

## 部署说明

### 数据库要求
- 确保 `hour_record` 表存在
- 确保相关索引已创建
- 确保系统配置表包含工时配置

### 配置要求
- 系统配置中需要 `work_hour_per_day` 配置项
- 请求头处理器正常工作
- 用户认证服务可用

## 总结

此接口为工时明细查询提供了灵活的参数组合支持，能够满足不同层级的工时统计需求。通过合理的错误处理和性能优化，确保了接口的稳定性和可用性。
