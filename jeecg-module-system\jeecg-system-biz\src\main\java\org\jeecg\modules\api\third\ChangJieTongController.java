package org.jeecg.modules.api.third;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.api.dto.BizContent;
import org.jeecg.modules.api.dto.TicketDto;
import org.jeecg.modules.api.dto.TicketResult;
import org.jeecg.modules.api.vo.TicketVO;
import org.jeecg.modules.system.service.ICjtSys3rdLogsService;
import org.jeecg.modules.system.util.AESUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.bind.annotation.*;
import org.apache.commons.codec.binary.Base64;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
* @Description: 畅捷通相关接口
 *
 *
*/
@Api(tags="畅捷通相关接口")
@RestController
@RequestMapping("/changJieTong/")
@Slf4j
public class ChangJieTongController {
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private RestTemplate restTemplate;

    private final String TICKET_KEY = "changJieTong::getTicket";
    // private final String FEISHU_WEBHOOK_URL = "https://open.feishu.cn/anycross/trigger/callback/MDAzNjkwM2Q3ZDk4MDk3NDdhMDk5OTE0YTcxZjQxZTQx";
    private final String FEISHU_WEBHOOK_URL = "https://open.feishu.cn/anycross/trigger/callback/MDFkZWY4YzI1YzQ0MzU0MjllZjhkNjI5NmQ0NDFhYzIw";

    private final String AUTH_USERNAME = "changjietong";
    private final String AUTH_PASSWORD = "changjietong@123";
    
    @Autowired
    private ICjtSys3rdLogsService cjtSys3rdLogsService;

    /**
     * 转发请求到飞书webhook
     */
    private String forwardToFeishu(JSONObject requestBody) {
        try {
            // 创建Basic Auth认证头
            String auth = AUTH_USERNAME + ":" + AUTH_PASSWORD;
            byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.UTF_8));
            String authHeader = "Basic " + new String(encodedAuth);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", authHeader);

            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toString(), headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                FEISHU_WEBHOOK_URL,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            log.info("飞书webhook响应状态: {}", response.getStatusCode());
            log.info("飞书webhook响应内容: {}", response.getBody());

            return response.getBody();
        } catch (Exception e) {
            log.error("调用飞书webhook失败", e);
            throw e;
        }
    }

   /**
    * 畅捷通Ticket
    *
    * @param ticketVO
    * @param req
    * @return
    */
   @ApiOperation(value="畅捷通Ticket", notes="畅捷通Ticket")
   @PostMapping(value = "/getTicket")
   public Result<String> getTicket(@RequestBody TicketVO ticketVO,
                                              HttpServletRequest req) throws Exception {
       String result = null;
       String msgType = null;
       String encryptMsg = ticketVO.getEncryptMsg();
       String request = null;
       String response = null;
       try {
    	   log.info("获取畅捷通Ticket,ticketVO：{}",ticketVO);
           // 解密数据
           result = AESUtils.aesDecrypt(encryptMsg, "1234567890qwerty");
           log.info("解密后数据：{}",result);
           
           if(Objects.nonNull(result)){
        	   request = result;
               JSONObject ticketResult = JSON.parseObject(result);
               log.info("解密后数据：ticketResult{}",ticketResult);
               
               if(Objects.nonNull(ticketResult)){
                   JSONObject bizContent = ticketResult.getJSONObject("bizContent");
                   log.info("解密后数据：bizContent{}",bizContent);
               }
               
               msgType = ticketResult.getString("msgType");
               
               if("APP_TICKET".equals(msgType)){
                   redisUtil.set(TICKET_KEY,encryptMsg,60*60*24);
                   response = "success";
               }else {
            	   // 非ticket需要转发到集成平台webhook
            	   response = forwardToFeishu(ticketResult);
               }
           }
           
           return Result.OK("success");
       } catch (Exception e) {
           log.error("处理畅捷通Ticket失败", e);
           result = "处理失败：" + e.getMessage();
           response = result;
           throw e;
       } finally {
           // 确保无论成功失败都记录日志
           try {
               cjtSys3rdLogsService.saveLog(
                   msgType != null ? msgType : "UNKNOWN",  // 如果获取msgType失败则使用UNKNOWN
                   JSON.toJSONString(req.getHeaderNames()), // 请求头
                   request,  // 加密的请求数据
                   response,  // 解密后的数据或错误信息
                   encryptMsg  // 加密的请求数据
               );
           } catch (Exception logEx) {
               log.error("记录畅捷通日志失败", logEx);
           }
       }
   }
    @ApiOperation(value="获取畅捷通Ticket", notes="获取畅捷通Ticket")
    @GetMapping  (value = "/getTicketMsg")
    public Result<String> getTicketMsg(
                                            HttpServletRequest req) throws Exception {
        String ticketMsg = (String) redisUtil.get(TICKET_KEY);
        log.info("获取畅捷通ticketMsg：{}",ticketMsg);
        return Result.OK(ticketMsg);
    }


}
