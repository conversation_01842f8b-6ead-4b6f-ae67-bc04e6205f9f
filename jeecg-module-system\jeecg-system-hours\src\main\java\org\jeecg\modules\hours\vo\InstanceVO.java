package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 实例信息VO
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "InstanceVO", description = "实例信息")
public class InstanceVO {

    @ApiModelProperty(value = "实例ID")
    private String id;

    @ApiModelProperty(value = "工作项名称")
    private String workItemName;

    @ApiModelProperty(value = "项目标识")
    private String projectKey;
}
