<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.UserHourVarianceStatMapper">

    <!-- 计算指定年月的人员工时差值数据 -->
    <select id="calculateMonthlyVarianceData" resultType="org.jeecg.modules.hours.dto.UserVarianceDataDTO">
        SELECT 
            fu.user_key as userId,
            fu.name as userName,
            fu.user_key as userCode,
            fd.id as deptId,
            fd.name as deptName,
            COALESCE(actual_hours.total_hours, 0) as actualHours,
            COALESCE(required_hours.total_hours, 0) as requiredHours
        FROM fs_user fu
        LEFT JOIN fs_department_user fdu ON fu.union_id = fdu.union_id
        LEFT JOIN fs_department fd ON fdu.dept_id = fd.dept_id
        LEFT JOIN (
            -- 实际工时统计：从工时记录中按人员汇总
            SELECT 
                hr.user_id,
                SUM(hrd.actual_hours) as total_hours
            FROM hour_record_detail hrd
            INNER JOIN hour_record hr ON hrd.record_id = hr.id
            WHERE YEAR(hrd.work_date) = #{year}
              AND MONTH(hrd.work_date) = #{month}
              AND hr.status = 2
              <if test="!includeHoliday">
                AND hrd.is_rest = 0
              </if>
              <if test="includeHoliday">
                AND hrd.is_rest = 1
              </if>
            GROUP BY hr.user_id
        ) actual_hours ON fu.user_key = actual_hours.user_id
        LEFT JOIN (
            -- 可登记工时统计：从考勤统计表中按人员汇总
            SELECT 
                sas.union_id,
                SUM(CASE 
                    WHEN sas.required_attend_hours = 0 AND #{includeHoliday} = false THEN 0
                    WHEN sas.required_attend_hours > 0 OR #{includeHoliday} = true THEN sas.required_attend_hours
                    ELSE 0
                END) as total_hours
            FROM sys_user_attendance_stat sas
            WHERE YEAR(sas.stat_date) = #{year}
              AND MONTH(sas.stat_date) = #{month}
            GROUP BY sas.union_id
        ) required_hours ON fu.union_id = required_hours.union_id
        WHERE fu.user_key IS NOT NULL
          AND fu.status = 1
          AND (actual_hours.total_hours > 0 OR required_hours.total_hours > 0)
        ORDER BY fd.name, fu.name
    </select>

</mapper>