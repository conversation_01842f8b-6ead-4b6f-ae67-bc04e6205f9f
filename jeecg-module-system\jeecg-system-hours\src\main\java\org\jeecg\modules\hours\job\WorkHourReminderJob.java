package org.jeecg.modules.hours.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.hours.entity.FsSysReminderConfig;
import org.jeecg.modules.hours.service.IFsSysReminderConfigService;
import org.jeecg.modules.hours.service.IWorkHourReminderService;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 工时登记提醒定时任务
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Slf4j
@Component
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
public class WorkHourReminderJob implements Job {

    @Autowired
    private IWorkHourReminderService workHourReminderService;
    
    @Autowired
    private IFsSysReminderConfigService reminderConfigService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        String configId = dataMap.getString("configId");
        String projectKey = dataMap.getString("projectKey");
        String reminderType = dataMap.getString("reminderType");
        String repeatMode = dataMap.getString("repeatMode");
        
        log.info("=== 开始执行工时登记提醒任务 ===");
        log.info("配置ID: {}, 项目: {}, 提醒类型: {}, 重复方式: {}", 
                configId, projectKey, reminderType, repeatMode);
        
        try {
            // 获取完整的提醒配置
            FsSysReminderConfig reminderConfig = reminderConfigService.getById(Long.valueOf(configId));
            if (reminderConfig == null) {
                log.error("未找到提醒配置，配置ID: {}", configId);
                return;
            }
            
            // 检查配置状态
            if (!"启用".equals(reminderConfig.getStatus())) {
                log.info("提醒配置已禁用，跳过执行，配置ID: {}", configId);
                return;
            }
            
            log.info("提醒配置详情: {}", reminderConfig);
            
            // 执行提醒逻辑
            workHourReminderService.executeReminder(reminderConfig);
            
            log.info("=== 工时登记提醒任务执行完成 ===");
            
        } catch (Exception e) {
            log.error("执行工时登记提醒任务时发生异常，配置ID: {}", configId, e);
            throw new JobExecutionException("工时登记提醒任务执行失败", e);
        }
    }
}
