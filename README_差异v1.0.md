# 利氪项目工时管理系统与产品工时系统差异分析

## 1. 概述

本文档旨在分析利氪项目工时管理系统与原产品工时系统之间的需求差异，并提供针对性的实施建议，以便开发团队能够基于现有产品工时系统快速实现利氪项目的特定需求。

## 2. 系统定位差异

### 产品工时系统
- 通用型工时管理平台
- 与飞书项目系统深度集成
- 面向多种类型的项目和组织

### 利氪项目工时系统
- 特定客户定制化工时管理解决方案
- 需要与PLM系统集成
- 更强调项目数据同步和审批流程

## 3. 核心功能差异分析

### 3.1 工时登记模块

| 功能点 | 产品工时系统 | 利氪项目工时系统 | 差异说明 |
|-------|------------|---------------|--------|
| 登记方式 | 单条/批量登记 | 按周登记并支持跨月截断 | 利氪项目需增加按周登记逻辑和跨月截断规则 |
| 工时单位 | 小时数 | 百分比（报表显示工时数） | 利氪项目需增加百分比转换功能 |
| 考勤关联 | 基础关联 | 严格校验（无考勤记录禁止登记） | 利氪项目需增强考勤校验逻辑 |
| 加班工时 | 支持 | 需与客户确认处理方式 | 需增加配置项以支持不同加班工时处理策略 |

### 3.2 项目数据同步模块

| 功能点 | 产品工时系统 | 利氪项目工时系统 | 差异说明 |
|-------|------------|---------------|--------|
| 数据来源 | 飞书项目系统 | PLM系统 | 需开发PLM系统接口对接 |
| 同步范围 | 全部项目 | 仅同步已启动项目 | 需增加项目状态筛选逻辑 |
| 项目人员维护 | 基础维护 | 明确区分只读字段与可编辑字段 | 需完善项目人员数据维护界面 |
| 项目展示逻辑 | 全部可见 | 默认展示参与项目，可手动添加 | 需调整项目筛选和展示逻辑 |

### 3.3 审批流程模块

| 功能点 | 产品工时系统 | 利氪项目工时系统 | 差异说明 |
|-------|------------|---------------|--------|
| 审批人确定 | 可配置 | 固定为直属上级+项目负责人会签 | 需调整审批人确定逻辑 |
| 审批状态展示 | 基础状态 | 参考飞书审批，用"待办"/"已办"区分 | 需调整审批状态展示方式 |
| 特殊情况处理 | 基础处理 | 明确任一人拒绝则整单结束 | 需完善审批流程特殊情况处理 |

### 3.4 报表统计模块

| 功能点 | 产品工时系统 | 利氪项目工时系统 | 差异说明 |
|-------|------------|---------------|--------|
| 统计维度 | 多维度统计 | 增加部门和项目维度统计 | 需增加部门维度统计功能 |
| 差值分析 | 基础分析 | 计算可登记工时与实际工时差值 | 需增加差值分析功能 |
| 数值转换 | 直接展示 | 百分比填写转换为工时数展示 | 需增加数值转换逻辑 |
| 日期统计规则 | 灵活配置 | 按月统计，日期连续，未填记为0 | 需调整日期统计规则 |

## 4. 系统集成差异

| 集成点 | 产品工时系统 | 利氪项目工时系统 | 差异说明 |
|-------|------------|---------------|--------|
| 项目数据集成 | 飞书项目 | PLM系统 | 需开发PLM系统接口对接 |
| 考勤集成 | 基础集成 | 严格校验，作为工时登记前置条件 | 需增强考勤集成逻辑 |
| 组织架构集成 | 飞书组织架构 | 需获取直属上级信息用于审批 | 需完善组织架构数据获取 |

## 5. 界面原型差异

| 页面 | 产品工时系统 | 利氪项目工时系统 | 差异说明 |
|-----|------------|---------------|--------|
| 工时登记页面 | 基础登记界面 | 按周为单位的表格式界面 | 需调整工时登记界面布局 |
| 项目人员数据维护页面 | 基础维护界面 | 明确区分只读字段与可编辑字段 | 需完善项目人员数据维护界面 |
| 报表页面 | 多种报表 | 增加部门和项目维度统计 | 需增加新的报表类型 |

## 6. 实施建议

### 6.1 代码调整策略

1. **模块化改造**：
   - 将工时登记、项目数据同步、审批流程和报表统计模块进行模块化改造
   - 提取公共逻辑，增加可配置项以支持不同业务场景

2. **接口适配层**：
   - 开发PLM系统接口适配层，兼容原有飞书项目接口
   - 设计统一的数据模型，减少业务逻辑层的修改

3. **组件复用与扩展**：
   - 复用现有UI组件，通过属性配置支持不同展示需求
   - 对工时登记表格、项目选择器等核心组件进行扩展

### 6.2 优先级实施建议

按照以下优先级实施利氪项目特有需求：

1. **P0 - 核心功能调整**：
   - PLM系统接口对接
   - 按周登记与跨月截断规则
   - 项目人员数据维护界面完善

2. **P1 - 流程与校验**：
   - 考勤校验逻辑增强
   - 审批流程调整
   - 百分比与工时数转换

3. **P2 - 报表与统计**：
   - 部门维度统计功能
   - 差值分析功能
   - 日期统计规则调整

4. **P3 - 体验优化**：
   - 界面样式调整
   - 消息提醒优化
   - 工作台功能完善

### 6.3 技术实现要点

1. **PLM系统集成**：
   ```typescript
   // 示例：PLM系统接口适配器
   class PLMAdapter implements ProjectDataProvider {
     async getProjects(filters: ProjectFilter): Promise<Project[]> {
       // 调用PLM API并转换为统一数据格式
       const plmProjects = await plmApi.getProjects({ status: 'active' });
       return plmProjects.map(this.convertToStandardFormat);
     }
     
     // 其他接口实现...
   }
   ```

2. **按周登记与跨月截断**：
   ```typescript
   // 示例：日期处理工具函数
   function splitWeekByMonth(startDate: Date, endDate: Date): Array<{start: Date, end: Date}> {
     // 实现按月截断周的逻辑
     const result = [];
     // ...截断逻辑实现
     return result;
   }
   ```

3. **百分比与工时数转换**：
   ```typescript
   // 示例：工时转换工具
   function percentToHours(percent: number, scheduledHours: number): number {
     return (percent / 100) * scheduledHours;
   }
   
   function hoursToPercent(hours: number, scheduledHours: number): number {
     return (hours / scheduledHours) * 100;
   }
   ```

## 7. 风险与注意事项

1. **数据兼容性风险**：
   - PLM系统数据格式与飞书项目数据可能存在差异
   - 建议先进行数据映射分析，确保兼容性

2. **业务逻辑冲突**：
   - 百分比与工时数的双向转换可能导致精度问题
   - 建议与客户确认具体计算规则和精度要求

3. **用户体验一致性**：
   - 在满足利氪项目特殊需求的同时，需保持整体用户体验一致性
   - 建议进行用户测试，确保操作流程符合预期

4. **性能考量**：
   - PLM系统接口调用可能影响系统响应速度
   - 建议实现数据缓存机制，减少频繁接口调用

## 8. 结论

利氪项目工时管理系统与产品工时系统在核心功能上具有较高的相似度，主要差异集中在项目数据来源、工时登记方式、审批流程和报表统计等方面。通过合理的模块化改造和接口适配，可以在现有产品工时系统的基础上快速实现利氪项目的特定需求。

建议采用渐进式开发策略，先实现核心功能差异，再逐步完善其他功能，确保系统稳定性和用户体验。 