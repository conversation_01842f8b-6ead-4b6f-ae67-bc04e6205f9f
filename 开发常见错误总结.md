# 开发常见错误总结

## 📋 **项目结构和路径错误**

### 1. **Mapper XML文件路径错误**
- ❌ **错误**: 将XML文件放在 `src/main/resources/mapper/` 目录
- ✅ **正确**: 应放在 `src/main/java/org/jeecg/modules/hours/mapper/xml/` 目录
- 📝 **说明**: JeecgBoot项目的Mapper XML文件应与Java接口文件在同一包结构下的xml子目录

### 2. **前端组件路径混淆**
- ❌ **错误**: 将组件放在错误的features目录下
- ✅ **正确**: 遵循 `features/board_web_gw38j1/` 的目录结构
- 📝 **说明**: 前端组件应按照项目约定的features结构组织

## 🗄️ **数据库字段映射错误**

### 1. **字段名不匹配**
- ❌ **错误**: 使用 `work_hours` 字段名
- ✅ **正确**: 实际字段名是 `hours`
- ❌ **错误**: 使用 `project_key` 字段名  
- ✅ **正确**: 实际字段名是 `project_id`

### 2. **数据类型错误**
- ❌ **错误**: 使用 `Long` 类型的ID
- ✅ **正确**: 实际使用 `String` 类型的ID (varchar)
- 📝 **说明**: 飞书项目中的ID字段通常是字符串类型

### 3. **表名错误**
- ❌ **错误**: 使用 `sys_reminder_config` 表名
- ✅ **正确**: 实际表名是 `fs_sys_reminder_config`
- 📝 **说明**: 飞书相关表都有 `fs_` 前缀

## 🎨 **前端组件开发错误**

### 1. **Semi Design组件使用错误**
- ❌ **错误**: 使用 `mode="multiple"` 属性
- ✅ **正确**: 使用 `multiple` 属性
- ❌ **错误**: 在renderSelectedItem中使用disabled参数
- ✅ **正确**: disabled应该是Select组件级别的属性

### 2. **日期组件依赖错误**
- ❌ **错误**: 引入moment.js依赖
- ✅ **正确**: 使用原生Date API和Semi Design内置功能
- 📝 **说明**: Semi Design已内置日期处理，无需额外依赖

### 3. **导出功能实现错误**
- ❌ **错误**: 使用 `window.open()` 在沙盒环境中导出
- ✅ **正确**: 使用fetch + blob下载方式
- 📝 **说明**: 沙盒环境会阻止弹窗，需要使用下载方式

## 🔧 **API设计错误**

### 1. **projectKey传递错误**
- ❌ **错误**: 在前端组件中传递projectKey参数
- ✅ **正确**: projectKey由后端从请求header中获取
- 📝 **说明**: 遵循后端统一认证机制，减少前端复杂度

### 2. **API响应处理错误**
- ❌ **错误**: 直接使用response.result
- ✅ **正确**: 使用 `(response as any)?.result` 进行类型断言
- 📝 **说明**: TypeScript严格模式下需要正确的类型处理

## 📱 **UI/UX设计错误**

### 1. **布局结构错误**
- ❌ **错误**: 使用Space组件进行复杂布局
- ✅ **正确**: 使用flex布局实现左右分区
- 📝 **说明**: 复杂布局应使用CSS flex而非组件库的简单布局组件

### 2. **快捷方式实现错误**
- ❌ **错误**: 自定义Popover实现快捷方式
- ✅ **正确**: 使用Semi Design内置的presets功能
- 📝 **说明**: 优先使用组件库官方功能，减少自定义代码

## 🏗️ **架构设计错误**

### 1. **组件耦合度过高**
- ❌ **错误**: 组件间传递过多配置参数
- ✅ **正确**: 通过全局状态或context管理共享数据
- 📝 **说明**: 减少组件间的直接依赖，提高可维护性

### 2. **类型定义不一致**
- ❌ **错误**: 前后端使用不同的数据类型
- ✅ **正确**: 保持前后端类型定义一致
- 📝 **说明**: 统一的类型定义减少转换错误

## 🧪 **测试和验证错误**

### 1. **缺少边界条件测试**
- ❌ **错误**: 只测试正常流程
- ✅ **正确**: 测试空数据、错误响应等边界情况
- 📝 **说明**: 完整的测试覆盖提高系统稳定性

### 2. **忽略浏览器兼容性**
- ❌ **错误**: 只在单一环境测试
- ✅ **正确**: 考虑不同浏览器和沙盒环境限制
- 📝 **说明**: 特别注意沙盒环境的安全限制

## 📚 **文档和规范错误**

### 1. **未遵循项目规范**
- ❌ **错误**: 忽略README.md中的开发规范
- ✅ **正确**: 严格按照项目规范进行开发
- 📝 **说明**: 项目规范是团队协作的基础

### 2. **注释和文档不完整**
- ❌ **错误**: 缺少关键逻辑的注释说明
- ✅ **正确**: 提供清晰的中文注释和文档
- 📝 **说明**: 良好的文档提高代码可维护性

## 🔄 **版本控制和部署错误**

### 1. **文件路径混乱**
- ❌ **错误**: 在错误的工作目录下操作文件
- ✅ **正确**: 确认当前工作目录和文件路径
- 📝 **说明**: 多项目开发时特别注意路径切换

### 2. **依赖管理错误**
- ❌ **错误**: 手动编辑package.json等配置文件
- ✅ **正确**: 使用包管理器命令安装依赖
- 📝 **说明**: 包管理器能自动处理版本冲突和依赖关系

## 💡 **最佳实践建议**

1. **开发前准备**
   - 仔细阅读项目README和规范文档
   - 确认数据库表结构和字段名
   - 了解项目的技术栈和架构模式

2. **开发过程中**
   - 优先使用官方组件库功能
   - 保持前后端类型定义一致
   - 及时测试和验证功能

3. **代码质量**
   - 提供清晰的中文注释
   - 遵循项目的代码风格
   - 考虑边界条件和错误处理

4. **部署和维护**
   - 测试不同环境的兼容性
   - 提供完整的部署文档
   - 建立错误监控和日志机制
