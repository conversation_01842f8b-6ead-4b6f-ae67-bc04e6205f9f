
-- 创建部门工时差值统计表
CREATE TABLE `dept_hour_variance_stat` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `dept_id` varchar(36) NOT NULL COMMENT '部门ID',
  `dept_name` varchar(100) NOT NULL COMMENT '部门名称',
  `stat_year` int NOT NULL COMMENT '统计年份',
  `stat_month` int NOT NULL COMMENT '统计月份',
  `actual_hours` decimal(10,2) DEFAULT '0.00' COMMENT '实际工时',
  `required_hours` decimal(10,2) DEFAULT '0.00' COMMENT '可登记工时',
  `variance_hours` decimal(10,2) DEFAULT '0.00' COMMENT '差值工时(可登记-实际)',
  `include_holiday` tinyint(1) DEFAULT '0' COMMENT '是否包含假日(0-不包含,1-包含)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_dept_stat` (`dept_id`, `stat_year`, `stat_month`, `include_holiday`),
  KEY `idx_stat_date` (`stat_year`, `stat_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门工时差值统计表';

CREATE TABLE `user_hour_variance_stat` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `user_name` varchar(100) NOT NULL COMMENT '用户姓名',
  `user_code` varchar(50) DEFAULT NULL COMMENT '工号',
  `dept_id` varchar(36) DEFAULT NULL COMMENT '部门ID',
  `dept_name` varchar(100) DEFAULT NULL COMMENT '部门名称',
  `stat_year` int(11) NOT NULL COMMENT '统计年份',
  `stat_month` int(11) NOT NULL COMMENT '统计月份',
  `actual_hours` decimal(10,2) DEFAULT '0.00' COMMENT '实际工时',
  `required_hours` decimal(10,2) DEFAULT '0.00' COMMENT '可登记工时',
  `variance_hours` decimal(10,2) DEFAULT '0.00' COMMENT '差值工时(可登记-实际)',
  `include_holiday` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否包含假日(0:不包含, 1:包含)',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_stat_period` (`user_id`,`stat_year`,`stat_month`,`include_holiday`,`dept_id`) USING BTREE,
  KEY `idx_stat_year_month` (`stat_year`,`stat_month`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_dept_id` (`dept_id`),
  KEY `idx_include_holiday` (`include_holiday`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人员工时差值统计表';