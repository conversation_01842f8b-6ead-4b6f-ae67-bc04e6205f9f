package org.jeecg.modules.hapi.job;

import java.util.Calendar;
import java.util.List;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.feishu.service.FeiShuService;
import org.jeecg.modules.hours.constant.ConfigKeyConstant;
import org.jeecg.modules.hours.entity.FsProject;
import org.jeecg.modules.hours.entity.FsSys3rdLog;
import org.jeecg.modules.hours.service.IFsProjectService;
import org.jeecg.modules.hours.service.IFsSys3rdLogService;
import org.jeecg.modules.hours.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.event.EventDispatcher;
import com.lark.oapi.service.contact.ContactService;
import com.lark.oapi.service.contact.v3.model.P2DepartmentCreatedV3;
import com.lark.oapi.service.contact.v3.model.P2DepartmentDeletedV3;
import com.lark.oapi.service.contact.v3.model.P2DepartmentUpdatedV3;
import com.lark.oapi.service.contact.v3.model.P2UserCreatedV3;
import com.lark.oapi.service.contact.v3.model.P2UserDeletedV3;
import com.lark.oapi.service.contact.v3.model.P2UserUpdatedV3;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RestController
@Profile({ "test", "prod" }) // 事件配置仅限生产环境|测试环境可用
public class FeiShuEvent {

	@Autowired
	private ISysConfigService sysConfigService;
	@Autowired
	private IFsProjectService fsProjectService;
	@Autowired
	private IFsSys3rdLogService fsSys3rdLogService;
	@Autowired
	private FeiShuService feiShuService;

	private final EventDispatcher EVENT_HANDLER = EventDispatcher.newBuilder("", "")
			.onP2DepartmentUpdatedV3(new ContactService.P2DepartmentUpdatedV3Handler() {
				// 部门修改
				@Override
				public void handle(P2DepartmentUpdatedV3 event) throws Exception {
					saveEventLog(event.getHeader().getEventType(), event);
					feiShuService.updateDept(event.getEvent().getObject());
				}
			}).onP2DepartmentDeletedV3(new ContactService.P2DepartmentDeletedV3Handler() {
				// 部门删除
				@Override
				public void handle(P2DepartmentDeletedV3 event) throws Exception {
					saveEventLog(event.getHeader().getEventType(), event);
					feiShuService.deleteDept(event.getEvent().getObject());
				}
			}).onP2DepartmentCreatedV3(new ContactService.P2DepartmentCreatedV3Handler() {
				// 部门新增
				@Override
				public void handle(P2DepartmentCreatedV3 event) throws Exception {
					saveEventLog(event.getHeader().getEventType(), event);
					feiShuService.addDept(event.getEvent().getObject());
				}
			}).onP2UserUpdatedV3(new ContactService.P2UserUpdatedV3Handler() {
				// 修改员工信息
				@Override
				public void handle(P2UserUpdatedV3 event) throws Exception {
					saveEventLog(event.getHeader().getEventType(), event);
					feiShuService.updateUser(event.getEvent().getObject());
				}
			}).onP2UserCreatedV3(new ContactService.P2UserCreatedV3Handler() {
				// 员工入职
				@Override
				public void handle(P2UserCreatedV3 event) throws Exception {
					log.info("eventId={},eventType={}", event.getHeader().getEventId(),
							event.getHeader().getEventType());
					log.info("event={}", Jsons.DEFAULT.toJson(event));
					saveEventLog(event.getHeader().getEventType(), event);
					feiShuService.addUser(event.getEvent().getObject());
				}
			}).onP2UserDeletedV3(new ContactService.P2UserDeletedV3Handler() {
				// 员工离职
				@Override
				public void handle(P2UserDeletedV3 event) throws Exception {
					saveEventLog(event.getHeader().getEventType(), event);
					log.info("eventId={},eventType={}", event.getHeader().getEventId(),
							event.getHeader().getEventType());
					log.info("event={}", Jsons.DEFAULT.toJson(event));
					feiShuService.deleteUser(event.getEvent().getObject());
				}
			}).build();

	private <T> void saveEventLog(String eventType, T record) {
		FsSys3rdLog bean = new FsSys3rdLog();
		// 飞书开放平台
		bean.setSource("feishu:open:event:".concat(eventType));
		bean.setHeader(null);
		bean.setCreateTime(Calendar.getInstance().getTime());
		bean.setRes(Jsons.DEFAULT.toJson(record));
		bean.setRemark("飞书订阅事件");
		this.fsSys3rdLogService.save(bean);
	}

	@PostConstruct
	public void init() {
		// 1.获取fs_project所有数据+遍历
		List<FsProject> projects = fsProjectService.list();
		if (CollectionUtils.isEmpty(projects)) {
			log.error("未获取到任何工作空间数据!");
//			throw new JeecgBootException("未获取到任何工作空间数据!");
			return;
		}
		StringBuilder exceptionBuilder = new StringBuilder();
		for (FsProject fsProject : projects) {
			String projectKey = fsProject.getProjectKey();
			try {
				// 3.获取工作空间的配置appId、appSecret
				String appId = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.BIND_APP_ID);
				String appSecret = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.BIND_APP_SECRET);
				if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(appSecret)) {
					log.warn("项目{}配置无效,appId={},appSecret={}", projectKey, appId, appSecret);
					exceptionBuilder
							.append("projectKey=" + projectKey + "配置无效,appId=" + appId + ",appSecret=" + appSecret);
					continue;
				}
				// 初始化事件长连接
				com.lark.oapi.ws.Client cli = new com.lark.oapi.ws.Client.Builder(appId, appSecret)
						.eventHandler(EVENT_HANDLER).build();
				cli.start();
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				exceptionBuilder.append("projectKey=" + projectKey + "加载考勤数据失败!");
			}
		}
		if (StringUtils.isNotEmpty(exceptionBuilder.toString())) {
//			throw new JeecgBootException(exceptionBuilder.toString());
			log.error(exceptionBuilder.toString());
		}
	}

	// 示例：全量同步飞书部门及部门-用户关系（可用于定时任务或手动触发）
	public void syncAllFeishuDepartmentsAndUsers(String appId, String appSecret, String projectKey) {
		feiShuService.syncAllDepartmentsAndUsers(appId, appSecret, projectKey);
	}
}
