package org.jeecg.modules.hours.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Transient;

/**
 * @Description: 飞书项目任务缓存表
 * @Author: jeecg-boot
 * @Date: 2023-08-20
 * @Version: V1.0
 */
@Data
@TableName("fs_task_cache")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="飞书项目任务缓存对象", description="缓存飞书项目任务数据")
public class FsTaskCache implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    /** 飞书项目key */
    @ApiModelProperty(value = "飞书项目key")
    private String projectKey;
    
    /** 工作项键值 */
    @ApiModelProperty(value = "工作项键值")
    private String workItemKey;
    
    /** 实例键值 */
    @ApiModelProperty(value = "实例键值")
    private String instanceKey;
    
    /** 所属节点键值 */
    @ApiModelProperty(value = "所属节点键值")
    private String nodeKey;
    
    /** 任务键值 */
    @ApiModelProperty(value = "任务键值")
    private String taskKey;
    
    /** 任务名称 */
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    
    /** 状态 */
    @ApiModelProperty(value = "状态")
    private String status;
    
    /** 负责人ID */
    @ApiModelProperty(value = "负责人ID")
    private String assignee;
    
    /** 负责人姓名 */
    @ApiModelProperty(value = "负责人姓名")
    private String assigneeName;
    
    /** 排期开始时间 */
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "排期开始时间")
    private Date startTime;
    
    /** 排期结束时间 */
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "排期结束时间")
    private Date endTime;
    
    /** 预估工时 */
    @TableField(exist = false)
    @ApiModelProperty(value = "预估工时")
    private BigDecimal estimateHours;
    
    /** 数据更新时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "数据更新时间")
    private Date updateTime;

    /**
     * 任务描述
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "任务描述")
    private String description;

//    /**
//     * 创建时间
//     */
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @ApiModelProperty(value = "创建时间")
//    private Date createTime;
} 