package org.jeecg.hours;

import java.util.ArrayList;
import java.util.List;

import org.jeecg.modules.hours.context.AuthContext;
import org.springframework.test.context.ActiveProfiles;

import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.core.utils.Jsons;
import com.lark.project.service.plugin.builder.GetPluginTokenReq;
import com.lark.project.service.plugin.builder.GetPluginTokenResp;
import com.lark.project.service.user.builder.QueryUserDetailReq;
import com.lark.project.service.user.builder.QueryUserDetailResp;
import com.lark.project.service.user.group.UserGroupService;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 消息推送测试
 * @Author: lsq
 */
@Slf4j
@ActiveProfiles("dev")
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class SpaceUserGroupTest {

	@org.junit.jupiter.api.Test
	public void userGroup() throws Exception {
		
		this.initLogin();
		
		String projectKey = "6846a6670ee55facde5f6ba5";
		String pluginId = "MII_6846998B99B18004";
		String pluginSecret = "ADDF2CF2C07287865B81B89848178CD3";
		String feishuApiBaseUrl = "https://project.feishu.cn/";
		
		
		Client client = Client.newBuilder(pluginId, pluginSecret).openBaseUrl(feishuApiBaseUrl).build();

		// 从认证上下文获取token和userKey
		AuthContext authContext = AuthContext.getContext();
		if (authContext == null) {
			throw new Exception("无法获取认证上下文，请确保在Web请求上下文中调用");
		}

//		 String token = authContext.getToken();
		String token = "u-ab9f9a73-b5f1-4f7f-9b84-0f0dea4fc406";
		String userKey = authContext.getUserKey();
		
		String userGroupType ="PROJECT_MEMBER";
		List<String> members = UserGroupService.getMembers(token, userKey, projectKey, userGroupType);

		// 业务数据处理
		log.info("resp={}", Jsons.DEFAULT.toJson(members));
		
		log.info("\n\n\n------------------");
	}
	
	
	@org.junit.jupiter.api.Test
	public void testname() throws Exception {
		
		this.initLogin();
		
		String projectKey = "6846a6670ee55facde5f6ba5";
		String pluginId = "MII_6846998B99B18004";
		String pluginSecret = "ADDF2CF2C07287865B81B89848178CD3";
		String feishuApiBaseUrl = "https://project.feishu.cn/";
		
		
		Client client = Client.newBuilder(pluginId, pluginSecret).openBaseUrl(feishuApiBaseUrl).build();

		// 从认证上下文获取token和userKey
		AuthContext authContext = AuthContext.getContext();
		if (authContext == null) {
			throw new Exception("无法获取认证上下文，请确保在Web请求上下文中调用");
		}

		String token = authContext.getToken();
		String userKey = authContext.getUserKey();

		// 验证token和userKey是否有效
		if (token == null || token.isEmpty()) {
			throw new Exception("无法获取有效的访问令牌(token)，请检查飞书授权配置");
		}

		if (userKey == null || userKey.isEmpty()) {
			throw new Exception("无法获取有效的用户标识(userKey)，请检查飞书授权配置");
		}

		log.info("调用飞书API: userKey={}, tokenLength={}", userKey, token.length());
		
		List<String> userKeys = new ArrayList<String>() {
			{
				this.add("7376349997018693636");
			}
		};
		QueryUserDetailReq req = QueryUserDetailReq.newBuilder().userKeys(userKeys).build();

		// 构建请求选项
		RequestOptions options = RequestOptions.newBuilder().userKey(userKey).accessToken(token).build();

		// 调用飞书API
		QueryUserDetailResp resp1 = client.getUserService().queryUserDetail(req, options);
		if(null!=resp1.getErr() && resp1.getErr().getCode()!=0) {
			log.error("errCode={},errMsg={}",resp1.getErrCode(),resp1.getErrMsg());
		}

		// 业务数据处理
		log.info("resp={}", Jsons.DEFAULT.toJson(resp1.getData()));
		
		log.info("\n\n\n------------------");
	}
	
	public void initLogin() throws Exception {
		String pluginId = "MII_6846998B99B18004";
		String pluginSecret = "ADDF2CF2C07287865B81B89848178CD3";
		// 初始化飞书项目SDK
		Client client = Client.newBuilder(pluginId, pluginSecret).openBaseUrl("https://project.feishu.cn/").build();

		// 通过授权码获取飞书token
		GetPluginTokenReq tokenReq = GetPluginTokenReq.newBuilder().pluginID(pluginId).pluginSecret(pluginSecret).type(1).build();

		GetPluginTokenResp tokenResp = client.getPluginService().getPluginToken(tokenReq,
				RequestOptions.newBuilder().build());

		String feishuToken = tokenResp.getData().getToken();
		log.info("feishuToken={}",feishuToken);
		
//		String refreshToken = tokenResp.getData().getRefreshToken();
		Integer expireTime = tokenResp.getData().getExpireTime();
//		Integer refreshExpireTime = tokenResp.getData().getRefreshTokenExpireTime();
		String userKey = "7376349997018693636";

		// 保存飞书授权信息
//		feishuAuthService.setAuthInfo(feishuToken, null, expireTime, null, userKey);
		
		log.info("initLogin>>token={},userKey={}",feishuToken,userKey);
		AuthContext.setContext(AuthContext.create(feishuToken, userKey));
	}

}
