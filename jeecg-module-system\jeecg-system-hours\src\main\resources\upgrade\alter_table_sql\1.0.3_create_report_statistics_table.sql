-- 创建部门/项目工时统计报表聚合表
-- 版本：1.0.3
-- 创建时间：2025-07-07
-- 说明：用于预聚合统计数据，支持动态列展示，定时任务每小时更新一次

-- 1. 创建部门项目工时统计汇总表
CREATE TABLE `dept_project_hour_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_period` varchar(20) NOT NULL DEFAULT 'daily' COMMENT '统计周期：daily-按日, weekly-按周, monthly-按月',
  `department_id` varchar(64) NOT NULL COMMENT '部门ID',
  `department_name` varchar(100) NOT NULL COMMENT '部门名称',
  `project_id` varchar(64) NOT NULL COMMENT '项目ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `project_code` varchar(50) DEFAULT NULL COMMENT '项目编码',
  `total_hours` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总工时',
  `user_count` int(11) NOT NULL DEFAULT '0' COMMENT '参与人数',
  `record_count` int(11) NOT NULL DEFAULT '0' COMMENT '记录条数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_holiday` tinyint(1) DEFAULT '0' COMMENT '是否假日：默认 否',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_dept_project` (`stat_date`,`stat_period`,`department_id`,`project_id`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_stat_period` (`stat_period`),
  KEY `idx_dept_date_period` (`department_id`,`stat_date`,`stat_period`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='部门项目工时统计表';
