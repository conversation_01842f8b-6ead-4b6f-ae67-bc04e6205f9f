package org.jeecg.modules.hours.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.hours.entity.HourRecordApprover;

import java.util.List;

/**
 * @Description: 工时单审批人清单表 Service接口
 * @Author: jeecg-boot
 * @Date: 2024-05-01
 * @Version: V1.0
 */
public interface IHourRecordApproverService extends IService<HourRecordApprover> {
    // 可扩展自定义方法，如批量移交等

    /**
     * 根据工时单、项目列表、项目key，批量生成审批人清单（含项目负责人、预留直属领导）
     * @param hourRecordId 工时单ID
     * @param projectIds 项目ID列表
     * @param projectKey 工作空间标识
     * @return 审批人清单
     */
    List<HourRecordApprover> buildApproverList(String hourRecordId, List<String> projectIds, String projectKey,Long applyId);

    /**
     * 审批处理（单个或批量）
     * @param recordId 工时单ID
     * @param approverId 审批人ID
     * @param approverName 审批人姓名
     * @param approve 是否同意 true-同意 false-拒绝
     * @param remarks 审批意见/驳回原因
     * @return 是否成功
     */
    boolean processApproval(String recordId, String approverId, String approverName, boolean approve, String remarks,Long applyId);

    /**
     * 批量移交审批人，严格去重，移交前如新审批人已存在则直接删除当前审批人
     * @param hourRecordApproverIds 逗号分隔的applyId
     * @param currentUserId 当前用户ID
     * @param newApproverId 新审批人ID
     * @param newApproverName 新审批人姓名
     * @return 被忽略（直接删除）的applyId列表
     */
    boolean batchTransferApprover(String hourRecordApproverIds, String currentUserId, String newApproverId, String newApproverName);
} 