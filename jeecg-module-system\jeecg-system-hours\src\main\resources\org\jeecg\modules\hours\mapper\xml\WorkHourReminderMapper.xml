<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.WorkHourReminderMapper">

    <!-- 查询有考勤无工时登记的用户（飞书考勤校验模式） -->
    <select id="findUnsubmittedWithAttendance" resultType="org.jeecg.modules.hours.vo.UnsubmittedWorkHourVO">
        SELECT
            a.user_id as userId,
            fu.name as userName,
            fu.union_id as unionId,
            #{projectKey} as projectKey,
            GROUP_CONCAT(DISTINCT DATE(a.stat_date) ORDER BY a.stat_date SEPARATOR ',') as unsubmittedDatesStr
        FROM sys_user_attendance_stat a
        INNER JOIN fs_user fu ON a.union_id  = fu.union_id AND fu.project_key = #{projectKey}
        LEFT JOIN hour_record hr ON fu.user_key = hr.user_id
            AND DATE(a.stat_date) = DATE(hr.work_date)
            AND hr.project_id = #{projectKey}
            AND hr.status IN (1, 2) <!-- 1-审批中, 2-已完成（已提交状态） -->
        WHERE a.stat_date IN
        <foreach collection="businessDates" item="date" open="(" separator="," close=")">
            #{date}
        </foreach>
        AND a.work_hours > 0 <!-- 有考勤记录 -->
        AND hr.id IS NULL <!-- 无工时登记记录 -->
        AND fu.status = '1' <!-- 用户状态正常 -->
        GROUP BY a.user_id, fu.name, fu.union_id
        ORDER BY a.user_id
    </select>

    <!-- 根据工时上限查询未提交工时的用户（手动校验模式） -->
    <select id="findUnsubmittedByWorkHourLimit" resultType="org.jeecg.modules.hours.vo.UnsubmittedWorkHourVO">
        SELECT
            fu.user_key as userId,
            fu.name as userName,
            fu.union_id as unionId,
            #{projectKey} as projectKey,
            GROUP_CONCAT(DISTINCT dates.business_date ORDER BY dates.business_date SEPARATOR ',') as unsubmittedDatesStr
        FROM (
            <foreach collection="businessDates" item="date" separator=" UNION ALL ">
                SELECT #{date} as business_date
            </foreach>
        ) dates
        CROSS JOIN fs_user fu
        LEFT JOIN hour_record hr ON fu.user_key = hr.user_id
            AND DATE(hr.work_date) = dates.business_date
            AND hr.project_id = #{projectKey}
            AND hr.status IN (1, 2) <!-- 1-审批中, 2-已完成（已提交状态） -->
        WHERE fu.project_key = #{projectKey}
        AND fu.status = '1' <!-- 用户状态正常 -->
        AND (
            hr.id IS NULL <!-- 无工时登记记录 -->
            OR
            (
                SELECT COALESCE(SUM(hr2.hours), 0)
                FROM hour_record hr2
                WHERE hr2.user_id = fu.user_key
                AND DATE(hr2.work_date) = dates.business_date
                AND hr2.project_id = #{projectKey}
                AND hr2.status IN (1, 2)
            ) <![CDATA[ < ]]> #{maxWorkHourPerDay} <!-- 工时登记小于上限 -->
        )
        GROUP BY fu.user_key, fu.name, fu.union_id
        ORDER BY fu.user_key
    </select>

</mapper>
