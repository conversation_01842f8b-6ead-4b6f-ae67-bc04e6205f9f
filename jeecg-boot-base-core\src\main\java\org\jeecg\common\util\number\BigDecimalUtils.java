package org.jeecg.common.util.number;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

public class BigDecimalUtils {

	public static BigDecimal zero = BigDecimal.valueOf(0);

	/**
	 * a < b
	 */
	public static boolean lessThan(BigDecimal a, BigDecimal b) {
		return a.compareTo(b) == -1;
	}

	/**
	 * a == b
	 */
	public static boolean equalsThan(BigDecimal a, BigDecimal b) {
		return a.compareTo(b) == 0;
	}

	/**
	 * a > b
	 */
	public static boolean greaterThan(BigDecimal a, BigDecimal b) {
		return a.compareTo(b) == 1;
	}

	/**
	 * a >= b
	 */
	public static boolean greaterEqualsThan(BigDecimal a, BigDecimal b) {
		return a.compareTo(b) > -1;
	}

	/**
	 * a <= b
	 */
	public static boolean lessEqualsThan(BigDecimal a, BigDecimal b) {
		return a.compareTo(b) < 1;
	}

	/**
	 * a + b
	 */
	public static BigDecimal add(BigDecimal a, BigDecimal b) {
		return a.add(b);
	}

	/**
	 * |a|
	 */
	public static BigDecimal abs(BigDecimal a) {
		if (a != null) {
			return a.abs();
		} else {
			return BigDecimal.valueOf(0);
		}

	}

	/**
	 * a - b
	 */
	public static BigDecimal subtract(BigDecimal a, BigDecimal b) {
		if (null == a) {
			a = BigDecimal.valueOf(0);
		}
		if (null == b) {
			b = BigDecimal.valueOf(0);
		}
		return a.subtract(b);
	}

	/**
	 * a * b
	 */
	public static BigDecimal multiply(BigDecimal a, BigDecimal b) {
		return a.multiply(b);
	}

	/**
	 * a / b
	 *
	 * @return 保留两位小数
	 */
	public static BigDecimal divide(BigDecimal a, BigDecimal b) {
		return a.divide(b, 2, RoundingMode.HALF_UP);
	}

	/**
	 * a/b * 100
	 *
	 * @return 保留两位小数的百分比
	 */
	public static BigDecimal divide2Percentage(BigDecimal a, BigDecimal b) {
		if (null == b || equalsThan(b, zero)) {
			return BigDecimal.valueOf(0);
		}
		if (null == a || equalsThan(b, zero)) {
			return BigDecimal.valueOf(0);
		}
		try {
			return a.divide(b, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(1),
					2, RoundingMode.HALF_UP);
		} catch (Exception e) {
			return BigDecimal.valueOf(0);
		}
	}

	/**
	 * a * 100%
	 *
	 * @return 保留两位小数
	 */
	public static BigDecimal toPercentage(BigDecimal a) {
		return a.multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(1), 2, RoundingMode.HALF_UP);
	}

	public static String toEnNum(Integer num) {
		if (num == null || num > 20 || num < 0) {
			return "";
		}

		String[] enNum = { "zero", "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten",
				"eleven", "twelve", "thirteen", "fourteen", "fifteen", "sixteen", "seventeen", "eighteen", "nineteen",
				"twenty" };
		String str = enNum[num];
		if (str != null) {
			return str.toUpperCase();
		} else {
			return "";
		}
	}

	public static String fmt(BigDecimal value, DecimalFormat df0) {
		if (null == value) {
			value = new BigDecimal(0);
		}
		return df0.format(value);
	}

	private static BigDecimal get(BigDecimal value, BigDecimal defValue) {
		if (null == value) {
			return defValue;
		}
		return value;
	}

}
