package org.jeecg.modules.hours.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.hours.entity.FsDataTask;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Description: Mapper for fs_data_task table
 * @Author: jeecg-boot
 * @Date: 2023-08-20
 * @Version: V1.0
 */
public interface FsDataTaskMapper extends BaseMapper<FsDataTask> {
    /**
     * 插入或更新任务数据
     * @param dataTask 任务数据对象
     * @return 影响的行数
     */
    int insertOrUpdate(FsDataTask dataTask);

    /**
     * 批量插入或更新任务数据
     * @param taskList 任务数据列表
     * @return 影响的行数
     */
    int saveOrUpdateBatch(@Param("list") List<FsDataTask> taskList);

    // Custom methods for FsDataTask can be added here
} 