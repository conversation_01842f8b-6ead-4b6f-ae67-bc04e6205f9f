package org.jeecg.modules.hours.service.impl;

import org.jeecg.modules.hours.dto.DeptProjectDailyHourReportDTO;
import org.jeecg.modules.hours.service.IDeptProjectDailyHourExportService;
import org.jeecg.modules.hours.service.IDeptProjectDailyHourStatisticsService;
import org.jeecg.modules.hours.vo.DeptProjectDailyHourReportResult;
import org.jeecg.modules.hours.vo.DeptProjectDailyHourReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.apache.poi.ss.usermodel.Workbook;

/**
 * @Description: 部门项目每日工时分布导出服务实现
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Service
@Slf4j
public class DeptProjectDailyHourExportServiceImpl implements IDeptProjectDailyHourExportService {

    @Autowired
    private IDeptProjectDailyHourStatisticsService deptProjectDailyHourStatisticsService;

    @Override
    public void exportDeptProjectDailyHourReport(DeptProjectDailyHourReportDTO reportDTO, HttpServletResponse response) {
        try {
            // 获取报表数据
            DeptProjectDailyHourReportResult reportResult = 
                deptProjectDailyHourStatisticsService.getDeptProjectDailyHourReport(reportDTO);

            // 设置响应头
            String fileName = "部门项目每日工时分布报表_" + reportDTO.getYearMonth() + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", 
                "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));

            // 导出Excel格式
            exportToExcel(reportResult, response.getOutputStream());
            
            log.info("部门项目每日工时分布报表导出完成: {}", fileName);

        } catch (Exception e) {
            log.error("导出部门项目每日工时分布报表失败", e);
            throw new RuntimeException("导出报表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出为Excel格式
     */
    private void exportToExcel(DeptProjectDailyHourReportResult reportResult, OutputStream outputStream) 
            throws IOException {
        
        // 构建列定义
        List<ExcelExportEntity> colList = new ArrayList<>();
        
        // 固定列
        colList.add(new ExcelExportEntity("部门", "departmentName", 20));
        colList.add(new ExcelExportEntity("项目", "projectName", 25));
        
        // 动态日期列 - 只添加当前月份实际存在的日期
        if (reportResult.getDayColumns() != null) {
            for (DeptProjectDailyHourReportResult.DayColumnVO dayColumn : reportResult.getDayColumns()) {
                String columnTitle = dayColumn.getDayName();
                if (dayColumn.getIsHoliday() != null && dayColumn.getIsHoliday()) {
                    columnTitle += "(假)";
                }
                // 使用columnKey作为属性名，避免固定的day1Hours格式
                colList.add(new ExcelExportEntity(columnTitle, dayColumn.getColumnKey(), 10));
            }
        }
        
        // 小计列
        colList.add(new ExcelExportEntity("小计", "totalHours", 15));
        
        // 构建数据列表 - 使用动态数据结构
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        // 添加数据行
        if (reportResult.getDeptProjectHourData() != null) {
            for (DeptProjectDailyHourReportVO row : reportResult.getDeptProjectHourData()) {
                Map<String, Object> rowData = new HashMap<>();
                rowData.put("departmentName", row.getDepartmentName());
                rowData.put("projectName", row.getProjectName() + 
                    (row.getProjectCode() != null ? " (" + row.getProjectCode() + ")" : ""));
                rowData.put("totalHours", row.getTotalHours());
                
                // 设置每日工时 - 动态设置
                if (reportResult.getDayColumns() != null && row.getDailyHours() != null) {
                    for (DeptProjectDailyHourReportResult.DayColumnVO dayColumn : reportResult.getDayColumns()) {
                        BigDecimal hours = row.getDailyHours().get(dayColumn.getColumnKey());
                        rowData.put(dayColumn.getColumnKey(), hours != null ? hours : BigDecimal.ZERO);
                    }
                }
                
                dataList.add(rowData);
            }
        }
        
        // 添加总计行
        if (reportResult.getTotalRow() != null) {
            DeptProjectDailyHourReportVO totalRow = reportResult.getTotalRow();
            Map<String, Object> totalData = new HashMap<>();
            totalData.put("departmentName", totalRow.getDepartmentName());
            totalData.put("projectName", "");
            totalData.put("totalHours", totalRow.getTotalHours());
            
            // 设置总计的每日工时
            if (reportResult.getDayColumns() != null && totalRow.getDailyHours() != null) {
                for (DeptProjectDailyHourReportResult.DayColumnVO dayColumn : reportResult.getDayColumns()) {
                    BigDecimal hours = totalRow.getDailyHours().get(dayColumn.getColumnKey());
                    totalData.put(dayColumn.getColumnKey(), hours != null ? hours : BigDecimal.ZERO);
                }
            }
            
            dataList.add(totalData);
        }
        
        // 创建导出参数
        ExportParams exportParams = new ExportParams("部门项目每日工时分布报表", "工时统计");
        exportParams.setCreateHeadRows(true);
        
        // 生成Excel
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, dataList);
        workbook.write(outputStream);
        workbook.close();
    }

}