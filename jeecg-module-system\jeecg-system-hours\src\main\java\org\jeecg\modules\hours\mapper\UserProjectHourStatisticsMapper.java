package org.jeecg.modules.hours.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.UserProjectHourStatistics;
import org.jeecg.modules.hours.vo.ProjectColumnVO;
import org.jeecg.modules.hours.vo.UserProjectHourReportVO;
import org.jeecg.modules.hours.vo.UserProjectHourReportResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 人员项目工时统计表
 * @Author: jeecg-boot
 * @Date: 2025-07-08
 * @Version: V1.0
 */
public interface UserProjectHourStatisticsMapper extends BaseMapper<UserProjectHourStatistics> {
    
    /**
     * 获取人员项目工时统计的项目列信息
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statPeriod 统计周期
     * @param userIds 用户ID列表
     * @param projectIds 项目ID列表
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @return 项目列信息
     */
    List<ProjectColumnVO> getProjectColumns(@Param("startDate") Date startDate,
                                           @Param("endDate") Date endDate,
                                           @Param("statPeriod") String statPeriod,
                                           @Param("userIds") List<String> userIds,
                                           @Param("projectIds") List<String> projectIds,
                                           @Param("isHoliday") Integer isHoliday);
    
    /**
     * 获取人员项目工时统计报表数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statPeriod 统计周期
     * @param userIds 用户ID列表
     * @param projectIds 项目ID列表
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @return 人员项目工时统计数据
     */
    List<UserProjectHourReportVO> getUserProjectHourReport(@Param("startDate") Date startDate,
                                                          @Param("endDate") Date endDate,
                                                          @Param("statPeriod") String statPeriod,
                                                          @Param("userIds") List<String> userIds,
                                                          @Param("projectIds") List<String> projectIds,
                                                          @Param("isHoliday") Integer isHoliday);
    
    /**
     * 获取人员项目工时统计总计行数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statPeriod 统计周期
     * @param userIds 用户ID列表
     * @param projectIds 项目ID列表
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @return 总计行数据
     */
    List<UserProjectHourReportVO> getTotalRow(@Param("startDate") Date startDate,
                                            @Param("endDate") Date endDate,
                                            @Param("statPeriod") String statPeriod,
                                            @Param("userIds") List<String> userIds,
                                            @Param("projectIds") List<String> projectIds,
                                            @Param("isHoliday") Integer isHoliday);
    
    /**
     * 批量插入或更新统计数据
     * @param list 统计数据列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<UserProjectHourStatistics> list);
    
    /**
     * 删除指定日期的统计数据
     * @param statDate 统计日期
     * @param statPeriod 统计周期
     * @return 影响行数
     */
    int deleteByStatDate(@Param("statDate") Date statDate, @Param("statPeriod") String statPeriod);
    
    /**
     * 获取汇总统计数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statPeriod 统计周期
     * @param userIds 用户ID列表
     * @param projectIds 项目ID列表
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @return 汇总统计数据
     */
    UserProjectHourReportResult.ReportSummaryVO getSummaryData(@Param("startDate") Date startDate,
                                                               @Param("endDate") Date endDate,
                                                               @Param("statPeriod") String statPeriod,
                                                               @Param("userIds") List<String> userIds,
                                                               @Param("projectIds") List<String> projectIds,
                                                               @Param("isHoliday") Integer isHoliday);
}