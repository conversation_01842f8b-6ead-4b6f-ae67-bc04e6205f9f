package org.jeecg.modules.hours.job;

import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.ExecutionException;

import org.jeecg.modules.hours.service.IDeptProjectHourStatisticsService;
import org.jeecg.modules.hours.service.IUserProjectHourStatisticsService;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: (部门)人员/项目工时统计报表-批量统计
 * @Author: jeecg-boot
 * @Date: 2025-07-07
 * @Version: V1.0
 */
@Component
@Slf4j
public class HourStatisticsBatchJob  {

    @Autowired
    private IDeptProjectHourStatisticsService deptProjectHourStatisticsService;

    /**
     * 批量统计的天数（默认最近7天）
     */
    @Value("${hour.statistics.batch.days:60}")
    private int batchDays;
    @Autowired
    private IUserProjectHourStatisticsService userProjectHourStatisticsService;
//    @Scheduled(cron = "0 0 1 * * ?")
    @Scheduled(cron = "0 */14 * * * ?")
    public void execute() throws ExecutionException {
        log.info("人员项目工时统计报表-批量统计任务开始执行, 批量天数={}", batchDays);
        
        try {
            // 计算开始和结束日期
            Calendar calendar = Calendar.getInstance();
            Date endDate = calendar.getTime();
            
            calendar.add(Calendar.DAY_OF_MONTH, -batchDays);
            Date startDate = calendar.getTime();
            
            log.info("批量统计日期范围: {} 至 {}", startDate, endDate);
            
            // 执行批量统计
            int processedCount = deptProjectHourStatisticsService.batchCalculateHourStatistics(
                startDate, endDate, "daily");

            int totalProcessed = userProjectHourStatisticsService.batchCalculateHourStatistics(startDate, endDate, "daily");

            log.info("人员项目工时统计报表-批量统计任务执行完成, 处理记录数={}", totalProcessed);
            
        } catch (Exception e) {
            log.error("人员项目工时统计报表任务执行失败", e);
            throw new ExecutionException("人员项目工时统计报表任务执行失败: " + e.getMessage(), e);
        }
    }
}