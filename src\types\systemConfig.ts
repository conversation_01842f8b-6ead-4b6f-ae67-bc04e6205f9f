// 系统配置项类型定义
export interface SysConfig {
  id: string;
  configKey: string;
  configValue: string;
  configName: string;
  configGroup: string;
  remark?: string;
  status: '0' | '1'; // '0'正常 '1'停用
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
}

// 系统配置分页查询参数
export interface SysConfigQuery {
  configKey?: string;
  configName?: string;
  configGroup?: string;
  status?: string;
  pageNo: number;
  pageSize: number;
}

// 系统配置分组统计
export interface SysConfigGroupStat {
  groupName: string;
  count: number;
}

// 分页结果
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
} 