package org.jeecg.modules.hapi.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import javax.servlet.http.HttpServletRequest;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.hapi.vo.FeishuLoginUserVO;
import org.jeecg.modules.hapi.vo.MeegoTokenVO;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.entity.FsProject;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.service.IFsUserService;
import org.jeecg.modules.hours.service.IFsProjectService;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.jeecg.modules.hours.utils.UserInfoUtils;
import org.jeecg.modules.hours.util.RequestHeaderUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.core.utils.Jsons;
import com.lark.project.core.utils.Lists;
import com.lark.project.service.plugin.builder.GetUserPluginTokenReq;
import com.lark.project.service.plugin.builder.GetUserPluginTokenResp;
import com.lark.project.service.plugin.builder.RefreshTokenReq;
import com.lark.project.service.plugin.builder.RefreshTokenResp;
import com.lark.project.service.user.builder.QueryUserDetailReq;
import com.lark.project.service.user.builder.QueryUserDetailResp;
import com.lark.project.service.user.model.UserBasicInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api(tags = "飞书授权")
@RestController
@RequestMapping("/hapi/plugin/auth/")
public class MeegoAuthController  {

	@Value("${feishu.project.plugin.id}")
	private String pluginId;

	@Value("${feishu.project.plugin.secret}")
	private String pluginSecret;
	
	@Value("${feishu.api.base-url:https://project.feishu.cn/}")
	private String feishuApiBaseUrl;
	
	@Autowired
    private RedisUtil redisUtil;
	
	@Autowired
	private IFeishuAuthService feishuAuthService;
	
	@Autowired
	private IFsUserService fsUserService;

	@Autowired
	private IFsProjectService fsProjectService;

	@Autowired
	private FsSyncTask fsSyncTask;
	
	@Autowired
	private UserInfoUtils userInfoUtils;

	/**
	 * 初始化配置
	 */
	@AutoLog(value = "初始化配置")
	@ApiOperation(value = "初始化配置", notes = "获取系统配置并初始化项目空间数据")
	@GetMapping(value = "/initConfig")
	public Result<?> initConfig(HttpServletRequest req) {
		try {
			// 从请求头中获取项目标识
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			
			// 验证项目是否存在
			FsProject project = fsProjectService.lambdaQuery()
				.eq(FsProject::getProjectKey, projectKey)
				.eq(FsProject::getStatus, "1")
				.one();
				
			if (project == null) {
				log.warn("项目[{}]不存在或状态异常", projectKey);
				return Result.error("项目不存在或状态异常");
			}
			
			try {
				fsSyncTask.startInitSpace(projectKey);
			}catch(Exception e) {
				log.error(e.getMessage(),e);
			}
			
			// 返回配置数据
			Map<String, Object> config = new HashMap<>();
			// TODO: 后续可以在这里添加更多配置数据
			
			return Result.OK(config);
		} catch (Exception e) {
			log.error("初始化配置异常", e);
			return Result.error("初始化配置失败: " + e.getMessage());
		}
	}

	/**
	 * 飞书用户登录
	 */
	@AutoLog(value = "飞书用户登录")
	@ApiOperation(value = "飞书用户登录", notes = "飞书用户登录，保存用户信息并生成系统token")
	@GetMapping(value = "/login")
	public Result<?> login(@RequestParam("code") String code, HttpServletRequest req) {
		log.info("接收到飞书用户登录请求，code: {}", code);

		try {
			// 从请求头中获取项目标识
			String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
			
			// 验证项目是否存在
			FsProject project = fsProjectService.lambdaQuery()
				.eq(FsProject::getProjectKey, projectKey)
				.eq(FsProject::getStatus, "1")
				.one();
				
			if (project == null) {
				log.warn("项目[{}]不存在或状态异常", projectKey);
				return Result.error("项目不存在或状态异常");
			}

			// 初始化飞书项目SDK
			Client client = Client.newBuilder(pluginId, pluginSecret).openBaseUrl("https://project.feishu.cn/").build();

			// 通过授权码获取飞书token
			GetUserPluginTokenReq tokenReq = GetUserPluginTokenReq.newBuilder()
					.code(code)
					.grantType("authorization_code")
					.build();

			GetUserPluginTokenResp tokenResp = client.getPluginService().getUserPluginToken(
					tokenReq, RequestOptions.newBuilder().build());
			if(null!=tokenResp.getErr() && tokenResp.getErr().getCode()!=0) {
				log.error("errCode={},errMsg={}",tokenResp.getErr().getCode(),tokenResp.getErr().getMsg());
			}

			// 检查token响应
			if (tokenResp.getData() == null || tokenResp.getData().getToken() == null) {
				log.error("飞书登录失败，获取token结果为空");
				return Result.error("登录失败：无法获取用户授权");
			}
			
			String feishuToken = tokenResp.getData().getToken();
			String refreshToken = tokenResp.getData().getRefreshToken();
			Integer expireTime = tokenResp.getData().getExpireTime();
			Integer refreshExpireTime = tokenResp.getData().getRefreshTokenExpireTime();
			String userKey = tokenResp.getData().getUserKey();
			
			// 保存飞书授权信息
			feishuAuthService.setAuthInfo(
				feishuToken,
				refreshToken,
				expireTime,
				refreshExpireTime,
				userKey
			);
			
			log.info("获取飞书token成功，用户Key: {},token:{}", userKey,feishuToken);
			
			// 获取用户信息
			QueryUserDetailReq req1 = QueryUserDetailReq.newBuilder()
					.userKeys(Lists.newArrayList(userKey))
					.build();

			QueryUserDetailResp resp1 = client.getUserService().queryUserDetail(req1, RequestOptions.newBuilder()
					.userKey(userKey).accessToken(feishuToken)
					.build());
			
			log.info("resp={}", Jsons.DEFAULT.toJson(resp1.getData()));
			
			UserBasicInfo userInfo = resp1.getData().get(0);
			
			// 这里简化处理，将userKey作为用户ID，其他信息可能需要通过其他方式获取
			String userId = userKey;
			String name = userInfo.getNameCn();
			String email = userInfo.getEmail();
			String mobile = "";
			
			// 查询用户是否已存在
			LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(FsUser::getUserKey, userId);
			FsUser user = fsUserService.getOne(queryWrapper);
			
			if (user == null) {
				// 用户不存在，创建新用户
				user = new FsUser();
				user.setId(UUID.randomUUID().toString().replace("-", ""));
				user.setUserKey(userId);
				user.setStatus("1"); // 正常状态
			}
			
			// 更新用户信息
			user.setName(name);
			user.setEmail(email);
			user.setMobile(mobile);
			user.setSyncTime(new Date());
			user.setUnionId(userInfo.getOutID());
			
			// 保存或更新用户
			boolean saved = fsUserService.saveOrUpdate(user);
			if (!saved) {
				log.error("保存用户信息失败");
				return Result.error("登录失败：保存用户信息失败");
			}
			
			log.info("用户信息保存成功: {}", user.getUserKey());
			
			// 使用JwtUtil生成系统token
			String customToken = JwtUtil.sign(userId, "meego");
			
			// 将token存入Redis，有效期24小时
			redisUtil.set("meego:login:token:" + userId, customToken, 24 * 60 * 60);
			
			// 构建返回信息
			FeishuLoginUserVO loginVO = new FeishuLoginUserVO();
			loginVO.setToken(customToken);
			loginVO.setUserId(userId);
			loginVO.setName(name);
			loginVO.setEmail(email);
			loginVO.setMobile(mobile);
			loginVO.setDeptId(user.getDeptId());
			
			// 设置过期时间
			Date expiryDate = new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000);
			loginVO.setExpireTime(expiryDate);
			
			log.info(">>用户登录凭证: token={},userKey={},name={}",customToken, user.getUserKey(),user.getName());

			return Result.OK(loginVO);
		} catch (Exception e) {
			log.error("飞书用户登录异常", e);
			return Result.error("登录失败: " + e.getMessage());
		}
	}

	/**
	 * 验证登录token
	 */
	@AutoLog(value = "验证token")
	@ApiOperation(value = "验证token", notes = "验证用户登录token是否有效")
	@GetMapping(value = "/verify")
	public Result<?> verifyToken(@RequestParam("token") String token, HttpServletRequest req) {
		log.info("接收到验证token请求");

		try {
			// 解析token，获取用户ID
			String userId = JwtUtil.getUsername(token);
			if (userId == null || userId.isEmpty()) {
				log.warn("无效的token");
				return Result.error("无效的token");
			}
			
			// 验证token是否存在于Redis中
			String cacheKey = "meego:login:token:" + userId;
			if (!redisUtil.hasKey(cacheKey)) {
				log.warn("token不存在或已失效");
				return Result.error("token已失效，请重新登录");
			}
			
			// 验证Redis中存储的token是否与传入的token一致
			String cachedToken = (String) redisUtil.get(cacheKey);
			if (!token.equals(cachedToken)) {
				log.warn("token不匹配");
				return Result.error("无效的token");
			}
			
			// 查询用户信息
			LambdaQueryWrapper<FsUser> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(FsUser::getUserKey, userId);
			FsUser user = fsUserService.getOne(queryWrapper);
			
			if (user == null) {
				log.warn("未找到对应用户");
				return Result.error("未找到用户信息");
			}
			
			// 刷新token有效期
			redisUtil.expire(cacheKey, 24 * 60 * 60);
			
			// 构建返回信息
			FeishuLoginUserVO loginVO = new FeishuLoginUserVO();
			loginVO.setToken(token);
			loginVO.setUserId(userId);
			loginVO.setName(user.getName());
			loginVO.setEmail(user.getEmail());
			loginVO.setMobile(user.getMobile());
			loginVO.setDeptId(user.getDeptId());
			
			// 设置过期时间
			Date expiryDate = new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000);
			loginVO.setExpireTime(expiryDate);
			
			return Result.OK(loginVO);
		} catch (Exception e) {
			log.error("验证token异常", e);
			return Result.error("验证失败: " + e.getMessage());
		}
	}
	
	/**
	 * 用户注销
	 */
	@AutoLog(value = "用户注销")
	@ApiOperation(value = "用户注销", notes = "清除用户登录token")
	@GetMapping(value = "/logout")
	public Result<?> logout(@RequestParam("token") String token, HttpServletRequest req) {
		log.info("接收到用户注销请求");

		try {
			// 解析token，获取用户ID
			String userId = JwtUtil.getUsername(token);
			if (userId == null || userId.isEmpty()) {
				return Result.OK("注销成功");
			}
			
			// 删除Redis中的token
			String cacheKey = "meego:login:token:" + userId;
			if (redisUtil.hasKey(cacheKey)) {
				redisUtil.del(cacheKey);
			}
			
			return Result.OK("注销成功");
		} catch (Exception e) {
			log.error("用户注销异常", e);
			return Result.error("注销失败: " + e.getMessage());
		}
	}

	/**
	 * 将token部分字符替换为*，用于日志打印
	 */
	private String maskToken(String token) {
		if (token == null || token.length() <= 10) {
			return "***";
		}
		return token.substring(0, 5) + "..." + token.substring(token.length() - 5);
	}
}