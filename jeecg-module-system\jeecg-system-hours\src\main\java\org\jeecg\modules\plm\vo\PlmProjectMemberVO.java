package org.jeecg.modules.plm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * PLM项目成员VO类
 */
@Data
@ApiModel(value = "PlmProjectMemberVO对象", description = "PLM项目成员VO")
public class PlmProjectMemberVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 用户角色
     */
    @ApiModelProperty(value = "用户角色")
    private String userRole;
} 