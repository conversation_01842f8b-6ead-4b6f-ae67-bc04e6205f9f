package org.jeecg.modules.hours.service.impl;

import org.jeecg.modules.hours.entity.FsDepartment;
import org.jeecg.modules.hours.mapper.FsDepartmentMapper;
import org.jeecg.modules.hours.service.IFsDepartmentService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * @Description: 部门表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
@Service
public class FsDepartmentServiceImpl extends ServiceImpl<FsDepartmentMapper, FsDepartment> implements IFsDepartmentService {

    /**
     * 说明：isHour 字段已支持，ServiceImpl 层无需特殊处理，MyBatis-Plus自动映射
     */
    @Override
    public FsDepartment getDepartmentTreeById(String departmentId) {
        // 获取部门信息
        FsDepartment department = this.getById(departmentId);
        if (department == null) {
            return null;
        }
        
        // 查找子部门
        LambdaQueryWrapper<FsDepartment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FsDepartment::getParentId, departmentId);
        List<FsDepartment> children = this.list(queryWrapper);
        
        // 递归查找子部门的子部门
        if (children != null && !children.isEmpty()) {
            for (FsDepartment child : children) {
                FsDepartment childTree = this.getDepartmentTreeById(child.getId());
                if (childTree != null) {
                    child.setChildren(childTree.getChildren());
                }
            }
            department.setChildren(children);
        }
        
        return department;
    }
    
    @Override
    public List<FsDepartment> getFilteredDepartmentTree() {
        // 1. 先查询所有部门
        List<FsDepartment> targetDepartments = this.lambdaQuery().eq(FsDepartment::getIsHour, 1).list();

        if (targetDepartments.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 3. 为每个目标部门构建完整的树结构
        List<FsDepartment> result = new ArrayList<>();
        for (FsDepartment dept : targetDepartments) {
            FsDepartment tree = this.getDepartmentTreeById(dept.getId());
            if (tree != null) {
                result.add(tree);
            }
        }
        
        return result;
    }
} 