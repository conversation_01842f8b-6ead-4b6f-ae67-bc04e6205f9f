# 项目人员数据维护设计文档

## 1. 功能概述

项目人员数据维护模块主要用于管理从PLM系统同步的项目数据，并维护项目的人员配置。该模块允许用户查看项目基本信息（只读），并对项目经理（单选）和项目成员（多选）进行配置和管理。

## 2. 数据模型

### 2.1 数据表结构

#### `plm_project` 表 - PLM项目基本信息
```sql
CREATE TABLE `plm_project` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `project_code` varchar(50) NOT NULL COMMENT '系统项目ID(PLM项目编码)',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `project_status` char(2) NOT NULL COMMENT '项目状态(AN:未启动/AO:进行中/AD:冻结/AI:完成)',
  `project_level` varchar(20) DEFAULT NULL COMMENT '项目级别',
  `project_desc` varchar(500) DEFAULT NULL COMMENT '项目描述',
  `manager_id` varchar(50) DEFAULT NULL COMMENT '项目经理ID',
  `manager_name` varchar(50) DEFAULT NULL COMMENT '项目经理姓名',
  `sync_time` datetime DEFAULT NULL COMMENT '同步时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_code` (`project_code`),
  KEY `idx_project_status` (`project_status`),
  KEY `idx_manager_id` (`manager_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PLM项目信息表';
```

#### `plm_project_member` 表 - 项目成员关联
```sql
CREATE TABLE `plm_project_member` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `project_id` varchar(36) NOT NULL COMMENT '关联项目表ID',
  `project_code` varchar(50) NOT NULL COMMENT '系统项目ID',
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户姓名',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_user` (`project_id`,`user_id`),
  KEY `idx_project_code` (`project_code`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PLM项目成员关联表';
```

#### `plm_sync_log` 表 - 同步日志
```sql
CREATE TABLE `plm_sync_log` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `sync_type` varchar(20) NOT NULL COMMENT '同步类型(全量/增量)',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `total_count` int DEFAULT '0' COMMENT '总记录数',
  `success_count` int DEFAULT '0' COMMENT '成功数',
  `fail_count` int DEFAULT '0' COMMENT '失败数',
  `status` varchar(20) DEFAULT NULL COMMENT '状态(进行中/成功/失败)',
  `error_msg` text COMMENT '错误信息',
  PRIMARY KEY (`id`),
  KEY `idx_sync_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PLM同步日志表';
```

### 2.2 状态映射

PLM项目状态映射关系：
| 状态码 | 状态名称 | 说明 |
|-------|---------|------|
| AN | 未启动 | 项目已创建但尚未开始 |
| AO | 进行中 | 项目正在进行中 |
| AD | 冻结 | 项目暂时冻结 |
| AI | 完成 | 项目已完成 |

## 3. 功能设计

### 3.1 功能列表

1. **项目列表查询**：
   - 支持按项目ID、项目名称、项目状态筛选
   - 分页展示项目信息
   - 显示项目基本信息和项目经理
   - 页面在 SettingsPage.tsx中添加一个板块[项目人员数据维护],可参考[登记提醒设置]

2. **项目人员维护**：
   - 查看项目基本信息（只读）
   - 配置项目经理（单选）
   - 配置项目成员（多选）
   - 编辑时选择的用户下拉框,可参考[工时管理]中人员选择框
   - 保存人员配置

3. **PLM数据同步**（此部分接口预留）：
   - 手动触发同步
   - 查看同步日志
   - 同步状态展示

### 3.2 权限设计

| 功能点 | 管理员 | 项目经理 | 普通用户 |
|-------|-------|---------|---------|
| 项目列表查询 | ✓ | ✓ | ✓ |
| 项目人员维护 | ✓ | ✓ | ✗ |
| PLM数据同步 | ✓ | ✗ | ✗ |

### 3.3 流程设计

#### 项目人员维护流程
1. 用户进入项目人员维护页面
2. 系统展示项目列表
3. 用户选择需要维护的项目，点击"编辑人员"按钮
4. 系统弹出编辑对话框，展示项目基本信息和人员配置界面
5. 用户选择项目经理（单选）和项目成员（多选）
6. 用户点击保存按钮
7. 系统保存人员配置并刷新项目列表

## 4. 接口设计

### 4.1 后端接口

#### 项目列表查询接口
- **URL**: `/api/project/list`
- **Method**: GET
- **请求参数**:
  ```json
  {
    "pageNo": 1,
    "pageSize": 10,
    "projectCode": "",
    "projectName": "",
    "status": ""
  }
  ```
- **响应结果**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": {
      "records": [
        {
          "id": "1",
          "projectCode": "PRJ001",
          "projectName": "示例项目1",
          "projectStatus": "AO",
          "projectLevel": "A",
          "projectDesc": "这是一个示例项目",
          "managerId": "user1",
          "managerName": "张三",
          "syncTime": "2023-06-01 12:00:00",
          "createTime": "2023-06-01 10:00:00",
          "updateTime": "2023-06-01 12:00:00"
        }
      ],
      "total": 100,
      "size": 10,
      "current": 1,
      "pages": 10
    }
  }
  ```

#### 项目成员查询接口
- **URL**: `/api/project/members`
- **Method**: GET
- **请求参数**:
  ```json
  {
    "projectId": "1"
  }
  ```
- **响应结果**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": [
      {
        "id": "member1",
        "userId": "user2",
        "userName": "李四",
        "projectId": "1",
        "projectCode": "PRJ001"
      }
    ]
  }
  ```

#### 用户列表查询接口
- **URL**: `/api/user/list`
- **Method**: GET
- **请求参数**:
  ```json
  {
    "keyword": ""
  }
  ```
- **响应结果**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": [
      {
        "id": "user1",
        "userName": "张三",
        "department": "研发部"
      }
    ]
  }
  ```

#### 更新项目经理接口
- **URL**: `/api/project/updateManager`
- **Method**: POST
- **请求参数**:
  ```json
  {
    "projectId": "1",
    "managerId": "user1",
    "managerName": "张三"
  }
  ```
- **响应结果**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": true
  }
  ```

#### 更新项目成员接口
- **URL**: `/api/project/updateMembers`
- **Method**: POST
- **请求参数**:
  ```json
  {
    "projectId": "1",
    "projectCode": "PRJ001",
    "members": [
      {
        "id": "user2",
        "name": "李四"
      },
      {
        "id": "user3",
        "name": "王五"
      }
    ]
  }
  ```
- **响应结果**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "result": true
  }
  ```

### 4.2 前端组件

#### 项目列表组件
- 展示项目列表
- 支持筛选和分页
- 提供"编辑人员"按钮

#### 项目人员编辑对话框
- 展示项目基本信息（只读）
- 提供项目经理选择器（单选）
- 提供项目成员选择器（多选）
- 保存和取消按钮

## 5. 界面设计

### 5.1 项目列表页面

![项目列表页面](../assets/project_list.png)

主要元素：
- 搜索区域：项目ID、项目名称、项目状态筛选
- 列表区域：展示项目信息
- 操作区域：编辑人员按钮

### 5.2 项目人员编辑对话框

![项目人员编辑对话框](../assets/project_member_edit.png)

主要元素：
- 项目基本信息区域（只读）
- 项目经理选择器（单选）
- 项目成员选择器（多选）
- 底部按钮区域：保存、取消

## 6. 开发计划

### 6.1 开发任务拆分

1. 数据库表创建（已完成）
2. 后端接口开发
   - 项目列表查询接口
   - 项目成员查询接口
   - 用户列表查询接口
   - 更新项目经理接口
   - 更新项目成员接口
3. 前端页面开发
   - 项目列表组件
   - 项目人员编辑对话框
   - 集成API调用

### 6.2 开发排期

| 任务 | 工作量(人天) | 负责人 |
|-----|------------|-------|
| 数据库表创建 | 0.5 | DBA |
| 后端接口开发 | 2 | 后端开发 |
| 前端页面开发 | 2 | 前端开发 |
| 联调测试 | 1 | 全员 |
| 总计 | 5.5 | - |

## 7. 注意事项

1. **数据同步策略**：
   - 默认只同步"进行中(AO)"状态的项目
   - 可通过配置调整同步范围

2. **数据权限控制**：
   - 项目基本信息为只读
   - 只允许修改项目经理和项目成员

3. **性能优化**：
   - 为常用查询添加索引
   - 实现数据缓存机制

4. **数据一致性**：
   - 确保项目经理唯一性
   - 防止重复添加项目成员 