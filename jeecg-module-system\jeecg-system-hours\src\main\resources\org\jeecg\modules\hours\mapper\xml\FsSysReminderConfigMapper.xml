<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.FsSysReminderConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.jeecg.modules.hours.entity.FsSysReminderConfig">
        <id column="id" property="id" />
        <result column="project_key" property="projectKey" />
        <result column="reminder_type" property="reminderType" />
        <result column="repeat_mode" property="repeatMode" />
        <result column="reminder_day" property="reminderDay" />
        <result column="reminder_time" property="reminderTime" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_key, reminder_type, repeat_mode, reminder_day, reminder_time, 
        remark, status, create_by, create_time, update_by, update_time, del_flag
    </sql>

    <!-- 根据工作空间标识查询提醒配置列表 -->
    <select id="selectByProjectKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM fs_sys_reminder_config
        WHERE project_key = #{projectKey}
          AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据工作空间标识和状态查询提醒配置列表 -->
    <select id="selectByProjectKeyAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM fs_sys_reminder_config
        WHERE project_key = #{projectKey}
          AND status = #{status}
          AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据提醒类型查询启用的提醒配置 -->
    <select id="selectEnabledByReminderType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM fs_sys_reminder_config
        WHERE reminder_type = #{reminderType}
          AND status = '启用'
          AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 批量更新状态 -->
    <update id="batchUpdateStatus">
        UPDATE fs_sys_reminder_config
        SET status = #{status},
            update_by = #{updateBy},
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = 0
    </update>

    <!-- 根据工作空间标识统计提醒配置数量 -->
    <select id="countByProjectKey" resultType="int">
        SELECT COUNT(1)
        FROM fs_sys_reminder_config
        WHERE project_key = #{projectKey}
          AND del_flag = 0
    </select>

    <!-- 根据工作空间标识和提醒类型查询提醒配置 -->
    <select id="selectByProjectKeyAndReminderType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM fs_sys_reminder_config
        WHERE project_key = #{projectKey}
          AND reminder_type = #{reminderType}
          AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询需要执行的提醒配置 -->
    <select id="selectReminderConfigsToExecute" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM fs_sys_reminder_config
        WHERE status = '启用'
          AND del_flag = 0
          AND reminder_time = #{currentTime}
          AND (
            <!-- 每天提醒 -->
            (reminder_type = 'daily')
            OR
            <!-- 每周提醒 -->
            (reminder_type = 'weekly' AND (
                (reminder_day = 'monday' AND #{dayOfWeek} = 1) OR
                (reminder_day = 'tuesday' AND #{dayOfWeek} = 2) OR
                (reminder_day = 'wednesday' AND #{dayOfWeek} = 3) OR
                (reminder_day = 'thursday' AND #{dayOfWeek} = 4) OR
                (reminder_day = 'friday' AND #{dayOfWeek} = 5) OR
                (reminder_day = 'saturday' AND #{dayOfWeek} = 6) OR
                (reminder_day = 'sunday' AND #{dayOfWeek} = 7)
            ))
            OR
            <!-- 每月提醒 -->
            (reminder_type = 'monthly' AND (
                (reminder_day = 'last_day' AND #{isLastDayOfMonth} = true) OR
                (reminder_day != 'last_day' AND CAST(reminder_day AS UNSIGNED) = #{dayOfMonth})
            ))
          )
        ORDER BY create_time ASC
    </select>

</mapper>
