package org.jeecg.modules.feishu.service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.contact.v3.model.BatchUserReq;
import com.lark.oapi.service.contact.v3.model.BatchUserResp;
import com.lark.oapi.service.contact.v3.model.FindByDepartmentUserReq;
import com.lark.oapi.service.contact.v3.model.FindByDepartmentUserResp;
import com.lark.oapi.service.contact.v3.model.GetUserReq;
import com.lark.oapi.service.contact.v3.model.GetUserResp;
import com.lark.oapi.service.contact.v3.model.User;

import lombok.extern.slf4j.Slf4j;

@Slf4j
//@Service
public class FeiShuUserService {
	
	private static Integer pageSize = 5;

	private static Client client;

	private static FeiShuUserService _instance;

	private FeiShuUserService() {
	}

	public static FeiShuUserService instance(String appId, String appSecret) {
		client = Client.newBuilder(appId, appSecret).build();
		if (null == _instance) {
			_instance = new FeiShuUserService();
		}
		return _instance;
	}

	public User getUser(String unionId) throws Exception {
		// 创建请求对象
		GetUserReq req = GetUserReq.newBuilder().userId(unionId).userIdType("union_id")
				.departmentIdType("open_department_id").build();

		// 发起请求
		GetUserResp resp = client.contact().v3().user().get(req);

		// 处理服务端错误
		if (!resp.success()) {
			log.error(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
							.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return null;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
		return resp.getData().getUser();
	}

	public List<User> getUsers(String[] unionIds) throws Exception {

		// 创建请求对象
		BatchUserReq req = BatchUserReq.newBuilder().userIds(unionIds).userIdType("union_id")
				.departmentIdType("open_department_id").build();

		// 发起请求
		BatchUserResp resp = client.contact().v3().user().batch(req);

		// 处理服务端错误
		if (!resp.success()) {
			log.error(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
							.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return new ArrayList<User>();
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
		List<User> list = new ArrayList<User>();
		for (User user : resp.getData().getItems()) {
			list.add(user);
		}
		return list;
	}

	public List<User> getUserByDepartId(String departId) throws Exception {
		List<User> allUsers = new ArrayList<>();
		String pageToken = null;
		boolean hasMore = true;
		while (hasMore) {
			FindByDepartmentUserReq.Builder reqBuilder = FindByDepartmentUserReq.newBuilder()
				.userIdType("union_id")
				.departmentIdType("department_id")
				.departmentId(departId)
				.pageSize(pageSize);
			if (pageToken != null) {
				reqBuilder.pageToken(pageToken);
			}
			FindByDepartmentUserReq req = reqBuilder.build();

			FindByDepartmentUserResp resp = client.contact().v3().user().findByDepartment(req);

			if (!resp.success()) {
				log.error(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
						.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
				break;
			}

			if (resp.getData() != null && resp.getData().getItems() != null) {
				User[] usersArr = resp.getData().getItems();
				for (User user : usersArr) {
					allUsers.add(user);
				}
			}
			pageToken = resp.getData() != null ? resp.getData().getPageToken() : null;
			hasMore = resp.getData() != null && Boolean.TRUE.equals(resp.getData().getHasMore());
		}
		return allUsers;
	}

}