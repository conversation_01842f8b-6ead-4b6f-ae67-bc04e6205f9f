package org.jeecg.modules.plm.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.plm.entity.PlmSyncLog;
import org.jeecg.modules.plm.mapper.PlmSyncLogMapper;
import org.jeecg.modules.plm.service.IPlmSyncLogService;
import org.springframework.stereotype.Service;

/**
 * PLM同步日志Service实现类
 */
@Service
@Slf4j
public class PlmSyncLogServiceImpl extends ServiceImpl<PlmSyncLogMapper, PlmSyncLog> implements IPlmSyncLogService {

    @Override
    public IPage<PlmSyncLog> querySyncLogList(Page<PlmSyncLog> page, String syncType, String status) {
        return baseMapper.querySyncLogList(page, syncType, status);
    }

    @Override
    public PlmSyncLog queryLastSuccessLog(String syncType) {
        return baseMapper.queryLastSuccessLog(syncType);
    }
} 