package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.entity.FsNodeCache;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 飞书项目节点缓存表服务接口
 * @Author: jeecg-boot
 * @Date: 2023-08-20
 * @Version: V1.0
 */
public interface IFsNodeCacheService extends IService<FsNodeCache> {

    /**
     * 根据项目key和实例key获取节点列表
     * @param projectKey 项目key
     * @param instanceKey 实例key
     * @return 节点列表
     */
    List<FsNodeCache> getNodesByProjectAndInstance(String projectKey, String instanceKey);
    
    /**
     * 根据项目key、实例key和节点key获取节点详情
     * @param projectKey 项目key
     * @param instanceKey 实例key
     * @param nodeKey 节点key
     * @return 节点详情
     */
    FsNodeCache getNodeDetail(String projectKey, String instanceKey, String nodeKey);
    
    /**
     * 根据项目key和工作流ID获取节点列表
     * @param projectKey 项目key
     * @param workflowId 工作流ID
     * @return 节点列表
     */
    List<FsNodeCache> getNodesByWorkflow(String projectKey, String workflowId);
    
    /**
     * 根据状态获取节点列表
     * @param status 状态
     * @return 节点列表
     */
    List<FsNodeCache> getNodesByStatus(String status);
    
    /**
     * 分页查询节点数据
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<FsNodeCache> pageList(Page<FsNodeCache> page, QueryWrapper<FsNodeCache> queryWrapper);
    
    /**
     * 批量保存或更新节点数据
     * @param nodeList 节点列表
     * @return 是否成功
     */
    boolean saveOrUpdateBatch(List<FsNodeCache> nodeList);
    
    /**
     * 保存或更新节点数据，并汇总到实例
     * @param node 节点数据
     * @return 是否成功
     */
    boolean saveOrUpdateAndAggregate(FsNodeCache node);
    
    /**
     * 批量保存或更新节点数据，并汇总到实例
     * @param nodeList 节点列表
     * @return 是否成功
     */
    boolean saveOrUpdateBatchAndAggregate(List<FsNodeCache> nodeList);
    
} 