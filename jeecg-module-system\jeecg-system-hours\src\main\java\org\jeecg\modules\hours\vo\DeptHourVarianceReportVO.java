package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@ApiModel(value = "部门工时差值报表VO", description = "部门工时差值分析报表数据")
@Data
public class DeptHourVarianceReportVO {

    @ApiModelProperty(value = "部门ID")
    private String deptId;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "统计年份")
    private Integer statYear;

    @ApiModelProperty(value = "月度数据 - key为字段名称，value为数值")
    private Map<String, BigDecimal> monthlyData;

    @ApiModelProperty(value = "年度总计")
    private YearlyTotal yearlyTotal;

    @Data
    @ApiModel(value = "月度统计数据")
    public static class MonthlyData {
        @ApiModelProperty(value = "实际工时")
        private BigDecimal actualHours;

        @ApiModelProperty(value = "可登记工时")
        private BigDecimal requiredHours;

        @ApiModelProperty(value = "差值工时")
        private BigDecimal varianceHours;
    }

    @Data
    @ApiModel(value = "年度总计")
    public static class YearlyTotal {
        @ApiModelProperty(value = "年度实际工时总计")
        private BigDecimal totalActualHours;

        @ApiModelProperty(value = "年度可登记工时总计")
        private BigDecimal totalRequiredHours;

        @ApiModelProperty(value = "年度差值工时总计")
        private BigDecimal totalVarianceHours;
    }
}