package org.jeecg.modules.hours.config;

import org.jeecg.modules.hours.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

/**
 * 测试ReminderJobInitializer在dev环境中是否被正确排除
 */
public class ReminderJobInitializerTest extends BaseTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void testReminderJobInitializerNotLoadedInDevProfile() {
        printTestSeparator("测试ReminderJobInitializer在dev环境中不加载");

        // 验证ReminderJobInitializer没有被加载到Spring容器中
        boolean hasReminderJobInitializer = applicationContext.containsBean("reminderJobInitializer");

        System.out.println("ReminderJobInitializer是否存在于Spring容器中: " + hasReminderJobInitializer);

        // 在dev环境中，ReminderJobInitializer应该不存在
        assert !hasReminderJobInitializer : "ReminderJobInitializer不应该在dev环境中加载";

        System.out.println("✅ 测试通过：ReminderJobInitializer在dev环境中被正确排除");
    }
    
    @Test
    public void testApplicationContextLoads() {
        printTestSeparator("测试Spring应用上下文正常加载");
        
        // 验证Spring上下文正常加载
        assert applicationContext != null : "Spring应用上下文应该正常加载";
        
        // 打印一些基本信息
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        System.out.println("Spring容器中的Bean数量: " + beanNames.length);
        
        // 验证一些核心服务是否正常加载
        boolean hasDataSource = applicationContext.containsBean("dataSource");
        System.out.println("数据源是否加载: " + hasDataSource);
        
        System.out.println("✅ 测试通过：Spring应用上下文正常加载");
    }
}
