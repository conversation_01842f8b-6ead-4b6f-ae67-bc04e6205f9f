package org.jeecg.modules.hours.controller;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.hours.entity.HourRecordApprover;
import org.jeecg.modules.hours.entity.HourRecord;
import org.jeecg.modules.hours.service.IHourRecordApproverService;
import org.jeecg.modules.hours.service.IHourRecordService;
import org.jeecg.modules.hours.util.RequestHeaderUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.stream.Collectors;
import org.jeecg.modules.hours.utils.UserInfoUtils;
import org.jeecg.modules.hours.service.IHourRecordLogService;
import org.jeecg.modules.hours.service.IFsUserService;
import org.jeecg.modules.hours.entity.FsUser;

/**
 * @Description: 工时单审批人清单表
 * @Author: jeecg-boot
 * @Date: 2024-05-01
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "工时单审批人清单表管理")
@RestController
@RequestMapping("/hapi/hours/hourRecordApprover")
public class HourRecordApproverController extends JeecgController<HourRecordApprover, IHourRecordApproverService> {

    @Autowired
    private IHourRecordApproverService hourRecordApproverService;

    @Autowired
    private IHourRecordService hourRecordService;

    @Autowired
    private UserInfoUtils userInfoUtils;

    @Autowired
    private IHourRecordLogService hourRecordLogService;

    @Autowired
    private IFsUserService fsUserService;

    @ApiOperation(value = "列表查询", notes = "分页列表查询")
    @GetMapping(value = "/list")
    public Result<List<HourRecordApprover>> list(HourRecordApprover approver) {
        List<HourRecordApprover> list = hourRecordApproverService.lambdaQuery()
                .eq(approver.getRecordId() != null, HourRecordApprover::getRecordId, approver.getRecordId())
                .eq(approver.getApproverId() != null, HourRecordApprover::getApproverId, approver.getApproverId())
                .list();
        return Result.OK(list);
    }

    @ApiOperation(value = "添加", notes = "添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody HourRecordApprover approver) {
        hourRecordApproverService.save(approver);
        return Result.OK("添加成功");
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody HourRecordApprover approver) {
        hourRecordApproverService.updateById(approver);
        return Result.OK("编辑成功");
    }

    @ApiOperation(value = "删除", notes = "通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") Long id) {
        hourRecordApproverService.removeById(id);
        return Result.OK("删除成功");
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") List<Long> ids) {
        hourRecordApproverService.removeByIds(ids);
        return Result.OK("批量删除成功");
    }

    @ApiOperation(value = "详情查询", notes = "通过id查询")
    @GetMapping(value = "/queryById")
    public Result<HourRecordApprover> queryById(@RequestParam(name = "id") Long id) {
        HourRecordApprover entity = hourRecordApproverService.getById(id);
        return Result.OK(entity);
    }

    /**
     * 获取工时单审批流节点（发起、已审批、待审批）
     */
    @ApiOperation(value = "审批流节点流", notes = "根据工时单ID返回审批流节点（发起、已审批、待审批）")
    @GetMapping("/approvalFlow")
    public Result<List<Map<String, Object>>> getApprovalFlow(@RequestParam("recordId") String recordId,@RequestParam("applyId") Long applyId) {
        // 查询主表
        HourRecord hourRecord = hourRecordService.getById(recordId);
        if (hourRecord == null) {
            return Result.error("未找到工时记录");
        }
        // 查询所有审批人
        List<HourRecordApprover> approvers = hourRecordApproverService.lambdaQuery()
            .eq(HourRecordApprover::getRecordId, recordId)
            .eq(HourRecordApprover::getApplyId, applyId)
            .orderByAsc(HourRecordApprover::getActionTime)
            .list();
        List<Map<String, Object>> approvalFlow = new ArrayList<>();
        // 1. 发起节点
        Map<String, Object> startNode = new HashMap<>();
        startNode.put("type", "start");
        startNode.put("userName", hourRecord.getUserName());
        startNode.put("userId", hourRecord.getUserId());
        startNode.put("time", hourRecord.getCreateTime());
        startNode.put("desc", "发起");
        approvalFlow.add(startNode);
        // 2. 已审批节点
        List<HourRecordApprover> approved = approvers.stream().filter(a -> a.getStatus() != null && (a.getStatus() == 1 || a.getStatus() == 2)).sorted(java.util.Comparator.comparing(HourRecordApprover::getActionTime, java.util.Comparator.nullsLast(java.util.Date::compareTo))).collect(java.util.stream.Collectors.toList());
        for (HourRecordApprover a : approved) {
            Map<String, Object> node = new HashMap<>();
            node.put("type", a.getStatus() == 1 ? "approved" : "rejected");
            node.put("userName", a.getApproverName());
            node.put("userId", a.getApproverId());
            node.put("role", a.getRole());
            node.put("time", a.getActionTime());
            node.put("remarks", a.getRemarks());
            approvalFlow.add(node);
        }
        // 3. 待审批节点
        List<HourRecordApprover> pending = approvers.stream().filter(a -> a.getStatus() != null && a.getStatus() == 0).collect(java.util.stream.Collectors.toList());
        if (!pending.isEmpty()) {
            Map<String, Object> node = new HashMap<>();
            node.put("type", "pending");
            node.put("userNames", pending.stream().map(HourRecordApprover::getApproverName).collect(java.util.stream.Collectors.joining("、")));
            node.put("userIds", pending.stream().map(HourRecordApprover::getApproverId).collect(java.util.stream.Collectors.toList()));
            approvalFlow.add(node);
        }
        return Result.OK(approvalFlow);
    }

    @ApiOperation(value = "工时单审批操作", notes = "审批人对工时单进行通过/拒绝操作")
    @PostMapping("/approve")
    public Result<?> approve(@RequestBody Map<String, Object> req) {
    	String applyId = MapUtils.getString(req, "applyId");
        String recordId = (String) req.get("recordId");
        String action = (String) req.get("action"); // "approve" or "reject"
        String remarks = (String) req.get("remarks");
        // 审批人信息后端获取
        String approverId = userInfoUtils.getCurrentUserKey();
        String approverName = userInfoUtils.getUserName();

        if (recordId == null || action == null || applyId == null) {
            return Result.error("参数不完整");
        }
        if (!"approve".equals(action) && !"reject".equals(action)) {
            return Result.error("无效的操作类型，只支持approve或reject");
        }
        if ("reject".equals(action) && (remarks == null || remarks.trim().isEmpty())) {
            return Result.error("驳回时必须填写原因");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(approverId)) {
            return Result.error("未获取到有效的用户信息");
        }

        // 查询工时单并校验状态
        HourRecord record = hourRecordService.getById(recordId);
        if (record == null || record.getStatus() != 1) {
            return Result.error("工时单不存在或状态不允许审批");
        }

        boolean approve = "approve".equals(action);
        boolean ok = hourRecordApproverService.processApproval(recordId, approverId, approverName, approve, remarks,Long.valueOf(applyId));
        if (ok) {
            hourRecordLogService.logApprove(record, approve, approverId, approverName, approve ? null : remarks);
            return Result.OK(approve ? "审批通过成功" : "审批驳回成功");
        } else {
            return Result.error("审批失败，可能已被处理或无权限");
        }
    }

    @ApiOperation(value = "批量移交审批人", notes = "批量移交审批人，严格去重，移交前如新审批人已存在则直接删除当前审批人")
    @PostMapping("/batchTransfer")
    public Result<?> batchTransfer(@RequestBody Map<String, Object> req) {
        String hourRecordApproverIds = (String) req.get("ids");
        String newApproverId = (String) req.get("newApproverId");
        if (hourRecordApproverIds == null || newApproverId == null) {
            return Result.error("参数不完整");
        }
        // 获取工作空间标识
        String projectKey = RequestHeaderUtil.getSpaceIdFromHeader();
        if (StringUtils.isBlank(projectKey)) {
        	throw new JeecgBootException("未提供有效的工作空间标识");
        }
        String currentUserId = userInfoUtils.getCurrentUserKey();
        String newApproverName = null;
        org.jeecg.modules.hours.entity.FsUser user = fsUserService.getUserByProjectAndUserKey(projectKey, newApproverId);
        if (user != null) {
            newApproverName = user.getName();
        }
        hourRecordApproverService.batchTransferApprover(hourRecordApproverIds, currentUserId, newApproverId, newApproverName);
//        if (!ignoredApplyIds.isEmpty()) {
//            return Result.error("部分批次已存在该审批人，已自动忽略: " + ignoredApplyIds);
//        }
        return Result.OK("批量移交成功");
    }

    @ApiOperation(value = "可选审批人下拉", notes = "获取可选审批人列表，排除当前用户，支持搜索")
    @GetMapping("/availableApprovers")
    public Result<List<Map<String, String>>> availableApprovers(
            @RequestParam(required = false) String keyword) {
    	// 获取当前登录用户
        FsUser loginUser = userInfoUtils.getLoginUser();
        if (loginUser == null) {
            throw new JeecgBootException("用户未登录");
        }
        String excludeUserId = loginUser.getUserKey();
    	
        List<FsUser> users = fsUserService.lambdaQuery()
            .ne(FsUser::getUserKey, excludeUserId)
            .like(org.apache.commons.lang3.StringUtils.isNotBlank(keyword), FsUser::getName, keyword)
            .list();
        List<Map<String, String>> result = users.stream()
            .map(u -> {
                Map<String, String> map = new HashMap<>();
                map.put("userId", u.getUserKey());
                map.put("userName", u.getName());
                return map;
            }).collect(Collectors.toList());
        return Result.OK(result);
    }
} 