package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 可登记工时日期VO
 *
 * <AUTHOR>
 * @date 2023-07-01
 */
@Data
@ApiModel(value = "可登记工时日期VO", description = "可登记工时日期VO")
public class AvailableDateVO {
    
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private String date;
    
    /**
     * 星期几
     */
    @ApiModelProperty(value = "星期几")
    private String week;
    
    /**
     * 考勤工时
     */
    @ApiModelProperty(value = "考勤工时")
    private BigDecimal attendanceHours;
    
    /**
     * 是否工作日
     */
    @ApiModelProperty(value = "是否工作日")
    private Boolean isWorkday;
} 