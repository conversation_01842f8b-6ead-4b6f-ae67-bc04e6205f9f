#app {
  min-height: 400px;
  width: 100%;
  display: flex;
  align-items: center;
}

a.primary {
  border: 1px solid rgba(28,31,35,.08);
  background: rgba(28,31,35,1);
  color: #fff;
  padding: 6px 12px;
  text-decoration: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

a.primary .icon {
  color: #fff;
}

a.secondary {
  border: 1px solid rgba(28,31,35,.08);
  background: #fff;
  color: rgba(28,31,35,1);
  padding: 6px 12px;
  text-decoration: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

a.secondary .icon {
  color: rgba(28,31,35,1);
}

a.primary:hover {
  background: rgba(28,31,35,.8);
}

a.secondary:hover {
  background: rgba(46,50,56,.09);
}

.icon {
  font-size: 20px;
  margin-right: 4px;
}

.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  line-height: 20px;
}

.title {
  display: flex;
  justify-content: center;
  font-size: 32px;
  margin-bottom: 8px;
  line-height: 45px;
  word-break: break-all;
  white-space: pre-wrap;
  width: 100%;
}

.desc {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
  padding: 6px 16px;
  background: rgba(137,126,246,0.08);
  border-radius: 32px;
  line-height: 20px;
  margin: 8px 0 24px;
  max-width: 100%;
  box-sizing: border-box;
}

.desc-text {
  font-weight: 500;
  margin-right: 10px;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-all;
  max-width: 100%;
}

.desc-text > span {
  margin-right: 10px;
}

.desc-entry {
  margin: 0;
  color: #3250EB;
  white-space: pre-wrap;
  word-break: break-all;
  max-width: 100%;
  text-align: center;
}

.btn-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;
  max-width: 100%;
}