package org.jeecg.modules.hours.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.FsDepartmentUser;
import com.baomidou.mybatisplus.extension.service.IService;

public interface IFsDepartmentUserService extends IService<FsDepartmentUser> {
	
    Map<String, String> selectDeptNamesByUnionIds(Collection<String> unionIds);
	
} 