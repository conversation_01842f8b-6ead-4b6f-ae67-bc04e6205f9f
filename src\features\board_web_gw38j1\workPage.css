/* 防止表格表头折行 */
.semi-table th.semi-table-cell {
  white-space: nowrap;
  overflow: visible;
}

/* 确保表头内容完全可见 */
.semi-table-thead .semi-table-cell-content {
  overflow: visible !important;
  text-overflow: clip !important;
}

/* 调整表头单元格内部间距 */
.semi-table-thead .semi-table-cell-content {
  padding-left: 6px;
  padding-right: 6px;
}

/* 添加以下CSS规则来隐藏分页控件中的总数信息 */
.semi-pagination-total {
  display: none !important;
}

/* 工时管理页面根容器样式 */
.work-list-root {
  width: 100%;
  max-width: 100%;
  padding: 6px 6px 6px 6px; /* 顶部内边距设为5px，保留左右和底部内边距 */
  margin-top: 0; /* 移除顶部边距 */
  box-sizing: border-box;
}

/* 调整表格列宽的样式 */
.semi-table .projectName-column {
  max-width: 100px !important;
  width: 100px !important;
}

/* 确保表格内容不换行并显示省略号 */
.semi-table-tbody .semi-table-cell-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  position: relative;
}

/* 添加鼠标悬停时的工具提示效果 */
.semi-table-tbody .semi-table-cell {
  position: relative;
}

/* 针对需要省略显示的列添加特殊处理 */
.truncate-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: block;
  cursor: pointer;
}

/* 鼠标悬停时的提示样式 */
.cell-tooltip {
  position: absolute;
  display: none;
  background-color: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  z-index: 1000;
  max-width: 300px;
  word-wrap: break-word;
  white-space: normal;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  pointer-events: none;
}

/* 统计卡片样式 */
.work-list-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  width: 100%;
}

.work-list-stat-card {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 12px 16px;
  margin-right: 12px;
}

.work-list-stat-card:last-child {
  margin-right: 0;
}

.stat-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  white-space: nowrap;
  float: left;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--semi-color-primary);
  float: right;
}

/* 顶部工具栏样式 */
.work-list-toolbar {
  display: flex;
  width: 100%;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: space-between; /* 修改为两端对齐 */
}

/* 工具栏左侧筛选区域 */
.toolbar-filters {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  flex-wrap: wrap;
}

/* 工具栏右侧按钮组 */
.toolbar-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto; /* 确保按钮组在最右侧 */
}

/* Semi UI Table样式优化 */
.semi-table {
  width: 100% !important;
  box-sizing: border-box !important;
  max-width: none !important;
}

.semi-table-wrapper {
  width: 100% !important;
  max-width: none !important;
}

.semi-table-container {
  width: 100% !important;
  max-width: none !important;
}

/* 确保分页器在右侧对齐 */
.semi-pagination {
  width: 100%;
  display: flex;
  justify-content: flex-end;
} 