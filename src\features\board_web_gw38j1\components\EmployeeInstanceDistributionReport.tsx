import React, { useState, useEffect } from 'react';
import { Table, Button, Select, Spin, Notification, Space, Pagination } from '@douyinfe/semi-ui';
import { IconDownload, IconRefresh } from '@douyinfe/semi-icons';
import { ReportApi } from '../../../api/report';
import DateRangePicker from './DateRangePicker';
import UserSelector from './UserSelector';
import InstanceSelector from './InstanceSelector';
import type { 
  ReportQueryParams, 
  EmployeeInstanceDistributionResponse,
  EmployeeInstanceDistributionData 
} from '../../../types/report';
import { getThisMonthDateRange } from '../../../utils/date';

/**
 * 员工工时实例分布报表组件
 * projectKey已通过全局请求header传递，无需在组件中单独传递
 */
const EmployeeInstanceDistributionReport: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<EmployeeInstanceDistributionResponse | null>(null);
  const [dimension, setDimension] = useState<'month' | 'week' | 'day'>('day');
  const [dateRange, setDateRange] = useState<[string, string]>(getThisMonthDateRange());
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedInstances, setSelectedInstances] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 查询报表数据
  const fetchReportData = async () => {
    if (!dateRange) return;

    setLoading(true);
    try {
      const params: ReportQueryParams = {
        dimension,
        startDate: dateRange[0],
        endDate: dateRange[1],
        userIds: selectedUsers.length > 0 ? selectedUsers : undefined,
        instanceIds: selectedInstances.length > 0 ? selectedInstances : undefined,
        page: currentPage,
        size: pageSize
      };

      const result = await ReportApi.getEmployeeInstanceDistributionReport(params);
      setData(result);
    } catch (error) {
      console.error('获取员工工时实例分布报表失败:', error);
      Notification.error({
        title: '获取报表数据失败',
        content: error instanceof Error ? error.message : '请稍后重试'
      });
    } finally {
      setLoading(false);
    }
  };

  // 导出报表
  const handleExport = async () => {
    if (!dateRange) return;

    try {
      const params: ReportQueryParams = {
        dimension: 'day',
        startDate: dateRange[0],
        endDate: dateRange[1],
        userIds: selectedUsers.length > 0 ? selectedUsers : undefined,
        instanceIds: selectedInstances.length > 0 ? selectedInstances : undefined
      };

      await ReportApi.exportEmployeeInstanceDistributionReport(params);
      Notification.success({
        title: '导出成功',
        content: '报表正在下载中...'
      });
    } catch (error) {
      console.error('导出报表失败:', error);
      Notification.error({
        title: '导出失败',
        content: error instanceof Error ? error.message : '请稍后重试'
      });
    }
  };

  // 构建表格列
  const buildColumns = () => {
    if (!data) return [];

    const columns = [
      {
        title: '员工姓名',
        dataIndex: 'employeeName',
        width: 150,
        fixed: 'left' as const,
      },
      {
        title: '小计(小时)',
        dataIndex: 'totalHours',
        width: 120,
        fixed: 'left' as const,
        render: (hours: number) => `${hours?.toFixed(1) || 0}h`,
      },
      {
        title: '折算天数(天)',
        dataIndex: 'totalDays',
        width: 120,
        fixed: 'left' as const,
        render: (days: number) => `${days?.toFixed(1) || 0}天`,
      }
    ];

    // 动态添加实例列
    data.instances.forEach(instance => {
      columns.push({
        title: instance,
        dataIndex: instance,
        width: 150,
        render: (hours: number) => `${hours?.toFixed(1) || 0}h`,
      } as any);
    });

    return columns;
  };

  // 构建表格数据（包含合计行）
  const buildTableData = () => {
    if (!data) return [];

    const tableData = data.data.map((item, index) => {
      const row: any = {
        key: index,
        employeeName: item.employeeName,
        totalHours: item.totalHours,
        totalDays: item.totalDays,
        ...item.instances
      };
      return row;
    });

    // 添加合计行
    if (data.summary) {
      const summaryRow: any = {
        key: 'summary',
        employeeName: '合计',
        totalHours: data.summary.grandTotalHours,
        totalDays: data.summary.grandTotalDays,
        ...data.summary.instances
      };
      tableData.push(summaryRow);
    }

    return tableData;
  };

  // 处理分页变化
  const handlePageChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  // 初始加载和依赖更新
  useEffect(() => {
    fetchReportData();
  }, [dimension, dateRange, selectedUsers, selectedInstances, currentPage, pageSize]);

  return (
    <div>
      {/* 筛选条件 */}
      <div style={{ marginBottom: 16, padding: 16, background: '#fafafa', borderRadius: 6 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', gap: 16 }}>
          {/* 左侧筛选条件 */}
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16, alignItems: 'center', flex: 1 }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <DateRangePicker
                value={dateRange}
                onChange={setDateRange}
                dimension={dimension}
              />
            </div>

            <div style={{ display: 'flex', alignItems: 'center' }}>
              <UserSelector
                value={selectedUsers}
                onChange={setSelectedUsers}
                placeholder="全部人员"
              />
            </div>

            <div style={{ display: 'flex', alignItems: 'center' }}>
              <InstanceSelector
                value={selectedInstances}
                onChange={setSelectedInstances}
                placeholder="全部实例"
              />
            </div>

            <Button
              icon={<IconRefresh />}
              onClick={fetchReportData}
              loading={loading}
            >
              刷新
            </Button>
          </div>

          {/* 右侧操作区域 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Select
                value={dimension}
                onChange={setDimension}
                style={{ width: 80 }}
              >
                <Select.Option value="month">按月</Select.Option>
                <Select.Option value="week">按周</Select.Option>
                <Select.Option value="day">按日</Select.Option>
              </Select>
            </div>

            <Button
              theme="solid"
              type="primary"
              icon={<IconDownload />}
              onClick={handleExport}
              disabled={!data}
            >
              导出
            </Button>
          </div>
        </div>
      </div>

      {/* 报表表格 */}
      <Table
        columns={buildColumns()}
        dataSource={buildTableData()}
        loading={loading}
        scroll={{ x: 'max-content' }}
        size="middle"
        pagination={false}
        empty={
          <div style={{ padding: 40, textAlign: 'center' }}>
            {loading ? <Spin /> : '暂无数据'}
          </div>
        }
        className="report-table"
      />

      {/* 分页 */}
      {data && data.total > 0 && (
        <div style={{
          marginTop: 16,
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-end'
        }}>
          <Pagination
            currentPage={currentPage}
            pageSize={pageSize}
            total={data.total}
            onChange={handlePageChange}
            showSizeChanger
            showQuickJumper
            showTotal
          />
        </div>
      )}

      <style>{`
        .report-table .summary-row {
          background-color: #f6f8fa;
          font-weight: bold;
        }
      `}</style>
    </div>
  );
};

export default EmployeeInstanceDistributionReport;
