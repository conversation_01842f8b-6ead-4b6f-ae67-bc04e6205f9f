<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.hours.mapper.UserProjectHourStatisticsMapper">

    <!-- 获取项目列信息（用于动态列） -->
    <select id="getProjectColumns" resultType="org.jeecg.modules.hours.vo.ProjectColumnVO">
        SELECT 
            s.project_id,
            s.project_name,
            s.project_code,
            SUM(s.total_hours) as total_hours
        FROM user_project_hour_statistics s
        WHERE s.stat_date BETWEEN #{startDate} AND #{endDate}
          AND s.stat_period = #{statPeriod}
        <if test="userIds != null and userIds.size() > 0">
            AND s.user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            AND s.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="isHoliday != null">
            AND s.is_holiday = #{isHoliday}
        </if>
        GROUP BY s.project_id, s.project_name, s.project_code
        ORDER BY total_hours DESC
    </select>

    <!-- 获取人员项目工时统计报表数据 -->
    <select id="getUserProjectHourReport" resultType="org.jeecg.modules.hours.vo.UserProjectHourReportVO">
        SELECT 
            s.user_id,
            s.user_name,
            s.user_code,
            s.department_id,
            s.department_name,
            SUM(s.total_hours) as total_hours,
            CONCAT('project_', s.project_id) as project_key,
            s.project_id,
            s.total_hours as project_hours2
        FROM user_project_hour_statistics s
        WHERE s.stat_date BETWEEN #{startDate} AND #{endDate}
          AND s.stat_period = #{statPeriod}
        <if test="userIds != null and userIds.size() > 0">
            AND s.user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            AND s.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="isHoliday != null">
            AND s.is_holiday = #{isHoliday}
        </if>
        GROUP BY s.user_id, s.user_name, s.user_code, s.department_id, s.department_name, s.project_id, s.total_hours
        ORDER BY s.user_name, s.project_id
    </select>

    <!-- 获取总计行数据 -->
    <select id="getTotalRow" resultType="org.jeecg.modules.hours.vo.UserProjectHourReportVO">
        SELECT 
            'total' as user_id,
            '总计' as user_name,
            '' as user_code,
            '' as department_id,
            '' as department_name,
            SUM(s.total_hours) as total_hours,
            CONCAT('project_', s.project_id) as project_key,
            s.project_id,
            SUM(s.total_hours) as project_hours2
        FROM user_project_hour_statistics s
        WHERE s.stat_date BETWEEN #{startDate} AND #{endDate}
          AND s.stat_period = #{statPeriod}
        <if test="userIds != null and userIds.size() > 0">
            AND s.user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            AND s.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="isHoliday != null">
            AND s.is_holiday = #{isHoliday}
        </if>
        GROUP BY s.project_id
    </select>

    <!-- 批量插入或更新统计数据 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO user_project_hour_statistics 
        (stat_date, stat_period, user_id, user_name, user_code, department_id, department_name, 
         project_id, project_name, project_code, total_hours, record_count, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.statDate}, #{item.statPeriod}, #{item.userId}, #{item.userName}, #{item.userCode},
             #{item.departmentId}, #{item.departmentName}, #{item.projectId}, #{item.projectName}, 
             #{item.projectCode}, #{item.totalHours}, #{item.recordCount}, NOW(), NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE
        total_hours = VALUES(total_hours),
        record_count = VALUES(record_count),
        update_time = NOW()
    </insert>

    <!-- 删除指定日期的统计数据 -->
    <delete id="deleteByStatDate">
        DELETE FROM user_project_hour_statistics 
        WHERE stat_date = DATE(#{statDate}) AND stat_period = #{statPeriod}
    </delete>

    <!-- 获取汇总统计数据 -->
    <select id="getSummaryData" resultType="org.jeecg.modules.hours.vo.UserProjectHourReportResult$ReportSummaryVO">
        SELECT 
            CONCAT(DATE_FORMAT(#{startDate}, '%Y-%m-%d'), ' 至 ', DATE_FORMAT(#{endDate}, '%Y-%m-%d')) as date_range,
            SUM(s.total_hours) as total_hours,
            COUNT(DISTINCT s.user_id) as user_count,
            COUNT(DISTINCT s.project_id) as project_count,
            COUNT(DISTINCT s.department_id) as dept_count
        FROM user_project_hour_statistics s
        WHERE s.stat_date BETWEEN #{startDate} AND #{endDate}
          AND s.stat_period = #{statPeriod}
        <if test="userIds != null and userIds.size() > 0">
            AND s.user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size() > 0">
            AND s.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="isHoliday != null">
            AND s.is_holiday = #{isHoliday}
        </if>
    </select>

</mapper>