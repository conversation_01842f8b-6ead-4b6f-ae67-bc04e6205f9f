package org.jeecg.modules.hours.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.hours.entity.FsDepartment;
import org.jeecg.modules.hours.service.IFsDepartmentService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 部门表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
@Slf4j
@Api(tags="部门表")
@RestController
@RequestMapping("/hapi/system/department")
public class FsDepartmentController extends JeecgController<FsDepartment, IFsDepartmentService> {
	@Autowired
	private IFsDepartmentService fsDepartmentService;
	
	/**
	 * 分页列表查询
	 *
	 * @param fsDepartment
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "部门表-分页列表查询")
	@ApiOperation(value="部门表-分页列表查询", notes="部门表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(FsDepartment fsDepartment,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<FsDepartment> queryWrapper = QueryGenerator.initQueryWrapper(fsDepartment, req.getParameterMap());
		Page<FsDepartment> page = new Page<FsDepartment>(pageNo, pageSize);
		IPage<FsDepartment> pageList = fsDepartmentService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 * 添加
	 *
	 * @param fsDepartment
	 * @return
	 */
	@AutoLog(value = "部门表-添加")
	@ApiOperation(value="部门表-添加", notes="部门表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody FsDepartment fsDepartment) {
		fsDepartmentService.save(fsDepartment);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param fsDepartment
	 * @return
	 */
	@AutoLog(value = "部门表-编辑")
	@ApiOperation(value="部门表-编辑", notes="部门表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody FsDepartment fsDepartment) {
		fsDepartmentService.updateById(fsDepartment);
		return Result.OK("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "部门表-通过id删除")
	@ApiOperation(value="部门表-通过id删除", notes="部门表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		fsDepartmentService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "部门表-批量删除")
	@ApiOperation(value="部门表-批量删除", notes="部门表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.fsDepartmentService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "部门表-通过id查询")
	@ApiOperation(value="部门表-通过id查询", notes="部门表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		FsDepartment fsDepartment = fsDepartmentService.getById(id);
		return Result.OK(fsDepartment);
	}
	
    /**
     * 导出excel
     *
     * @param request
     * @param fsDepartment
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, FsDepartment fsDepartment) {
        return super.exportXls(request, fsDepartment, FsDepartment.class, "部门表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, FsDepartment.class);
    }
	
	/**
	 * 获取部门树结构
	 *
	 * @return
	 */
	@AutoLog(value = "部门表-获取部门树结构")
	@ApiOperation(value="部门表-获取部门树结构", notes="部门表-获取部门树结构")
	@GetMapping(value = "/tree")
	public Result<?> getDepartmentTree() {
		// 先查询所有顶级部门（parentId为空或为0的）
//		QueryWrapper<FsDepartment> queryWrapper = new QueryWrapper<>();
//		queryWrapper.isNull("parent_id").or().eq("parent_id", "");
//		List<FsDepartment> rootDepartments = fsDepartmentService.list(queryWrapper);
//
//		// 递归构建每个顶级部门的树
//		for (FsDepartment dept : rootDepartments) {
//			FsDepartment tree = fsDepartmentService.getDepartmentTreeById(dept.getId());
//			if (tree != null) {
//				dept.setChildren(tree.getChildren());
//			}
//		}
//
//		return Result.OK(rootDepartments);
		return Result.OK(fsDepartmentService.getFilteredDepartmentTree());
	}
	
	/**
	 * 获取所有部门（不分页）
	 *
	 * @return
	 */
	@AutoLog(value = "部门表-获取所有用户")
	@ApiOperation(value="部门表-获取所有用户", notes="部门表-获取所有用户")
	@GetMapping(value = "/all")
	public Result<?> getAllDepartments() {
		List<FsDepartment> departments = fsDepartmentService.list();
		return Result.OK(departments);
	}
}