package org.jeecg.modules.hours.task;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.feishu.service.FeiShuUserService;
import org.jeecg.modules.hours.constant.ConfigKeyConstant;
import org.jeecg.modules.hours.entity.FsProject;
import org.jeecg.modules.hours.entity.FsUser;
import org.jeecg.modules.hours.entity.SysUserAttendanceStat;
import org.jeecg.modules.hours.service.IFsProjectService;
import org.jeecg.modules.hours.service.IFsUserService;
import org.jeecg.modules.hours.service.ISysConfigService;
import org.jeecg.modules.hours.service.ISysUserAttendanceStatService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.attendance.v1.model.ChildItem;
import com.lark.oapi.service.attendance.v1.model.Item;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataRespBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UserStatsData;
import com.lark.oapi.service.attendance.v1.model.UserStatsDataCell;
import com.lark.oapi.service.attendance.v1.model.UserStatsView;
import com.lark.oapi.service.contact.v3.model.User;

import lombok.extern.slf4j.Slf4j;

/**
 * 考勤数据采集
 * <p>
 * 1. 依赖于appId/appSecret 执行前需要保障appId/appSecret已配置
 * 2. 在开发环境中不会执行，避免影响开发调试
 * </p>
 * @ClassName AttendanceStatJob
 * <AUTHOR>
 * @date 2025年6月6日
 * @Description
 * @version V0.1
 *
 */
@Slf4j
@Component
//@Profile("!dev") // 排除dev环境
//@ConditionalOnProperty(name = "attendance.stat.enabled", havingValue = "true", matchIfMissing = true)
public class AttendanceStatJob // implements Job 
{

//	private static final String appId = "cli_a7b2f4bbcb38500d";
//	private static final String appSecret = "kzen0nGwHB15eEUVsDXkp1wn0YOF4HUK";
	
	@Autowired
	private ISysUserAttendanceStatService sysUserAttendanceStatService;
	
	@Autowired
	private IFsProjectService fsProjectService;
	@Autowired
	private IFsUserService fsUserService;
	@Autowired
	private ISysConfigService sysConfigService;
	
//	@Override
//	public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
//		log.info(" Job Execution key：" + jobExecutionContext.getJobDetail().getKey());
//		
//		// TODO 这里补充与飞书项目插件相关的配置+用户
//		// 接口要求，至多查询最近31天的,临时仅查询最近7天
//		Integer start = this.getBefore7Day();
//		Integer end = this.getYester();
//		this.start(start,end);
//		
//		log.info(String.format(" Jeecg-Boot 定时采集考勤任务 AttendanceStatJob !  时间:" + DateUtils.getTimestamp()));
//	}
	
	
	
	
	public void startTest(Integer start,Integer end) {
//		// 接口要求，至多查询最近31天的
//		Integer start = 20250601;
//		Integer end = this.getYester();
		this.start(start,end);
	}
	
	@Scheduled(cron = "0 0 1 * * ?")
	public void startJob() {
		log.info(" Job Execution key：" + Thread.currentThread().getId());
		
		// TODO 这里补充与飞书项目插件相关的配置+用户
		// 接口要求，至多查询最近31天的,临时仅查询最近7天
		Integer start = this.getBefore7Day();
		Integer end = this.getYester();
		this.start(start,end);
		
		log.info(String.format(" Jeecg-Boot 定时采集考勤任务 AttendanceStatJob !  时间:" + DateUtils.getTimestamp()));

	}
	
	/** 
	 * T+1 定期采集考勤数据
	 * 1.获取fs_project所有数据+遍历
	 * 2.获取工作空间的默认1条用户 + projectKey + 并且是空间管理员(可保障飞书应用中大概率有权限)
	 * 3.获取工作空间的配置appId、appSecret
	 * 4.采集考勤数据
	 * <AUTHOR>
	 * @date 2025年6月8日 
	 * @version V0.1 
	 * 
	 */
	private void start(Integer start,Integer end) throws JeecgBootException {
		try {
			// 1.获取fs_project所有数据+遍历
			List<FsProject> projects = fsProjectService.list();
			if(CollectionUtils.isEmpty(projects)) {
				log.warn("未获取到任何项目数据");
				throw new JeecgBootException("未获取到任何项目数据");
			}
			
			StringBuilder exceptionBuilder = new StringBuilder();
			
			for(FsProject project : projects) {
				String projectKey = project.getProjectKey();
				try {
					// 2.获取工作空间的默认1条用户 + projectKey + 并且是空间管理员
					FsUser userInfo = fsUserService.getAdminUserLimit1(projectKey);
					if(null == userInfo) {
						log.error("projectKey={},无管理员用户!",projectKey);
						exceptionBuilder.append("projectKey="+projectKey+",无管理员用户!\n");
						continue;
					}
					if(StringUtils.isEmpty(userInfo.getUnionId())) {
						log.error("projectKey={},管理员用户({})无unionId!",projectKey,userInfo.getUserKey());
						exceptionBuilder.append("projectKey="+projectKey+",管理员用户("+userInfo.getUserKey()+")无unionId!");
						continue;
					}
					
					// 3.获取工作空间的配置appId、appSecret
					String appId = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.BIND_APP_ID);
					String appSecret = sysConfigService.getConfigValue(projectKey, ConfigKeyConstant.BIND_APP_SECRET);
					if(StringUtils.isEmpty(appId) || StringUtils.isEmpty(appSecret)) {
						log.warn("项目{}配置无效,appId={},appSecret={}", projectKey,appId,appSecret);
						exceptionBuilder.append("projectKey="+projectKey+"配置无效,appId="+appId+",appSecret="+appSecret);
						continue;
					}
					
					// 4.采集考勤数据
					// 考勤周期=T+1
					List<String> queryUnionIds = this.fsUserService.getUnionIds(projectKey);
					if(CollectionUtils.isEmpty(queryUnionIds)) {
						log.warn("projectKey={}考勤用户为空!", projectKey,appId,appSecret);
						exceptionBuilder.append("projectKey="+projectKey+"考勤用户为空!");
						continue;
					}
					// TODO 临时指定
					String unionId = userInfo.getUnionId();
					this.getUserData(unionId, start, end,queryUnionIds,appId,appSecret);
				}catch(Exception ex1) {
					log.error(ex1.getMessage(),ex1);
					exceptionBuilder.append("projectKey="+projectKey+"加载考勤数据失败!");
				}
			}
			
			if(StringUtils.isNotEmpty(exceptionBuilder.toString())) {
				throw new JeecgBootException(exceptionBuilder.toString());
			}
			
		} catch (Exception e) {
			log.error("考勤数据采集异常", e);
			throw new RuntimeException("考勤数据采集失败", e);
		}
	}
	
	public static void main(String[] args) {
		
		System.out.println(new AttendanceStatJob().getYester());
		
	}
	
	private Integer getBefore7Day() {
		LocalDate yesterday = LocalDate.now().minusDays(7);
        
        // 格式化为 yyyyMMdd 的整数
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        Integer date = Integer.valueOf(yesterday.format(formatter));
        return date;
	}
	
	private Integer getYester() {
		LocalDate yesterday = LocalDate.now().minusDays(1);
        
        // 格式化为 yyyyMMdd 的整数
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        Integer date = Integer.valueOf(yesterday.format(formatter));
        return date;
	}
	

	public void getUserData(String unionId,Integer start,Integer end,List<String> queryUnionIds,String appId,String appSecret) throws Exception {
		
		log.info("\n\nunionId={},start={},end={},queryUnionIds={},\n\n",unionId,start,end,queryUnionIds);

//		String unionId = "on_03144b4670493fe27269925cf7e9cdbf";
		// 考勤周期
//		Integer start = 20250424;
//		Integer end = 20250428;
		String currentUserId = "";
		User fsUser = FeiShuUserService.instance(appId, appSecret).getUser(unionId);
		if(null!=fsUser) {
			currentUserId = fsUser.getUserId();
		}
		String[] unionIdArr = queryUnionIds.toArray(new String[0]);
		List<User> fsUserList = FeiShuUserService.instance(appId, appSecret).getUsers(unionIdArr);
		Map<String,String> userIdUnionIdMap = new HashMap<String, String>();
		for (User user : fsUserList) {
			userIdUnionIdMap.put(user.getUserId(), user.getUnionId());
		}
		String[] queryUserIds = fsUserList.stream().map(item->item.getUserId()).toArray(String[]::new);
		log.info("queryUserIds={}",queryUserIds);

		// 1.调用“查询统计表头”接口，查询目前考勤统计所支持查询的所有统计表头字段。开发者可以在这些字段中选出自己关注的字段。
		this.queryHeader01(start, end,appId,appSecret);
		// 2.调用“查询统计设置”接口，查询当前开发者账号已保存的统计表头字段。当开发者在调用“查询统计数据”接口时，所返回的统计数据就是这些表头字段的数据。
		UserStatsView userStatsView = this.queryStat02(currentUserId,appId,appSecret);
		// 3.调用“更新统计设置”接口，将需要查询的统计表头字段进行保存或更新。如果开发者有新的统计字段需要关注，或者希望剔除不感兴趣的统计字段，可以使用该接口更新表头字段。更新后，再次调用“查询统计数据”接口，就会返回包含了新字段或者剔除了不感兴趣字段的统计数据。
		// 需要采集的字段信息
		Map<String, String> fieldCodeMap = new HashMap<String, String>();
		if (null != userStatsView) {
			// 设置过滤项
			for (Item item : userStatsView.getItems()) {
				boolean isBanCi = "班次信息".equals(item.getTitle());
				boolean isSt = "时长统计".equals(item.getTitle());
				ChildItem[] childItems = item.getChildItems();
				for (ChildItem childItem : childItems) {
					if (isBanCi && "日期".equals(childItem.getTitle())) {
						// 开关字段，0：关闭，1：开启
						childItem.setValue("1");
						fieldCodeMap.put("stat_date", childItem.getCode());
					} else if (isBanCi && "班次".equals(childItem.getTitle())) {
						// 开关字段，0：关闭，1：开启
						childItem.setValue("1");
						fieldCodeMap.put("shift", childItem.getCode());
					} else if (isSt && "加班时长".equals(childItem.getTitle())) {
						childItem.setValue("1");
						fieldCodeMap.put("over_work_hours", childItem.getCode());
					} else if (isSt && "班内工作时长".equals(childItem.getTitle())) {
						childItem.setValue("1");
						fieldCodeMap.put("work_hours", childItem.getCode());
					}
					// 考勤日期、班次、应出勤时长(小时)、实际出勤时长(小时)、加班总时长(小时)
					else if (isSt && "应出勤时长".equals(childItem.getTitle())) {
						childItem.setValue("1");
						fieldCodeMap.put("required_attend_hours", childItem.getCode());
					}else if (isSt && "实际出勤时长".equals(childItem.getTitle())) {
						childItem.setValue("1");
						fieldCodeMap.put("real_attend_hours", childItem.getCode());
					}else if (isSt && "加班总时长".equals(childItem.getTitle())) {
						childItem.setValue("1");
						fieldCodeMap.put("over_total_work_hours", childItem.getCode());
					}
				}
				item.setChildItems(childItems);
//									log.info("item>>title={},code={}",item.getTitle(),item.getCode());
//									if(!("基本信息".equals(item.getTitle()) || "时长统计".equals(item.getTitle()) || "班次信息".equals(item.getTitle()))) {
//										item.setChildItems(setChildItem(item.getChildItems(), true));	
//									}
			}
			userStatsView.setItems(userStatsView.getItems());

			log.info("\n\nuserStatsView={}", Jsons.DEFAULT.toJson(userStatsView));

			this.queryUserStat03(currentUserId, userStatsView.getViewId(), userStatsView,appId,appSecret);
		}
		// 4.调用“查询统计数据”接口，进行统计数据的查询。
		QueryUserStatsDataRespBody resp = this.query04(start, end, currentUserId, queryUserIds,appId,appSecret);
		if (null != resp) {
			List<Map<String, String>> mapList = new ArrayList<Map<String, String>>();

			for (UserStatsData userStat : resp.getUserDatas()) {
				Map<String, String> map = new HashMap<String, String>();
				// 用户ID、用户名称、日期、班内工作时长(小时)、加班时长、班次信息(确认是否是工作日?)
				map.put("userId", userStat.getUserId());
				map.put("userName", userStat.getName());

				Map<String, UserStatsDataCell> codeMap = this.parseMap(userStat.getDatas());
				// 动态字段
				for (String field : fieldCodeMap.keySet()) {
					String code = fieldCodeMap.get(field);

					UserStatsDataCell value = codeMap.get(code);
					// log.info("{}:{}",field,Jsons.DEFAULT.toJson(value));
					// 时长，这个字段是一个map，key位时间单位，value为对应的时长值
					if ("over_work_hours".equals(field) 
							|| "work_hours".equals(field)
							|| "required_attend_hours".equals(field)
							|| "real_attend_hours".equals(field)
							|| "over_total_work_hours".equals(field)) {
						map.put(field, value.getDurationNum().getHour());
					} else {
						map.put(field, value.getValue());
					}

				}
				log.info("userId={},name={},map={}\n\n", userStat.getUserId(), userStat.getName(),
						Jsons.DEFAULT.toJson(map));
						mapList.add(map);
			}

			// 将产生的结果,批量保存到表：sys_user_attendance_stat
			List<SysUserAttendanceStat> stats = new ArrayList<>();
			for (Map<String, String> map : mapList) {
				SysUserAttendanceStat stat = new SysUserAttendanceStat();
				stat.setUserId(map.get("userId"));
				stat.setUnionId(MapUtils.getString(userIdUnionIdMap, map.get("userId")));
								// 将字符串日期转换为Date类型
				SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
				stat.setStatDate(sdf.parse(map.get("stat_date")));
				stat.setWorkHours(new BigDecimal(MapUtils.getString(map, "work_hours","0")));
				stat.setOverWorkHours(new BigDecimal(MapUtils.getString(map, "over_work_hours","0")));
				
				stat.setRequiredAttendHours(new BigDecimal(MapUtils.getString(map, "required_attend_hours","0")));
				stat.setRealAttendHours(new BigDecimal(MapUtils.getString(map, "real_attend_hours","0")));
				stat.setOverTotalWorkHours(new BigDecimal(MapUtils.getString(map, "over_total_work_hours","0")));
				
				stat.setShift(map.get("shift"));
				
				log.info("userId={},unionId={},statDate={},work_hours={},over_work_hours={},required_attend_hours={},real_attend_hours={},over_total_work_hours={}",
						map.get("userId"),MapUtils.getString(userIdUnionIdMap, map.get("userId")),sdf.parse(map.get("stat_date")),
						map.get("work_hours"),map.get("over_work_hours"),map.get("required_attend_hours"),map.get("real_attend_hours"),map.get("over_total_work_hours"));
				stats.add(stat);
			}
			
			// 使用服务方法批量保存或更新
			sysUserAttendanceStatService.saveOrUpdateBatch(stats);
			
		}

	}

	private Map<String, UserStatsDataCell> parseMap(UserStatsDataCell[] userStatDatas) {
		Map<String, UserStatsDataCell> map = new HashMap<String, UserStatsDataCell>();
		for (UserStatsDataCell userStatsDataCell : userStatDatas) {
			map.put(userStatsDataCell.getCode(), userStatsDataCell);
		}

		return map;
	}

	private ChildItem[] setChildItem(ChildItem[] childItem, boolean isClose) {
		for (ChildItem childItem2 : childItem) {
			// 开关字段，0：关闭，1：开启
			childItem2.setValue(isClose ? "1" : "0");
		}
		return childItem;
	}

	public QueryUserStatsDataRespBody query04(Integer start, Integer end, String currentUserId, String[] queryUserIds,String appId,String appSecret)
			throws Exception {

		log.info("\n\nstart={},end={},currentUserId={},queryUserIds={}\n\n",start,end,currentUserId,queryUserIds);
		
		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		QueryUserStatsDataReq req = QueryUserStatsDataReq.newBuilder().employeeType("employee_id")
				.queryUserStatsDataReqBody(QueryUserStatsDataReqBody.newBuilder().locale("zh").statsType("daily")
						.startDate(start).endDate(end).userIds(queryUserIds).needHistory(true)
						.currentGroupOnly(true).userId(currentUserId).build())
				.build();

		// 发起请求
		QueryUserStatsDataResp resp = client.attendance().v1().userStatsData().query(req);

		// 处理服务端错误
		if (!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
							.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return null;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));

		return resp.getData();

	}

	public void queryUserStat03(String currentUserId, String viewId, UserStatsView userStatsView,String appId,String appSecret) throws Exception {
		
		log.info("\n\ncurrentUserId={},viewId={},userStatsView={}\n\n",currentUserId,viewId,userStatsView);

		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		UpdateUserStatsViewReq req = UpdateUserStatsViewReq.newBuilder().userStatsViewId(viewId)
				.employeeType("employee_id")
				.updateUserStatsViewReqBody(UpdateUserStatsViewReqBody.newBuilder().view(userStatsView).build())
				.build();

		// 发起请求
		UpdateUserStatsViewResp resp = client.attendance().v1().userStatsView().update(req);

		// 处理服务端错误
		if (!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
							.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
	}

	public UserStatsView queryStat02(String currentUserId,String appId,String appSecret) throws Exception {

		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		QueryUserStatsViewReq req = QueryUserStatsViewReq.newBuilder().employeeType("employee_id")
				.queryUserStatsViewReqBody(QueryUserStatsViewReqBody.newBuilder().locale("zh").statsType("daily")
						.userId(currentUserId).build())
				.build();

		// 发起请求
		QueryUserStatsViewResp resp = client.attendance().v1().userStatsView().query(req);

		// 处理服务端错误
		if (!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
							.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return null;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));

		return resp.getData().getView();

	}

	public void queryHeader01(Integer start, Integer end,String appId,String appSecret) throws Exception {
		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		QueryUserStatsFieldReq req = QueryUserStatsFieldReq.newBuilder().employeeType("employee_id")
				.queryUserStatsFieldReqBody(QueryUserStatsFieldReqBody.newBuilder().locale("zh").statsType("daily")
						.startDate(start).endDate(end).build())
				.build();

		// 发起请求
		QueryUserStatsFieldResp resp = client.attendance().v1().userStatsField().query(req);

		// 处理服务端错误
		if (!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
							.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
	}

}