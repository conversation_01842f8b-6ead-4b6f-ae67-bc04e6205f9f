import { get, post, put, del } from '../utils/request';

// 工时相关接口
export const workHoursApi = {
  // 获取工时列表
  getWorkHoursList: (params: any) => get<any>('/work-hours/list', params),
  
  // 创建工时记录
  createWorkHours: (data: any) => post<any>('/work-hours/create', data),
  
  // 更新工时记录
  updateWorkHours: (id: string, data: any) => put<any>(`/work-hours/${id}`, data),
  
  // 删除工时记录
  deleteWorkHours: (id: string) => del<any>(`/work-hours/${id}`),
  
  // 提交工时审批
  submitWorkHours: (id: string) => post<any>(`/work-hours/${id}/submit`),
  
  // 获取工时统计
  getWorkHoursStats: (params: any) => get<any>('/work-hours/stats', params),
};

// 审批相关接口
export const approvalApi = {
  // 获取审批列表
  getApprovalList: (params: any) => get<any>('/approval/list', params),
  
  // 审批操作
  approve: (id: string, data: any) => post<any>(`/approval/${id}/approve`, data),
  
  // 驳回操作
  reject: (id: string, data: any) => post<any>(`/approval/${id}/reject`, data),
};

// 系统设置相关接口
export const settingsApi = {
  // 获取飞书集成配置
  getFeishuConfig: () => get<any>('/settings/feishu-config'),
  
  // 更新飞书集成配置
  updateFeishuConfig: (data: any) => put<any>('/settings/feishu-config', data),
};

// 用户相关接口
export const userApi = {
  // 获取用户信息
  getUserInfo: () => get<any>('/user/info'),

  // 获取用户权限
  getUserPermissions: () => get<any>('/user/permissions'),

  // 更新用户信息
  updateUserInfo: (data: any) => put<any>('/user/info', data),

  // 获取管理员列表
  getAdminList: () => get<any>('/system/user/listAdmins'),

  // 添加单个管理员
  addAdmin: (id: string) => get<any>(`/system/user/addAdmin?id=${id}`),

  // 批量添加管理员
  batchAddAdmins: (ids: string[]) => post<any>(`/system/user/batchAddAdmins`, { ids }),

  // 移除管理员
  removeAdmin: (id: string) => get<any>(`/system/user/removeAdmin?id=${id}`),

  // 获取所有可选为管理员的用户列表
  getAllUsersForAdminSelection: () => get<any>('/system/user/listAllUsersForAdmin'),
};

// 登记提醒配置相关接口
export const reminderConfigApi = {
  // 获取登记提醒配置列表
  getReminderConfigList: (params?: any) => get<any>('/hours/fsSysReminderConfig/list', params),

  // 根据ID获取登记提醒配置
  getReminderConfigById: (id: string) => get<any>('/hours/fsSysReminderConfig/queryById', { id }),

  // 添加登记提醒配置
  addReminderConfig: (data: any) => post<any>('/hours/fsSysReminderConfig/add', data),

  // 更新登记提醒配置
  updateReminderConfig: (data: any) => put<any>('/hours/fsSysReminderConfig/edit', data),

  // 删除登记提醒配置
  deleteReminderConfig: (id: string) => del<any>(`/hours/fsSysReminderConfig/delete?id=${id}`),

  // 批量删除登记提醒配置
  batchDeleteReminderConfig: (ids: string[]) => del<any>(`/hours/fsSysReminderConfig/deleteBatch?ids=${ids.join(',')}`),

  // 启用/禁用登记提醒配置
  toggleReminderConfigStatus: (id: string, status: string) => put<any>('/hours/fsSysReminderConfig/toggleStatus', { id, status }),
};