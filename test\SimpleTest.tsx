/**
 * 简单测试组件 - 验证WaterfallCascader是否能正常加载
 */
import React, { useState } from 'react';
import { Card, Typography, Space, Button } from '@douyinfe/semi-ui';
import WaterfallCascader from './WaterfallCascader';

const { Title, Text } = Typography;

const SimpleTest: React.FC = () => {
  const [value, setValue] = useState<(string | number)[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const handleChange = (newValue: (string | number)[], selectedOptions: any[]) => {
    console.log('SimpleTest - 值变化:', newValue, selectedOptions);
    setValue(newValue);
  };

  const handleDropdownVisibleChange = (visible: boolean) => {
    console.log('SimpleTest - 下拉框可见性变化:', visible);
  };

  const handleStatisticsLoad = (workItemKey: string, instanceKey: string, nodeKey: string) => {
    console.log('SimpleTest - 统计信息加载:', { workItemKey, instanceKey, nodeKey });
  };

  const clearValue = () => {
    setValue([]);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>WaterfallCascader 简单测试</Title>
        
        <Space vertical style={{ width: '100%' }} spacing={24}>
          {/* 当前值显示 */}
          <div>
            <Text strong>当前选择值：</Text>
            <div style={{ 
              marginTop: '8px', 
              padding: '8px', 
              backgroundColor: '#f8f9fa', 
              borderRadius: '4px',
              fontFamily: 'monospace'
            }}>
              {value.length > 0 ? JSON.stringify(value) : '未选择'}
            </div>
          </div>

          {/* 级联选择器 */}
          <div>
            <Text strong>级联选择器：</Text>
            <div style={{ marginTop: '8px' }}>
              <WaterfallCascader
                placeholder="请选择工作内容"
                style={{ width: '100%' }}
                value={value}
                onChange={handleChange}
                onDropdownVisibleChange={handleDropdownVisibleChange}
                loading={loading}
                onStatisticsLoad={handleStatisticsLoad}
              />
            </div>
          </div>

          {/* 操作按钮 */}
          <div>
            <Space>
              <Button onClick={clearValue} type="secondary">
                清空选择
              </Button>
              <Button onClick={() => setLoading(!loading)} type="tertiary">
                {loading ? '停止加载' : '模拟加载'}
              </Button>
            </Space>
          </div>

          {/* 说明 */}
          <div>
            <Text strong>测试说明：</Text>
            <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
              <li>点击级联选择器应该能看到工作项列表</li>
              <li>选择工作项后应该能看到实例列表</li>
              <li>如果实例超过5条，应该显示"📄 加载更多"按钮</li>
              <li>查看浏览器控制台的网络请求和日志输出</li>
            </ul>
          </div>

          {/* 调试信息 */}
          <div>
            <Text strong>调试检查：</Text>
            <ol style={{ marginTop: '8px', paddingLeft: '20px' }}>
              <li>打开浏览器F12开发者工具</li>
              <li>切换到Network（网络）标签页</li>
              <li>点击级联选择器，应该能看到API请求</li>
              <li>切换到Console（控制台）标签页，查看日志输出</li>
            </ol>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default SimpleTest;
