package org.jeecg.modules.hours.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.servlet.http.HttpServletRequest;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.hours.service.IFsUserRoleService;
import org.jeecg.modules.hours.service.IHourRecordService;
import org.jeecg.modules.hours.utils.UserInfoUtils;
import org.jeecg.modules.hours.vo.HourRecordStatsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 工时统计报表
 * @Author: jeecg-boot
 * @Date: 2023-07-01
 * @Version: V1.0
 */
@Slf4j
@RestController
@RequestMapping("/hapi/hours/report")
public class HourReportController {
    
    @Autowired
    private IHourRecordService hourRecordService;
    @Autowired
    private UserInfoUtils userInfoUtils;
    @Autowired
    private IFsUserRoleService fsUserRoleService;
    
    /**
     * 获取项目工时统计
     *
     * @param projectId 项目ID
     * @return
     */
    @AutoLog(value = "工时统计-项目工时统计")
    @GetMapping(value = "/projectSummary")
    public Result<?> getProjectSummary(@RequestParam(name = "projectId", required = true) String projectId) {
        List<Map<String, Object>> summaryList = hourRecordService.queryHoursSummaryByProject(projectId);
        return Result.OK(summaryList);
    }
    
    /**
     * 获取个人工时统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    @AutoLog(value = "工时统计-个人工时统计")
    @GetMapping(value = "/personal")
    public Result<?> getPersonalReport(
            @RequestParam(name="startDate", required=true) String startDate,
            @RequestParam(name="endDate", required=true) String endDate) {
        // 获取当前用户标识
        String userId = userInfoUtils.getCurrentUserKey();
        if (userId == null) {
            return Result.error("未获取到有效的用户信息");
        }
        
        // 从数据库查询统计数据
        // 这里是示例代码，实际需要从工时记录表中统计
        HourRecordStatsVO stats = new HourRecordStatsVO();
        stats.setUserId(userId);
        stats.setUserName(userInfoUtils.getUserName());
        stats.setStandardHours(new BigDecimal("40.00"));  // 示例：标准工时
        stats.setOvertimeHours(new BigDecimal("5.00"));   // 示例：加班工时
        stats.setTotalHours(new BigDecimal("45.00"));     // 示例：总工时
        stats.setSubmittedHours(new BigDecimal("45.00")); // 示例：已提交工时
        stats.setApprovedHours(new BigDecimal("40.00"));  // 示例：已审批工时
        
        return Result.OK(stats);
    }
    
    /**
     * 获取部门工时统计
     *
     * @param departmentId 部门ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    @ApiOperation(value="获取部门工时统计", notes="获取指定部门的工时统计信息")
    @GetMapping(value = "/department")
    public Result<?> getDepartmentReport(
            @RequestParam(name="departmentId", required=true) String departmentId,
            @RequestParam(name="startDate", required=true) String startDate,
            @RequestParam(name="endDate", required=true) String endDate) {
        // 获取当前用户标识
        String userId = userInfoUtils.getCurrentUserKey();
        if (userId == null) {
            return Result.error("未获取到有效的用户信息");
        }
        
        // 检查用户权限
        if (!fsUserRoleService.hasRole(userId, "admin")) {
            return Result.error("没有权限查看部门工时统计");
        }
        
        // 从数据库查询部门统计数据
        // 这里是示例代码，实际需要从工时记录表中统计
        List<HourRecordStatsVO> statsList = new ArrayList<>();
        
        // 示例数据
        HourRecordStatsVO stats1 = new HourRecordStatsVO();
        stats1.setUserId("user1");
        stats1.setUserName("张三");
        stats1.setTotalHours(new BigDecimal("40.00"));
        stats1.setApprovedHours(new BigDecimal("40.00"));
        statsList.add(stats1);
        
        HourRecordStatsVO stats2 = new HourRecordStatsVO();
        stats2.setUserId("user2");
        stats2.setUserName("李四");
        stats2.setTotalHours(new BigDecimal("38.00"));
        stats2.setApprovedHours(new BigDecimal("38.00"));
        statsList.add(stats2);
        
        return Result.OK(statsList);
    }
    
    /**
     * 获取工时分布统计
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    @AutoLog(value = "工时统计-工时分布统计")
    @GetMapping(value = "/hourDistribution")
    public Result<?> getHourDistribution(
            @RequestParam(name = "userId", required = false) String userId,
            @RequestParam(name = "startDate", required = true) String startDate,
            @RequestParam(name = "endDate", required = true) String endDate) {
        
        // 如果未指定用户ID，则使用当前登录用户
        if (userId == null || userId.isEmpty()) {
            userId = userInfoUtils.getCurrentUserKey();
            if (userId == null) {
                return Result.error("未获取到有效的用户信息，请重新授权");
            }
        }
        
        // 从数据库查询工时分布统计数据
        // 这里是示例代码，实际需要从工时记录表中统计
        Map<String, Object> result = new HashMap<>();
        
        // 按项目分布
        List<Map<String, Object>> projectDistribution = new ArrayList<>();
        Map<String, Object> project1 = new HashMap<>();
        project1.put("projectId", "P001");
        project1.put("projectName", "项目管理系统");
        project1.put("hours", 20.0);
        project1.put("percentage", 50.0);
        projectDistribution.add(project1);
        
        Map<String, Object> project2 = new HashMap<>();
        project2.put("projectId", "P002");
        project2.put("projectName", "工时管理模块");
        project2.put("hours", 12.0);
        project2.put("percentage", 30.0);
        projectDistribution.add(project2);
        
        Map<String, Object> project3 = new HashMap<>();
        project3.put("projectId", "P003");
        project3.put("projectName", "飞书集成开发");
        project3.put("hours", 8.0);
        project3.put("percentage", 20.0);
        projectDistribution.add(project3);
        
        result.put("projectDistribution", projectDistribution);
        
        // 按工作类型分布
        List<Map<String, Object>> typeDistribution = new ArrayList<>();
        Map<String, Object> type1 = new HashMap<>();
        type1.put("type", "需求分析");
        type1.put("hours", 10.0);
        type1.put("percentage", 25.0);
        typeDistribution.add(type1);
        
        Map<String, Object> type2 = new HashMap<>();
        type2.put("type", "设计开发");
        type2.put("hours", 20.0);
        type2.put("percentage", 50.0);
        typeDistribution.add(type2);
        
        Map<String, Object> type3 = new HashMap<>();
        type3.put("type", "测试部署");
        type3.put("hours", 10.0);
        type3.put("percentage", 25.0);
        typeDistribution.add(type3);
        
        result.put("typeDistribution", typeDistribution);
        
        return Result.OK(result);
    }

    @ApiOperation(value="获取工时统计", notes="获取工时统计信息")
    @GetMapping(value = "/statistics")
    public Result<?> getStatistics(
            @RequestParam(name="startDate", required=true) String startDate,
            @RequestParam(name="endDate", required=true) String endDate,
            @RequestParam(name="departmentId", required=false) String departmentId) {
        // 获取当前用户标识
        String userId = userInfoUtils.getCurrentUserKey();
        if (userId == null) {
            return Result.error("未获取到有效的用户信息");
        }
        
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("userId", userId);
        result.put("startDate", startDate);
        result.put("endDate", endDate);
        result.put("departmentId", departmentId);
        
        return Result.OK(result);
    }
} 