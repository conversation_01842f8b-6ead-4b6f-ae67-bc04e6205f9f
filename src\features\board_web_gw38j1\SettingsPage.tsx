import React, { useState, useEffect, use<PERSON>allback, useRef } from "react";
import {
  RadioGroup,
  Card,
  Table,
  Button,
  Switch,
  Typography,
  Input,
  Select,
  Divider,
  TextArea,
  Radio,
  Toast,
  Modal,
  Transfer,
  Checkbox,
  Avatar,
  Highlight,
  Spin,
  Popconfirm,
} from "@douyinfe/semi-ui";
import {
  IconEdit,
  IconDelete,
  IconPlus,
  IconClose,
  IconSync,
} from "@douyinfe/semi-icons";
import { userApi } from "../../services/api";
import { RichTextEditor } from "@lark-project/ui-kit-plugin";
import { getFeishuSpaceId } from "../../utils/auth";
import {
  getWorkHourSettings,
  saveWorkHourSettings,
  getWorkHourRules,
  saveWorkHourRules,
  getWorkContentRange,
  saveWorkContentRange,
} from "../../services/api/systemConfig";
import ReminderSettings from "./components/ReminderSettings";
import ProjectMemberSettings from "./components/ProjectMemberSettings";
import "./index.css";
import { get, post } from "../../utils/request";

const { Title, Paragraph } = Typography;

// 添加全局类型声明
declare global {
  interface Window {
    JSSDK: {
      richTextEditor: {
        show: (
          options: {
            title?: string;
            value: string;
            context?: {
              spaceId: string;
            };
          },
          callback: (result: {
            confirmed: boolean;
            canceled: boolean;
            nextValue?: string;
          }) => void
        ) => void;
      };
      navigation: {
        open: (url: string) => void;
      };
    };
  }
}

// 添加类型定义
interface ApiResponse<T = any> {
  success: boolean;
  result?: T;
  message?: string;
}

interface WorkHourSettings {
  workHourPerDay: string;
  minWorkHour: string;
  maxWorkHourPerDay: string;
}

interface SysConfig {
  configKey: string;
  configValue: string;
  configName: string;
  configGroup: string;
  status: "0" | "1";
}

// 添加富文本内容的类型定义
interface RichTextContent {
  doc: string;
  doc_html: string;
  doc_text: string;
  is_empty: boolean;
}

interface RichTextEditorResult {
  confirmed: boolean;
  canceled: boolean;
  nextValue?: RichTextContent;
}

const SettingsPage: React.FC = () => {
  const [adminList, setAdminList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingIds, setLoadingIds] = useState<string[]>([]);
  const [isTransferModalVisible, setIsTransferModalVisible] = useState(false);
  const [allUsersForAdminSelection, setAllUsersForAdminSelection] = useState<
    any[]
  >([]);
  const [selectedUserIdsInTransfer, setSelectedUserIdsInTransfer] = useState<
    string[]
  >([]);
  const [transferLoading, setTransferLoading] = useState(false);
  const [searchText, setSearchText] = useState("");

  // 提醒设置组件的ref
  const reminderSettingsRef = useRef<any>(null);

  // 获取飞书空间ID
  const [spaceId, setSpaceId] = useState<string | null>(null);

  // 富文本内容
  const [richTextContent, setRichTextContent] = useState<any>({
    doc: `<p>1. 工时填写需真实反映实际工作情况，禁止弄虚作假。</p>
<p>2. 每日工时填写不得超过当日考勤工时。</p>
<p>3. 请在当天或次日完成工时填写，月底务必在规定时间前完成。</p>
<p>4. 工时需经项目负责人和直属上级审批通过。</p>
<p>5. 请假当天不得填写工时。</p>`,
    doc_html: "",
    doc_text: "",
    is_empty: false,
  });
  const [richTextLoading, setRichTextLoading] = useState(false);

  // 添加每个设置板块的激活状态
  const [sectionStatus, setSectionStatus] = useState({
    reminder: true,
    basic: true,
    approval: true,
    validation: true,
    feishu: true,
    workContent: true,
    rules: true,
    admin: true,
    projectMember: true, // 添加项目人员数据维护的状态
  });

  // 切换板块激活状态
  const toggleSectionStatus = (section: string) => {
    setSectionStatus((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  // 在组件挂载时获取飞书空间ID
  useEffect(() => {
    const feishuSpaceId = getFeishuSpaceId();
    console.log("获取到飞书空间ID:", feishuSpaceId);
    setSpaceId(feishuSpaceId);

    // 加载富文本内容
    // loadRichTextContent();

    // 加载系统配置
    loadSysSettings();
  }, []);

  // 加载富文本内容
  const loadRichTextContent = async (sourceContent) => {
    try {
      setRichTextLoading(true);

      // 获取富文本规则
      let content = { doc: "", doc_html: "", doc_text: "", is_empty: true };
      try {
        content = JSON.parse(sourceContent);
      } catch (e) {
        content = {
          doc: sysSettings.work_hour_record_rules,
          doc_html: "",
          doc_text: "",
          is_empty: false,
        };
      }
      setRichTextContent({
        doc: content.doc,
        doc_html: content.doc_html,
        doc_text: content.doc_text,
        is_empty: content.is_empty,
      });

      // const response = await getWorkHourRules();

      // if (response && response.success) {
      //   // 设置富文本内容
      //   if (response.result) {
      //     try {
      //       const parsedContent = JSON.parse(response.result);
      //       const content = JSON.parse(parsedContent.content);
      //       setRichTextContent(content);
      //       console.log('成功加载富文本内容:', content);
      //     } catch (e) {
      //       console.error('解析富文本内容失败:', e);
      //       // 如果解析失败，说明是旧格式，将其作为doc内容
      //       setRichTextContent({
      //         doc: response.result,
      //         doc_html: '',
      //         doc_text: '',
      //         is_empty: false
      //       });
      //     }
      //   } else {
      //     console.warn('服务器返回空富文本内容，使用默认值');
      //     setRichTextContent({
      //       doc: '<p>1. 工时填写需真实反映实际工作情况，禁止弄虚作假。</p><p>2. 每日工时填写不得超过当日考勤工时。</p><p>3. 请在当天或次日完成工时填写，月底务必在规定时间前完成。</p><p>4. 工时需经项目负责人和直属上级审批通过。</p><p>5. 请假当天不得填写工时。</p>',
      //       doc_html: '',
      //       doc_text: '',
      //       is_empty: false
      //     });
      //   }
      // } else {
      //   console.error('获取富文本内容失败:', response?.message);
      //   Toast.error('获取规则说明内容失败');
      //   // 使用默认内容
      //   setRichTextContent({
      //     doc: '<p>1. 工时填写需真实反映实际工作情况，禁止弄虚作假。</p><p>2. 每日工时填写不得超过当日考勤工时。</p><p>3. 请在当天或次日完成工时填写，月底务必在规定时间前完成。</p><p>4. 工时需经项目负责人和直属上级审批通过。</p><p>5. 请假当天不得填写工时。</p>',
      //     doc_html: '',
      //     doc_text: '',
      //     is_empty: false
      //   });
      // }
    } catch (error) {
      console.error("加载富文本内容出错:", error);
      Toast.error("获取规则说明内容失败");
      // 使用默认内容
      setRichTextContent({
        doc: "<p>1. 工时填写需真实反映实际工作情况，禁止弄虚作假。</p><p>2. 每日工时填写不得超过当日考勤工时。</p><p>3. 请在当天或次日完成工时填写，月底务必在规定时间前完成。</p><p>4. 工时需经项目负责人和直属上级审批通过。</p><p>5. 请假当天不得填写工时。</p>",
        doc_html: "",
        doc_text: "",
        is_empty: false,
      });
    } finally {
      setRichTextLoading(false);
    }
  };

  // 处理富文本内容变更
  const handleRichTextChange = (value: any) => {
    setRichTextContent(value);
  };

  // 获取管理员列表
  const fetchAdminList = async () => {
    try {
      setLoading(true);
      const response = await userApi.getAdminList();
      if (response && response.success) {
        const admins = response.result.map((item, index) => ({
          id: item.id,
          seqNo: index + 1,
          name: item.name,
          department: item.departmentName ? item.departmentName : "-",
          addBy: item.createByName || item.createBy || "-",
          addTime: item.createdAt ? formatDateTime(item.createdAt) : "-",
        }));
        setAdminList(admins);
      } else {
        setAdminList([]);
        Toast.error("获取管理员列表失败");
      }
    } catch (error) {
      console.error("获取管理员列表出错：", error);
      Toast.error("获取管理员列表失败");
      setAdminList([]);
    } finally {
      setLoading(false);
    }
  };

  // 格式化日期时间
  const formatDateTime = (dateStr) => {
    if (!dateStr) return "-";

    try {
      const date = new Date(dateStr);

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (e) {
      console.error("日期格式化错误:", e);
      return dateStr; // 返回原始值
    }
  };

  // 移除管理员
  const handleRemoveAdmin = async (id) => {
    try {
      // 设置当前行为loading状态
      setLoadingIds((prev) => [...prev, id]);

      const response = await userApi.removeAdmin(id);
      if (response && response.success) {
        Toast.success("管理员移除成功");
        fetchAdminList(); // 重新获取列表
      } else {
        Toast.error("管理员移除失败");
      }
    } catch (error) {
      console.error("移除管理员出错：", error);
      Toast.error("移除管理员失败");
    } finally {
      // 移除当前行的loading状态
      setLoadingIds((prev) => prev.filter((item) => item !== id));
    }
  };

  // 获取可添加为管理员的用户列表
  const fetchUsersForAdminSelection = async () => {
    try {
      setTransferLoading(true);
      const response = await userApi.getAllUsersForAdminSelection();
      if (response && response.success) {
        // 转换数据格式以适应Transfer组件
        const users = response.result.map((user) => ({
          key: user.id, // key是用户ID
          label: user.name || "未命名",
          value: user.id, // 将value也设置为用户ID，不再使用邮箱
          email: user.email || "N/A", // 将邮箱单独存储为email属性
          abbr: user.name ? user.name.substr(0, 1) : "U",
          color: getRandomColor(user.id),
          department: user.deptId || "未知部门",
          disabled: false,
        }));
        setAllUsersForAdminSelection(users);
      } else {
        Toast.error("获取可选管理员用户列表失败");
        setAllUsersForAdminSelection([]);
      }
    } catch (error) {
      console.error("获取可选管理员用户列表出错：", error);
      Toast.error("获取可选管理员用户列表出错");
      setAllUsersForAdminSelection([]);
    } finally {
      setTransferLoading(false);
    }
  };

  // 为用户生成随机颜色
  const getRandomColor = (id) => {
    const colors = [
      "amber",
      "indigo",
      "cyan",
      "blue",
      "green",
      "orange",
      "purple",
      "red",
      "teal",
    ];
    // 使用ID作为种子以保证同一用户每次获得相同颜色
    const index =
      typeof id === "string"
        ? id.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0) %
          colors.length
        : id % colors.length;
    return colors[index];
  };

  // 自定义过滤器
  const customFilter = (sugInput, item) => {
    if (!sugInput) return true;
    const input = sugInput.toLowerCase();
    return (
      (item.label && item.label.toLowerCase().includes(input)) ||
      (item.email && item.email.toLowerCase().includes(input)) ||
      (item.department && item.department.toLowerCase().includes(input))
    );
  };

  // 渲染左侧源项目
  const renderSourceItem = (item) => {
    return (
      <div className="components-transfer-demo-source-item" key={item.key}>
        <Checkbox
          onChange={() => {
            if (item.onChange) {
              item.onChange();
            }
          }}
          checked={item.checked}
          style={{
            height: 52,
            alignItems: "center",
            width: "100%",
            display: "flex",
          }}
        >
          <Avatar color={item.color} size="small">
            {item.abbr}
          </Avatar>
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              marginLeft: 8,
              flex: 1,
              overflow: "hidden",
            }}
          >
            <div
              style={{
                fontWeight: "bold",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              <Highlight sourceString={item.label} searchWords={[searchText]} />
            </div>
            <div
              style={{
                fontSize: "12px",
                color: "var(--semi-color-text-2)",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              <Highlight sourceString={item.email} searchWords={[searchText]} />
              {item.department && <span> | {item.department}</span>}
            </div>
          </div>
        </Checkbox>
      </div>
    );
  };

  // 渲染右侧已选项目
  const renderSelectedItem = (item) => {
    return (
      <div className="components-transfer-demo-selected-item" key={item.key}>
        <Avatar color={item.color} size="small">
          {item.abbr}
        </Avatar>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            marginLeft: 8,
            flex: 1,
            minWidth: 0,
            overflow: "hidden",
          }}
        >
          <div
            style={{
              fontWeight: "500",
              fontSize: "14px",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {item.label}
          </div>
          <div
            style={{
              fontSize: "12px",
              color: "var(--semi-color-text-2)",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {item.email}
          </div>
        </div>
        <IconClose
          onClick={item.onRemove}
          className="remove-icon"
          style={{
            cursor: "pointer",
            color: "var(--semi-color-tertiary)",
            visibility: "hidden", // 默认隐藏，鼠标悬停时显示
          }}
        />
      </div>
    );
  };

  // 添加管理员
  const handleAddAdmin = () => {
    fetchUsersForAdminSelection(); // 获取用户列表数据
    setSelectedUserIdsInTransfer([]); // 清空上次选择
    setIsTransferModalVisible(true); // 打开Modal
  };

  // 添加提醒配置
  const handleAddReminder = () => {
    if (reminderSettingsRef.current && reminderSettingsRef.current.handleAdd) {
      reminderSettingsRef.current.handleAdd();
    }
  };

  // 组件挂载时获取管理员列表
  useEffect(() => {
    fetchAdminList();
  }, []);

  // 管理员列表表格列
  const adminColumns = [
    { title: "序号", dataIndex: "seqNo", width: 80 },
    { title: "姓名", dataIndex: "name", width: 100 },
    { title: "部门", dataIndex: "department", width: 120 },
    { title: "添加人", dataIndex: "addBy", width: 100 },
    { title: "添加时间", dataIndex: "addTime", width: 150 },
    {
      title: "操作",
      dataIndex: "operation",
      width: 100,
      render: (_, record) => (
        <Popconfirm
          title="确定要删除管理员账号吗？"
          okType="danger"
          onConfirm={() => handleRemoveAdmin(record.id)}
        >
          <Button
            icon={<IconDelete />}
            type="danger"
            theme="borderless"
            loading={loadingIds.includes(record.id)}
          >
            移除
          </Button>
        </Popconfirm>
      ),
    },
  ];

  // 标题栏包含Switch的布局组件
  const SectionHeader = ({
    title,
    section,
    extraButtons = undefined,
    showSwitch = true,
  }: {
    title: string;
    section: string;
    extraButtons?: React.ReactNode;
    showSwitch?: boolean;
  }) => (
    <div
      className="settings-section-header"
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
      }}
    >
      <div className="settings-section-title">
        <Title
          heading={5}
          style={{
            margin: 0,
            lineHeight: "24px",
            fontSize: "16px",
            fontWeight: "bold",
          }}
        >
          {title}
        </Title>
        {showSwitch && (
          <Switch
            checked={sectionStatus[section]}
            onChange={() => toggleSectionStatus(section)}
            size="small"
          />
        )}
      </div>
      {extraButtons && (
        <div className="settings-section-extra-buttons">{extraButtons}</div>
      )}
    </div>
  );

  // 渲染表格
  const renderTable = (columns, dataSource) => (
    <div className="settings-table-wrapper">
      <Table
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        size="middle"
        scroll={{ x: "100%" }}
      />
    </div>
  );

  // 保存富文本内容
  const saveRichTextContent = async (content: any) => {
    try {
      setRichTextLoading(true);

      // 将整个富文本对象转为JSON字符串
      const contentStr = JSON.stringify(content);

      // 保存富文本规则
      const response = await saveWorkHourRules(contentStr);

      if (response && response.success) {
        Toast.success("保存成功");
        setRichTextContent(content);
      } else {
        Toast.error("保存失败");
      }
    } catch (error) {
      console.error("保存富文本内容出错:", error);
      Toast.error("保存失败");
    } finally {
      setRichTextLoading(false);
    }
  };

  const handleResult = async (result: {
    confirmed: boolean;
    canceled: boolean;
    nextValue?: any;
  }) => {
    if (result.confirmed && result.nextValue) {
      await saveRichTextContent(result.nextValue);
    }
  };

  const [sysSettings, setSysSettings] = useState<Record<string, string>>({});

  // 加载系统配置
  const loadSysSettings = async () => {
    try {
      const result = (await get(
        "/hours/sys/config/sys/settings"
      )) as ApiResponse<Record<string, string>>;
      if (result.success) {
        setSysSettings(result.result || {});
        loadRichTextContent(result.result["work_hour_record_rules"]);
      }
    } catch (error) {
      console.error("加载系统配置失败:", error);
      Toast.error("加载系统配置失败");
    }
  };

  useEffect(() => {
    // 加载飞书配置
    const loadFeishuConfig = async () => {
      try {
        const res = await get("/hours/sys/config/sys/settings");
        const resAny = res as any;
        if (resAny && resAny.success && resAny.result) {
          setFeishuAppId(resAny.result["bind_app_id"] || "");
          setFeishuAppSecret(resAny.result["bind_app_secret"] || "");
          if (resAny.result["wok_hour_rule"]) {
            setWorkHourRule(parseInt(resAny.result["wok_hour_rule"]));
          }
          if (resAny.result["feishu_attendance_validate_switch"]) {
            setFeishuAttendanceValidateSwitch(
              parseInt(resAny.result["feishu_attendance_validate_switch"])
            );
          }
        }
      } catch (e) {
        console.error("加载飞书配置失败:", e);
      }
    };

    loadFeishuConfig();
  }, []);

  // 打开飞书富文本编辑器
  const openFeishuRichTextEditor = () => {
    if (typeof window.JSSDK !== "undefined" && window.JSSDK.richTextEditor) {
      try {
        // 确保richTextContent是一个对象
        let editorValue: any;
        if (typeof richTextContent === "string") {
          try {
            editorValue = JSON.parse(richTextContent);
          } catch (e) {
            console.error("解析富文本内容失败:", e);
            editorValue = {
              doc: richTextContent,
              doc_html: "",
              doc_text: "",
              is_empty: false,
            };
          }
        } else {
          editorValue = richTextContent;
        }
        console.log(
          "editorValue=",
          editorValue,
          ";;;richTextContent=",
          richTextContent
        );

        // 根据飞书文档，配置富文本编辑器
        const options = {
          title: "登记规则富文本配置说明",
          defaultValue: editorValue.doc || "", // 传递doc字符串内容
          context: {
            spaceId: spaceId || "DEFAULT_SPACE_ID",
          },
        };

        console.log("打开富文本编辑器，传递的内容:", options);

        // 富文本编辑器回调
        const handleResult = async (result: {
          confirmed: boolean;
          canceled: boolean;
          nextValue?: any;
        }) => {
          console.log("富文本编辑器返回结果:", result);

          if (result.confirmed && result.nextValue) {
            try {
              // 构建新的富文本内容对象
              const newContent = {
                doc: result.nextValue.doc,
                doc_html: result.nextValue.doc_html,
                doc_text: result.nextValue.doc_text,
                is_empty: result.nextValue.is_empty,
              };

              // 将整个对象转为JSON字符串
              const contentStr = JSON.stringify(newContent);

              // 保存到后端
              await saveRichTextContent(newContent);

              // 更新本地状态
              setRichTextContent(newContent);
            } catch (error) {
              console.error("保存富文本内容失败:", error);
              Toast.error("保存失败，请重试");
            }
          }
        };

        window.JSSDK.richTextEditor.show(options, handleResult);
      } catch (error) {
        console.error("调用富文本编辑器出错:", error);
        Toast.error("无法打开编辑器");
      }
    } else {
      console.error("JSSDK.richTextEditor 不可用");
      Toast.error("富文本编辑器不可用，请确保在飞书环境中运行");
    }
  };

  // 公共方法：保存配置
  const saveConfigurations = async (configs: Record<string, any>) => {
    try {
      const params = Object.entries(configs).map(([key, value]) => ({
        key,
        value,
      }));

      const res = await post("/hours/sys/config/saveBatchByKeys", params);
      const resAny = res as any;
      if (resAny && resAny.success) {
        Toast.success("保存成功");
        return true;
      } else {
        Toast.error(resAny?.message || "保存失败");
        return false;
      }
    } catch (error) {
      console.error("保存设置出错:", error);
      Toast.error("设置保存失败");
      return false;
    }
  };

  const [feishuAppId, setFeishuAppId] = useState("");
  const [feishuAppSecret, setFeishuAppSecret] = useState("");
  const [workHourRule, setWorkHourRule] = useState(1); // 工时计算规则
  const [feishuAttendanceValidateSwitch, setFeishuAttendanceValidateSwitch] =
    useState(0); // 飞书假勤校验开关：0-关闭；1-开启

  const handleSaveSettings = async () => {
    if (!feishuAppId || !feishuAppSecret) {
      Toast.error("请填写飞书应用ID和密钥");
      return;
    }

    const configs = {
      bind_app_id: feishuAppId,
      bind_app_secret: feishuAppSecret,
      wok_hour_rule: workHourRule,
      feishu_attendance_validate_switch: feishuAttendanceValidateSwitch,
    };

    await saveConfigurations(configs);
  };

  // 添加项目人员数据维护的状态
  const [projectMemberVisible, setProjectMemberVisible] = useState(true);

  // 项目人员数据维护组件的ref
  const projectMemberSettingsRef = useRef<any>(null);

  return (
    <div
      className="settings-root settings-vertical"
      style={{
        paddingBottom: 80,
        maxWidth: "100%",
        boxSizing: "border-box",
        overflow: "hidden",
      }}
    >
      {/**
      <Card className="settings-card" style={{ width: '100%', height: 'auto', overflow: 'visible' }}>
        <SectionHeader
          title="登记提醒设置"
          section="reminder"
          extraButtons={
            <Button
              type="primary"
              icon={<IconPlus />}
              onClick={handleAddReminder}
              size="small"
            >
              添加
            </Button>
          }
        />
        <Divider />

        <div className="settings-form-section" style={{ display: sectionStatus.reminder ? 'block' : 'none' }}>
          <ReminderSettings
            visible={sectionStatus.reminder}
            ref={reminderSettingsRef}
          />
        </div>
      </Card>
       */}

      {/**
      
      <Card className="settings-card" style={{ width: '100%', height: 'auto', overflow: 'visible' }}>
        <SectionHeader title="飞书对接设置" section="feishu" />
        <Divider />
        
        <div style={{ display: sectionStatus.feishu ? 'block' : 'none' }}>
          <div className="settings-form-section">
            <div className="settings-form-row" style={{ 
              display: 'flex',
              alignItems: 'flex-start',
              gap: '40px',
              padding: '0 16px'
            }}>
              <div style={{ flex: 1 }}>
                <Title heading={6} style={{ marginBottom: '16px' }}>基础设置</Title>
                
                <div className="settings-form-item" style={{ marginBottom: '24px' }}>
                  <label style={{ 
                    display: 'block', 
                    marginBottom: '8px',
                    fontSize: '14px',
                    color: 'var(--semi-color-text-0)'
                  }}>
                    AppID
                  </label>
                  <Input 
                    placeholder="请输入飞书AppID" 
                    style={{ width: '320px' }}
                    size="default"
                    value={feishuAppId || ''}
                    onChange={value => setFeishuAppId(value)}
                />
                </div>
                <div className="settings-form-item" style={{ marginBottom: '24px' }}>
                  <label style={{ 
                    display: 'block', 
                    marginBottom: '8px',
                    fontSize: '14px',
                    color: 'var(--semi-color-text-0)'
                  }}>
                    AppSecret
                  </label>
                  <Input 
                    placeholder="请输入飞书AppSecret" 
                    style={{ width: '320px' }}
                    size="default"
                    value={feishuAppSecret}
                    onChange={value => setFeishuAppSecret(value)}
                  />
                </div>


                <div className="settings-form-item" style={{ marginBottom: '24px' }}>
                  <label style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    marginBottom: '8px',
                    fontSize: '14px',
                    color: 'var(--semi-color-text-0)'
                  }}>
                    启用飞书假勤校验
                    <Switch checked={feishuAttendanceValidateSwitch===1} size="small" style={{ marginLeft: '12px' }} onChange={value=>{
                      console.log('change::value=',value);
                      setFeishuAttendanceValidateSwitch(value?1:0);
                    }} />
                  </label>
                  <div style={{ color: 'var(--semi-color-text-2)', fontSize: '12px' }}>
                    开启后将与飞书系统进行数据同步
                  </div>
                </div>

                <div className="settings-form-item" style={{ marginBottom: '24px' }}>
                  <label style={{ 
                    display: 'block', 
                    marginBottom: '8px',
                    fontSize: '14px',
                    color: 'var(--semi-color-text-0)'
                  }}>
                    每日可登记工时计算规则
                  </label>
                  <RadioGroup aria-label="每日可登记工时计算规则" name="rule-reg-work-hours"
                    value={workHourRule} onChange={e => {
                      console.log('value=',e);
                      setWorkHourRule(e.target.value);
                    }}>
                      <Radio value={1}>站内工作时长</Radio>
                      <Radio value={2}>站内工作时长+加班时长</Radio>
                  </RadioGroup>
                </div>
              </div>

              <div style={{ flex: 1 }}>
                <Title heading={6} style={{ marginBottom: '16px' }}>审批对接设置</Title>
                
                <div className="settings-form-item" style={{ marginBottom: '24px' }}>
                  <label style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    marginBottom: '8px',
                    fontSize: '14px',
                    color: 'var(--semi-color-text-0)'
                  }}>
                    启用飞书请假审批校验
                    <Switch size="small" style={{ marginLeft: '12px' }} />
                  </label>
                  <Input 
                    placeholder="请输入审批ID" 
                    style={{ width: '320px' }}
                    size="default"
                  />
                </div>

                <div className="settings-form-item" style={{ marginBottom: '24px' }}>
                  <label style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    marginBottom: '8px',
                    fontSize: '14px',
                    color: 'var(--semi-color-text-0)'
                  }}>
                    启用飞书加班审批校验
                    <Switch size="small" style={{ marginLeft: '12px' }} />
                  </label>
                  <Input 
                    placeholder="请输入审批ID" 
                    style={{ width: '320px' }}
                    size="default"
                  />
                </div>

                <div className="settings-form-item" style={{ marginBottom: '24px' }}>
                  <label style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    marginBottom: '8px',
                    fontSize: '14px',
                    color: 'var(--semi-color-text-0)'
                  }}>
                    启用飞书工时审批
                    <Switch size="small" style={{ marginLeft: '12px' }} />
                  </label>
                  <Input 
                    placeholder="请输入审批ID" 
                    style={{ width: '320px' }}
                    size="default"
                    disabled
                  />
                  <div style={{ color: 'var(--semi-color-text-2)', fontSize: '12px', marginTop: '4px' }}>
                    该功能涉及到可行性评估，暂不可用
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          
        </div>
          
        <div className="settings-form-actions" style={{ marginTop: '24px', borderTop: '1px solid var(--semi-color-border)', paddingTop: '24px' }}>
          <Button type="primary" onClick={handleSaveSettings}>保存设置</Button>
        </div>
      </Card>
     */}
      <Card
        className="settings-card"
        style={{ width: "100%", height: "auto", overflow: "visible" }}
      >
        <SectionHeader
          title="登记规则富文本配置"
          section="rules"
          showSwitch={false}
          extraButtons={
            <Button
              icon={<IconEdit />}
              type="primary"
              theme="light"
              size="small"
              onClick={openFeishuRichTextEditor}
              loading={richTextLoading}
            >
              编辑
            </Button>
          }
        />
        <Divider />

        <div
          className="settings-form-section"
          style={{ display: sectionStatus.rules ? "block" : "none" }}
        >
          <div className="settings-form-row">
            <div
              className="settings-form-item settings-form-item-wide"
              style={{ position: "relative" }}
            >
              <label>工时登记规则说明</label>
              <div
                style={{
                  border: "1px dashed gray",
                  height: 200,
                  padding: 8,
                  backgroundColor: richTextLoading ? "#fafafa" : "transparent",
                  position: "relative",
                }}
              >
                {richTextLoading && (
                  <div
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: "rgba(255, 255, 255, 0.7)",
                    }}
                  >
                    <Spin />
                  </div>
                )}
                <RichTextEditor
                  spaceId={spaceId || "DEFAULT_SPACE_ID"}
                  editable={false}
                  defaultValue={{
                    doc: richTextContent?.doc,
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </Card>

      <Card
        className="settings-card"
        style={{
          width: "100%",
          height: "auto",
          overflow: "hidden",
          maxWidth: "100%",
          boxSizing: "border-box",
          padding: "0",
          margin: 0,
        }}
      >
        <div
          className="settings-section-header"
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div className="settings-section-title">
            <Title
              heading={5}
              style={{
                margin: 0,
                lineHeight: "24px",
                fontSize: "16px",
                fontWeight: "bold",
              }}
            >
              项目人员数据维护
            </Title>
          </div>
        </div>
        <Divider />

        <div
          className="settings-form-section"
          style={{
            display: sectionStatus.projectMember ? "block" : "none",
            overflow: "hidden",
            maxWidth: "100%",
            boxSizing: "border-box",
            padding: "0",
          }}
        >
          <ProjectMemberSettings
            visible={sectionStatus.projectMember}
            ref={projectMemberSettingsRef}
          />
        </div>
      </Card>

      <Card
        className="settings-card"
        style={{ width: "100%", height: "auto", overflow: "visible" }}
      >
        <div
          className="settings-section-header"
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div className="settings-section-title">
            <Title
              heading={5}
              style={{
                margin: 0,
                lineHeight: "24px",
                fontSize: "16px",
                fontWeight: "bold",
              }}
            >
              管理员配置
            </Title>
          </div>
          <Button
            type="primary"
            icon={<IconPlus />}
            onClick={handleAddAdmin}
            loading={loading}
          >
            添加
          </Button>
        </div>
        <Divider />

        <div>
          <div
            style={{
              padding: "12px 16px",
              margin: "0 0 16px 0",
              backgroundColor: "#f8f9fa",
              borderRadius: "4px",
            }}
          >
            <Paragraph style={{ margin: 0, padding: 0, color: "#333" }}>
              根据组织架构，普通员工拥有工时管理模块(仅登录和查看自己的数据)权限，管理员拥有全部数据
            </Paragraph>
          </div>

          <div className="settings-table-wrapper">
            <Table
              columns={adminColumns}
              dataSource={adminList}
              pagination={false}
              size="middle"
              scroll={{ x: "100%" }}
              loading={loading}
            />
          </div>
        </div>
      </Card>

      <Modal
        title="添加管理员"
        visible={isTransferModalVisible}
        onOk={async () => {
          if (selectedUserIdsInTransfer.length === 0) {
            Toast.warning("请至少选择一个用户");
            return;
          }
          setLoading(true);

          try {
            const batchResponse = await userApi.batchAddAdmins(
              selectedUserIdsInTransfer
            );

            if (batchResponse && batchResponse.success) {
              Toast.success(
                `成功添加 ${selectedUserIdsInTransfer.length} 名管理员`
              );
              fetchAdminList();
            } else {
              console.warn("批量添加失败，尝试逐个添加");

              let successCount = 0;
              for (const userId of selectedUserIdsInTransfer) {
                const response = await userApi.addAdmin(userId);
                if (response && response.success) {
                  successCount++;
                }
              }

              if (successCount > 0) {
                Toast.success(`成功添加 ${successCount} 名管理员`);
                fetchAdminList();
              }

              if (successCount < selectedUserIdsInTransfer.length) {
                Toast.error(
                  `添加失败 ${
                    selectedUserIdsInTransfer.length - successCount
                  } 名管理员`
                );
              }
            }
          } catch (error) {
            console.error("添加管理员出错:", error);
            Toast.error("添加管理员时发生错误");
          } finally {
            setLoading(false);
            setIsTransferModalVisible(false);
            setSelectedUserIdsInTransfer([]);
          }
        }}
        onCancel={() => {
          setIsTransferModalVisible(false);
          setSelectedUserIdsInTransfer([]);
        }}
        width={700}
        bodyStyle={{ height: 400, overflow: "auto" }}
      >
        <style>
          {`
          .components-transfer-demo-selected-item {
            padding: 8px 12px;
            display: flex;
            align-items: center;
            border-radius: 3px;
            transition: background-color 0.2s;
          }
          
          .components-transfer-demo-selected-item:hover {
            background-color: var(--semi-color-fill-0);
          }
          
          .components-transfer-demo-selected-item:hover .remove-icon {
            visibility: visible !important;
          }
          `}
        </style>
        <Transfer
          style={{ width: "100%" }}
          dataSource={allUsersForAdminSelection}
          value={selectedUserIdsInTransfer}
          onChange={(values) =>
            setSelectedUserIdsInTransfer(values as string[])
          }
          filter={customFilter}
          renderSelectedItem={renderSelectedItem}
          renderSourceItem={renderSourceItem}
          inputProps={{ placeholder: "搜索姓名、邮箱或部门" }}
          onSearch={(text) => setSearchText(text)}
          loading={transferLoading}
          emptyContent={{
            left: "暂无可选用户",
            right: "暂无已选用户",
          }}
        />
      </Modal>
    </div>
  );
};

export default SettingsPage;
