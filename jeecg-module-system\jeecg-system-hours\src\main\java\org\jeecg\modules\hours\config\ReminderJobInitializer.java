package org.jeecg.modules.hours.config;

import java.util.List;

import org.jeecg.modules.hours.entity.FsSysReminderConfig;
import org.jeecg.modules.hours.service.IFsSysReminderConfigService;
import org.jeecg.modules.hours.service.IReminderScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 提醒任务初始化器
 * 应用启动时自动加载已启用的提醒配置并启动对应的定时任务
 * 在dev环境中不会启动，避免影响开发调试
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Slf4j
@Component
@Profile("!dev") // 排除dev环境
@ConditionalOnProperty(name = "reminder.job.enabled", havingValue = "true", matchIfMissing = true) // 可通过配置控制
public class ReminderJobInitializer implements ApplicationRunner {

    @Autowired
    private IFsSysReminderConfigService reminderConfigService;
    
    @Autowired
    private IReminderScheduleService reminderScheduleService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("=== 开始初始化工时提醒定时任务 ===");
        
        try {
            // 查询所有启用状态的提醒配置
            List<FsSysReminderConfig> enabledConfigs = reminderConfigService.list(
                    reminderConfigService.lambdaQuery()
                            .eq(FsSysReminderConfig::getStatus, "启用")
                            .eq(FsSysReminderConfig::getDelFlag, 0)
                            .getWrapper()
            );
            
            if (enabledConfigs.isEmpty()) {
                log.info("没有找到启用状态的提醒配置");
                return;
            }
            
            log.info("找到 {} 个启用状态的提醒配置，开始启动定时任务", enabledConfigs.size());
            
            int successCount = 0;
            int failCount = 0;
            
            // 为每个启用的配置启动定时任务
            for (FsSysReminderConfig config : enabledConfigs) {
                try {
                    log.info("启动定时任务，配置ID: {}, 项目: {}, 类型: {}", 
                            config.getId(), config.getProjectKey(), config.getReminderType());
                    
                    boolean result = reminderScheduleService.startReminderJob(config);
                    if (result) {
                        successCount++;
                        log.info("成功启动定时任务，配置ID: {}", config.getId());
                    } else {
                        failCount++;
                        log.error("启动定时任务失败，配置ID: {}", config.getId());
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("启动定时任务时发生异常，配置ID: {}", config.getId(), e);
                }
            }
            
            log.info("=== 工时提醒定时任务初始化完成，成功: {}, 失败: {} ===", successCount, failCount);
            
        } catch (Exception e) {
            log.error("初始化工时提醒定时任务时发生异常", e);
        }
    }
}
