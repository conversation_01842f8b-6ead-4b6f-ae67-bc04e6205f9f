package org.jeecg.modules.hours.mapper;

import org.jeecg.modules.hours.entity.FsUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2024-06-17
 * @Version: V1.0
 */
public interface FsUserMapper extends BaseMapper<FsUser> {
    
    /**
     * 获取指定项目下所有不是管理员角色的活跃用户
     * 
     * @param projectKey 项目Key
     * @param adminRoleCode 管理员角色编码
     * @return 用户列表
     */
    List<FsUser> getNonAdminActiveUsersByProject(@Param("projectKey") String projectKey, 
                                                @Param("adminRoleCode") String adminRoleCode);
    
    FsUser getAdminUserLimit1(String projectKey,String roleId);
    
    List<FsUser> getLeadersByUnionId(@Param("unionId")String unionId);
    
} 