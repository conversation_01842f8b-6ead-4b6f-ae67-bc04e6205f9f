import React, { useState, useEffect, useRef } from 'react';
import { Select } from '@douyinfe/semi-ui';
import { getDictItems } from '../../services/api/dict';

// 全局字典数据缓存
const globalDictCache: Record<string, DictItem[]> = {};

export interface DictItem {
  value: string;
  text: string;
  title?: string;
  disabled?: boolean;
}

export interface ApiResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  result: T;
}

export interface DictSelectProps {
  /**
   * 字典代码
   */
  dictCode: string;
  
  /**
   * 选择框的值
   */
  value?: string | string[];
  
  /**
   * 值变化时的回调函数
   */
  onChange?: (value: string | string[]) => void;
  
  /**
   * 是否禁用
   */
  disabled?: boolean;
  
  /**
   * 占位符文本
   */
  placeholder?: string;
  
  /**
   * 是否支持多选
   */
  multiple?: boolean;
  
  /**
   * 是否显示清除按钮
   */
  showClear?: boolean;
  
  /**
   * 选择框样式
   */
  style?: React.CSSProperties;
  
  /**
   * 是否显示全选选项（仅在multiple为true时有效）
   */
  showSelectAll?: boolean;
  
  /**
   * 加载完成后的回调
   */
  onLoadFinish?: (items: DictItem[]) => void;
  
  /**
   * 是否强制刷新缓存
   */
  forceRefresh?: boolean;
}

/**
 * 数据字典选择组件
 * 
 * 用于从后端获取数据字典项并渲染成下拉选择框
 * 
 * @example
 * ```tsx
 * // 基本用法
 * <DictSelect dictCode="project_status" />
 * 
 * // 带默认值
 * <DictSelect dictCode="project_status" value="AO" />
 * 
 * // 多选
 * <DictSelect dictCode="project_status" multiple />
 * 
 * // 禁用状态
 * <DictSelect dictCode="project_status" disabled />
 * ```
 */
const DictSelect: React.FC<DictSelectProps> = ({
  dictCode,
  value,
  onChange,
  disabled = false,
  placeholder = '请选择',
  multiple = false,
  showClear = true,
  style,
  showSelectAll = false,
  onLoadFinish,
  forceRefresh = false
}) => {
  const [loading, setLoading] = useState(false);
  const [dictItems, setDictItems] = useState<DictItem[]>([]);
  
  // 使用 useRef 保存回调函数，避免因回调变化导致的重复请求
  const onLoadFinishRef = useRef(onLoadFinish);
  
  // 更新回调引用
  useEffect(() => {
    onLoadFinishRef.current = onLoadFinish;
  }, [onLoadFinish]);

  // 获取字典数据
  useEffect(() => {
    // 如果已经有缓存数据且不强制刷新，直接使用缓存
    if (!forceRefresh && globalDictCache[dictCode]) {
      setDictItems(globalDictCache[dictCode]);
      
      // 调用加载完成回调
      if (onLoadFinishRef.current) {
        onLoadFinishRef.current(globalDictCache[dictCode]);
      }
      return;
    }
    
    const fetchDictItems = async () => {
      setLoading(true);
      try {
        // 使用 forceRefresh 参数调用 getDictItems
        const response = await getDictItems(dictCode, forceRefresh) as ApiResponse<DictItem[]>;
        if (response.success && response.result) {
          const items = response.result.map((item: DictItem) => ({
            value: item.value,
            text: item.text,
            title: item.text
          }));
          
          // 更新全局缓存
          globalDictCache[dictCode] = items;
          
          setDictItems(items);
          
          // 调用加载完成回调
          if (onLoadFinishRef.current) {
            onLoadFinishRef.current(items);
          }
        }
      } catch (error) {
        console.error(`获取字典数据失败: ${dictCode}`, error);
      } finally {
        setLoading(false);
      }
    };

    fetchDictItems();
  }, [dictCode, forceRefresh]); // 只依赖 dictCode 和 forceRefresh，不依赖 onLoadFinish

  return (
    <Select
      value={value}
      onChange={onChange}
      disabled={disabled}
      placeholder={placeholder}
      loading={loading}
      multiple={multiple}
      showClear={showClear}
      style={{ width: '100%', ...style }}
      filter
      optionList={dictItems.map(item => ({
        value: item.value,
        label: item.text,
        disabled: item.disabled
      }))}
    />
  );
};

/**
 * 预加载字典数据
 * @param dictCodes 字典代码数组
 * @returns Promise
 */
export const preloadDictItems = async (dictCodes: string[]) => {
  const promises = dictCodes.map(dictCode => getDictItems(dictCode));
  await Promise.all(promises);
};

export default DictSelect; 