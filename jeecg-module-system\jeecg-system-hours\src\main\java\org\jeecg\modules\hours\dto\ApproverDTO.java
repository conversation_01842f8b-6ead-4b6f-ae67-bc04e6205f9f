package org.jeecg.modules.hours.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

@Data
@ApiModel(value = "审批人DTO", description = "审批人DTO")
public class ApproverDTO implements Serializable {
    @ApiModelProperty(value = "审批人ID")
    private String userId;
    @ApiModelProperty(value = "审批人姓名")
    private String userName;
    @ApiModelProperty(value = "审批状态(0:待审批,1:已通过,2:已拒绝,3:已移交)")
    private Integer status;
    @ApiModelProperty(value = "审批状态名称")
    private String statusName;
    @ApiModelProperty(value = "更新时间")
    private String updatedAt;
} 