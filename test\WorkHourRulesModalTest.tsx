import React, { useState } from 'react';
import { Button, Card, Typography, Space } from '@douyinfe/semi-ui';
import WorkHourRulesModal from '../src/features/board_web_gw38j1/components/WorkHourRulesModal';

const { Title, Text } = Typography;

/**
 * 工时登记规则说明模态窗测试页面
 * 
 * 测试功能：
 * 1. 模态窗的显示和隐藏
 * 2. 富文本内容的加载和渲染
 * 3. 飞书富文本编辑器的集成
 * 4. 错误处理和默认内容显示
 */
const WorkHourRulesModalTest: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);

  const handleOpenModal = () => {
    console.log('打开工时登记规则说明模态窗');
    setModalVisible(true);
  };

  const handleCloseModal = () => {
    console.log('关闭工时登记规则说明模态窗');
    setModalVisible(false);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>工时登记规则说明模态窗测试</Title>
        
        <div style={{ marginBottom: '24px' }}>
          <Text>
            此测试页面用于验证工时登记规则说明模态窗的功能，包括：
          </Text>
          <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
            <li>模态窗的正常显示和关闭</li>
            <li>从后端API加载富文本内容</li>
            <li>飞书富文本编辑器的正确渲染</li>
            <li>加载状态的显示</li>
            <li>错误处理和默认内容的显示</li>
          </ul>
        </div>

        <Space>
          <Button 
            type="primary" 
            onClick={handleOpenModal}
          >
            打开工时登记规则说明
          </Button>
          
          <Button 
            type="secondary" 
            onClick={() => {
              console.log('模拟工时规则提示中的超链接点击');
              setModalVisible(true);
            }}
          >
            模拟工时提示中的超链接
          </Button>
        </Space>

        <div style={{ 
          marginTop: '24px', 
          padding: '16px', 
          backgroundColor: 'rgba(var(--semi-blue-0), .2)',
          borderRadius: '6px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <Text size="small">
            当前工作日期可登记工时8.00小时，剩余可登记工时5.00小时
          </Text>
          <Button
            type="tertiary"
            theme="borderless"
            size="small"
            style={{ 
              padding: '0 4px',
              height: 'auto',
              fontSize: '12px',
              color: 'var(--semi-color-primary)',
              textDecoration: 'underline'
            }}
            onClick={handleOpenModal}
          >
            工时登记规则
          </Button>
        </div>

        <div style={{ marginTop: '24px' }}>
          <Title heading={5}>测试说明</Title>
          <Text>
            1. 点击上方按钮打开模态窗<br/>
            2. 检查模态窗是否正确显示<br/>
            3. 验证富文本内容是否正确加载<br/>
            4. 检查加载状态和错误处理<br/>
            5. 确认模态窗可以正常关闭
          </Text>
        </div>

        <div style={{ marginTop: '16px' }}>
          <Title heading={5}>API接口</Title>
          <Text>
            模态窗会调用以下API获取富文本内容：<br/>
            <code>GET /hours/sys/config/rules/richtext</code>
          </Text>
        </div>

        <div style={{ marginTop: '16px' }}>
          <Title heading={5}>预期行为</Title>
          <Text>
            - 如果API返回成功，显示配置的富文本内容<br/>
            - 如果API失败或返回空内容，显示默认的规则说明<br/>
            - 在飞书环境中，使用飞书富文本编辑器渲染<br/>
            - 在非飞书环境中，显示纯文本内容
          </Text>
        </div>
      </Card>

      {/* 工时登记规则说明模态窗 */}
      <WorkHourRulesModal
        visible={modalVisible}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default WorkHourRulesModalTest;
