package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 员工工时周期汇总报表响应
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "EmployeeHoursReportResponse", description = "员工工时周期汇总报表响应")
public class EmployeeHoursReportResponse {

    @ApiModelProperty(value = "报表数据")
    private List<EmployeeHoursReportData> data;

    @ApiModelProperty(value = "时间周期列表")
    private List<String> periods;

    @ApiModelProperty(value = "总记录数")
    private Long total;

    @ApiModelProperty(value = "合计数据")
    private EmployeeHoursSummary summary;

    /**
     * 员工工时报表数据
     */
    @Data
    @ApiModel(value = "EmployeeHoursReportData", description = "员工工时报表数据")
    public static class EmployeeHoursReportData {

        @ApiModelProperty(value = "员工姓名")
        private String employeeName;

        @ApiModelProperty(value = "员工ID")
        private String employeeId;

        @ApiModelProperty(value = "时间周期数据")
        private Map<String, Double> periods;

        @ApiModelProperty(value = "小计(小时)")
        private Double totalHours;

        @ApiModelProperty(value = "折算天数(天)")
        private Double totalDays;
    }

    /**
     * 员工工时合计数据
     */
    @Data
    @ApiModel(value = "EmployeeHoursSummary", description = "员工工时合计数据")
    public static class EmployeeHoursSummary {

        @ApiModelProperty(value = "各时间周期合计")
        private Map<String, Double> periods;

        @ApiModelProperty(value = "总工时")
        private Double grandTotalHours;

        @ApiModelProperty(value = "总天数")
        private Double grandTotalDays;
    }
}
