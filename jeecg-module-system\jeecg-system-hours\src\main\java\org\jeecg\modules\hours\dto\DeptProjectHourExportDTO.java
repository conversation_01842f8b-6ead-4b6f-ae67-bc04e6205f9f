package org.jeecg.modules.hours.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 部门项目工时统计报表导出参数DTO
 */
@Data
@ApiModel(value = "DeptProjectHourExportDTO", description = "部门项目工时统计报表导出参数")
public class DeptProjectHourExportDTO {

    @ApiModelProperty(value = "开始日期（格式：yyyy-MM-dd）", required = true)
    @NotBlank(message = "开始日期不能为空")
    private String startDate;

    @ApiModelProperty(value = "结束日期（格式：yyyy-MM-dd）", required = true)
    @NotBlank(message = "结束日期不能为空")
    private String endDate;

    @ApiModelProperty(value = "统计周期", required = false)
    private String statPeriod = "daily";

    @ApiModelProperty(value = "部门ID列表（逗号分隔）", required = false)
    private String departmentIds;

    @ApiModelProperty(value = "项目ID列表（逗号分隔）", required = false)
    private String projectIds;

    @ApiModelProperty(value = "假日统计：1-统计假日，0-不统计假日", required = false)
    private Integer isHoliday;
}