package org.jeecg.modules.hours.service;

import java.util.Map;

/**
 * @Description: 飞书认证服务接口
 * @Author: jeecg-boot
 * @Date: 2024-06-17
 * @Version: V1.0
 */
public interface IFeishuAuthService {
    
    /**
     * 获取飞书认证信息
     * @return 认证信息Map，包含token和userKey
     */
    Map<String, String> getAuthInfo();
    
    /**
     * 获取飞书访问令牌
     * @return 访问令牌
     */
    String getAccessToken();
    
    /**
     * 获取当前用户的飞书标识
     * 优先从请求头token中获取，如果获取失败则从缓存中获取
     * @return 飞书用户标识，获取失败返回null
     */
    String getCurrentUserKey();
    
    /**
     * 从token中解析用户标识
     * @param token JWT token
     * @return 用户标识，解析失败返回null
     */
    String parseUserKeyFromToken(String token);
    
    /**
     * 刷新飞书访问令牌
     * @param refreshToken 刷新令牌
     * @return 是否刷新成功
     */
    boolean refreshToken(String refreshToken);

    /**
     * 设置飞书认证信息
     * @param token 访问令牌
     * @param refreshToken 刷新令牌
     * @param expireTime 过期时间(秒)
     * @param refreshExpireTime 刷新令牌过期时间(秒)
     * @param userKey 用户标识
     */
    void setAuthInfo(String token, String refreshToken, Integer expireTime, 
                    Integer refreshExpireTime, String userKey);
} 