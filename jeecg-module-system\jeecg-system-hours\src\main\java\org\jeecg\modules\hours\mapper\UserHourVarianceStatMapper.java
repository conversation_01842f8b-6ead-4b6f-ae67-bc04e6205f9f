package org.jeecg.modules.hours.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.dto.UserVarianceDataDTO;
import org.jeecg.modules.hours.entity.UserHourVarianceStat;

import java.util.List;

/**
 * @Description: 人员工时差值统计Mapper
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Mapper
public interface UserHourVarianceStatMapper extends BaseMapper<UserHourVarianceStat> {

    /**
     * 计算指定年月的人员工时差值数据
     * @param year 年份
     * @param month 月份
     * @param includeHoliday 是否包含假日
     * @return 人员差值数据列表
     */
    List<UserVarianceDataDTO> calculateMonthlyVarianceData(@Param("year") int year, 
                                                           @Param("month") int month, 
                                                           @Param("includeHoliday") boolean includeHoliday);
}