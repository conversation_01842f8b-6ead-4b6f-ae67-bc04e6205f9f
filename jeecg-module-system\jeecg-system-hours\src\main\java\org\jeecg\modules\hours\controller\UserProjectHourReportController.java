package org.jeecg.modules.hours.controller;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.hours.entity.UserProjectHourStatistics;
import org.jeecg.modules.hours.service.IUserProjectHourStatisticsService;
import org.jeecg.modules.hours.service.IUserProjectHourExportService;
import org.jeecg.modules.hours.vo.UserProjectHourReportResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 人员项目工时统计报表
 * @Author: jeecg-boot
 * @Date: 2025-07-08
 * @Version: V1.0
 */
@Api(tags = "人员项目工时统计报表")
@RestController
@RequestMapping("/hapi/hours/userProjectHourReport")
@Slf4j
public class UserProjectHourReportController extends JeecgController<UserProjectHourStatistics, IUserProjectHourStatisticsService> {

    @Autowired
    private IUserProjectHourStatisticsService userProjectHourStatisticsService;
    
    @Autowired
    private IUserProjectHourExportService userProjectHourExportService;

    /**
     * 查询人员项目工时统计报表
     */
    @AutoLog(value = "人员项目工时统计报表-查询")
    @ApiOperation(value = "人员项目工时统计报表-查询", notes = "人员项目工时统计报表-查询")
    @GetMapping("/query")
    public Result<UserProjectHourReportResult> queryReport(
            @ApiParam(value = "开始日期", required = true) @RequestParam String startDate,
            @ApiParam(value = "结束日期", required = true) @RequestParam String endDate,
            @ApiParam(value = "统计周期", required = false, defaultValue = "daily") @RequestParam(defaultValue = "daily") String statPeriod,
            @ApiParam(value = "用户ID列表") @RequestParam(required = false) String userIds,
            @ApiParam(value = "项目ID列表") @RequestParam(required = false) String projectIds,
            @ApiParam(value = "假日统计：1-统计假日，0-不统计假日") @RequestParam(required = false) Integer isHoliday) {

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);

            // 解析用户ID列表
            List<String> userIdList = null;
            if (userIds != null && !userIds.trim().isEmpty()) {
                userIdList = Arrays.asList(userIds.split(","));
            }

            // 解析项目ID列表
            List<String> projectIdList = null;
            if (projectIds != null && !projectIds.trim().isEmpty()) {
                projectIdList = Arrays.asList(projectIds.split(","));
            }

            UserProjectHourReportResult result = userProjectHourStatisticsService.getUserProjectHourReport(
                    start, end, statPeriod, userIdList, projectIdList, isHoliday);

            return Result.OK(result);

        } catch (ParseException e) {
            log.error("日期格式错误", e);
            return Result.error("日期格式错误，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("查询人员项目工时统计报表失败", e);
            return Result.error("查询报表失败：" + e.getMessage());
        }
    }

    /**
     * 导出人员项目工时统计报表
     */
    @AutoLog(value = "人员项目工时统计报表-导出")
    @ApiOperation(value = "人员项目工时统计报表-导出", notes = "人员项目工时统计报表-导出")
    @GetMapping("/export")
    public void exportReport(
            HttpServletRequest request,
            HttpServletResponse response,
            @ApiParam(value = "开始日期", required = true) @RequestParam String startDate,
            @ApiParam(value = "结束日期", required = true) @RequestParam String endDate,
            @ApiParam(value = "统计周期", required = false, defaultValue = "daily") @RequestParam(defaultValue = "daily") String statPeriod,
            @ApiParam(value = "用户ID列表") @RequestParam(required = false) String userIds,
            @ApiParam(value = "项目ID列表") @RequestParam(required = false) String projectIds,
            @ApiParam(value = "假日统计：1-统计假日，0-不统计假日") @RequestParam(required = false) Integer isHoliday) {

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);

            // 解析用户ID列表
            List<String> userIdList = null;
            if (userIds != null && !userIds.trim().isEmpty()) {
                userIdList = Arrays.asList(userIds.split(","));
            }

            // 解析项目ID列表
            List<String> projectIdList = null;
            if (projectIds != null && !projectIds.trim().isEmpty()) {
                projectIdList = Arrays.asList(projectIds.split(","));
            }

            // 调用导出服务
            userProjectHourExportService.exportUserProjectHourReportToExcel(
                    start, end, statPeriod, userIdList, projectIdList, isHoliday, response);

        } catch (ParseException e) {
            log.error("日期格式错误", e);
            try {
                response.getWriter().write("日期格式错误");
            } catch (IOException ex) {
                log.error("写入响应失败", ex);
            }
        } catch (Exception e) {
            log.error("导出人员项目工时统计报表失败", e);
            try {
                response.getWriter().write("导出失败：" + e.getMessage());
            } catch (IOException ex) {
                log.error("写入响应失败", ex);
            }
        }
    }

    /**
     * 手动执行人员项目工时统计
     */
    @AutoLog(value = "人员项目工时统计报表-手动统计")
    @ApiOperation(value = "人员项目工时统计报表-手动统计", notes = "人员项目工时统计报表-手动统计")
    @PostMapping("/calculate")
    public Result<String> calculateStatistics(
            @ApiParam(value = "统计日期", required = true) @RequestParam String statDate,
            @ApiParam(value = "统计周期", required = false, defaultValue = "daily") @RequestParam(defaultValue = "daily") String statPeriod) {

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = sdf.parse(statDate);

            int processed = userProjectHourStatisticsService.calculateHourStatistics(date, statPeriod);

            return Result.OK("统计完成，处理记录数：" + processed);

        } catch (ParseException e) {
            log.error("日期格式错误", e);
            return Result.error("日期格式错误，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("执行人员项目工时统计失败", e);
            return Result.error("统计失败：" + e.getMessage());
        }
    }

    /**
     * 批量执行人员项目工时统计
     */
    @AutoLog(value = "人员项目工时统计报表-批量统计")
    @ApiOperation(value = "人员项目工时统计报表-批量统计", notes = "人员项目工时统计报表-批量统计")
    @PostMapping("/batchCalculate")
    public Result<String> batchCalculateStatistics(
            @ApiParam(value = "开始日期", required = true) @RequestParam String startDate,
            @ApiParam(value = "结束日期", required = true) @RequestParam String endDate,
            @ApiParam(value = "统计周期", required = false, defaultValue = "daily") @RequestParam(defaultValue = "daily") String statPeriod) {

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);

            int totalProcessed = userProjectHourStatisticsService.batchCalculateHourStatistics(start, end, statPeriod);

            return Result.OK("批量统计完成，总处理记录数：" + totalProcessed);

        } catch (ParseException e) {
            log.error("日期格式错误", e);
            return Result.error("日期格式错误，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("批量执行人员项目工时统计失败", e);
            return Result.error("批量统计失败：" + e.getMessage());
        }
    }

    /**
     * 重新计算人员项目工时统计
     */
    @AutoLog(value = "人员项目工时统计报表-重新计算")
    @ApiOperation(value = "人员项目工时统计报表-重新计算", notes = "人员项目工时统计报表-重新计算")
    @PostMapping("/recalculate")
    public Result<String> recalculateStatistics(
            @ApiParam(value = "统计日期", required = true) @RequestParam String statDate,
            @ApiParam(value = "统计周期", required = false, defaultValue = "daily") @RequestParam(defaultValue = "daily") String statPeriod) {

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = sdf.parse(statDate);

            int processed = userProjectHourStatisticsService.recalculateHourStatistics(date, statPeriod);

            return Result.OK("重新计算完成，处理记录数：" + processed);

        } catch (ParseException e) {
            log.error("日期格式错误", e);
            return Result.error("日期格式错误，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("重新计算人员项目工时统计失败", e);
            return Result.error("重新计算失败：" + e.getMessage());
        }
    }
}