package org.jeecg.modules.hours.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.hours.entity.DeptProjectHourStatistics;
import org.jeecg.modules.hours.vo.DeptProjectHourReportResult;
import org.jeecg.modules.hours.vo.DeptProjectHourReportVO;
import org.jeecg.modules.hours.vo.ProjectColumnVO;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 部门项目工时统计表
 * @Author: jeecg-boot
 * @Date: 2025-07-07
 * @Version: V1.0
 */
public interface DeptProjectHourStatisticsMapper extends BaseMapper<DeptProjectHourStatistics> {

    /**
     * 获取指定时间范围内的项目列表（用于动态列）
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statPeriod 统计周期
     * @param departmentIds 部门ID列表（可选）
     * @param projectIds 项目ID列表（可选）
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @return 项目列信息
     */
    List<ProjectColumnVO> getProjectColumns(@Param("startDate") Date startDate,
                                          @Param("endDate") Date endDate,
                                          @Param("statPeriod") String statPeriod,
                                          @Param("departmentIds") List<String> departmentIds,
                                          @Param("projectIds") List<String> projectIds,
                                          @Param("isHoliday") Integer isHoliday);

    /**
     * 获取部门项目工时统计报表数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statPeriod 统计周期
     * @param departmentIds 部门ID列表（可选）
     * @param projectIds 项目ID列表（可选）
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @return 部门工时统计数据
     */
    List<DeptProjectHourReportVO> getDeptProjectHourReport(@Param("startDate") Date startDate,
                                                          @Param("endDate") Date endDate,
                                                          @Param("statPeriod") String statPeriod,
                                                          @Param("departmentIds") List<String> departmentIds,
                                                          @Param("projectIds") List<String> projectIds,
                                                          @Param("isHoliday") Integer isHoliday);

    /**
     * 获取总计行数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statPeriod 统计周期
     * @param departmentIds 部门ID列表（可选）
     * @param projectIds 项目ID列表（可选）
     * @param isHoliday 假日统计：1-统计假日，0-不统计假日，null-不限制
     * @return 总计数据列表（按项目分组）
     */
    List<DeptProjectHourReportVO> getTotalRow(@Param("startDate") Date startDate,
                                            @Param("endDate") Date endDate,
                                            @Param("statPeriod") String statPeriod,
                                            @Param("departmentIds") List<String> departmentIds,
                                            @Param("projectIds") List<String> projectIds,
                                            @Param("isHoliday") Integer isHoliday);

    /**
     * 批量插入或更新统计数据
     *
     * @param statisticsList 统计数据列表
     */
    void batchInsertOrUpdate(@Param("list") List<DeptProjectHourStatistics> statisticsList);

    /**
     * 删除指定日期的统计数据
     *
     * @param statDate 统计日期
     * @param statPeriod 统计周期
     */
    void deleteByStatDate(@Param("statDate") Date statDate, @Param("statPeriod") String statPeriod);

    /**
     * 按条件统计汇总数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statPeriod 统计周期
     * @param departmentIds 部门ID列表（可选）
     * @param projectIds 项目ID列表（可选）
     * @return 汇总统计结果
     */
    DeptProjectHourReportResult.ReportSummaryVO getSummaryData(@Param("startDate") Date startDate,
                                                               @Param("endDate") Date endDate,
                                                               @Param("statPeriod") String statPeriod,
                                                               @Param("departmentIds") List<String> departmentIds,
                                                               @Param("projectIds") List<String> projectIds);
}