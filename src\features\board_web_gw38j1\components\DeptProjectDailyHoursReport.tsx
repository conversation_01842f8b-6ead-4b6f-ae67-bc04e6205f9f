import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Table,
  Button,
  Space,
  Toast,
  Card,
  Select,
  Spin,
  Typography,
  Row,
  Col,
  TreeSelect,
  DatePicker
} from '@douyinfe/semi-ui';
import { IconDownload, IconRefresh2 } from '@douyinfe/semi-icons';
import type { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import {
  getAllDepartments,
  getDeptProjectDailyHourReport,
  exportDeptProjectDailyHourReport,
  type Department,
  type DeptProjectDailyHourReportParams,
  type DeptProjectDailyHourReportResult,
  type DeptProjectDailyHourData,
  type DayColumn
} from '../../../api/report';

const { Text } = Typography;

interface ReportFilters {
  yearMonth: string;
  departmentIds: string[];
  isHoliday: number | undefined;
  projectKeyword: string;
}

const DeptProjectDailyHoursReport: React.FC = () => {
  const [reportData, setReportData] = useState<DeptProjectDailyHourReportResult | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [filters, setFilters] = useState<ReportFilters>({
    yearMonth: '',
    departmentIds: [],
    isHoliday: undefined,
    projectKeyword: ''
  });

  // 基础数据
  const [departments, setDepartments] = useState<Department[]>([]);
  const [dataLoading, setDataLoading] = useState(false);

  useEffect(() => {
    // 设置默认月份为当前月
    const now = new Date();
    const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

    setFilters(prev => ({
      ...prev,
      yearMonth: currentMonth
    }));

    // 加载基础数据
    loadInitialData();
  }, []);

  // 加载基础数据
  const loadInitialData = async () => {
    setDataLoading(true);
    try {
      const departmentData = await getAllDepartments();
      setDepartments(departmentData);
    } catch (error) {
      console.error('加载基础数据失败:', error);
      Toast.error('加载基础数据失败');
    } finally {
      setDataLoading(false);
    }
  };

  // 递归获取部门树下所有部门ID
  const getAllDepartmentIds = (departments: Department[]): string[] => {
    let allIds: string[] = [];
    departments.forEach(dept => {
      allIds.push(dept.id);
      if (dept.children && dept.children.length > 0) {
        allIds = [...allIds, ...getAllDepartmentIds(dept.children)];
      }
    });
    return allIds;
  };

  // 递归获取所有选中部门的完整ID列表（包括父级选中时的所有子级）
  const getAllSelectedDepartmentIds = (departments: Department[], selectedIds: string[]): string[] => {
    let allIds: string[] = [];
    
    departments.forEach(dept => {
      if (selectedIds.includes(dept.id)) {
        // 添加当前部门ID
        allIds.push(dept.id);
        
        // 如果有子部门，递归添加所有子部门ID
        if (dept.children && dept.children.length > 0) {
          const allChildIds = getAllDepartmentIds(dept.children);
          allIds = [...allIds, ...allChildIds];
        }
      } else if (dept.children && dept.children.length > 0) {
        // 如果父级未被选中，但可能有子级被选中
        allIds = [...allIds, ...getAllSelectedDepartmentIds(dept.children, selectedIds)];
      }
    });
    
    return [...new Set(allIds)]; // 去重
  };

  // 将部门数据转换为TreeSelect所需的格式
  const transformDepartmentData = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      label: dept.name,
      value: dept.id,
      key: dept.id,
      children: dept.children && dept.children.length > 0
        ? transformDepartmentData(dept.children)
        : undefined
    }));
  };

  // 获取报表数据
  const fetchReportData = useCallback(async () => {
    if (!filters.yearMonth) {
      Toast.warning('请选择查询月份');
      return;
    }

    if (loading) {
      return;
    }

    setLoading(true);
    try {
      // 将用户选择的部门ID展开为所有实际需要查询的部门ID
      const expandedDepartmentIds = filters.departmentIds.length > 0 
        ? getAllSelectedDepartmentIds(departments, filters.departmentIds)
        : [];

      const params: DeptProjectDailyHourReportParams = {
        yearMonth: filters.yearMonth,
        ...(expandedDepartmentIds.length > 0 && { departmentIds: expandedDepartmentIds.join(',') }),
        ...(filters.isHoliday !== undefined && { isHoliday: filters.isHoliday }),
        ...(filters.projectKeyword && { projectKeyword: filters.projectKeyword })
      };

      const result = await getDeptProjectDailyHourReport(params);
      setReportData(result);
      Toast.success('报表数据加载成功');
    } catch (error) {
      console.error('获取报表数据失败:', error);
      Toast.error('获取报表数据失败');
    } finally {
      setLoading(false);
    }
  }, [filters, loading]);

  // 导出报表
  const handleExport = useCallback(async () => {
    if (!reportData) {
      Toast.warning('请先查询报表数据');
      return;
    }

    try {
      // 将用户选择的部门ID展开为所有实际需要查询的部门ID
      const expandedDepartmentIds = filters.departmentIds.length > 0 
        ? getAllSelectedDepartmentIds(departments, filters.departmentIds)
        : [];

      const params: DeptProjectDailyHourReportParams = {
        yearMonth: filters.yearMonth,
        ...(expandedDepartmentIds.length > 0 && { departmentIds: expandedDepartmentIds.join(',') }),
        ...(filters.isHoliday !== undefined && { isHoliday: filters.isHoliday }),
        ...(filters.projectKeyword && { projectKeyword: filters.projectKeyword })
      };

      await exportDeptProjectDailyHourReport(params);
      Toast.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      Toast.error('导出失败');
    }
  }, [filters, reportData]);

  // 构建表格列
  const buildTableColumns = useMemo(() => {
    if (!reportData?.dayColumns) return [];

    const columns: ColumnProps<DeptProjectDailyHourData>[] = [
      {
        title: '部门',
        dataIndex: 'departmentName',
        key: 'departmentName',
        fixed: 'left',
        width: 150,
        render: (text: string, record: DeptProjectDailyHourData) => {
          return record.isTotalRow ? <Text strong>{text}</Text> : text;
        },
      },
      {
        title: '项目',
        dataIndex: 'projectName',
        key: 'projectName',
        fixed: 'left',
        width: 200,
        render: (text: string, record: DeptProjectDailyHourData) => {
          return record.isTotalRow ? <Text strong>{text}</Text> : (
            <div>
              <div>{text}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {record.projectCode}
              </div>
            </div>
          );
        },
      },
    ];

    // 添加动态日期列
    reportData.dayColumns.forEach((dayColumn: DayColumn) => {
      columns.push({
        title: (
          <div style={{ textAlign: 'center' }}>
            <div>{dayColumn.dayName}</div>
            {dayColumn.isHoliday && (
              <div style={{ fontSize: '10px', color: '#ff4d4f' }}>假</div>
            )}
          </div>
        ),
        dataIndex: ['dailyHours', dayColumn.columnKey],
        key: dayColumn.columnKey,
        width: 80,
        align: 'center',
        render: (hours: number, record: DeptProjectDailyHourData) => {
          const value = hours || 0;
          return record.isTotalRow ? (
            <Text strong>{value.toFixed(1)}</Text>
          ) : (
            value.toFixed(1)
          );
        },
      });
    });

    // 添加小计列
    columns.push({
      title: '小计',
      dataIndex: 'totalHours',
      key: 'totalHours',
      fixed: 'right',
      width: 100,
      align: 'center',
      render: (hours: number, record: DeptProjectDailyHourData) => {
        const value = hours || 0;
        return record.isTotalRow ? (
          <Text strong style={{ color: '#3370ff' }}>{value.toFixed(1)}</Text>
        ) : (
          <Text strong>{value.toFixed(1)}</Text>
        );
      },
    });

    return columns;
  }, [reportData]);

  // 准备表格数据
  const tableData = useMemo(() => {
    if (!reportData) return [];

    const data = [...reportData.deptProjectHourData];
    if (reportData.totalRow) {
      data.push(reportData.totalRow);
    }

    return data.map((item, index) => ({
      ...item,
      key: item.isTotalRow ? 'total' : `${item.departmentId}_${item.projectId}`,
    }));
  }, [reportData]);

  // 处理月份选择
  const handleMonthChange = (date: Date | null) => {
    if (date) {
      const yearMonth = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      setFilters(prev => ({ ...prev, yearMonth }));
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 筛选条件 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[12, 8]}>
          <Col span={6}>
            <Space vertical style={{ width: '100%' }}>
              <DatePicker
                type="month"
                value={filters.yearMonth ? new Date(filters.yearMonth + '-01') : undefined}
                onChange={handleMonthChange}
                style={{ width: '100%' }}
                placeholder="选择月份"
              />
            </Space>
          </Col>
          <Col span={5}>
            <Space vertical style={{ width: '100%' }}>
              <TreeSelect
                multiple
                value={filters.departmentIds}
                onChange={(value) => {
                  // 保存用户选择的原始部门ID
                  setFilters(prev => ({ ...prev, departmentIds: value }));
                }}
                style={{ width: '100%' }}
                placeholder="选择部门"
                loading={dataLoading}
                maxTagCount={1}
                showClear
                treeData={transformDepartmentData(departments)}
                searchable
                showSearchClear
                expandAll
                checkRelation="related"
              />
            </Space>
          </Col>
          {/*<Col span={4}>*/}
          {/*  <Space vertical style={{ width: '100%' }}>*/}
          {/*    <Text>项目关键字</Text>*/}
          {/*    <Select*/}
          {/*      value={filters.projectKeyword}*/}
          {/*      onChange={(value) => setFilters(prev => ({ ...prev, projectKeyword: value }))}*/}
          {/*      style={{ width: '100%' }}*/}
          {/*      placeholder="输入项目关键字"*/}
          {/*      showClear*/}
          {/*      filter*/}
          {/*    />*/}
          {/*  </Space>*/}
          {/*</Col>*/}
          <Col span={3}>
            <Space vertical style={{ width: '100%' }}>
              <Select
                value={filters.isHoliday}
                onChange={(value) => setFilters(prev => ({ ...prev, isHoliday: value }))}
                style={{ width: '100%' }}
                placeholder="请选择"
                showClear
              >
                <Select.Option value={1}>统计假日</Select.Option>
                <Select.Option value={0}>不统计假日</Select.Option>
              </Select>
            </Space>
          </Col>
          <Col span={9}>
            <Space>
              <Button
                theme="solid"
                type="primary"
                onClick={fetchReportData}
                loading={loading}
              >
                查询
              </Button>
              <Button
                type="secondary"
                onClick={handleExport}
                disabled={!reportData || loading}
                icon={<IconDownload />}
              >
                导出Excel
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 统计摘要 */}
      {/*{reportData?.summary && (*/}
      {/*  <Card style={{ marginBottom: '16px' }}>*/}
      {/*    <Row gutter={16}>*/}
      {/*      <Col span={4}>*/}
      {/*        <Text type="tertiary">统计月份</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{filters.yearMonth}</Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={4}>*/}
      {/*        <Text type="tertiary">总工时</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong style={{ color: '#1890ff', fontSize: '18px' }}>*/}
      {/*          {reportData.summary.totalHours?.toFixed(1) || 0}小时*/}
      {/*        </Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={3}>*/}
      {/*        <Text type="tertiary">部门数量</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.deptCount || 0}个</Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={3}>*/}
      {/*        <Text type="tertiary">项目数量</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.projectCount || 0}个</Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={3}>*/}
      {/*        <Text type="tertiary">参与人数</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.userCount || 0}人</Text>*/}
      {/*      </Col>*/}
      {/*      <Col span={3}>*/}
      {/*        <Text type="tertiary">统计天数</Text>*/}
      {/*        <br />*/}
      {/*        <Text strong>{reportData.summary.dayCount || 0}天</Text>*/}
      {/*      </Col>*/}
      {/*    </Row>*/}
      {/*  </Card>*/}
      {/*)}*/}

      {/* 报表表格 */}
      <Card>
        <Spin spinning={loading}>
          <Table
            columns={buildTableColumns}
            dataSource={tableData}
            pagination={false}
            bordered
            size="small"
            scroll={{ x: 'max-content' }}
            rowKey={(record) => record.key}
            rowClassName={(record) =>
              record.isTotalRow ? 'total-row' : ''
            }
            empty={
              <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
                <Text type="tertiary">暂无数据</Text>
              </div>
            }
          />
        </Spin>
      </Card>

      <style>
        {`
          .total-row {
            background-color: #f6f8fa !important;
            font-weight: bold;
          }
          .total-row td {
            border-top: 2px solid #d9d9d9 !important;
          }
        `}
      </style>
    </div>
  );
};

export default DeptProjectDailyHoursReport;
