package org.jeecg.modules.hours.service;

import org.jeecg.modules.hours.vo.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 报表分析服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface IReportAnalysisService {

    /**
     * 获取实例工时周期汇总报表
     */
    InstanceHoursReportResponse getInstanceHoursReport(ReportQueryParams params);

    /**
     * 获取员工工时周期汇总报表
     */
    EmployeeHoursReportResponse getEmployeeHoursReport(ReportQueryParams params);

    /**
     * 获取员工工时实例分布报表
     */
    EmployeeInstanceDistributionResponse getEmployeeInstanceDistributionReport(ReportQueryParams params);

    /**
     * 获取项目下的用户列表
     */
    List<UserVO> getProjectUsers(String projectKey);

    /**
     * 获取项目下的实例列表
     */
    List<InstanceVO> getProjectInstances(String projectKey);

    /**
     * 导出实例工时周期汇总报表
     */
    ModelAndView exportInstanceHoursReport(HttpServletRequest request, HttpServletResponse response, ReportQueryParams params);

    /**
     * 导出员工工时周期汇总报表
     */
    ModelAndView exportEmployeeHoursReport(HttpServletRequest request, HttpServletResponse response, ReportQueryParams params);

    /**
     * 导出员工工时实例分布报表
     */
    ModelAndView exportEmployeeInstanceDistributionReport(HttpServletRequest request, HttpServletResponse response, ReportQueryParams params);
}
