package org.jeecg.modules.hours.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 项目百分比VO
 *
 * <AUTHOR>
 * @date 2023-07-01
 */
@Data
@ApiModel(value = "项目百分比VO", description = "项目百分比VO")
public class ProjectPercentageVO {
    
    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    
    /**
     * 工作描述
     */
    @ApiModelProperty(value = "工作描述")
    private String description;
    
    /**
     * 明细列表
     */
    @ApiModelProperty(value = "明细列表")
    private List<DatePercentageVO> details;
} 