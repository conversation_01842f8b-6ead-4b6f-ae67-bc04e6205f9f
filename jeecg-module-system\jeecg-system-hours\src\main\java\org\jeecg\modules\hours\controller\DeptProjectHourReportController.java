package org.jeecg.modules.hours.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.hours.service.IDeptProjectHourStatisticsService;
import org.jeecg.modules.hours.service.IDeptProjectHourExportService;
import org.jeecg.modules.hours.vo.DeptProjectHourReportResult;
import org.jeecg.modules.hours.dto.DeptProjectHourExportDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * @Description: 部门项目工时统计报表
 * @Author: jeecg-boot
 * @Date: 2025-07-07
 * @Version: V1.0
 */
@Api(tags = "部门项目工时统计报表")
@RestController
@RequestMapping("/hapi/hours/report/dept-project")
@Slf4j
public class DeptProjectHourReportController {

    @Autowired
    private IDeptProjectHourStatisticsService deptProjectHourStatisticsService;
    
    @Autowired
    private IDeptProjectHourExportService deptProjectHourExportService;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 获取部门项目工时统计报表
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate   结束日期（格式：yyyy-MM-dd）
     * @param statPeriod 统计周期：daily-按日, weekly-按周, monthly-按月
     * @param departmentIds 部门ID列表（逗号分隔，可选）
     * @param projectIds 项目ID列表（逗号分隔，可选）
     * @return 部门项目工时统计报表数据
     */
    @AutoLog(value = "部门项目工时统计报表-查询")
    @ApiOperation(value = "获取部门项目工时统计报表", notes = "获取部门项目工时统计报表")
    @GetMapping(value = "/list")
    public Result<DeptProjectHourReportResult> getDeptProjectHourReport(
            @ApiParam(value = "开始日期（格式：yyyy-MM-dd）", required = true) @RequestParam String startDate,
            @ApiParam(value = "结束日期（格式：yyyy-MM-dd）", required = true) @RequestParam String endDate,
            @ApiParam(value = "统计周期", required = false) @RequestParam(defaultValue = "daily") String statPeriod,
            @ApiParam(value = "部门ID列表（逗号分隔）") @RequestParam(required = false) String departmentIds,
            @ApiParam(value = "项目ID列表（逗号分隔）") @RequestParam(required = false) String projectIds,
            @ApiParam(value = "假日统计：1-统计假日，0-不统计假日") @RequestParam(required = false) Integer isHoliday) {

        try {
            // 解析日期参数
            Date start = DATE_FORMAT.parse(startDate);
            Date end = DATE_FORMAT.parse(endDate);

            // 解析列表参数
            List<String> deptIdList = null;
            if (departmentIds != null && !departmentIds.trim().isEmpty()) {
                deptIdList = Arrays.asList(departmentIds.split(","));
            }

            List<String> projectIdList = null;
            if (projectIds != null && !projectIds.trim().isEmpty()) {
                projectIdList = Arrays.asList(projectIds.split(","));
            }

            // 获取报表数据
            DeptProjectHourReportResult result = deptProjectHourStatisticsService.getDeptProjectHourReport(
                    start, end, statPeriod, deptIdList, projectIdList, isHoliday);

            return Result.OK(result);

        } catch (ParseException e) {
            log.error("日期格式解析错误: startDate={}, endDate={}", startDate, endDate, e);
            return Result.error("日期格式错误，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("获取部门项目工时统计报表失败", e);
            return Result.error("获取报表数据失败: " + e.getMessage());
        }
    }

    /**
     * 导出部门项目工时统计报表Excel
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statPeriod 统计周期
     * @param departmentIds 部门ID列表
     * @param projectIds 项目ID列表
     * @param request   请求对象
     * @param response  响应对象
     */
    @AutoLog(value = "部门项目工时统计报表-导出")
    @ApiOperation(value = "导出部门项目工时统计报表Excel", notes = "导出部门项目工时统计报表Excel")
    @PostMapping(value = "/export")
    public void exportDeptProjectHourReport(
            @ApiParam(value = "导出参数") @RequestBody DeptProjectHourExportDTO exportDTO,
            HttpServletRequest request, HttpServletResponse response) {

        try {
            // 解析日期参数
            Date start = DATE_FORMAT.parse(exportDTO.getStartDate());
            Date end = DATE_FORMAT.parse(exportDTO.getEndDate());

            // 解析列表参数
            List<String> deptIdList = null;
            if (exportDTO.getDepartmentIds() != null && !exportDTO.getDepartmentIds().trim().isEmpty()) {
                deptIdList = Arrays.asList(exportDTO.getDepartmentIds().split(","));
            }

            List<String> projectIdList = null;
            if (exportDTO.getProjectIds() != null && !exportDTO.getProjectIds().trim().isEmpty()) {
                projectIdList = Arrays.asList(exportDTO.getProjectIds().split(","));
            }

            // 调用Excel导出服务
            deptProjectHourExportService.exportDeptProjectHourReportToExcel(
                    start, end, exportDTO.getStatPeriod() != null ? exportDTO.getStatPeriod() : "daily", 
                    deptIdList, projectIdList, exportDTO.getIsHoliday(), response);
            
            log.info("部门项目工时统计报表Excel导出完成, startDate={}, endDate={}", exportDTO.getStartDate(), exportDTO.getEndDate());

        } catch (Exception e) {
            log.error("导出部门项目工时统计报表失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"error\":\"导出失败: " + e.getMessage() + "\"}");
            } catch (Exception ex) {
                log.error("返回错误信息失败", ex);
            }
        }
    }

    /**
     * 手动触发工时统计任务
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statPeriod 统计周期
     * @return 执行结果
     */
    @AutoLog(value = "部门项目工时统计报表-手动统计")
    @ApiOperation(value = "手动触发工时统计任务", notes = "手动触发工时统计任务")
    @PostMapping(value = "/calculate")
    public Result<String> calculateHourStatistics(
            @ApiParam(value = "开始日期（格式：yyyy-MM-dd）", required = true) @RequestParam String startDate,
            @ApiParam(value = "结束日期（格式：yyyy-MM-dd）", required = true) @RequestParam String endDate,
            @ApiParam(value = "统计周期", required = false) @RequestParam(defaultValue = "daily") String statPeriod) {

        try {

            // 解析日期参数
            Date start = DATE_FORMAT.parse(startDate);
            Date end = DATE_FORMAT.parse(endDate);

            // 执行统计任务
            int processedCount = deptProjectHourStatisticsService.batchCalculateHourStatistics(
                    start, end, statPeriod);

            String message = String.format("工时统计任务执行完成，处理记录数：%d", processedCount);
            log.info("手动触发工时统计任务完成, startDate={}, endDate={}, 处理记录数={}", 
                    startDate, endDate, processedCount);

            return Result.OK(message);

        } catch (ParseException e) {
            log.error("日期格式解析错误: startDate={}, endDate={}", startDate, endDate, e);
            return Result.error("日期格式错误，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("手动触发工时统计任务失败", e);
            return Result.error("统计任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 重新计算指定日期的统计数据
     *
     * @param statDate 统计日期
     * @param statPeriod 统计周期
     * @return 执行结果
     */
    @AutoLog(value = "部门项目工时统计报表-重新计算")
    @ApiOperation(value = "重新计算指定日期的统计数据", notes = "重新计算指定日期的统计数据")
    @PostMapping(value = "/recalculate")
    public Result<String> recalculateHourStatistics(
            @ApiParam(value = "统计日期（格式：yyyy-MM-dd）", required = true) @RequestParam String statDate,
            @ApiParam(value = "统计周期", required = false) @RequestParam(defaultValue = "daily") String statPeriod) {

        try {

            // 解析日期参数
            Date date = DATE_FORMAT.parse(statDate);

            // 重新计算统计数据
            int processedCount = deptProjectHourStatisticsService.recalculateHourStatistics(date, statPeriod);

            String message = String.format("重新计算统计数据完成，处理记录数：%d", processedCount);
            log.info("重新计算工时统计数据完成, statDate={}, 处理记录数={}", statDate, processedCount);

            return Result.OK(message);

        } catch (ParseException e) {
            log.error("日期格式解析错误: statDate={}", statDate, e);
            return Result.error("日期格式错误，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("重新计算工时统计数据失败", e);
            return Result.error("重新计算失败: " + e.getMessage());
        }
    }
}