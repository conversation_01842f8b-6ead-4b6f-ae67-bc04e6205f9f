import React, { useState } from 'react';
import { Button, Card, Typography, Space, Table, Tag } from '@douyinfe/semi-ui';

const { Title, Text } = Typography;

/**
 * 批量登记明细参数传递测试页面
 * 
 * 用于测试和验证批量登记表格中点击"明细"时参数传递的正确性
 */
const BatchDetailParamsTest: React.FC = () => {
  const [selectedParams, setSelectedParams] = useState<any>(null);

  // 模拟批量登记表格的数据结构（更接近真实情况）
  const mockTableData = [
    {
      key: '616578422',
      uniqueKey: '616578422',
      title: '阶段1',
      type: 'instance' as const,
      workItemId: '672d784c9a71acdb4f8180ef',
      instanceId: '616578422',
      children: [
        {
          key: 'state_0',
          uniqueKey: '616578422_state_0',
          title: '开发',
          type: 'node' as const,
          workItemId: '672d784c9a71acdb4f8180ef',
          instanceId: '616578422',
          nodeId: 'state_0',
          children: [
            {
              key: '616581875',
              uniqueKey: '616578422_state_0_616581875',
              title: '功能实现',
              type: 'task' as const,
              workItemId: '672d784c9a71acdb4f8180ef',
              instanceId: '616578422',
              nodeId: 'state_0',
              taskId: '616581875',
              hours: 2.5,
              description: '实现核心功能'
            },
            {
              key: '616581876',
              uniqueKey: '616578422_state_0_616581876',
              title: '单元测试',
              type: 'task' as const,
              workItemId: '672d784c9a71acdb4f8180ef',
              instanceId: '616578422',
              nodeId: 'state_0',
              taskId: '616581876',
              hours: 1.0,
              description: '编写单元测试'
            }
          ]
        },
        {
          key: 'state_1',
          uniqueKey: '616578422_state_1',
          title: '测试',
          type: 'node' as const,
          workItemId: '672d784c9a71acdb4f8180ef',
          instanceId: '616578422',
          nodeId: 'state_1',
          children: [
            {
              key: '616581877',
              uniqueKey: '616578422_state_1_616581877',
              title: '集成测试',
              type: 'task' as const,
              workItemId: '672d784c9a71acdb4f8180ef',
              instanceId: '616578422',
              nodeId: 'state_1',
              taskId: '616581877',
              hours: 1.5,
              description: '执行集成测试'
            }
          ]
        }
      ]
    }
  ];

  // 模拟构建明细查询参数的逻辑（基于递归关系获取）
  const buildDetailParams = (record: any) => {
    const params: any = {};

    console.log('构建明细参数，原始记录:', {
      key: record.key,
      uniqueKey: record.uniqueKey,
      type: record.type,
      workItemId: record.workItemId,
      instanceId: record.instanceId,
      nodeId: record.nodeId,
      taskId: record.taskId
    });

    // 始终包含工作项信息
    if (record.workItemId) {
      params.workItemId = record.workItemId;
    }

    // 使用递归查找获取完整的层级关系
    const hierarchyInfo = findRecordHierarchy(record, mockTableData);
    console.log('递归查找到的层级信息:', hierarchyInfo);

    // 根据记录类型确定需要传递的参数
    switch (record.type) {
      case 'instance':
        // 实例级别：传递工作项ID和实例ID
        params.instanceId = record.instanceId || record.key;
        break;

      case 'node':
        // 节点级别：传递工作项ID、实例ID和节点ID
        params.instanceId = hierarchyInfo.instanceId || record.instanceId;
        params.nodeId = record.nodeId || record.key;
        break;

      case 'task':
        // 任务级别：传递完整的四级参数
        params.instanceId = hierarchyInfo.instanceId || record.instanceId;
        params.nodeId = hierarchyInfo.nodeId || record.nodeId;
        params.taskId = record.taskId || record.key;
        break;
    }

    console.log('最终构建的参数:', params);
    return params;
  };

  // 递归查找记录的层级关系
  const findRecordHierarchy = (targetRecord: any, data: any[]): {
    instanceId?: string;
    instanceName?: string;
    nodeId?: string;
    nodeName?: string;
  } => {
    const result = {
      instanceId: undefined as string | undefined,
      instanceName: undefined as string | undefined,
      nodeId: undefined as string | undefined,
      nodeName: undefined as string | undefined
    };

    const searchInData = (
      items: any[],
      currentInstance?: { id: string; name: string },
      currentNode?: { id: string; name: string }
    ): boolean => {
      for (const item of items) {
        // 更新当前层级的实例和节点信息
        let updatedInstance = currentInstance;
        let updatedNode = currentNode;

        if (item.type === 'instance') {
          updatedInstance = { id: item.key, name: item.title };
        } else if (item.type === 'node') {
          updatedNode = { id: item.key, name: item.title };
        }

        // 检查是否找到目标记录
        if (item.key === targetRecord.key && item.uniqueKey === targetRecord.uniqueKey) {
          result.instanceId = updatedInstance?.id;
          result.instanceName = updatedInstance?.name;
          result.nodeId = updatedNode?.id;
          result.nodeName = updatedNode?.name;
          return true;
        }

        // 递归搜索子项
        if (item.children && item.children.length > 0) {
          if (searchInData(item.children, updatedInstance, updatedNode)) {
            return true;
          }
        }
      }
      return false;
    };

    searchInData(data);
    return result;
  };

  // 模拟点击明细的处理
  const handleViewDetail = (record: any) => {
    const params = buildDetailParams(record);
    setSelectedParams({
      record: record,
      params: params,
      apiUrl: `/hours/hourRecord/detail?${new URLSearchParams(params).toString()}`
    });
  };

  // 扁平化表格数据用于显示
  const flattenTableData = (data: any[], level = 0): any[] => {
    const result: any[] = [];
    
    data.forEach(item => {
      result.push({
        ...item,
        level: level,
        indent: '　'.repeat(level) // 使用全角空格缩进
      });
      
      if (item.children && item.children.length > 0) {
        result.push(...flattenTableData(item.children, level + 1));
      }
    });
    
    return result;
  };

  const flatData = flattenTableData(mockTableData);

  const columns = [
    {
      title: '工作内容',
      dataIndex: 'title',
      width: 200,
      render: (text: string, record: any) => (
        <span>
          {record.indent}{text}
        </span>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 80,
      render: (type: string) => {
        const colorMap = {
          instance: 'blue',
          node: 'green',
          task: 'orange'
        };
        return <Tag color={colorMap[type as keyof typeof colorMap]}>{type}</Tag>;
      }
    },
    {
      title: 'Key',
      dataIndex: 'key',
      width: 120
    },
    {
      title: 'UniqueKey',
      dataIndex: 'uniqueKey',
      width: 200
    },
    {
      title: '工时',
      dataIndex: 'hours',
      width: 80,
      render: (hours: number) => hours ? `${hours}H` : '-'
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      render: (_: any, record: any) => (
        <Button
          type="tertiary"
          theme="borderless"
          size="small"
          style={{
            padding: '0 4px',
            height: 'auto',
            fontSize: '12px',
            color: 'var(--semi-color-primary)',
            textDecoration: 'underline'
          }}
          onClick={() => handleViewDetail(record)}
        >
          明细
        </Button>
      )
    }
  ];

  return (
    <div style={{ padding: '24px', maxWidth: '1400px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>批量登记明细参数传递测试</Title>
        
        <div style={{ marginBottom: '24px' }}>
          <Text>
            此测试页面用于验证批量登记表格中点击"明细"时参数传递的正确性。
            点击不同层级的"明细"按钮，查看传递给工时明细模态窗的参数是否正确。
          </Text>
        </div>

        {/* 模拟表格 */}
        <div style={{ marginBottom: '24px' }}>
          <Title heading={5}>模拟批量登记表格</Title>
          <Table
            columns={columns}
            dataSource={flatData}
            pagination={false}
            size="small"
            bordered
            rowKey="uniqueKey"
          />
        </div>

        {/* 参数显示 */}
        {selectedParams && (
          <div style={{ marginTop: '24px' }}>
            <Title heading={5}>点击明细后的参数</Title>
            <div style={{ 
              padding: '16px',
              backgroundColor: 'var(--semi-color-fill-0)',
              borderRadius: '6px',
              marginTop: '16px'
            }}>
              <div style={{ marginBottom: '16px' }}>
                <Text strong>选中的记录信息：</Text>
                <div style={{ 
                  marginTop: '8px',
                  padding: '12px',
                  backgroundColor: 'var(--semi-color-bg-2)',
                  borderRadius: '4px',
                  fontFamily: 'monospace'
                }}>
                  <Text>
                    类型: {selectedParams.record.type}<br/>
                    标题: {selectedParams.record.title}<br/>
                    Key: {selectedParams.record.key}<br/>
                    UniqueKey: {selectedParams.record.uniqueKey}<br/>
                    WorkItemId: {selectedParams.record.workItemId}<br/>
                    InstanceId: {selectedParams.record.instanceId}<br/>
                    NodeId: {selectedParams.record.nodeId}<br/>
                    TaskId: {selectedParams.record.taskId}
                  </Text>
                </div>
              </div>

              <div style={{ marginBottom: '16px' }}>
                <Text strong>传递给模态窗的参数：</Text>
                <div style={{ 
                  marginTop: '8px',
                  padding: '12px',
                  backgroundColor: 'var(--semi-color-success-light-default)',
                  borderRadius: '4px',
                  fontFamily: 'monospace'
                }}>
                  <Text>
                    {Object.entries(selectedParams.params).map(([key, value]) => (
                      <div key={key}>{key}: {value as string}</div>
                    ))}
                  </Text>
                </div>
              </div>

              <div>
                <Text strong>API调用URL：</Text>
                <div style={{ 
                  marginTop: '8px',
                  padding: '12px',
                  backgroundColor: 'var(--semi-color-warning-light-default)',
                  borderRadius: '4px',
                  fontFamily: 'monospace',
                  wordBreak: 'break-all'
                }}>
                  <Text>{selectedParams.apiUrl}</Text>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 预期结果说明 */}
        <div style={{ marginTop: '24px' }}>
          <Title heading={5}>预期结果</Title>
          <div style={{
            padding: '16px',
            backgroundColor: 'var(--semi-color-info-light-default)',
            borderRadius: '6px',
            border: '1px solid var(--semi-color-info-light-active)'
          }}>
            <Text>
              <strong>实例级别 (阶段1)：</strong><br/>
              应该传递: workItemId=672d784c9a71acdb4f8180ef + instanceId=616578422<br/><br/>

              <strong>节点级别 (开发)：</strong><br/>
              应该传递: workItemId=672d784c9a71acdb4f8180ef + instanceId=616578422 + nodeId=state_0<br/><br/>

              <strong>节点级别 (测试)：</strong><br/>
              应该传递: workItemId=672d784c9a71acdb4f8180ef + instanceId=616578422 + nodeId=state_1<br/><br/>

              <strong>任务级别 (功能实现)：</strong><br/>
              应该传递: workItemId=672d784c9a71acdb4f8180ef + instanceId=616578422 + nodeId=state_0 + taskId=616581875<br/><br/>

              <strong>任务级别 (单元测试)：</strong><br/>
              应该传递: workItemId=672d784c9a71acdb4f8180ef + instanceId=616578422 + nodeId=state_0 + taskId=616581876<br/><br/>

              <strong>任务级别 (集成测试)：</strong><br/>
              应该传递: workItemId=672d784c9a71acdb4f8180ef + instanceId=616578422 + nodeId=state_1 + taskId=616581877<br/><br/>

              <strong>关键修复：</strong>末级任务的nodeId应该从uniqueKey中正确解析，格式为 instanceId_nodeId_taskId，nodeId是倒数第二个部分。
            </Text>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default BatchDetailParamsTest;
