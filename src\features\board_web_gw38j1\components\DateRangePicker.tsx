import React from 'react';
import { DatePicker } from '@douyinfe/semi-ui';

interface DateRangePickerProps {
  value?: [string | undefined, string | undefined];                    // 当前选中的日期范围
  onChange: (dates: [string, string]) => void; // 日期变化回调
  dimension: 'month' | 'week' | 'day';         // 时间维度
  placeholder?: string;                        // 占位符
  style?: React.CSSProperties;                 // 自定义样式
}

/**
 * 时间周期选择组件
 * 支持快捷方式选择：上周、本周、上月、本月、今年、去年
 */
const DateRangePicker: React.FC<DateRangePickerProps> = ({
  value,
  onChange,
  dimension,
  placeholder = "请选择时间范围",
  style
}) => {
  // 定制快捷方式 - 符合Semi Design格式
  const presets = [
    {
      text: '上周',
      start: new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000 - (new Date().getDay() - 1) * 24 * 60 * 60 * 1000),
      end: new Date(new Date().getTime() - (new Date().getDay() - 1) * 24 * 60 * 60 * 1000 - 24 * 60 * 60 * 1000),
    },
    {
      text: '本周',
      start: new Date(new Date().getTime() - (new Date().getDay() - 1) * 24 * 60 * 60 * 1000),
      end: new Date(new Date().getTime() + (7 - new Date().getDay()) * 24 * 60 * 60 * 1000),
    },
    {
      text: '上月',
      start: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
      end: new Date(new Date().getFullYear(), new Date().getMonth(), 0),
    },
    {
      text: '本月',
      start: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      end: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
    },
    {
      text: '今年',
      start: new Date(new Date().getFullYear(), 0, 1),
      end: new Date(new Date().getFullYear(), 11, 31),
    },
    {
      text: '去年',
      start: new Date(new Date().getFullYear() - 1, 0, 1),
      end: new Date(new Date().getFullYear() - 1, 11, 31),
    },
  ];

  // 处理日期变化
  const handleDateChange = (dates: Date[] | null) => {
    //console.log('test.dates.lefnth',dates);
    if (dates?.length === 0) {
      onChange(['', '']);
      return;
    }
    if (dates && dates.length === 2) {
      const startDate = formatDate(dates[0]);
      const endDate = formatDate(dates[1]);
      onChange([startDate, endDate]);
    }
  };

  // 格式化日期为YYYY-MM-DD
  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 转换value为Date对象
  const dateValue = value && value[0] && value[1] ? [new Date(value[0]), new Date(value[1])] : undefined;


  return (
    <DatePicker
      type="dateRange"
      presetPosition="left"
      presets={presets}
      value={dateValue}
      onChange={handleDateChange}
      placeholder={placeholder}
      style={{ width: 320, ...style }}
      format="yyyy-MM-dd"
    />
  );
};

export default DateRangePicker;
