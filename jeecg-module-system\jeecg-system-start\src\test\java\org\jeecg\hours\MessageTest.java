package org.jeecg.hours;

import java.nio.charset.StandardCharsets;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.hours.service.IFeishuAuthService;
import org.jeecg.modules.hours.task.FsSyncTask;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.attendance.v1.model.ChildItem;
import com.lark.oapi.service.attendance.v1.model.Item;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsDataResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsFieldResp;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.QueryUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReq;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewReqBody;
import com.lark.oapi.service.attendance.v1.model.UpdateUserStatsViewResp;
import com.lark.oapi.service.attendance.v1.model.UserStatsView;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 消息推送测试
 * @Author: lsq
 */
@Slf4j
//@ActiveProfiles("dev")
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class MessageTest {

	@Autowired
	private FsSyncTask faSyncTask;
	@Autowired
	private IFeishuAuthService feishuAuthService;
	
	private static final String appId = "cli_a7b2f4bbcb38500d";
	
	private static final String appSecret = "kzen0nGwHB15eEUVsDXkp1wn0YOF4HUK";

	@org.junit.jupiter.api.Test
	public void testname() throws Exception {
		sendMessage();
		
	}
	
	public void sendMessage() throws Exception {
		
		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();
		
		JSONObject json = new JSONObject();
		json.put("type", "template");
		json.put("template_id", "AAqdphG0IvpsK");
		json.put("template_version_name", "1.0.1");
		
		JSONObject params = new JSONObject();
		params.put("title", "测试");
		params.put("btn_name", "立即登记");
		params.put("content", "测试");
		
		JSONObject goLinkParam = new JSONObject();
		goLinkParam.put("pc_url", "");
		goLinkParam.put("android_url", "");
		goLinkParam.put("ios_url", "");
		goLinkParam.put("url", "https://www.baidu.com");
		params.put("go_link", JSON.toJSONString(goLinkParam));
		
		json.put("template_variable", JSON.toJSONString(params));
		
//		String content = JSON.toJSONString(json,SerializerFeature.BrowserCompatible,
//			    SerializerFeature.WriteSlashAsSpecial);
		String content = JSON.toJSONString(json)+"";
		log.info("content={}",content);

		// 创建请求对象
		CreateMessageReq req = CreateMessageReq.newBuilder()
			.receiveIdType("union_id")
			.createMessageReqBody(CreateMessageReqBody.newBuilder()
				.receiveId("on_03144b4670493fe27269925cf7e9cdbf")
				.msgType("interactive")
				.content(content)
				.uuid("选填，每次调用前请更换，如a0d69e20-1dd1-458b-k525-dfeca4015205")
				.build())
			.build();

		// 发起请求
		CreateMessageResp resp = client.im().v1().message().create(req);

		// 处理服务端错误
		if(!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s",
				resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
	
	}
	
	public void query04() throws Exception {

		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		QueryUserStatsDataReq req = QueryUserStatsDataReq.newBuilder()
			.employeeType("employee_id")
			.queryUserStatsDataReqBody(QueryUserStatsDataReqBody.newBuilder()
				.locale("zh")
				.statsType("daily")
				.startDate(20250424)
				.endDate(20250428)
				.userIds(new String[] {
					"b4b15ac7"
				})
				.needHistory(true)
				.currentGroupOnly(true)
				.userId("169ced82")
				.build())
			.build();

		// 发起请求
		QueryUserStatsDataResp resp = client.attendance().v1().userStatsData().query(req);

		// 处理服务端错误
		if(!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s",
				resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
		
	}
	
	public void queryUserStat03() throws Exception {

		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		UpdateUserStatsViewReq req = UpdateUserStatsViewReq.newBuilder()
			.userStatsViewId("TnpNM05qUTFNVE0xTmpnM05UVTNNVEl3TXl0a1lXbHNlUT09")
			.employeeType("employee_id")
			.updateUserStatsViewReqBody(UpdateUserStatsViewReqBody.newBuilder()
				.view(UserStatsView.newBuilder()
					.viewId("TnpNM05qUTFNVE0xTmpnM05UVTNNVEl3TXl0a1lXbHNlUT09")
					.statsType("daily")
					.userId("169ced82")
					.items(new Item[] {
						Item.newBuilder()
							.code("501")
							.childItems(new ChildItem[] {
								ChildItem.newBuilder()
									.code("50101")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("50102")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("50111")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("50103")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("50104")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("50105")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("50107")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("9")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("50109")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("50110")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("101")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("93")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("94")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("95")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("96")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("84")
									.value("0")
									.build()
							})
							.build(),
							Item.newBuilder()
							.code("512")
							.childItems(new ChildItem[] {
								ChildItem.newBuilder()
									.code("51203")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("85")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51201")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51205")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51204")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51202")
									.value("0")
									.build()
							})
							.build(),
							Item.newBuilder()
							.code("515")
							.childItems(new ChildItem[] {
								ChildItem.newBuilder()
									.code("51501")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("51504")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("51507")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("51502")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("51503")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("81")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51505")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51506")
									.value("0")
									.build()
							})
							.build(),
							Item.newBuilder()
							.code("513")
							.childItems(new ChildItem[] {
								ChildItem.newBuilder()
									.code("51302")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("51303")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("51304")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("61")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51315")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51305")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51313")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51306")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51316")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51307")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("51308")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51309")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51310")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51311")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51312")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51314")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3001")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3002")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3003")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3008")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3011")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3014")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3009")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3012")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3015")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3010")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3013")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("3016")
									.value("0")
									.build()
							})
							.build(),
							Item.newBuilder()
							.code("514")
							.childItems(new ChildItem[] {
								ChildItem.newBuilder()
									.code("51401")
									.value("1")
									.build(),
									ChildItem.newBuilder()
									.code("51402")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51403")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51404")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("87")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51405")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("89")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51406")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51407")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51408")
									.value("0")
									.build(),
									ChildItem.newBuilder()
									.code("51409")
									.value("0")
									.build()
							})
							.build(),
							Item.newBuilder()
							.code("1007")
							.childItems(new ChildItem[] {

							})
							.build()
					})
					.build())
				.build())
			.build();

		// 发起请求
		UpdateUserStatsViewResp resp = client.attendance().v1().userStatsView().update(req);

		// 处理服务端错误
		if(!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s",
				resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
	}
	
	public void queryStat02() throws Exception {
		
		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		QueryUserStatsViewReq req = QueryUserStatsViewReq.newBuilder()
			.employeeType("employee_id")
			.queryUserStatsViewReqBody(QueryUserStatsViewReqBody.newBuilder()
				.locale("zh")
				.statsType("daily")
				.userId("169ced82")
				.build())
			.build();

		// 发起请求
		QueryUserStatsViewResp resp = client.attendance().v1().userStatsView().query(req);

		// 处理服务端错误
		if(!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s",
				resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
		
	}
	
	
	
	public void queryHeader01() throws Exception {
		// 构建client
		Client client = Client.newBuilder(appId, appSecret).build();

		// 创建请求对象
		Integer start = 20210316;
		Integer end = 20210323;
		QueryUserStatsFieldReq req = QueryUserStatsFieldReq.newBuilder()
			.employeeType("employee_id")
			.queryUserStatsFieldReqBody(QueryUserStatsFieldReqBody.newBuilder()
				.locale("zh")
				.statsType("daily")
				.startDate(start)
				.endDate(end)
				.build())
			.build();

		// 发起请求
		QueryUserStatsFieldResp resp = client.attendance().v1().userStatsField().query(req);

		// 处理服务端错误
		if(!resp.success()) {
			System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s",
				resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return;
		}

		// 业务数据处理
		System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
	}
	
}
