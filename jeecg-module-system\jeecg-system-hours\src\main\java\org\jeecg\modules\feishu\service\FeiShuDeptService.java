package org.jeecg.modules.feishu.service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.contact.v3.model.BatchUserReq;
import com.lark.oapi.service.contact.v3.model.BatchUserResp;
import com.lark.oapi.service.contact.v3.model.ChildrenDepartmentReq;
import com.lark.oapi.service.contact.v3.model.ChildrenDepartmentResp;
import com.lark.oapi.service.contact.v3.model.Department;
import com.lark.oapi.service.contact.v3.model.GetDepartmentReq;
import com.lark.oapi.service.contact.v3.model.GetDepartmentResp;
import com.lark.oapi.service.contact.v3.model.GetUserReq;
import com.lark.oapi.service.contact.v3.model.GetUserResp;
import com.lark.oapi.service.contact.v3.model.User;

import lombok.extern.slf4j.Slf4j;

@Slf4j
//@Service
public class FeiShuDeptService {
	
	private static Integer pageSize = 50;
	
	private static Client client;
	
	private static FeiShuDeptService _instance;
	
	private FeiShuDeptService() {}
	
	public static FeiShuDeptService instance(String appId,String appSecret) {
		client = Client.newBuilder(appId, appSecret).build();
		if(null==_instance) {
			_instance = new FeiShuDeptService();
		}
		return _instance;
	}

	public Department getDeptById(String departmentId) throws Exception {
		// 创建请求对象
		GetDepartmentReq req = GetDepartmentReq.newBuilder()
			.departmentId(departmentId)
			.userIdType("union_id")
			.departmentIdType("department_id")
			.build();

		// 发起请求
		GetDepartmentResp resp = client.contact().v3().department().get(req);
		
		// 处理服务端错误
		if (!resp.success()) {
			log.error(String.format("code:%s,msg:%s,reqId:%s, resp:%s", resp.getCode(), resp.getMsg(),
					resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser
							.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
			return null;
		}
		return resp.getData().getDepartment();
	}
	
	
	public List<Department> children(String departmentId) throws Exception {
		List<Department> dataList = new ArrayList<Department>();
		
		// 创建请求对象
		ChildrenDepartmentReq req = ChildrenDepartmentReq.newBuilder()
			.departmentId(departmentId)
			.userIdType("union_id")
			.departmentIdType("department_id")
			.pageSize(pageSize)
			.build();

		// 发起请求
		ChildrenDepartmentResp resp = client.contact().v3().department().children(req);

		if (!resp.success()) {
			System.out.printf("client.contact.department.children failed, code: %d, msg: %s, logId: %s%n",
					resp.getCode(), resp.getMsg(),
					resp.getRequestId());
			return dataList;
		}
//		log.info("resp=>{}", Jsons.DEFAULT.toJson(childrenDepartmentResp.getData()));
		Department[] items = resp.getData().getItems();
		if (null == items) {
			return dataList;
		}
		for (Department department : items) {
			List<Department> tmpList = this.children(department.getDepartmentId());
			if (!CollectionUtils.isEmpty(tmpList)) {
				dataList.addAll(tmpList);
			}
			dataList.add(department);
		}
		return dataList;
	}
	
}
