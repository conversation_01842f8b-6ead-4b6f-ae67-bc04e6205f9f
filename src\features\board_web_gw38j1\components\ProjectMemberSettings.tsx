import React, { useState, useEffect, useImperative<PERSON>andle, forwardRef } from 'react';
import { Table, Button, Toast, Modal, Tag, Form, Select, Spin } from '@douyinfe/semi-ui';
import { IconEdit } from '@douyinfe/semi-icons';
import { 
  getProjectList, 
  getProjectMembers, 
  saveProject
} from '../../../services/api/projectMember';
import { getDictItems } from '../../../services/api/dict';
import UserSelector from './UserSelector';
import DictSelect, { DictItem, preloadDictItems } from '../../../components/DictSelect';
import './ProjectMemberSettings.less';
import { get } from '../../../utils/request';

// 定义接口类型
interface ProjectItem {
  id: string;
  projectCode: string;
  projectName: string;
  projectStatus: string;
  managerId?: string;
  managerName?: string;
  userNames?: string;
  remark?: string;
  updateTime?: string;
  updateBy_dictText?: string;
  projectStatus_dictText?: string;
}

interface ProjectMember {
  id: string;
  projectId: string;
  projectCode: string;
  userId: string;
  userName: string;
  userRole?: string;
  createTime?: string;
  updateTime?: string;
}

interface ProjectMemberSettingsProps {
  visible: boolean;
}

interface ApiResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  result: T;
}

// 项目人员数据维护组件
const ProjectMemberSettings = forwardRef((props: ProjectMemberSettingsProps, ref) => {
  const { visible } = props;
  const [loading, setLoading] = useState(false);
  const [projectList, setProjectList] = useState<ProjectItem[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentProject, setCurrentProject] = useState<ProjectItem | null>(null);
  const [projectMembers, setProjectMembers] = useState<ProjectMember[]>([]);
  const [selectedManager, setSelectedManager] = useState<string>(''); // 修改为单选
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [remark, setRemark] = useState<string>('');
  // 添加状态字典项状态
  const [statusDictItems, setStatusDictItems] = useState<DictItem[]>([]);

  // 用户选项拉取逻辑（放在组件内合适位置）
  const [userOptions, setUserOptions] = useState<any[]>([]);
  const [userLoading, setUserLoading] = useState(false);
  useEffect(() => {
    setUserLoading(true);
    get('/system/user/listActiveUsers')
      .then((response: any) => {
        if (response && response.success && response.result) {
          setUserOptions(response.result);
        }
      })
      .finally(() => setUserLoading(false));
  }, []);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({}));

  // 处理表格分页变化（仅用于日志记录）
  const handleTableChange = (pagination: any) => {
    console.log('表格变化事件触发:', pagination);
    // 不再调用分页处理函数，避免重复调用
  };

  // 专门的分页处理函数
  const handlePaginationChange = (page: number, size: number) => {
    console.log(`分页变化: 页码=${page}, 每页条数=${size}`);
    // 更新分页状态
    setPagination({
      current: page,
      pageSize: size,
      total: pagination.total
    });
    
    // 直接调用获取项目列表，传入新的分页参数
    fetchProjectListWithParams(page, size);
  };

  // 带参数的获取项目列表
  const fetchProjectListWithParams = async (pageNo: number, pageSize: number) => {
    setLoading(true);
    try {
      const params = {
        pageNo,
        pageSize
      };
      console.log('获取项目列表参数:', params);

      const response = await getProjectList(params) as ApiResponse<{
        records: ProjectItem[];
        total: number;
        size: number;
        current: number;
      }>;

      if (response.success) {
        setProjectList(response.result.records);
        setPagination({
          current: pageNo,
          pageSize: pageSize,
          total: response.result.total,
        });
      } else {
        Toast.error(response.message || '获取项目列表失败');
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      Toast.error('获取项目列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取项目列表 - 使用当前分页状态
  const fetchProjectList = () => {
    fetchProjectListWithParams(pagination.current, pagination.pageSize);
  };

  // 获取项目成员
  const fetchProjectMembers = async (projectId: string) => {
    try {
      const response = await getProjectMembers(projectId) as ApiResponse<{
        project: ProjectItem;
        members: ProjectMember[];
      }>;
      
      if (response.success) {
        // 后端返回的是包含project和members的对象
        setProjectMembers(response.result.members);
        setRemark(response.result.project.remark || '');
        
        // 设置选中的项目经理（单选）
        setSelectedManager(response.result.project.managerId || '');
        
        // 设置选中的成员
        const memberIds = response.result.members.map(member => member.userId);
        setSelectedMembers(memberIds);
      } else {
        Toast.error(response.message || '获取项目成员失败');
      }
    } catch (error) {
      console.error('获取项目成员失败:', error);
      Toast.error('获取项目成员失败');
    }
  };

  // 打开编辑对话框
  const handleEdit = (record: ProjectItem) => {
    setCurrentProject(record);
    // 直接从记录中设置备注，避免等待异步请求
    setRemark(record.remark || '');
    // 获取项目成员
    fetchProjectMembers(record.id);
    setEditModalVisible(true);
    
    console.log('编辑项目:', record);
    console.log('备注值:', record.remark);
  };

  // 保存项目人员配置
  const handleSave = async () => {
    if (!currentProject) return;

    try {
      // 使用统一的保存接口
      const response = await saveProject({
        projectId: currentProject.id,
        managerId: selectedManager,
        memberIds: selectedMembers.join(','),
        remark: remark
      }) as ApiResponse<boolean>;

      if (response.success) {
        Toast.success('保存成功');
        setEditModalVisible(false);
        fetchProjectList(); // 刷新项目列表
      } else {
        Toast.error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('保存项目人员配置失败:', error);
      Toast.error('保存项目人员配置失败');
    }
  };

  // 渲染项目状态标签
  const renderStatusTag = (status: string) => {
    // 使用字典项获取状态文本和颜色
    const statusItem = statusDictItems.find(item => item.value === status);
    if (statusItem) {
      // 根据状态值设置不同的颜色
      let color = 'default';
      switch (status) {
        case 'AN': color = 'grey'; break;
        case 'AO': color = 'blue'; break;
        case 'AD': color = 'orange'; break;
        case 'AI': color = 'green'; break;
        default: color = 'default';
      }
      return <Tag color={color as any}>{statusItem.text}</Tag>;
    }
    return <Tag>{status}</Tag>;
  };

  // 预加载字典数据并设置状态
  const loadDictData = async () => {
    try {
      // 使用 getDictItems 获取字典数据
      const response = await getDictItems('project_status') as ApiResponse<DictItem[]>;
      if (response.success && response.result) {
        setStatusDictItems(response.result);
      }
    } catch (error) {
      console.error('加载字典数据失败:', error);
    }
  };

  // 监听分页变化和组件可见性
  useEffect(() => {
    if (visible) {
      // 组件可见时获取项目列表
      fetchProjectList();
      // 预加载字典数据
      loadDictData();
    }
  }, [visible]); // 移除分页参数的依赖，改为手动触发

  // 项目列表表格列
  const columns = [
    { title: '项目ID', dataIndex: 'projectCode', width: 120 },
    { title: '项目名称', dataIndex: 'projectName', width: 180 },
    { title: '项目经理', dataIndex: 'managerName', width: 100 },
    { title: '项目成员', dataIndex: 'memberNames', width: 120 },
    { 
      title: '项目状态', 
      dataIndex: 'projectStatus', 
      width: 100,
      render: (text: string, record: ProjectItem) => renderStatusTag(text)
    },
    { title: '备注', dataIndex: 'remark', width: 120 },
    { title: '更新时间', dataIndex: 'updateTime', width: 150 },
    { title: '更新人', dataIndex: 'updateBy_dictText', width: 100 },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      width: 130,
      render: (_: any, record: ProjectItem) => (
        <Button 
          icon={<IconEdit />} 
          type="primary" 
          theme="borderless"
          onClick={() => handleEdit(record)}
        >
          编辑人员
        </Button>
      )
    }
  ];

  // 处理项目经理选择（单选）
  const handleManagerChange = (value: string) => {
    // 确保只有一个选中值
    console.log('handleManagerChange:', value);
    // if (value && value.length > 0) {
      setSelectedManager(value);
    // } else {
    //   setSelectedManager('');
    // }
  };

  return (
    <div className="project-member-settings" style={{ 
      maxWidth: '100%', 
      overflowX: 'hidden', 
      boxSizing: 'border-box', 
      padding: 0 
    }}>
      {/* 项目列表表格 */}
      <div style={{ 
        width: '100%', 
        overflowX: 'auto', 
        boxSizing: 'border-box',
        padding: '0 20px',
        marginTop: '16px',
        marginBottom: '16px'
      }}>
        <Table
          columns={columns}
          dataSource={projectList}
          pagination={{
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            onPageChange: (page) => handlePaginationChange(page, pagination.pageSize),
            onPageSizeChange: (size) => handlePaginationChange(1, size),
            style: { padding: '0 20px' }
          }}
          loading={loading}
          size="middle"
          bordered
          scroll={{ x: 'max-content' }}
        />
      </div>

      {/* 项目人员编辑对话框 */}
      <Modal
        title="项目人员数据管理"
        visible={editModalVisible}
        onOk={handleSave}
        onCancel={() => setEditModalVisible(false)}
        width={700}
        maskClosable={false}
        okText="确定"
        cancelText="关闭"
        bodyStyle={{ maxWidth: '100%', overflow: 'auto', padding: '16px' }}
        style={{ maxWidth: '95vw' }}
      >
        {currentProject && (
          <div>
            <Form labelPosition="left" labelWidth={100}>
              <Form.Input
                field="projectId"
                label="项目ID"
                initValue={currentProject.projectCode}
                disabled
              />
              
              <Form.Input
                field="projectName"
                label="项目名称"
                initValue={currentProject.projectName}
                disabled
              />
              
              <Form.Slot label="项目状态">
                <DictSelect
                  dictCode="project_status"
                  value={currentProject.projectStatus}
                  disabled={true}
                  placeholder="请选择项目状态"
                />
              </Form.Slot>
              
              <Form.Slot label="项目经理">
                {/* 单选项目经理 */}
                <Select
                  value={selectedManager}
                  onChange={handleManagerChange}
                  placeholder="请选择项目经理"
                  style={{ width: '100%' }}
                  loading={userLoading}
                  filter
                  searchPlaceholder="搜索人员"
                  emptyContent={userLoading ? <Spin /> : "暂无数据"}
                >
                  {userOptions.map(user => (
                    <Select.Option key={user.userKey} value={user.userKey}>
                      {user.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Slot>
              
              <Form.Slot label="项目成员">
                <UserSelector
                  value={selectedMembers}
                  onChange={setSelectedMembers}
                  placeholder="请选择项目成员"
                />
              </Form.Slot>
              
              <Form.TextArea
                field="remark"
                label="备注"
                initValue={remark}
                onChange={value => setRemark(value)}
                placeholder="请输入备注信息"
                style={{ height: 80 }}
              />
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
});

export default ProjectMemberSettings; 