package org.jeecg.modules.hours.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 部门项目工时统计表
 * @Author: jeecg-boot
 * @Date: 2025-07-07
 * @Version: V1.0
 */
@Data
@TableName("dept_project_hour_statistics")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "dept_project_hour_statistics对象", description = "部门项目工时统计表")
public class DeptProjectHourStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 统计日期
     */
    @Excel(name = "统计日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "统计日期")
    private Date statDate;

    /**
     * 统计周期
     */
    @Excel(name = "统计周期", width = 15)
    @ApiModelProperty(value = "统计周期：daily-按日, weekly-按周, monthly-按月")
    private String statPeriod;

    /**
     * 部门ID
     */
    @Excel(name = "部门ID", width = 15)
    @ApiModelProperty(value = "部门ID")
    private String departmentId;

    /**
     * 部门名称
     */
    @Excel(name = "部门名称", width = 15)
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 项目ID
     */
    @Excel(name = "项目ID", width = 15)
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称", width = 15)
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目编码
     */
    @Excel(name = "项目编码", width = 15)
    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    /**
     * 总工时
     */
    @Excel(name = "总工时", width = 15)
    @ApiModelProperty(value = "总工时")
    private BigDecimal totalHours;

    /**
     * 参与人数
     */
    @Excel(name = "参与人数", width = 15)
    @ApiModelProperty(value = "参与人数")
    private Integer userCount;

    /**
     * 记录条数
     */
    @Excel(name = "记录条数", width = 15)
    @ApiModelProperty(value = "记录条数")
    private Integer recordCount;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 是否假日：0-否；1-是
     */
    @Excel(name = "是否假日：0-否；1-是", width = 15)
    @ApiModelProperty(value = "是否假日：0-否；1-是")
    private Integer isHoliday;
}