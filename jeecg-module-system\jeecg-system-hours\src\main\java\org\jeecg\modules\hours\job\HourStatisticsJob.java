//package org.jeecg.modules.hours.job;
//
//import java.util.Calendar;
//import java.util.Date;
//import java.util.concurrent.ExecutionException;
//
//import org.jeecg.modules.hours.service.IDeptProjectHourStatisticsService;
//import org.quartz.JobExecutionException;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import lombok.extern.slf4j.Slf4j;
//
///**
// * @Description: 工时统计定时任务
// * @Author: jeecg-boot
// * @Date: 2025-07-07
// * @Version: V1.0
// */
//@Component
//@Slf4j
//public class HourStatisticsJob  {
//
//    @Autowired
//    private IDeptProjectHourStatisticsService deptProjectHourStatisticsService;
//
//    @Scheduled(cron = "0 0 1 * * ?")
////@Scheduled(cron = "0 */10 * * * ?")
//    public void execute() throws ExecutionException {
//        log.info("工时统计定时任务开始执行");
//
//        try {
//            // 获取昨天的日期（统计前一天的工时数据）
//            Calendar calendar = Calendar.getInstance();
//            calendar.add(Calendar.DAY_OF_MONTH, -1);
//            Date yesterday = calendar.getTime();
//
//            // 执行日度统计
//            int processedCount = deptProjectHourStatisticsService.calculateHourStatistics(yesterday, "daily");
//
//            log.info("工时统计定时任务执行完成, 处理记录数={}", processedCount);
//
//        } catch (Exception e) {
//            log.error("工时统计定时任务执行失败", e);
//            throw new ExecutionException("工时统计定时任务执行失败: " + e.getMessage(), e);
//        }
//    }
//}