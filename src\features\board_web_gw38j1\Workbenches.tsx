import React, {useState, useRef} from "react";

import './css/Workbenches.scss';
import {<PERSON><PERSON>, Card, Col, Layout, Row, Typography} from "@douyinfe/semi-ui";
const {Title, Text} = Typography;
import workbenchesPic from '../../assets/pic/workbenches.png';
import {IconChevronRight} from "@douyinfe/semi-icons";
import ApprovalList from "./components/ApprovalList";
import WorkList from "./components/WorkList";

import HourRecordEditForm from "./HourRecordEditForm";
import HourRecordForm from "./HourRecordForm";
import store, {setActiveTab} from "../../services/AppState";
import WorkHourRulesModal from "./components/WorkHourRulesModal";
import {getUserInfo} from "../../utils/auth";
// 接口返回的工时记录类型
interface HourRecord {
    id: string | number;
    userId: string;
    userName: string;
    workDate: string;
    hours: number;
    description: string;
    workItemId: string;       // 工作项ID
    workItemName: string;     // 工作项名称
    projectId: string;
    projectName: string;
    instanceId?: string;
    instanceName?: string;
    nodeId?: string;
    nodeName?: string;
    taskId?: string;
    taskName?: string;
    status: number | string;
    statusName: string;
    createdAt: string;
    submitTime?: string | null;
    approverName?: string | null;
    rejectReason?: string | null;
    workPath?: string;
    workPeriodStart?: string;
    workPeriodEnd?: string;
}

function toFormInitialData(record: HourRecord | null): any {
    if (!record) return undefined;
    return {
        workDate: record.workDate ? new Date(record.workDate) : undefined,
        worker: record.userId,
        workContent: [record.workItemId, record.instanceId, record.nodeId, record.taskId].filter(Boolean),
        projectKey: record.projectId,
        recordId: record.id,
        projectName: record.projectName,
        instanceName: record.instanceName,
        nodeName: record.nodeName,
        taskName: record.taskName
    };
}
const Workbenches: React.FC = () => {
    // 获取当前登录用户
    const currentUser = getUserInfo();

    const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const [records, setRecords] = useState<HourRecord[]>([]);
    const [total, setTotal] = useState(0);
    // 用户选择状态
    const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
    // 状态选择状态
    const [selectedStatus, setSelectedStatus] = useState<string>('');
    // 添加记录模式状态，用于控制HourRecordForm的登记模式
    const [recordMode, setRecordMode] = useState<'single' | 'batch'>('single');
    // 添加当前编辑记录的状态
    const [currentRecord, setCurrentRecord] = useState<HourRecord | null>(null);
    // 添加工作内容数组的状态
    const [workContent, setWorkContent] = useState<(string | number)[]>([]);
    // 添加editMode状态区分'add'和'edit'
    const [editMode, setEditMode] = useState<'add' | 'edit' | 'view'>('add');
    // 工时登记规则说明模态窗状态
    const [rulesModalVisible, setRulesModalVisible] = useState(false);

    // 处理打开批量登记
    const handleOpenBatchRecord = () => {
        setCurrentRecord(null);
        setWorkContent([]);
        setRecordMode('batch');
        setEditMode('add');
        setVisible(false);
        setTimeout(() => {
            setVisible(true);
        }, 10);
    };
    const workListRef = useRef<any>(null);


    const fetchRecords = async () => {
        workListRef.current?.fetchRecords && workListRef.current.fetchRecords();
    }
  return (
    <Layout className={'Workbenches  p-4'}>

        <Layout.Content>
            {/* 顶部信息区 */}
            <Row gutter={[16, 16]} className="mb-4">
                <Col span={12}>
                    <Card className="Card">
                        <div className={'worTopView clearfix'}>
                            <div className={'userBaseView'}>
                                <div className={'hoView'}>
                                    <Title  heading={3} className="mb-2 nameView">你好,{currentUser?.name}  </Title>

                                    <div className="flex btnView">
                                        <Button  className={'btn'} theme="solid"  onClick={handleOpenBatchRecord}
                                                type="primary">立即报工</Button>
                                        <Button className={'btn'} theme="outline" type="primary" onClick={() => {
                                            store.dispatch(setActiveTab('approval'));
                                         //   fetchRecords();
                                        }}>审批</Button>
                                    </div>
                                    <div className={'review'}>
                                        <p className={'guize'}>工时填写规则<a onClick={() => setRulesModalVisible(true)}>查看</a></p>
                                    </div>

                                </div>

                                <div className="flex-shrink-0">
                                    <img src={workbenchesPic} alt="Illustration" className="workbenchesPic"/>
                                </div>
                            </div>

                        </div>
                    </Card>
                </Col>

                <Col span={12}>
                    <Card className="Card">
                        <div className={'boxView'}>
                            <div className={'headerView clearfix'}>
                                <Text className={'boxtitle'}>审批待办列表</Text>
                                <div className={'rightView'}>
                                    <a className={'btn'}  onClick={() => {
                                        store.dispatch(setActiveTab('approval'));
                                    }}>全部</a><IconChevronRight  className={'icon'}/>
                                </div>
                            </div>
                            <div className={'contentView'}>
                                <ApprovalList/>
                            </div>

                        </div>
                    </Card>
                </Col>
            </Row>

            <Row gutter={[16, 16]} className="mb-4">
                <Col span={24}>
                    <Card className="WorkListCard">
                        <WorkList ref={workListRef}/>
                    </Card>
                </Col>
            </Row>


        </Layout.Content>

        <WorkHourRulesModal
            visible={rulesModalVisible}
            onClose={() => setRulesModalVisible(false)}
        />

        {/* 工时登记表单 */}
        {editMode === 'edit' && (
            <HourRecordEditForm
                visible={visible}
                isEdit={true}
                initialData={toFormInitialData(currentRecord)}
                onCancel={() => setVisible(false)}
                onSave={() => { setVisible(false); fetchRecords(); }}
                onSubmit={() => { setVisible(false); fetchRecords(); }}
            />
        )}
        {/* 工时登记详情 */}
        {editMode === 'view' && (
            <HourRecordEditForm
                visible={visible}
                isEdit={false}
                readOnly={true}
                initialData={toFormInitialData(currentRecord)}
                onCancel={() => setVisible(false)}
                onSave={() => { setVisible(false); fetchRecords(); }}
                onSubmit={() => { setVisible(false); fetchRecords(); }}
            />
        )}
        {editMode === 'add' && (
            <HourRecordForm
                visible={visible}
                initialData={undefined}
                onCancel={() => setVisible(false)}
                onSave={() => { setVisible(false); fetchRecords(); }}
                onSubmit={() => { setVisible(false); fetchRecords(); }}
            />
        )}

    </Layout>
  )
}

export default Workbenches